package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("数据交叉检查规则")
@TableName("ors_device_data_rule_abnormal_detail_day")
public class DeviceDataRule extends Model<DeviceDataRule> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "statDate", value = "异常数据发生日期")
    @TableField(value = "stat_date")
    private Date statDate;

    @ApiModelProperty(name = "deviceName", value = "设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "abnormalPhenomenonCode", value = "异常现象编码")
    @TableField(value = "abnormal_phenomenon_code")
    private String abnormalPhenomenonCode;

    @ApiModelProperty(name = "checkRuleCode", value = "数据交叉检查规则编码")
    @TableField(value = "check_rule_code")
    private String checkRuleCode;

    @ApiModelProperty(name = "dataCenterId", value = "数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站")
    @TableField(value = "data_center_id")
    private Integer dataCenterId;

    @ApiModelProperty(name = "abnormalData", value = "异常数据")
    @TableField(value = "abnormal_data")
    private String abnormalData;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

