package com.rc.admin.ors.quality.disruptor;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.lmax.disruptor.EventHandler;
import com.rc.admin.ors.quality.model.DeviceDataModel;
import com.rc.admin.ors.quality.service.OrsRuleAdaptService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/10/24 9:04
 * @describe
 */
@Component
public class WorkIotHandler implements EventHandler<DeviceDataModel> {

    @Resource
    private OrsRuleAdaptService orsRuleAdaptService;
    ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("ors-pool-%d").build();

    ExecutorService executor = new ThreadPoolExecutor(2, 2,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    @Override
    public void onEvent(DeviceDataModel dataModel, long l, boolean b) {
         // 监听到消息，开始消费
        executor.execute(()->{
            // 开始处理对应的业务逻辑
            orsRuleAdaptService.ruleAdapt(dataModel);
        });
    }
}
