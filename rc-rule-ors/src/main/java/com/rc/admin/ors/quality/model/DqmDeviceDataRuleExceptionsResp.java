package com.rc.admin.ors.quality.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

@Data
@Slf4j
public class DqmDeviceDataRuleExceptionsResp {

    @ApiModelProperty(value = "事业部")
    private String sybbh;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "事业部Code")
    private String sybbhCode;

    @ApiModelProperty(value = "模型ID")
    private String modelId;


    @ApiModelProperty(value = "数据中心")
    private Integer dataCenterId;


    @ApiModelProperty(value = "设备编号")
    private String deviceName;


    @ApiModelProperty(value = "物联盒ID")
    private String rcAssetId;


    @ApiModelProperty(value = "检查规则")
    private String checkRuleCode;

    @ApiModelProperty(value = "检查规则名称")
    private String checkRuleName;


    @ApiModelProperty(value = "异常数量")
    private String abnormalData;


    @ApiModelProperty(value = "模型名称")
    private String modelName;


    @ApiModelProperty(value = "产品组")
    private String zehdSpart;

    @ApiModelProperty(value = "产品组名称")
    private String zehdSpartdesc;

    @ApiModelProperty(value = "前端展示JSON")
    private String  abnormalDetails;


    @ApiModelProperty(value = "前端展示List")
    private List<DqmRuleAbnormalModel> ruleAbnormalModelList;


    private final ObjectMapper objectMapper = new ObjectMapper();

    public void setAbnormalDetails(String abnormalDetails) {
        this.abnormalDetails = abnormalDetails;
        if (abnormalDetails != null && !abnormalDetails.isEmpty()) {
            try {
                this.ruleAbnormalModelList = objectMapper.readValue(abnormalDetails, new TypeReference<List<DqmRuleAbnormalModel>>() {});
            } catch (IOException e) {
                log.info("异常数据转换失败:{}",e.getMessage(),e);
            }
        }
    }

}