package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Setter
@Getter
@ApiModel("设备台账分页")
public class DeviceLedgerReq {

    @ApiModelProperty("设备状态 0=停用 1=未激活 2=已激活 默认未激活")
    private Integer deviceStatus;

    @ApiModelProperty("数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站")
    private Integer dataCenterId;

    @ApiModelProperty("新事业部编号")
    private String newDivisionCode;

    @ApiModelProperty("新产品组编号")
    private String newProductGroupCode;

    @ApiModelProperty("国区编号")
    private String countryRegionCode;

    @ApiModelProperty("新大区编码")
    private String newRegionCode;

    @ApiModelProperty("新国家编码")
    private String newCountryCode;

    @ApiModelProperty("事业部编号")
    private String division;

    @ApiModelProperty("大区")
    private String region;

    @ApiModelProperty("模型Id")
    private String modelId;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("产品组")
    private String productGroup;

    @ApiModelProperty("物标识/设备编号")
    private String assetId;

    @ApiModelProperty("实例名称")
    private String name;

    @ApiModelProperty("物模型名称")
    private String modelName;

//    @ApiModelProperty("存量分类")
//    private String stockClassification;

//    @ApiModelProperty("问题跟进(0==>否；1==>是)")
//    private Integer problemFollow;
//
//    @ApiModelProperty("是否剔除(0==>否；1==>是)")
//    private Integer isEliminate;

    @ApiModelProperty("出厂时长")
    private Integer factoryDuration;

    @ApiModelProperty("注册时间开始")
    private Date createdBegin;

    @ApiModelProperty("注册时间结束")
    private Date createdEnd;

    @ApiModelProperty("设备编号")
    private String deviceNo;

    @ApiModelProperty("crm注册时间（0==>否；1==>是）")
    private Integer crmRegister;

    @ApiModelProperty("离线时间开始")
    private Date beginDate;

    @ApiModelProperty("离线时间结束")
    private Date endDate;

    @ApiModelProperty("设备编号、物标识、物实例查询参数")
    private String searchDeviceNo;

    @ApiModelProperty("是否剔除(0==>否；1==>是)")
    private String isEliminate;

    @ApiModelProperty("离线时长CODE")
    public String offlineTimeCode;

    @ApiModelProperty("根云物标识")
    private String rcAssetId;

    private Integer installType;

    private String agentName;

    private String userName;

    private String noRegisterDeviceList;

    private List<String> deviceCodes;

    private String storeCategory;

    private Date startTime;

    private Date endTime;

    private long pageSize = 15;

    /**
     * 当前页
     */
    private long current = 1;

    @ApiModelProperty("排序属性")
    private String sortColumn = "obdi.created";

    @ApiModelProperty("排序方式 DESC=倒叙  ASC=正序")
    private String sortBy = "DESC";

    @ApiModelProperty("异常标识 0=无异常 1=异常")
    private String exceFlag;

    // 查询模式: offline_list 长期离线, not_activat_list 长期未激活
    private String queryModel;

}
