package com.rc.admin.ors.quality.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rc.admin.ors.quality.model.DeviceMqttConnectInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/02/26/16:54
 * @Version 1.0
 */
@Data
public class DeviceResponse {
    private DeviceMetadata metadata;
    private DeviceMqttConnectInfo[] payload;

    @Data
    public static class DeviceMetadata {
        @JsonProperty("skip")
        private int skip;

        @JsonProperty("limit")
        private int limit;

        @JsonProperty("totalCount")
        private int totalCount;
    }

    public static DeviceResponse getDeviceResponse(String jsonStr){
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        DeviceResponse deviceResponse = new DeviceResponse();
        try {
            deviceResponse = objectMapper.readValue(jsonStr, DeviceResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceResponse;
    }

}
