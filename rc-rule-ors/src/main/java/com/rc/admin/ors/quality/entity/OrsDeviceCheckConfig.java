package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-08 15:15:45
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_device_check_config", description = "设备剔除检查配置")
public class OrsDeviceCheckConfig extends Model<OrsDeviceCheckConfig> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @TableField(value = "uuid")
    private String uuid;

    @ApiModelProperty(name = "excludeType", value = "WHOLE=整机，该设备不做任何数据质量检查，只要存在该项，其他剔除类型都失效；指定设备的某一项剔除")
    @TableField(value = "exclude_type")
    private String excludeType;

    @ApiModelProperty(name = "excludeResean", value = "剔除原因")
    @TableField(value = "exclude_resean")
    private String excludeResean;

    @TableField(value = "create_user")
    private String createUser;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "update_user")
    private String updateUser;

    @ApiModelProperty(name = "delFlag", value = "删除标识 0=未删除 1=已删除")
    @TableField(value = "del_flag")
    private Integer delFlag;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "assetId", value = "物标识")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "deviceName", value = "设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    @TableField(value = "property_name")
    private String propertyName;

    @ApiModelProperty(name = "paramCode", value = "属性编码")
    @TableField(value = "param_code")
    private Integer paramCode;

    private String paramName;

    @TableField(exist = false)
    private List<MuiltiExcludType> excludeTypes;

    // "数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站")
    @TableField(exist = false)
    private Integer dataCenterId;
    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Getter
    @Setter
    public static class MuiltiExcludType {

        private Integer paramCode;

        private String propertyName;

        private String excludeType;
    }
}

