package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:40
 * @describe
 */
@Getter
@Setter
public class ModleIndicatorAndExclude {

    @ApiModelProperty(name = "property", value = "属性")
    private String property;

    @ApiModelProperty(name = "paramCode", value = "属性编码")
    private String paramCode;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    private String propertyName;

    @ApiModelProperty(name = "indicatorName", value = "指标名称")
    private String indicatorName;

    @ApiModelProperty(name = "indicatorRule", value = "指标规则")
    private String indicatorRule;

    @ApiModelProperty(name = "indicatorIdentity", value = "指标类型 1=完整性 2=合理性")
    private String indicatorIdentity;

    @ApiModelProperty(name = "indicatorIdentity", value = "指标类型 1=完整性 2=合理性")
    private String indicatorDesc;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(name = "excludeType", value = "WHOLE=整机，该设备不做任何数据质量检查，只要存在该项，其他剔除类型都失效；指定设备的某一项剔除")
    private String excludeType;

    @ApiModelProperty(name = "excludeResean", value = "剔除原因")
    private String excludeResean;

    @ApiModelProperty(name = "剔除加入人")
    private String createUser;

    @ApiModelProperty(name = "剔除加入时间")
    private Date createTime;

    @ApiModelProperty(name = "问题处理进度", value = "0=未处理 1=处理中 2=处理完成")
    private String quesProgress;

    private String indicatorJson;
}
