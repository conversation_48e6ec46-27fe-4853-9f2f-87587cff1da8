package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 根云设备数据同步表(DeviceInfoSync)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-21 11:16:44
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("根云设备数据同步表")
@TableName("ors_iot_device_info_sync")
public class DeviceInfoSync extends Model<DeviceInfoSync> {


    @ApiModelProperty(name = "assetId", value = "物标识", required = true)
    @NotNull(message = "物标识不能为空")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "modelId", value = "物模型id")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "thingId", value = "物实例id")
    @TableField(value = "thing_id")
    private String thingId;

    @ApiModelProperty(name = "classId", value = "设备类型")
    @TableField(value = "class_id")
    private String classId;

    @ApiModelProperty(name = "protocol", value = "设备协议")
    @TableField(value = "protocol")
    private String protocol;

    @ApiModelProperty(name = "phase", value = "同步")
    @TableField(value = "phase")
    private String phase;

    @ApiModelProperty(name = "name", value = "设备名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "description", value = "设备描述")
    @TableField(value = "description")
    private String description;

    @ApiModelProperty(name = "userName", value = "设备连接MQTT Broker时的用户名")
    @TableField(value = "user_name")
    private String userName;

    @ApiModelProperty(name = "authToken", value = "设备连接MQTT Broker时的密码")
    @TableField(value = "auth_token")
    private String authToken;

    @ApiModelProperty(name = "created", value = "创建时间")
    @TableField(value = "created")
    private Date created;

    @ApiModelProperty(name = "createdBy", value = "创建用户")
    @TableField(value = "created_by")
    private String createdBy;

    @ApiModelProperty(name = "updated", value = "修改时间")
    @TableField(value = "updated")
    private Date updated;

    @ApiModelProperty(name = "updatedBy", value = "修改用户")
    @TableField(value = "updated_by")
    private String updatedBy;

    @ApiModelProperty(name = "connectionType", value = "连接方式")
    @TableField(value = "connection_type")
    private String connectionType;

    @ApiModelProperty(name = "manufacturer", value = "设备厂商")
    @TableField(value = "manufacturer")
    private String manufacturer;

    @ApiModelProperty(name = "model", value = "设备型号")
    @TableField(value = "model")
    private String model;

    @ApiModelProperty(name = "fwVersion", value = "设备固件版本号")
    @TableField(value = "fw_version")
    private String fwVersion;

    @ApiModelProperty(name = "hwVersion", value = "设备硬件版本号")
    @TableField(value = "hw_version")
    private String hwVersion;

    @ApiModelProperty(name = "simImsi", value = "设备SIM卡信息")
    @TableField(value = "sim_imsi")
    private String simImsi;

    @ApiModelProperty(name = "phoneNumber", value = "手机号码")
    @TableField(value = "phone_number")
    private String phoneNumber;

    @ApiModelProperty(name = "dataSource", value = "数据源（1==>根云；2==>新C）")
    @TableField(value = "data_source")
    private Integer dataSource;

    @ApiModelProperty(name = "installType", value = "安装分类（1==>前装 ；2.==>后装）")
    @TableField(value = "install_type")
    private Integer installType;

    @TableField(value = "asset_id_nrc")
    private String assetIdNrc;

    @ApiModelProperty(name = "olineStatu", value = "在线状态 0=false 1=true")
    @TableField(value = "oline_statu")
    private Boolean olineStatu;

    @ApiModelProperty(name = "activeStatu", value = "激活状态 0=false 1=true")
    @TableField(value = "active_statu")
    private Boolean activeStatu;

    @ApiModelProperty(name = "deviceStatus", value = "设备状态 0=停用 1=未激活 2=已激活 默认未激活")
    @TableField(value = "device_status")
    private Integer deviceStatus;

    @ApiModelProperty(name = "dataCenterId", value = "数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站")
    @TableField(value = "data_center_id")
    private Integer dataCenterId;

    @ApiModelProperty(name = "firstDataTime", value = "首次上数时间，这里用作首次激活时间")
    @TableField(value = "first_data_time")
    private Date firstDataTime;

    @TableField(value = "country")
    private String country;


    @TableField(value = "country_code")
    private String countryCode;
}

