package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.Date;

/**
 * 物模型同步表(ModelInfoSync)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:09:27
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("物模型同步表")
@TableName("ors_iot_model_info_sync")
public class ModelInfoSync extends Model<ModelInfoSync> {


    @ApiModelProperty(name = "category", value = "设备模型行业分类索引")
    @TableField(value = "category")
    private String category;

    @ApiModelProperty(name = "deptScope", value = "组织id")
    @TableField(value = "dept_scope")
    private String deptScope;

    @ApiModelProperty(name = "deptScopeFullName", value = "组织长名称")
    @TableField(value = "dept_scope_full_name")
    private String deptScopeFullName;

    @ApiModelProperty(name = "description", value = "物模型描述")
    @TableField(value = "description")
    private String description;

    @ApiModelProperty(name = "fullGroupName", value = "全路径名称")
    @TableField(value = "full_group_name")
    private String fullGroupName;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "modelKey", value = "模型认证标识")
    @TableField(value = "model_key")
    private String modelKey;

    @ApiModelProperty(name = "name", value = "物模型名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "thingType", value = "模型类型")
    @TableField(value = "thing_type")
    private String thingType;

    @ApiModelProperty(name = "offlineCheckPeriod", value = "设备离线检测周期，单位：毫秒；")
    @TableField(value = "offline_check_period")
    private Integer offlineCheckPeriod;

    @ApiModelProperty(name = "isPublished", value = "是否已发布")
    @TableField(value = "is_published")
    private Boolean isPublished;

    @ApiModelProperty(name = "timestampVaryRangeMillis", value = "工况时间差范围，单位：毫秒")
    @TableField(value = "timestamp_vary_range_millis")
    private BigInteger timestampVaryRangeMillis;

    @ApiModelProperty(name = "timestampUnit", value = "工况时间单位")
    @TableField(value = "timestamp_unit")
    private String timestampUnit;

    @ApiModelProperty(name = "created", value = "创建时间")
    @TableField(value = "created")
    private Date created;

    @ApiModelProperty(name = "createdBy", value = "创建用户")
    @TableField(value = "created_by")
    private String createdBy;

    @ApiModelProperty(name = "creatorId", value = "创建用户Id")
    @TableField(value = "creator_id")
    private String creatorId;

    @ApiModelProperty(name = "updated", value = "更新时间")
    @TableField(value = "updated")
    private Date updated;

    @ApiModelProperty(name = "updatedBy", value = "更新用户")
    @TableField(value = "updated_by")
    private String updatedBy;

    @ApiModelProperty(name = "updatorId", value = "更新用户Id")
    @TableField(value = "updator_id")
    private String updatorId;

    @ApiModelProperty(name = "enableSelfReg", value = "是否支持自动注册物实例开关")
    @TableField(value = "enable_self_reg")
    private Boolean enableSelfReg;

    @ApiModelProperty(name = "modelSecret", value = "模型认证密钥")
    @TableField(value = "model_secret")
    private String modelSecret;

    @ApiModelProperty(name = "enableSetGateway", value = "是否支持实例自组网开关")
    @TableField(value = "enable_set_gateway")
    private Boolean enableSetGateway;

    @ApiModelProperty(name = "version", value = "版本")
    @TableField(value = "version")
    private String version;

    @ApiModelProperty(name = "isAbstract", value = "是否抽象模型")
    @TableField(value = "is_abstract")
    private Boolean isAbstract;

    @ApiModelProperty(name = "hasActiveVersion", value = "是否有已发版版本")
    @TableField(value = "has_active_version")
    private Boolean hasActiveVersion;

    @ApiModelProperty(name = "tenantId", value = "租户id")
    @TableField(value = "tenant_id")
    private String tenantId;

    @ApiModelProperty(name = "publishStatus", value = "异步发布场景下，标识发布状态 publishing（发布中），publish_success(发布成功)，publish_fail(发布失败)")
    @TableField(value = "publish_status")
    private String publishStatus;

    @ApiModelProperty(name = "enableDiagnosis", value = "是否启用周期诊断")
    @TableField(value = "enable_diagnosis")
    private Boolean enableDiagnosis;

    @ApiModelProperty(name = "cloudDataVisualizationName", value = "跳转云视界链接名称")
    @TableField(value = "cloud_data_visualization_name")
    private String cloudDataVisualizationName;

    @TableField(value = "send_input_switch")
    private Boolean sendInputSwitch;

    @ApiModelProperty(name = "modelStatus", value = "模型状态")
    @TableField(value = "model_status")
    private String modelStatus;

}

