package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备数据异常明细统计结果-天表(DeviceDataAbnormalDetailDay)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-09 13:21:31
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备数据异常明细统计结果-天表")
@TableName("ors_device_data_abnormal_detail_day")
public class DeviceDataAbnormalDetailDay extends Model<DeviceDataAbnormalDetailDay> {
// ors_device_data_abnormal_detail_day 这个表的数据是从异常地表来的汇总，一个设备的某一个属性只会保留一条记录
    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "deviceName", value = "设备编号", required = true)
    @NotNull(message = "设备编号不能为空")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "abnormalCount", value = "异常次数", required = true)
    @NotNull(message = "异常次数不能为空")
    @TableField(value = "abnormal_count")
    private Integer abnormalCount;

    @ApiModelProperty(name = "property", value = "属性", required = true)
    @NotNull(message = "属性不能为空")
    @TableField(value = "property")
    private String property;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    @TableField(value = "property_name")
    private String propertyName;

    @ApiModelProperty(name = "abnormalName", value = "异常项", required = true)
    @NotNull(message = "异常项不能为空")
    @TableField(value = "abnormal_name")
    private String abnormalName;

    @ApiModelProperty(name = "statDate", value = "统计日期")
    @TableField(value = "stat_date")
    private Date statDate;

    @ApiModelProperty(name = "createTime", value = "生成时间", required = true)
    @NotNull(message = "生成时间不能为空")
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "abnormal_time")
    private Date abnormalTime;

    @TableField(value = "abnormal_data")
    private String abnormalData;

    @ApiModelProperty(name = "abnormalCode", value = "异常项code")
    @TableField(value = "abnormal_code")
    private Integer abnormalCode;

    @ApiModelProperty(name = "paramCode", value = "属性code")
    @TableField(value = "param_code")
    private Integer paramCode;

    @ApiModelProperty(name = "detailId", value = "对应原始表的ID")
    @TableField(value = "detail_id")
    private String detailId;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

