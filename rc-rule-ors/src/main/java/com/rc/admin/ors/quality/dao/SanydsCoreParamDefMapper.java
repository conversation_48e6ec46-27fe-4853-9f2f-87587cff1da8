package com.rc.admin.ors.quality.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.entity.SanydsCoreParamDef;
import com.rc.admin.ors.quality.model.DeviceQuelityCountQuery;
import com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp;
import com.rc.admin.ors.quality.model.DeviceRatioResp;
import com.rc.admin.ors.quality.model.TotalDeviceStatisticsResp;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 设备台账(OrsBaseDeviceInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-08 18:20:11
 */
@DS("sany_data_service")
public interface SanydsCoreParamDefMapper extends BaseMapper<SanydsCoreParamDef> {

}

