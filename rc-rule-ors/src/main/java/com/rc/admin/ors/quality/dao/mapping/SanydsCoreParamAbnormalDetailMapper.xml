<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.SanydsCoreParamAbnormalDetailMapper">
    <select id="countAbnormalDevice" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT device_name)   FROM sany_data_service.sanyds_core_param_abnormal_detail WHERE (stat_date = #{bizDate}::DATE)
    </select>
</mapper>