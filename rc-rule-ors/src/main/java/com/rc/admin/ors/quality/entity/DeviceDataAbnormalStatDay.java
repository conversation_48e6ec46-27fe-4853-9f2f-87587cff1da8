package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 设备历史异常统计表(DeviceDataAbnormalStatDay)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-04 16:16:15
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备历史异常统计表")
@TableName("ors_device_data_abnormal_stat_day")
public class DeviceDataAbnormalStatDay extends Model<DeviceDataAbnormalStatDay> {
// ors_device_data_abnormal_stat_day 这个表的数据也是从异常底表汇总来的，他保留的是一个设备的某个属性当天出现了多少次异常
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "deviceName", value = "设备编号")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "statDate", value = "统计日期")
    @TableField(value = "stat_date")
    private Date statDate;

    @ApiModelProperty(name = "createTime", value = "生成时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "deviceStatus9008", value = "设备状态属性值未上报")
    @TableField(value = "device_status_9008")
    private Integer deviceStatus9008;

    @ApiModelProperty(name = "deviceLocation9008", value = "设备位置属性值未上报")
    @TableField(value = "device_location_9008")
    private Integer deviceLocation9008;

    @ApiModelProperty(name = "deviceLocation9001", value = "设备位置属性值异常")
    @TableField(value = "device_location_9001")
    private Integer deviceLocation9001;

    @ApiModelProperty(name = "deviceLocation9002", value = "设备位置属性值超限")
    @TableField(value = "device_location_9002")
    private Integer deviceLocation9002;

    @ApiModelProperty(name = "deviceLocation9007", value = "设备位置漂移")
    @TableField(value = "device_location_9007")
    private Integer deviceLocation9007;

    @ApiModelProperty(name = "engineWorktime9008", value = "发动机工作时间属性值未上报")
    @TableField(value = "engine_worktime_9008")
    private Integer engineWorktime9008;

    @ApiModelProperty(name = "engineWorktime9001", value = "发动机工作时间属性值异常")
    @TableField(value = "engine_worktime_9001")
    private Integer engineWorktime9001;

    @ApiModelProperty(name = "engineWorktime9004", value = "发动机工作时间属性值逆增长")
    @TableField(value = "engine_worktime_9004")
    private Integer engineWorktime9004;

    @ApiModelProperty(name = "workingTime9008", value = "工作时间属性值未上报")
    @TableField(value = "working_time_9008")
    private Integer workingTime9008;

    @ApiModelProperty(name = "workingTime9001", value = "工作时间属性值异常")
    @TableField(value = "working_time_9001")
    private Integer workingTime9001;

    @ApiModelProperty(name = "workingTime9004", value = "工作时间属性值逆增长")
    @TableField(value = "working_time_9004")
    private Integer workingTime9004;

    @ApiModelProperty(name = "totalFuelConsumption9008", value = "总油耗属性值未上报")
    @TableField(value = "total_fuel_consumption_9008")
    private Integer totalFuelConsumption9008;

    @ApiModelProperty(name = "totalFuelConsumption9001", value = "总油耗属性值异常")
    @TableField(value = "total_fuel_consumption_9001")
    private Integer totalFuelConsumption9001;

    @ApiModelProperty(name = "totalFuelConsumption9004", value = "总油耗属性值逆增长")
    @TableField(value = "total_fuel_consumption_9004")
    private Integer totalFuelConsumption9004;

    @ApiModelProperty(name = "pumpingVolume9008", value = "泵送方量属性值未上报")
    @TableField(value = "pumping_volume_9008")
    private Integer pumpingVolume9008;

    @ApiModelProperty(name = "pumpingVolume9001", value = "泵送方量属性值异常")
    @TableField(value = "pumping_volume_9001")
    private Integer pumpingVolume9001;

    @ApiModelProperty(name = "pumpingVolume9004", value = "泵送方量属性值逆增长")
    @TableField(value = "pumping_volume_9004")
    private Integer pumpingVolume9004;

    @ApiModelProperty(name = "drivingMileage9008", value = "行驶里程属性值未上报")
    @TableField(value = "driving_mileage_9008")
    private Integer drivingMileage9008;

    @ApiModelProperty(name = "drivingMileage9001", value = "行驶里程属性值异常")
    @TableField(value = "driving_mileage_9001")
    private Integer drivingMileage9001;

    @ApiModelProperty(name = "drivingMileage9004", value = "行驶里程属性值逆增长")
    @TableField(value = "driving_mileage_9004")
    private Integer drivingMileage9004;

    @ApiModelProperty(name = "deviceQuestionId", value = "问题跟进ID")
    @TableField(value = "device_question_id")
    private Integer deviceQuestionId;

    @TableField(value = "device_location_cnt")
    private Integer device_location_cnt;

}

