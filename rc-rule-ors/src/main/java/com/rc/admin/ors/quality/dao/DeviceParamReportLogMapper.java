package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.DeviceParamReportLog;
import com.rc.admin.ors.quality.model.DeviceCountModel;
import com.rc.admin.ors.quality.model.DeviceQuelityCountQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备工况上报记录(DeviceParamReportLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-26 12:35:44
 */
public interface DeviceParamReportLogMapper extends BaseMapper<DeviceParamReportLog> {

    void generateReportLog(@Param("bizDate") Date bizDate);

    void ors_device_param_report_day(@Param("bizDate") Date bizDate
            ,@Param("minDate") String minDate
            ,@Param("maxDate") String maxDate
            ,@Param("tbsuffix") String tbsuffix);

    @Select("select count(1) cnt FROM dqm.ors_device_param_report_log WHERE report_date = #{bizDate}::DATE")
    Integer getReportLogCnt(@Param("bizDate") Date bizDate);

    List<DeviceCountModel> countDataQuality(DeviceQuelityCountQuery query);
    List<DeviceCountModel> countDataQuality_new(DeviceQuelityCountQuery query);

    List<Map<String,Object>> analysis(@Param("bizDate") Date bizDate);
    List<Map<String,Object>> abnormal_analysis(@Param("bizDate") Date bizDate);
    List<Map<String,Object>> abnormal_detail(@Param("bizDate") Date bizDate);

    void job_analysis(@Param("bizDate") Date bizDate);
    void job_abnormal_analysis(@Param("bizDate") Date bizDate);
    void job_abnormal_detail(@Param("bizDate") Date bizDate);

}

