package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备问题跟进(DeviceQuestion)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-17 10:58:34
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备问题跟进")
@TableName("ors_device_question")
public class DeviceQuestion extends Model<DeviceQuestion> {

    @TableId(type = IdType.AUTO)
    private Integer id;


    @ApiModelProperty(name = "assetId", value = "设备物标识", required = true)
    @NotNull(message = "设备物标识不能为空")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "deviceCode", value = "设备编号", required = true)
    @NotNull(message = "设备编号不能为空")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "checkItem", value = "检查项", required = true)
    @NotNull(message = "检查项不能为空")
    @TableField(value = "check_item")
    private String checkItem;

    @ApiModelProperty(name = "exceItem", value = "异常项", required = true)
    @NotNull(message = "异常项不能为空")
    @TableField(value = "exce_item")
    private String exceItem;

    @ApiModelProperty(name = "quesLevel", value = "问题跟进优先级 P0 P1 P2 P3", required = true)
    @NotNull(message = "问题跟进优先级 P0 P1 P2 P3不能为空")
    @TableField(value = "ques_level")
    private String quesLevel;

    @ApiModelProperty(name = "curStep", value = "当前环节", required = true)
    @NotNull(message = "当前环节不能为空")
    @TableField(value = "cur_step")
    private String curStep;

    @ApiModelProperty(name = "userAccount", value = "当前处理责任人账号")
    @TableField(value = "user_account")
    @NotNull(message = "请选择处理人")
    private String userAccount;

    @ApiModelProperty(name = "userName", value = "当前处理责任人姓名")
    @TableField(value = "user_name")
    private String userName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @ApiModelProperty(name = "modelId", value = "物模型ID", required = true)
    @NotNull(message = "物模型ID不能为空")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "curStepName", value = "当前环节名称")
    @TableField(value = "cur_step_name")
    private String curStepName;

    @ApiModelProperty(name = "qStart", value = "问题发生的开始时间")
    @TableField(value = "q_start")
    @NotNull(message = "请选择问题发生的开始时间")
    private Date qStart;

    @ApiModelProperty(name = "qEnd", value = "问题发生的结束时间")
    @TableField(value = "q_end")
    @NotNull(message = "请选择问题发生的结束时间")
    private Date qEnd;

    @ApiModelProperty(name = "checkItemCode", value = "检查项编码集合")
    @TableField(value = "check_item_code")
    private String checkItemCode;

    @ApiModelProperty(name = "exceItemCode", value = "异常项编码集合")
    @TableField(value = "exce_item_code")
    private String exceItemCode;

    private int delFlag;

    @ApiModelProperty(name = "questionResean", value = "问题原因")
    @TableField(exist = false)
    private String questionResean;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

