<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsDeviceInfoRelationMapper">

    <insert id="transferData">
        delete from dqm.ors_ml_device_info_relation;
        insert into dqm.ors_ml_device_info_relation (
            device_id, device_name, serial_num, device_create_date, device_update, thing_instance_id,
            thing_model_id, host_id, tbox_id, create_time, bind_create_date, bind_update, ml_model_id,
            rc_asset_id
        )
        select device_id, device_name, serial_num, device_create_date, device_update, thing_instance_id,
               thing_model_id, host_id, tbox_id, CURRENT_TIMESTAMP, bind_create_date, bind_update, ml_model_id,
               rc_asset_id
        from dqm.ors_ml_device_info_relation_sync;
    </insert>


    <select id="countDeviceByParamCode" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
        T1.division,
        T1.division_code,
        T1.product_group,
        T1.product_group_code,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            T4.property_name,
        </if>
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_abnormal_num,
        COUNT ( DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.null_param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.null_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T3.asset_id END
        ) AS property_null_abnormal_total_num,
        COUNT ( DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.abnormal_param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.abnormal_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T3.asset_id END
        ) AS property_param_abnormal_total_num,
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.report_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_report_num,
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.report_param_codes not LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_un_report_num
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            ,COUNT(DISTINCT CASE WHEN T2.exclude_type != 'WHOLE' AND T2.param_code = T4.param_code THEN T1.asset_id END ) AS property_exclud_num,
            COUNT(DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END) AS property_active_num
        </if>
        FROM
        (
        SELECT
        T1.asset_id,
        <if test="sybbh == '1'.toString()">
            omd.division_name AS division,
            omd.division_code,
        </if>
        omd.product_group_name AS product_group,
        omd.product_group_code,
        T1.crm_register,
        T1.active_statu,
        T1.model_id
        FROM dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join (
        select distinct asset_id, device_location from dqm.ors_device_location
        where device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 on T1.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="endTime != null and endTime != ''">
                <!--                                <if test="storeCategory != null and storeCategory == '1'.toString()">-->
                <!--                                    AND T1.first_data_time &lt;= #{endTime}::DATE-->
                <!--                                </if>-->
                <if test="storeCategory != null and storeCategory == '2'.toString()">
                    AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                </if>
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                AND (
                omd.division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        ) T1
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            INNER JOIN dqm.ors_model_properties_config T4 ON T4.model_id = T1.model_id
            <if test="inspection != null and inspection != ''">
                AND T4.param_code IN
                <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::INT4
                </foreach>
            </if>
        </if>
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = T1.asset_id
        <if test="endTime != null and endTime != ''">
            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        LEFT JOIN (
        SELECT
        T3.param_codes,
        T3.null_param_codes,
        T3.abnormal_param_codes,
        T3.report_param_codes,
        T3.asset_id
        FROM
        dqm.ors_device_param_report_log T3
        WHERE
        T3.asset_id NOT IN (
        SELECT asset_id FROM dqm.ors_device_check_config T2
        <where>
            exclude_type = 'WHOLE'
            <if test="endTime != null and endTime != ''">
                AND T2.create_time &lt;= #{endTime}::TIMESTAMP
            </if>
        </where>
        )
        <if test="startTime != null and startTime != ''">
            AND T3.report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND T3.report_date &lt;= #{endTime}::DATE
        </if>
        ) T3 ON T3.asset_id = T1.asset_id

        GROUP BY
        T1.division,
        T1.division_code,
        T1.product_group,
        T1.product_group_code
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            ,T4.property_name
        </if>

    </select>
</mapper>
