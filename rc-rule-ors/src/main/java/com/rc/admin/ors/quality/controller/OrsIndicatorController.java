package com.rc.admin.ors.quality.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.entity.OrsIndicator;
import com.rc.admin.ors.quality.entity.OrsModelPropertyIndicator;
import com.rc.admin.ors.quality.service.OrsIndicatorService;
import com.rc.admin.ors.quality.service.OrsModelPropertyIndicatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 指标定义(OrsIndicator)表控制层  此功能未实际使用
 *
 * <AUTHOR>
 * @since 2023-10-30 14:10:13
 */
@ResponseResult
@RestController
@RequestMapping("orsIndicator")
@Api(tags = {"指标定义"})
public class OrsIndicatorController {
    /**
     * 服务对象
     */
    @Resource
    private OrsIndicatorService orsIndicatorService;

    @Resource
    private OrsModelPropertyIndicatorService orsModelPropertyIndicatorService;

    /**
     * 分页查询所有数据
     *
     * @param page         分页对象
     * @param orsIndicator 查询实体
     * @return 所有数据
     */
    @ApiOperation("查询配置的指标列表")
    @GetMapping
    public Page<OrsIndicator> selectAll(Page<OrsIndicator> page, OrsIndicator orsIndicator) {
        return orsIndicatorService.page(page, new QueryWrapper<>(orsIndicator));
    }

    /**
     * 新增数据
     *
     * @param orsIndicator 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增或更新某一项指标")
    @PostMapping
    public boolean insert(@RequestBody OrsIndicator orsIndicator) {
        if (null != orsIndicator.getId()) {
            return orsIndicatorService.updateById(orsIndicator);
        }
        return orsIndicatorService.save(orsIndicator);
    }

    @ApiOperation("绑定物模型属性与指标")
    @PostMapping("/bind")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelPropertyId", value = "配置的物模型属性id"),
            @ApiImplicitParam(name = "indicatorId", value = "指标id")
    })
    public boolean bindModelPropertiesIndicator(@RequestParam("modelPropertyId") Long modelPropertyId, @RequestParam("indicatorId") Long indicatorId){
        OrsModelPropertyIndicator indicator = new OrsModelPropertyIndicator();
        indicator.setIndicatorId(indicatorId);
        indicator.setModelPropertyId(modelPropertyId);
        return orsModelPropertyIndicatorService.save(indicator);
    }

    @ApiOperation("接触绑定物模型属性与指标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelPropertyId", value = "配置的物模型属性id，与指标id任一即可"),
            @ApiImplicitParam(name = "indicatorId", value = "指标id，与物模型属性id任一即可")
    })
    @PostMapping("/unbind")
    public boolean unBindModelPropertiesIndicator(Long modelPropertyId, Long indicatorId){
        if (null == modelPropertyId && null == indicatorId) {
            throw new IllegalArgumentException("缺少主要参数信息");
        }
        return orsModelPropertyIndicatorService.remove(
                new QueryWrapper<OrsModelPropertyIndicator>()
                        .lambda()
                        .eq(null != modelPropertyId, OrsModelPropertyIndicator::getModelPropertyId, modelPropertyId)
                        .eq(null != indicatorId, OrsModelPropertyIndicator::getIndicatorId, indicatorId)
        );
    }


    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation("删除某一项指标")
    @DeleteMapping
    public boolean delete(@RequestParam("id") Long id) {
        boolean b = orsIndicatorService.removeById(id);
        if (b) {
            orsModelPropertyIndicatorService.remove(
                    new QueryWrapper<OrsModelPropertyIndicator>()
                            .lambda()
                            .eq(OrsModelPropertyIndicator::getIndicatorId, id)
            );
        }
        return b;
    }
}

