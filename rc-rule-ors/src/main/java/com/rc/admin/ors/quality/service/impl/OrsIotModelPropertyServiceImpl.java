package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsModelPropertyMapper;
import com.rc.admin.ors.quality.entity.OrsModelProperty;
import com.rc.admin.ors.quality.service.OrsIotModelPropertyService;
import org.springframework.stereotype.Service;

/**
 * 物模型属性(OrsIotModelProperty)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 14:48:16
 */
@Service("orsIotModelPropertyService")
public class OrsIotModelPropertyServiceImpl extends ServiceImpl<OrsModelPropertyMapper, OrsModelProperty> implements OrsIotModelPropertyService {

}

