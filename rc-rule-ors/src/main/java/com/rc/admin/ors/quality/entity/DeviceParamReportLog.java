package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备工况上报记录(DeviceParamReportLog)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-10 11:26:08
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备工况上报记录")
@TableName("ors_device_param_report_log")
public class DeviceParamReportLog extends Model<DeviceParamReportLog> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "assetId", value = "设备标识", required = true)
    @NotNull(message = "设备标识不能为空")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "propertyNames", value = "上报的工况属性名称")
    @TableField(value = "property_names")
    private String propertyNames;

    @ApiModelProperty(name = "paramCodes", value = "上报的工况属性编码")
    @TableField(value = "param_codes")
    private String paramCodes;

    @ApiModelProperty(name = "nullProperties", value = "未上报工况的属性名称")
    @TableField(value = "null_properties")
    private String nullProperties;

    @ApiModelProperty(name = "nullParamCodes", value = "未上报工况的属性编码")
    @TableField(value = "null_param_codes")
    private String nullParamCodes;

    @ApiModelProperty(name = "reportDate", value = "数据上报时间")
    @TableField(value = "report_date")
    private Date reportDate;

    @ApiModelProperty(name = "createTime", value = "数据生成时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "abnormalProperties", value = "异常属性名称")
    @TableField(value = "abnormal_properties")
    private String abnormalProperties;

    @ApiModelProperty(name = "abnormalParamCodes", value = "异常属性编码")
    @TableField(value = "abnormal_param_codes")
    private String abnormalParamCodes;

    @TableField(value = "division")
    private String division;

    @TableField(value = "product_group")
    private String productGroup;

    @TableField(value = "division_code")
    private String divisionCode;

    @TableField(value = "product_group_code")
    private String productGroupCode;

    @TableField(value = "region")
    private String region;

    @TableField(value = "region_code")
    private String regionCode;

    @TableField(value = "country")
    private String country;

    @TableField(value = "country_code")
    private String countryCode;

    @TableField(value = "report_param_codes")
    private String report_param_codes;

    @TableField(value = "abnormal_codes")
    private String abnormal_codes;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

