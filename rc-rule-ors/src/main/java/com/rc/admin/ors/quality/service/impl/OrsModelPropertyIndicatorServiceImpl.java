package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsModelPropertyIndicatorMapper;
import com.rc.admin.ors.quality.entity.OrsModelPropertyIndicator;
import com.rc.admin.ors.quality.service.OrsModelPropertyIndicatorService;
import org.springframework.stereotype.Service;

/**
 * 物模型属性对应的指标(OrsModelPropertyIndicator)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-30 14:15:51
 */
@Service("orsModelPropertyIndicatorService")
public class OrsModelPropertyIndicatorServiceImpl extends ServiceImpl<OrsModelPropertyIndicatorMapper, OrsModelPropertyIndicator> implements OrsModelPropertyIndicatorService {

}

