package com.rc.admin.ors.quality.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.ors.quality.dao.OrsDeviceCheckConfigMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceDataAbnormalDetailMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.entity.OrsDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.DeviceDataModel;
import com.rc.admin.ors.quality.utils.GroovyShellUtil;
import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@EnableAsync
public class OrsRuleAdaptService {
    @Resource
    private OrsDeviceCheckConfigMapper deviceCheckConfigMapper;

    @Resource
    private OrsModelPropertiesConfigMapper devicePropertiesConfigMapper;

    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource
    private OrsDeviceDataAbnormalDetailMapper orsDeviceDataAbnormalDetailMapper;

    @NotNull
    private Binding buildBinding(JSONObject data, Long ruleId) {
        Binding binding = new Binding();
        binding.setVariable("data", data);
        binding.setVariable("time", System.currentTimeMillis());
        binding.setVariable("ruleId", ruleId);
        return binding;
    }

    private Object ruleScriptExecute(Binding binding, String ruleScript) {
        Object resultObject = null;
        try {
            resultObject = GroovyShellUtil.evaluate(ruleScript, binding);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("RuleAdaptService.ruleScriptExecute 脚本执行发生报错：", e);
        }
        return resultObject;
    }

    /**
     * 规则适配
     * @param model 工况
     */
    @Async
    public void ruleAdapt(DeviceDataModel model) {
        if (null != model.getData()) {
            JSONObject data = model.getData();
            String deviceCode = data.getString("__assetId__");
            if (StringUtils.isBlank(deviceCode)) {
                deviceCode = data.getString("serial_no");
            }
            if (StringUtils.isBlank(deviceCode)) {
                return;
            }

            // 需要剔除的属性
            List<String> excludeProperties = new ArrayList<>();
            String modelId = null;

            // 查找有没有配置剔除
            List<OrsDeviceCheckConfig> configs = deviceCheckConfigMapper.selectList(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .eq(OrsDeviceCheckConfig::getDeviceCode, deviceCode)
                            .eq(OrsDeviceCheckConfig::getDelFlag, 0)
            );
            if (!configs.isEmpty()) {
                // 先找出规则中是否有按设备排除的，如果有按设备排除的则不处理
                OrsDeviceCheckConfig whole = configs.stream().filter(x -> x.getExcludeType().equals("WHOLE")).findFirst().orElse(null);
                if (null != whole) {
                    return;
                }

                // 不需要进行检查的属性
                excludeProperties = configs.stream().map(OrsDeviceCheckConfig::getExcludeType).collect(Collectors.toList());
                modelId = configs.get(0).getModelId();
            }

            if (StringUtils.isBlank(modelId)) {
                OrsDeviceInfo deviceInfo = orsDeviceInfoMapper.selectOne(
                        new QueryWrapper<OrsDeviceInfo>()
                                .lambda()
                                .eq(OrsDeviceInfo::getAssetId, deviceCode)
                );
                if (null != deviceInfo) {
                    modelId = deviceInfo.getModelId();
                }
            }

            // 如果没有物模型，说明设备可能是无效数据，不处理
            if (StringUtils.isBlank(modelId)) {
                return;
            }

            // 查询对应的规则
            List<OrsModelPropertiesConfig> propertiesConfigs = devicePropertiesConfigMapper.selectList(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .eq(OrsModelPropertiesConfig::getModelId, modelId)
            );

            // 如果没有配置规则，不处理
            if (propertiesConfigs.isEmpty()) {
                return;
            }

            OrsDeviceDataAbnormalDetail abnormalDetail;
            // 执行groovy脚本
            for (OrsModelPropertiesConfig config : propertiesConfigs) {
                Binding binding = buildBinding(data, config.getId());
                // 如果存在剔除，且当前属性在剔除规则中，不进行后续处理
                if (!excludeProperties.isEmpty() && excludeProperties.contains(config.getProperty())) {
                    continue;
                }
                // 这里需要约定脚本规则，如果满足表达式规则，需要返回数据，不满足表达式返回Null
                Object resultObject = ruleScriptExecute(binding, config.getCheckRule());
                // 执行结果，后续操作
                System.out.println(resultObject);
                if (null == resultObject) {
                    continue;
                }

                abnormalDetail = new OrsDeviceDataAbnormalDetail();
                abnormalDetail.setCreateTime(new Date());
//                abnormalDetail.setAbnormalName();
                abnormalDetail.setPropertyName(config.getPropertyName());
                abnormalDetail.setProperty(config.getProperty());
//                abnormalDetail.setUuid();
//                abnormalDetail.setDeviceCode();
//                abnormalDetail.setDeviceName();
//                abnormalDetail.setModelId();
                orsDeviceDataAbnormalDetailMapper.insert(abnormalDetail);
            }
        }

    }
}
