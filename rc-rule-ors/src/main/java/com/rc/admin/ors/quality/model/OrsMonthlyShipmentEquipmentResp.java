package com.rc.admin.ors.quality.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class OrsMonthlyShipmentEquipmentResp implements Serializable {

  @ApiModelProperty("设备编号")
  @Excel(name = "设备编号", width = 20, orderNum = "1")
  private String deviceName ;
  private String region ;
  @ApiModelProperty("海外大区")
  @Excel(name = "海外大区", width = 20, orderNum = "2")
  private String name ;
  @ApiModelProperty("产品组")
  @Excel(name = "产品组", width = 20, orderNum = "4")
  private String agent;
  @ApiModelProperty("事业部")
  @Excel(name = "事业部", width = 20, orderNum = "3")
  private String sybbh ;
  private String time ;
  @ApiModelProperty("错误原因")
  @Excel(name = "错误原因", width = 50, orderNum = "5")
  private String  msg;

  public OrsMonthlyShipmentEquipmentResp(String deviceName, String name,String sybbh,String agent, String msg) {
    this.deviceName = deviceName;
    this.sybbh = sybbh;

    this.name = name;

    this.agent = agent;

    this.msg = msg;
  }
}
