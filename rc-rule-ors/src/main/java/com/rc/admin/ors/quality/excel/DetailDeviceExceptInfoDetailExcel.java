package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/28 13:24
 * @describe
 */
@Getter
@Setter
public class DetailDeviceExceptInfoDetailExcel {

    @Excel(name = "序号", width = 10)
    @ExcelProperty("序号")
    @ColumnWidth(10)
    private int sort;

    @Excel(name = "事业部", width = 15)
    @ExcelProperty("事业部")
    @ColumnWidth(15)
    private String division;

    @Excel(name = "产品组", width = 15)
    @ExcelProperty("产品组")
    @ColumnWidth(15)
    private String productGroup;
//
//    @Excel(name = "大区", width = 25)
//    private String region;

    @Excel(name = "数据中心", width = 15)
    @ExcelProperty("数据中心")
    @ColumnWidth(15)
    private String dataCenter;

    @Excel(name = "设备编号", width = 25)
    @ExcelProperty("设备编号")
    @ColumnWidth(25)
    private String deviceName;

    @Excel(name = "物联盒ID", width = 25)
    @ExcelProperty("物联盒ID")
    @ColumnWidth(25)
    private String deviceCode;

    @Excel(name = "异常发生时间", width = 25)
    @ExcelProperty("异常发生时间")
    @ColumnWidth(25)
    private String excetionTime;

    @Excel(name = "属性", width = 25)
    @ExcelProperty("属性")
    @ColumnWidth(25)
    private String paramName;

    @Excel(name = "异常项", width = 25)
    @ExcelProperty("异常项")
    @ColumnWidth(25)
    private String exceItem;

    @Excel(name = "最后异常内容", width = 25)
    @ExcelProperty("最后异常内容")
    @ColumnWidth(25)
    private String abnormalData;
}
