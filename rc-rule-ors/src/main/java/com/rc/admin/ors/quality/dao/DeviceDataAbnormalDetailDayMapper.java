package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.DeviceDataAbnormalDetailDay;
import com.rc.admin.ors.quality.model.AbnormalCodeRateResp;
import com.rc.admin.ors.quality.model.DeviceQuelityCountQuery;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsReq;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备数据异常明细统计结果-天表(DeviceDataAbnormalDetailDay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-09 13:21:30
 */
public interface DeviceDataAbnormalDetailDayMapper extends BaseMapper<DeviceDataAbnormalDetailDay> {

    List<DeviceDataAbnormalDetailDay> clearData();

    Page<DqmDeviceDataExceptionsResp> findAbnormalDetail(Page<?> page, @Param("req") DqmDeviceDataExceptionsReq req);

    List<AbnormalCodeRateResp> selectAbnormalCodeRate(@Param("req") DeviceQuelityCountQuery req);


    List<AbnormalCodeRateResp> countDevice(@Param("req") DeviceQuelityCountQuery req);
}

