package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.DeviceParamReportLogMapper;
import com.rc.admin.ors.quality.entity.DeviceParamReportLog;
import com.rc.admin.ors.quality.service.DeviceParamReportLogService;
import org.springframework.stereotype.Service;

/**
 * 设备工况上报记录(DeviceParamReportLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-27 14:39:03
 */
@Service("deviceParamReportLogService")
public class DeviceParamReportLogServiceImpl extends ServiceImpl<DeviceParamReportLogMapper, DeviceParamReportLog> implements DeviceParamReportLogService {

}

