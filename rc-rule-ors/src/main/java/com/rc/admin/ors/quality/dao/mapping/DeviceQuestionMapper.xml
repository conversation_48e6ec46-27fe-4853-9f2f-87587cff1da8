<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.DeviceQuestionMapper">

    <select id="questionList" resultType="com.rc.admin.ors.quality.model.DeviceLedgerResp">
        SELECT DISTINCT
            obdi.division,
            obdi.division_code,
            obdi.product_group,
            obdi.region,
            obdi.country,
            obdi.device_name as name,
            obdi.device_code AS device_no,
            obdi.asset_id,
            obdi.tbox_id,
            odq.create_time,
            odq.check_item,
            odq.exce_item,
            odq.ques_level,
            odq.cur_step,
            odq.cur_step_name,
            odq.user_account,
            odq.user_name,
            odq.id,
            odcc.eliminate_property
        FROM
            dqm.ors_device_question odq
            INNER JOIN dqm.ors_base_device_info obdi ON obdi.asset_id = odq.asset_id
            <if test="queryAccess != null and queryAccess != ''">
                LEFT JOIN dqm.ors_question_log oql ON oql.question_id = odq.id AND oql.user_account = #{queryAccess} AND oql.handle_state = 2
            </if>
            LEFT JOIN (
                SELECT asset_id,string_agg(property_name, ',') AS eliminate_property FROM dqm.ors_device_check_config GROUP BY asset_id
            ) odcc ON odcc.asset_id = odq.asset_id
                ${ew.customSqlSegment}
    </select>

    <select id="queryFilter" resultType="java.lang.String">
        SELECT DISTINCT
            <if test="req.name != null and req.name != ''">
                obdi.device_name as name
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                obdi.device_code AS device_no
            </if>
            <if test="req.tboxId != null and req.tboxId != ''">
                obdi.tbox_id
            </if>
        FROM
        dqm.ors_device_question odq
        INNER JOIN dqm.ors_base_device_info obdi ON obdi.asset_id = odq.asset_id
        <if test="req.queryAccess != null and req.queryAccess != ''">
            LEFT JOIN dqm.ors_question_log oql ON oql.question_id = odq.id AND oql.user_account = #{curUser} AND oql.handle_state = 2
        </if>
        <where>
            <if test="req.name != null and req.name != ''">
                (
                    obdi.device_name LIKE CONCAT('%', #{req.name}, '%')
                    OR
                    obdi.device_name IN (
                       <foreach collection="req.name.split(',')" item="item" open="(" separator="," close=")">
                           #{item}
                       </foreach>
                    )
                )
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND (
                        obdi.device_code LIKE CONCAT('%', #{req.deviceCode}, '%')
                        OR
                        obdi.device_code IN (
                        <foreach collection="req.deviceCode.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    )
            </if>
            <if test="req.tboxId != null and req.tboxId != ''">
                AND (
                        obdi.tbox_id LIKE CONCAT('%', #{req.tboxId}, '%')
                        OR
                        obdi.tbox_id IN (
                        <foreach collection="req.tboxId.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    )
            </if>
            <if test="req.queryAccess != null and req.queryAccess != ''">
                <if test="req.queryAccess == 'wait'">
                    AND odq.user_account = #{curUser}
                </if>
                <if test="req.queryAccess == 'handled'">
                    AND oql.user_account = #{curUser}
                </if>
            </if>
        </where>
        GROUP BY
            <if test="req.name != null and req.name != ''">
                obdi.device_name
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                obdi.device_code
            </if>
            <if test="req.tboxId != null and req.tboxId != ''">
                obdi.tbox_id
            </if>
    </select>

    <select id="findAbnormalDateByDate" resultType="com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail">
        SELECT
            T1.device_name as uuid,
            T1.model_id,
            T3.property,
            T3.property_name,
            T3.abnormal_code,
            T3.abnormal_name,
            T3.abnormal_data,
            T3.abnormal_time
        FROM
            ( SELECT device_name, model_id, create_time, stat_date, device_question_id FROM dqm.ors_device_data_abnormal_stat_day WHERE device_question_id = #{questionId} ) T1
                INNER JOIN dqm.ors_device_data_abnormal_detail_day T3 ON T3.device_name = T1.device_name AND T3.stat_date = T1.stat_date
                INNER JOIN dqm.ors_device_question T2 ON T2.id = T1.device_question_id AND  T2.check_item LIKE CONCAT('%',T3.property_name, '%') AND T2.del_flag = 0
    </select>

    <select id="countNum" resultType="com.rc.admin.ors.quality.model.QuestionCount">
        SELECT
            COUNT ( 1 ) AS total,
            SUM ( CASE WHEN cur_step='close' THEN 1 ELSE 0 END) AS closed_num,
            SUM ( CASE WHEN cur_step='platfrom' THEN 1 ELSE 0 END) AS platfrom_num,
            SUM ( CASE WHEN cur_step='hardware' THEN 1 ELSE 0 END) AS hardware_num,
            SUM ( CASE WHEN cur_step='research_institute' THEN 1 ELSE 0 END) AS research_institute_num,
            SUM ( CASE WHEN cur_step='wait_start' THEN 1 ELSE 0 END) AS wait_start_num,
            SUM ( CASE WHEN cur_step='wait_verify' THEN 1 ELSE 0 END) AS wait_verify_num,
            SUM ( CASE WHEN cur_step='operate' THEN 1 ELSE 0 END) AS operate_num
        FROM
            dqm.ors_device_question
        WHERE del_flag = 0
    </select>

    <select id="countByStepDays" resultType="com.rc.admin.ors.quality.model.QuestionCountDaysByStep">
        SELECT
            cur_step,
            SUM(CASE WHEN update_time::DATE &gt; CURRENT_DATE - INTERVAL '3 days' THEN 1 ELSE 0 END) AS less_3_days,
            SUM(CASE WHEN update_time::DATE &lt;= CURRENT_DATE - INTERVAL '3 days' AND update_time::DATE &gt;= CURRENT_DATE - INTERVAL '30 days' THEN 1 ELSE 0 END) AS between_3_a_30_days,
            SUM(CASE WHEN update_time::DATE &lt; CURRENT_DATE - INTERVAL '30 days' THEN 1 ELSE 0 END) AS more_than_30_days
        FROM
            dqm.ors_device_question
        WHERE del_flag = 0
        <if test="curSteps != null and curSteps.size > 0">
            AND cur_step IN
            <foreach collection="curSteps" item="step" open="(" close=")" separator=",">
                #{step}
            </foreach>
        </if>
        GROUP BY cur_step
    </select>

    <select id="questionCountBydUser" resultType="com.rc.admin.ors.quality.model.QuestionCountBydUser">
        SELECT
            COUNT ( 1 ) AS total,
            SUM (CASE WHEN T2.handle_state = 1 THEN 1 ELSE 0 END) AS wait_handle_num,
            SUM (CASE WHEN T2.handle_state = 2 THEN 1 ELSE 0 END) AS handled_num,
            SUM (CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE &lt; CURRENT_DATE - INTERVAL '7 days' THEN 1 ELSE 0 END) AS delay_7days_num,
            SUM (CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_wait_num,
            SUM (CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_wait_num,
            SUM (CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE &gt;= CURRENT_DATE - INTERVAL '8 days' AND T2.create_time::DATE &lt; CURRENT_DATE THEN 1 ELSE 0 END) AS yesterday_delay_num,
            SUM (CASE WHEN T2.handle_state = 2 THEN 1 ELSE 0 END) AS handle_total,
            SUM (CASE WHEN T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_handle_num,
            SUM (CASE WHEN T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_handle_num,
            T2.user_account,
            T2.user_name
        <if test="startTime != null">
            ,TO_CHAR(T2.create_time , 'YYYY-MM-DD') AS create_time
        </if>
        FROM
            dqm.ors_question_log T2
            INNER JOIN dqm.ors_device_question T1 ON T1.id = T2.question_id AND T1.del_flag = 0
        <where>
            <if test="curSteps != null and curSteps.size > 0">
                T2.cur_step IN
                <foreach collection="curSteps" item="step" open="(" close=")" separator=",">
                    #{step}
                </foreach>
            </if>
            <if test="userAccount != null and userAccount != ''">
                AND T2.user_account = #{userAccount}
            </if>
            <if test="startTime != null">
                AND T2.create_time::DATE &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null">
                AND T2.create_time::DATE &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY
            T2.user_account,
            T2.user_name
        <if test="startTime != null">
            ,TO_CHAR(T2.create_time , 'YYYY-MM-DD')
        </if>
        ORDER BY wait_handle_num DESC
    </select>

    <select id="questionCountByStepAndUser" resultType="com.rc.admin.ors.quality.model.QuestionCountBydUser">
        SELECT
            count(1) AS total,
            SUM (CASE WHEN T2.handle_state = 1 THEN 1 ELSE 0 END) AS wait_handle_num,
            SUM (CASE WHEN T2.cur_step = 'wait_verify' AND T2.handle_state = 1 THEN 1 ELSE 0 END) AS wait_verify_num,
            SUM (CASE WHEN T2.cur_step = 'close' AND T2.handle_state = 2 THEN 1 ELSE 0 END) AS closed_num,
            SUM (CASE WHEN T2.cur_step = 'operate' AND T2.handle_state = 1 THEN 1 ELSE 0 END) AS operate_num,
            SUM (CASE WHEN T2.cur_step = 'wait_verify' AND T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_wait_verify_num,
            SUM (CASE WHEN T2.cur_step = 'wait_verify' AND T2.handle_state = 1  AND T2.create_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_wait_verify_num,
            SUM (CASE WHEN T2.cur_step = 'close' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_close_num,
            SUM (CASE WHEN T2.cur_step = 'close' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_close_num,
            SUM (CASE WHEN T2.cur_step = 'operate' AND T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_operate_num,
            SUM (CASE WHEN T2.cur_step = 'operate' AND T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_operate_num,
            SUM (CASE WHEN T2.cur_step = 'operate' AND T2.handle_state = 2 AND T2.update_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_operate_finished_num,
            SUM (CASE WHEN T2.cur_step = 'wait_verify' AND T2.handle_state = 2 AND T2.update_time::DATE = CURRENT_DATE THEN 1 ELSE 0 END) AS today_wait_verify_finished_num,
        T2.user_account,
        T2.user_name
        <if test="startTime != null">
            ,TO_CHAR(T2.create_time , 'YYYY-MM-DD') AS create_time
        </if>
        FROM
            dqm.ors_question_log T2
            INNER JOIN dqm.ors_device_question T1 ON T1.id = T2.question_id AND T1.del_flag = 0
        <where>
            (T2.cur_step = 'wait_verify' OR T2.cur_step = 'close' OR T2.cur_step = 'operate') AND T2.user_account IS NOT NULL
            <if test="userAccount != null and userAccount != ''">
                AND T2.user_account = #{userAccount}
            </if>
            <if test="startTime != null">
                AND T2.create_time::DATE &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null">
                AND T2.create_time::DATE &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY
            T2.user_account,
            T2.user_name
        <if test="startTime != null">
            ,TO_CHAR(T2.create_time , 'YYYY-MM-DD')
        </if>
        ORDER BY wait_verify_num DESC
    </select>

    <select id="countByDivision" resultType="com.rc.admin.ors.quality.model.QuestionCountByDivision">
        SELECT
            T3.division_code,
            T3.division,
            SUM(CASE WHEN T2.opinion_classify_code='return_rootcloud' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS return_rootcloud_num,
            SUM(CASE WHEN T2.opinion_classify_code='return_rootcloud' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_return_rootcloud_num,
            SUM(CASE WHEN T2.opinion_classify_code='runing_device' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS runing_device_num,
            SUM(CASE WHEN T2.opinion_classify_code='runing_device' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_runing_device_num,
            SUM(CASE WHEN T2.opinion_classify_code='no_single' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS no_single_num,
            SUM(CASE WHEN T2.opinion_classify_code='no_single' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_no_single_num,
            SUM(CASE WHEN T2.opinion_classify_code='un_surport' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS un_surport_num,
            SUM(CASE WHEN T2.opinion_classify_code='un_surport' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_un_surport_num,
            SUM(CASE WHEN T2.opinion_classify_code='scene' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS scene_num,
            SUM(CASE WHEN T2.opinion_classify_code='scene' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_scene_num,
            SUM(CASE WHEN T2.opinion_classify_code='other' AND T2.handle_state = 2 AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS other_num,
            SUM(CASE WHEN T2.opinion_classify_code='other' AND T2.handle_state = 2 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' THEN 1 ELSE 0 END) AS yesterday_other_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='research_institute' AND T2.user_account != 'lei.xiao' THEN 1 ELSE 0 END) AS unhandle_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' AND T2.user_account != 'lei.xiao' THEN 1 ELSE 0 END) AS yesterday_unhandle_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='wait_start' AND T2.user_account != 'lei.xiao' THEN 1 ELSE 0 END) AS wait_start_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='wait_start' AND T2.user_account != 'lei.xiao' AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_wait_start_num,
            SUM (CASE WHEN T2.handle_state = 1 AND (T2.cur_step='wait_start' OR T2.cur_step='research_institute' ) AND T2.user_account != 'lei.xiao' THEN 1 ELSE 0 END) AS total
        FROM
            dqm.ors_question_log T2
            INNER JOIN dqm.ors_device_question T1 ON T1.id = T2.question_id AND T1.del_flag = 0
            INNER JOIN dqm.ors_base_device_info T3 ON T3.asset_id = T2.asset_id AND T3.division IS NOT NULL
        GROUP BY T3.division_code, T3.division
        UNION
        SELECT
            'lei.xiao' as division_code,
            '肖雷' AS division,
            0 AS return_rootcloud_num,
            0 AS yesterday_return_rootcloud_num,
            0 AS runing_device_num,
            0 AS yesterday_runing_device_num,
            0 AS no_single_num,
            0 AS yesterday_no_single_num,
            0 AS un_surport_num,
            0 AS yesterday_un_surport_num,
            0 AS scene_num,
            0 AS yesterday_scene_num,
            0 AS other_num,
            0 AS yesterday_other_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='research_institute' AND T2.user_account = 'lei.xiao' THEN 1 ELSE 0 END) AS unhandle_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' AND T2.cur_step='research_institute' AND T2.user_account = 'lei.xiao' THEN 1 ELSE 0 END) AS yesterday_unhandle_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='wait_start' AND T2.user_account = 'lei.xiao' THEN 1 ELSE 0 END) AS wait_start_num,
            SUM(CASE WHEN T2.handle_state = 1 AND T2.cur_step='wait_start' AND T2.user_account = 'lei.xiao' AND T2.create_time::DATE = CURRENT_DATE - INTERVAL '1 day' THEN 1 ELSE 0 END) AS yesterday_wait_start_num,
            SUM (CASE WHEN T2.handle_state = 1 AND (T2.cur_step='wait_start' OR T2.cur_step='research_institute' ) AND T2.user_account = 'lei.xiao' THEN 1 ELSE 0 END) AS total
        FROM
            dqm.ors_question_log T2
                INNER JOIN dqm.ors_device_question T1 ON T1.id = T2.question_id AND T1.del_flag = 0
        GROUP BY division_code
        ORDER BY total DESC
    </select>
</mapper>
