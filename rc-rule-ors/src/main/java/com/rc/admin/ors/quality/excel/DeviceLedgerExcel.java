package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Setter
@Getter
public class DeviceLedgerExcel {

    @Excel(name = "序号", width = 10)
    @ExcelProperty("序号")
    @ColumnWidth(10)
    private int sort;

    //@Excel(name = "CRM国家", width = 10)
    @Excel(name = "国家")
    @ExcelProperty("国家")
    @ColumnWidth(15)
    private String country;

    @Excel(name = "事业部名称", width = 15)
    @ExcelProperty("事业部名称")
    @ColumnWidth(15)
    private String newDivisionName;

    @Excel(name = "产品组名称", width = 15)
    @ExcelProperty("产品组名称")
    @ColumnWidth(15)
    private String newProductGroupName;

    @Excel(name = "数据中心", width = 25)
    @ExcelProperty("数据中心")
    @ColumnWidth(25)
    private String dataCenter;

    @Excel(name = "设备编号", width = 25)
    @ExcelProperty("设备编号")
    @ColumnWidth(25)
    private String deviceNo;

    @ExcelIgnore
    @ColumnWidth(25)
    private String assetId;

    @Excel(name = "物模型ID", width = 25)
    @ExcelProperty("物模型ID")
    @ColumnWidth(25)
    private String modelId;

    @Excel(name = "实例名称", width = 30)
    @ExcelProperty("实例名称")
    @ColumnWidth(30)
    private String name;

    @Excel(name = "设备状态", width = 15)
    @ExcelProperty("设备状态")
    @ColumnWidth(15)
    private String deviceStatus;

    @Excel(name = "物模型名称", width = 30)
    @ExcelProperty("物模型名称")
    @ColumnWidth(30)
    private String modelName;

    @Excel(name = "新C注册", width = 25)
    @ExcelProperty("新C注册")
    @ColumnWidth(25)
    private String ctime;

    //@Excel(name = "crm注册", replace = {"否_0", "是_1"}, width = 15)
    @ExcelIgnore
    private String crmRegister;

    //@Excel(name = "大区名称", width = 15)
    @ExcelIgnore
    private String newRegionName;

    //@Excel(name = "国区名称", width = 15)
    @ExcelIgnore
    private String countryRegionName;

    //@Excel(name = "国家名称", width = 15)
    @ExcelIgnore
    private String newCountryName;

    //@Excel(name = "安装分类", replace = {"前装_1", "后装_2"}, width = 15)
    @ExcelIgnore
    private String installType;

    //@Excel(name = "出厂时长", width = 15)
    @ExcelIgnore
    public String factoryDuration;

    //@Excel(name = "异常标识",replace = {"无异常_0", "异常_1"}, width = 15)
    //@ExcelProperty("异常标识")
    @ExcelIgnore
    @ColumnWidth(15)
    private String exceFlag;

    @Excel(name = "离线时长", width = 25)
    @ExcelProperty("离线时长")
    @ColumnWidth(25)
    private String offlineTime;

    @Excel(name = "剔除检查", replace = {"整机_0", "属性_1", " _null"}, width = 15)
    @ExcelProperty("剔除检查")
    @ColumnWidth(15)
    private String isEliminate;

    @ExcelIgnore
    @ColumnWidth(15)
    private String exceDesc;

    //@Excel(name = "问题跟进", replace = {"否_0", "是_1"}, width = 15)
    @ExcelIgnore
    private String problemFollow;

    //@Excel(name = "CRM事业部", width = 15)
    @ExcelIgnore
    private String division;

    //@Excel(name = "CRM产品组", width = 15)
    @ExcelIgnore
    private String productGroup;

    //@Excel(name = "CRM大区", width = 10)
    @ExcelIgnore
    private String region;

    @Excel(name = "硬件版本号")
    @ExcelProperty("硬件版本号")
    @ColumnWidth(15)
    private String hwVersion;

    @Excel(name = "固件版本号")
    @ExcelProperty("固件版本号")
    @ColumnWidth(15)
    private String fwVersion;

    @Excel(name = "物联盒ID")
    @ExcelProperty("物联盒ID")
    @ColumnWidth(15)
    private String rcAssetId;


}
