<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.SanydsCoreParamStatLatestMapper">

  <insert id="insertSanydsCoreParamStatLatestDay" parameterType="list">
    INSERT INTO sany_data_service.sanyds_core_param_stat_latest_day (
      tenant_id,
      model_id,
      device_name,
      param_code,
      param_value,
      is_param_value_abnormal,
      param_value_earliest_time,
      param_value_latest_time,
      create_time,
      stat_date
    )
    SELECT
      psl.tenant_id,
      psl.model_id,
      psl.device_name,
      psl.param_code,
      psl.param_value,
      psl.is_param_value_abnormal,
      psl.param_value_earliest_time,
      psl.param_value_latest_time,
      psl.create_time,
      to_date(TO_CHAR(psl.param_value_latest_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS stat_date
    FROM
    sany_data_service.sanyds_core_param_stat_latest psl
    WHERE
    NOT EXISTS (
      SELECT 1
      FROM sany_data_service.sanyds_core_param_stat_latest_day
      WHERE device_name = psl.device_name AND param_code = psl.param_code
    )
    <if test="modelIdList != null and modelIdList.size() > 0">
      AND psl.model_id IN
      <foreach item="modelId" index="index" collection="modelIdList" open="(" separator="," close=")">
        #{modelId}
      </foreach>
    </if>

    <if test="paramCodeList != null and paramCodeList.size() > 0">
      AND psl.param_code IN
      <foreach item="paramCode" index="index" collection="paramCodeList" open="(" separator="," close=")">
        #{paramCode}::int
      </foreach>
    </if>
  </insert>

</mapper>