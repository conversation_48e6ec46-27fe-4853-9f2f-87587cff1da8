package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/1/17 14:26
 * @describe
 */
@Getter
@Setter
public class DeviceLastIotExcel {

    @Excel(name = "设备编号")
    private String deviceCode;

    @Excel(name = "产品组")
    private String productGroup;

    @Excel(name = "机型")
    private String modelType;

    //经度	纬度	设备状态	发动机小时数	工作时间	累计油耗	累计里程
    @Excel(name = "经度")
    private String gpsLongitude8504;

    @Excel(name = "纬度")
    private String gpsLatitude8505;

    @Excel(name = "设备状态")
    private String deviceStatus8503;

    @Excel(name = "发动机小时数")
    private String engineWorktime8105;

    @Excel(name = "工作时间")
    private String workingTime8102;

    @Excel(name = "累计油耗")
    private String totalFuelConsumption8201;

    @Excel(name = "累计里程")
    private String drivingMileage8403;

}
