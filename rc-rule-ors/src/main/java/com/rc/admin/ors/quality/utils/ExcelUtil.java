package com.rc.admin.ors.quality.utils;


import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;


/**
 * 基于easyExcel封装的excel工具类
 *
 */
@Slf4j
public class ExcelUtil {

    /**
     * 导出Excel（使用swagger导出会有各种问题，请使用浏览器或者postMan）
     *
     * @param response HttpServletResponse
     * @param data     数据列表
     * @param fileName 文件名
     * @param clazz    数据类型
     * @throws IOException IO异常
     */
    public static void exportExcel(HttpServletResponse response, List<?> data, String fileName, Class<?> clazz) throws IOException {
        exportExcel(response, data, invokeHeadMapI18n(clazz), fileName, "sheet1", clazz);
    }

    /**
     * 导出Excel（使用swagger导出会有各种问题，请使用浏览器或者postMan）
     *
     * @param response  HttpServletResponse
     * @param data      数据列表
     * @param fileName  文件名
     * @param sheetName 工作簿名称
     * @param clazz     数据类型
     * @throws IOException IO异常
     */
    public static void exportExcel(HttpServletResponse response, List<?> data, String fileName, String sheetName, Class<?> clazz) throws IOException {
        exportExcel(response, data, invokeHeadMapI18n(clazz), fileName, sheetName, clazz);
    }

    /**
     * 导出Excel（使用swagger导出会有各种问题，请使用浏览器或者postMan）
     *
     * @param response  HttpServletResponse
     * @param data      数据列表
     * @param head      列头
     * @param fileName  文件名
     * @param sheetName 工作簿名称
     * @param clazz     数据类型
     * @throws IOException IO异常
     */
    public static void exportExcel(HttpServletResponse response, List<?> data, List<List<String>> head, String fileName, String sheetName, Class<?> clazz) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));

        // 处理表头国际化
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                //.head(head)
                .needHead(true)
                .sheet(sheetName)
                .doWrite(data);
    }

    public static ByteArrayOutputStream exportExcelStream(List<?> list, Class<?> clazz) {
        return exportExcelStream(list, invokeHeadMapI18n(clazz), clazz);
    }

    public static ByteArrayOutputStream exportExcelStream(List<?> data, List<List<String>> head, Class<?> clazz) {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            EasyExcel.write(byteArrayOutputStream, clazz)
                    .autoCloseStream(Boolean.TRUE)
                    //.head(head)
                    .needHead(true)
                    .sheet("sheet1")
                    .doWrite(data);
            byteArrayOutputStream.close();
        } catch (Exception e) {
            log.error("list转ByteArrayOutputStream异常", e);
            throw new RuntimeException("导出excel失败");
        }
        return byteArrayOutputStream;
    }

    private static List<List<String>> invokeHeadMapI18n(Class<?> clazz) {

        List<List<String>> result = new ArrayList<>();

        // 获取带有@ExcelProperty注解的字段信息
        Map<Integer, String> fieldMap = new HashMap<>(16);
        Field[] fields = ReflectUtil.getFields(clazz);
        int indexStart = 0;
        for (Field field : fields) {
            ExcelProperty excelProperty = AnnotationUtil.getAnnotation(field, ExcelProperty.class);
            if (excelProperty != null) {
                String columnName = excelProperty.value()[0];
                if(excelProperty.index() == -1){
                    fieldMap.put(indexStart, columnName);
                    indexStart = indexStart+1;
                }else{
                    fieldMap.put(excelProperty.index(), columnName);
                }
            }
        }
        // 将字段信息按照index排序
        Set<Integer> indexSet = fieldMap.keySet();
        List<Integer> indexList = new ArrayList<>(indexSet);
        Collections.sort(indexList);
        for (Integer index : indexList) {
            result.add(Collections.singletonList(fieldMap.get(index)));
        }
        return result;
    }



    public static List<Map<Integer, String>> importExcel(MultipartFile file, ReadListener readListener) throws IOException {
        return EasyExcel.read(file.getInputStream(), readListener)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet(0)
                .doReadSync();
    }


}
