package com.rc.admin.ors.quality.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备问题跟进(DeviceQuestion)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-01 12:30:24
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备问题跟进")
public class DeviceQuestionReq {


    @ApiModelProperty(name = "assetId", value = "设备物标识")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "checkItem", value = "检查项")
    @TableField(value = "check_item")
    private String checkItem;

    @ApiModelProperty(name = "exceItem", value = "异常项")
    @TableField(value = "exce_item")
    private String exceItem;

    @ApiModelProperty(name = "quesLevel", value = "问题跟进优先级 P0 P1 P2 P3")
    @TableField(value = "ques_level")
    private String quesLevel;

    @ApiModelProperty(name = "curStep", value = "当前环节")
    @TableField(value = "cur_step")
    private String curStep;

    @ApiModelProperty(name = "userAccount", value = "当前处理责任人账号")
    @TableField(value = "user_account")
    private String userAccount;

    private Date startTime;

    private Date endTime;

    @ApiModelProperty("事业部")
    private String division;

    @ApiModelProperty("大区")
    private String region;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("产品组")
    private String productGroup;

    @ApiModelProperty(name = "tboxId", value = "物联盒ID")
    private String tboxId;

    @ApiModelProperty(name = "userName", value = "当前处理责任人姓名")
    private String userName;

    @ApiModelProperty(name = "queryAccess", value = "数据范围 wait=当前用户待处理，handled=当前用户已处理")
    private String queryAccess;

    private String name;

    @ApiModelProperty(name = "qStart", value = "问题的开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qStart;

    @ApiModelProperty(value = "问题的结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qEnd;
}

