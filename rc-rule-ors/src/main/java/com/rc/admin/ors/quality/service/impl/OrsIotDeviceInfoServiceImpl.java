package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.entity.OrsDeviceInfo;
import com.rc.admin.ors.quality.service.OrsIotDeviceInfoService;
import org.springframework.stereotype.Service;

/**
 * 根云设备数据表(OrsIotDeviceInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 14:48:16
 */
@Service("orsIotDeviceInfoService")
public class OrsIotDeviceInfoServiceImpl extends ServiceImpl<OrsDeviceInfoMapper, OrsDeviceInfo> implements OrsIotDeviceInfoService {

}

