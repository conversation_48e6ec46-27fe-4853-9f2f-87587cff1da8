package com.rc.admin.ors.quality.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.OrsMonthlyShipmentEquipment;
import com.rc.admin.ors.quality.model.OrsMonthlyShipmentEquipmentReq;
import com.rc.admin.ors.quality.model.OrsMonthlyShipmentEquipmentResp;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrsMonthlyShipmentEquipmentMapper extends BaseMapper<OrsMonthlyShipmentEquipment> {

    @Select("select count(1) from ors_monthly_shipment_equipment where device_name = #{deviceName} and import_time = #{importTime}")
     int getDeviceCount(@Param("deviceName")String deviceName,@Param("importTime")String importTime);

    @Insert("  <script>  INSERT INTO ors_monthly_shipment_equipment(device_name,import_time,region,division,product_group) values\n" +
            "      <foreach collection='list' item='item' separator=',' >\n" +
            "     (#{item.deviceName},#{item.importTime},#{item.region},#{item.division},#{item.productGroup})\n" +
            "      </foreach>" +
            "</script>")
    void saveList(@Param("list") List<OrsMonthlyShipmentEquipment>  list);

    List<OrsMonthlyShipmentEquipmentResp> getDeviceInfo(@Param("req") OrsMonthlyShipmentEquipmentReq  req);
    int getDeviceInfoCount(@Param("req") OrsMonthlyShipmentEquipmentReq  req);
    void deleteDeviceInfo(@Param("req") OrsMonthlyShipmentEquipmentReq  req);
    List<String> getParamList(@Param("time")String time,@Param("paramType") String paramType, @Param("paramValue") String paramValue);
}

