package com.rc.admin.ors.quality.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceCheckConfigMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import com.rc.admin.ors.quality.service.OrsModelPropertiesConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 物模型与属性检查配置(OrsDevicePropertiesConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-23 18:56:32
 */
@Service("orsDevicePropertiesConfigService")
@Slf4j
public class OrsModelPropertiesConfigServiceImpl extends ServiceImpl<OrsModelPropertiesConfigMapper, OrsModelPropertiesConfig> implements OrsModelPropertiesConfigService {

    @Resource
    private OrsDeviceCheckConfigMapper orsDeviceCheckConfigMapper;


    private static final Map<String, List<String>> configMap;
    static {
        configMap = new HashMap<>();
        configMap.put("8106", Arrays.asList("8701", "8704"));
        configMap.put("8205", Arrays.asList("8701", "8703"));
        configMap.put("8102", Arrays.asList("8702", "8704"));
        configMap.put("8201", Arrays.asList("8702", "8703"));
        configMap.put("8403", Arrays.asList("8705", "8706"));
        configMap.put("8510", Arrays.asList("8705"));
        configMap.put("8501", Arrays.asList("8706"));
        // 可以在这里继续添加更多的配置
    }
    @Override
    public Page<ModleIndicatorAndExclude> findModleIndicatorAndExclude(Page<ModleIndicatorAndExclude> page, String deviceCode, String modelId, String assetId) {
        Page<ModleIndicatorAndExclude> modleIndicatorAndExclude = orsDeviceCheckConfigMapper.findModleIndicatorAndExclude(page, deviceCode, modelId, assetId);

        Map<String, List<ModleIndicatorAndExclude>> paramMap
                = orsDeviceCheckConfigMapper.findModleIndicatorAndExcludeMultiple(deviceCode, modelId, assetId)
                .stream()
                .collect(Collectors.groupingBy(ModleIndicatorAndExclude::getParamCode));

        List<ModleIndicatorAndExclude> records = modleIndicatorAndExclude.getRecords();

        for (ModleIndicatorAndExclude record : records) {
            String paramCode = record.getParamCode();
            List<String> relatedParamCodes = configMap.get(paramCode);
            if (relatedParamCodes != null && !relatedParamCodes.isEmpty()) {
                List<String> indicatorJsons = CollUtil.newArrayList();
                indicatorJsons.add(record.getIndicatorJson());

                List<String> multipleJsons = CollUtil.newArrayList();
                for (String relatedParamCode : relatedParamCodes) {
                    if (paramMap.containsKey(relatedParamCode)) {
                        List<ModleIndicatorAndExclude> relatedRecords = paramMap.get(relatedParamCode);
                        for (ModleIndicatorAndExclude relatedRecord : relatedRecords) {
                            multipleJsons.add(relatedRecord.getIndicatorJson());
                        }
                    }
                }
                indicatorJsons.add(multipleJsonLists(multipleJsons));

                record.setIndicatorJson(mergeJsonLists(indicatorJsons));
            }
        }
        return modleIndicatorAndExclude;
    }




    public String multipleJsonLists(List<String> jsonLists) {
        Map<String, List<String>> resultMap = new HashMap<>();

        for (String json : jsonLists) {
            Map<String, String> parsedJson = parseJsonToObject(json);
            if (parsedJson.containsKey("f1") && parsedJson.containsKey("f2")) {
                String f1 = parsedJson.get("f1");
                String f2 = parsedJson.get("f2");

                if (!resultMap.containsKey(f1)) {
                    resultMap.put(f1, new ArrayList<>());
                }
                resultMap.get(f1).add(f2);
            }
        }

        return convertMapToJsonArray(resultMap);
    }

    private Map<String, String> parseJsonToObject(String json) {
        Map<String, String> map = new HashMap<>();
        json = json.replaceAll("^\\[|\\]$", ""); // 移除最外层的方括号
        String[] elements = json.split("\\}\\,\\{");
        for (String element : elements) {
            if (!element.isEmpty()) {
                // 修正每个 JSON 对象的格式
                if (!element.startsWith("{")) {
                    element = "{" + element;
                }
                if (!element.endsWith("}")) {
                    element = element + "}";
                }

                // 解析 JSON 对象
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map<String, String> parsedMap = objectMapper.readValue(element, new TypeReference<Map<String, String>>() {});
                    map.putAll(parsedMap);
                } catch (IOException e) {
                   log.info("转换异常:{}",e.getMessage(),e);
                }
            }
        }
        return map;
    }


    private String convertMapToJsonArray(Map<String, List<String>> resultMap) {
        List<String> jsonElements = new ArrayList<>();

        for (Map.Entry<String, List<String>> entry : resultMap.entrySet()) {
            String f1 = entry.getKey();
            List<String> f2List = entry.getValue();
            String f2 = String.join("", f2List);

            String jsonElement = String.format("{\"f1\":\"%s\",\"f2\":\"%s\"}", f1, f2);
            jsonElements.add(jsonElement);
        }

        return "[" + String.join(",", jsonElements) + "]";
    }


    public  String mergeJsonLists(List<String> jsonLists) {
        List<String> jsonElements = new ArrayList<>();

        for (String json : jsonLists) {
            jsonElements.addAll(parseJsonToList(json));
        }

        return convertListToJsonArray(jsonElements);
    }

    private  List<String> parseJsonToList(String json) {
        List<String> list = new ArrayList<>();
        // 移除最外层的方括号
        json = json.replaceAll("^\\[|\\]$", "");
        // 使用 },{ 分割字符串
        String[] elements = json.split("\\}\\,\\{");
        for (String element : elements) {
            // 修正每个 JSON 对象的格式
            if (!element.isEmpty()) {
                // 如果是第一个元素，可能缺少前括号
                if (!element.startsWith("{")) {
                    element = "{" + element;
                }
                // 如果是最后一个元素，可能缺少后括号
                if (!element.endsWith("}")) {
                    element = element + "}";
                }
                list.add(element);
            }
        }
        return list;
    }

    private  String convertListToJsonArray(List<String> list) {
        return "[" + String.join(",", list) + "]";
    }
}

