package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/08/03
 */
@Setter
@Getter
@ApiModel("设备比率报表明细")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRatioReportEntryResp {

    @ApiModelProperty("标题描述")
    private String title;

    @ApiModelProperty("完整率")
    private String integrityRate;

    @ApiModelProperty("准确率")
    private String accuracy;

    @ApiModelProperty("上期完整率")
    private String preIntegrityRate;

    @ApiModelProperty("上期准确率")
    private String preAccuracy;

    @ApiModelProperty("属性未上报设备数量")
    private int nullParamNum;

    @ApiModelProperty("属性异常设备数量")
    private int abnormalParamNum;

    @ApiModelProperty("上报设备数量")
    private int reportNum;

    public DeviceRatioReportEntryResp(String title, String integrityRate, String accuracy) {
        this.title = title;
        this.integrityRate = integrityRate;
        this.accuracy = accuracy;
    }
}
