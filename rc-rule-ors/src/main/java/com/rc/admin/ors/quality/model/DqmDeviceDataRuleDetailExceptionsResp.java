package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DqmDeviceDataRuleDetailExceptionsResp {

    @ApiModelProperty(value = "事业部")
    private String sybbh;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "事业部Code")
    private String sybbhCode;

    @ApiModelProperty(value = "模型ID")
    private String modelId;


    @ApiModelProperty(value = "数据中心")
    private Integer dataCenterId;


    @ApiModelProperty(value = "设备编号")
    private String deviceName;


    @ApiModelProperty(value = "物联盒ID")
    private String rcAssetId;



    @ApiModelProperty(value = "检查规则")
    private String checkRuleCode;

    @ApiModelProperty(value = "检查规则名称")
    private String checkRuleName;


    @ApiModelProperty(value = "异常现象")
    private String abnormalPhenomenonCode;

    @ApiModelProperty(value = "异常现象名称")
    private String abnormalPhenomenonName;


    @ApiModelProperty(value = "异常发生时间")
    private String statDate;


    @ApiModelProperty(value = "物标识")
    private String deviceCode;


    @ApiModelProperty(value = "实例名称")
    private String thingId;

    @ApiModelProperty(value = "模型名称")
    private String modelName;


    @ApiModelProperty(value = "产品组")
    private String zehdSpart;

    @ApiModelProperty(value = "产品组名称")
    private String zehdSpartdesc;

    @ApiModelProperty(value = "异常数据")
    private String abnormalData;


}