package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/28 13:03
 * @describe
 */
@Getter
@Setter
public class DeviceExceptInfoCountExcel {

    @Excel(name = "序号", width = 10)
    @ExcelProperty("序号")
    @ColumnWidth(10)
    private int sort;

    @Excel(name = "事业部", width = 15)
    @ExcelProperty("事业部")
    @ColumnWidth(15)
    private String division;

    @Excel(name = "模型ID", width = 25)
    @ExcelProperty("模型ID")
    @ColumnWidth(25)
    private String modelId;

    @Excel(name = "数据中心", width = 15)
    @ExcelProperty("数据中心")
    @ColumnWidth(15)
    private String dataCenter;

    @Excel(name = "设备编号", width = 25)
    @ExcelProperty("设备编号")
    @ColumnWidth(15)
    private String deviceName;

//
//    @Excel(name = "大区", width = 25)
//    private String region;

    @Excel(name = "物联盒ID", width = 25)
    @ExcelProperty("物联盒ID")
    @ColumnWidth(15)
    private String deviceCode;

    @ExcelIgnore
    private String startTime;

    @ExcelIgnore
    private String endTime;

    @Excel(name = "开始日期", width = 25)
    @ExcelProperty("开始日期")
    @ColumnWidth(15)
    private String startStatDate;

    @Excel(name = "结束日期", width = 25)
    @ExcelProperty("结束日期")
    @ColumnWidth(15)
    private String endStatDate;

    @Excel(name = "定位国家", width = 15)
    @ExcelProperty("定位国家")
    @ColumnWidth(15)
    private String deviceLocation;

    @Excel(name = "设备状态（异常次数）", width = 25)
    @ExcelProperty("设备状态（异常次数）")
    @ColumnWidth(15)
    private String deviceStateExceptCount;

    @Excel(name = "总油耗（异常次数）", width = 25)
    @ExcelProperty("总油耗（异常次数）")
    @ColumnWidth(15)
    private String totalFuelConsumptionExceptCount;

    @Excel(name = "经纬度（异常次数）", width = 25)
    @ExcelProperty("经纬度（异常次数）")
    @ColumnWidth(15)
    private String deviceLocationExceptCount;

    @Excel(name = "发动机工作时间（异常次数）", width = 28)
    @ExcelProperty("发动机工作时间（异常次数）")
    @ColumnWidth(15)
    private String engineWorktimeExceptCount;

    @Excel(name = "行驶里程（异常次数）", width = 25)
    @ExcelProperty("行驶里程（异常次数）")
    @ColumnWidth(15)
    private String drivingMileageExceptCount;

    @Excel(name = "工作时间（异常次数）", width = 25)
    @ExcelProperty("工作时间（异常次数）")
    @ColumnWidth(15)
    private String workingTimeExceptCount;

    @Excel(name = "方量（异常次数）", width = 25)
    @ExcelProperty("方量（异常次数）")
    @ColumnWidth(15)
    private String pumpingVolumeExceptCount;

    @Excel(name = "行驶速度（异常次数）", width = 25)
    @ExcelProperty("行驶速度（异常次数）")
    @ColumnWidth(15)
    private String travelSpeedExceptCount;

    @Excel(name = "油位（异常次数）", width = 25)
    @ExcelProperty("油位（异常次数）")
    @ColumnWidth(15)
    private String fuelLevelExceptCount;

    @Excel(name = "发动机转速（异常次数）", width = 28)
    @ExcelProperty("发动机转速（异常次数）")
    @ColumnWidth(15)
    private String engineSpeedExceptCount;

    @Excel(name = "发动机水温（异常次数）", width = 28)
    @ExcelProperty("发动机水温（异常次数）")
    @ColumnWidth(15)
    private String engineTemperatureExceptCount;

    @Excel(name = "总电耗（异常次数）", width = 25)
    @ExcelProperty("总电耗（异常次数）")
    @ColumnWidth(15)
    private String totalElectricExceptCount;

    @Excel(name = "当前电量（异常次数）", width = 25)
    @ExcelProperty("当前电量（异常次数）")
    @ColumnWidth(15)
    private String stateOfChargeExceptCount;

//新增数据
    @Excel(name = "怠速油耗（异常次数）", width = 25)
    @ExcelProperty("怠速油耗（异常次数）")
    @ColumnWidth(15)
    private String totalIdleFuelConsumptionExceptCount;

    @Excel(name = "怠速时长（异常次数）", width = 25)
    @ExcelProperty("怠速时长（异常次数）")
    @ColumnWidth(15)
    private String totalIdleTimeExceptCount;

    @Excel(name = "档位（异常次数）", width = 25)
    @ExcelProperty("档位（异常次数）")
    @ColumnWidth(15)
    private String gearExceptCount;

    @Excel(name = "左行走工时（异常次数）", width = 25)
    @ExcelProperty("左行走工时（异常次数）")
    @ColumnWidth(15)
    private String totalTimeLeftMovingExceptCount;

    @Excel(name = "右行走工时（异常次数）", width = 25)
    @ExcelProperty("右行走工时（异常次数）")
    @ColumnWidth(15)
    private String totalTimeRightMovingExceptCount;

    @Excel(name = "机油压力（异常次数）", width = 25)
    @ExcelProperty("机油压力（异常次数）")
    @ColumnWidth(15)
    private String oilPressureExceptCount;

    @Excel(name = "泵吸收功率（异常次数）", width = 25)
    @ExcelProperty("泵吸收功率（异常次数）")
    @ColumnWidth(15)
    private String pumpTotalAbsorbedTorqueExceptCount;

//2024-12-30
    @Excel(name = "电机转速（异常次数）", width = 25)
    @ExcelProperty("电机转速（异常次数）")
    @ColumnWidth(15)
    private String pumpMotorRotateSpeedCount;

    @Excel(name = "充电状态（异常次数）", width = 25)
    @ExcelProperty("充电状态（异常次数）")
    @ColumnWidth(15)
    private String chargingStatusCount;

    @Excel(name = "充电剩余时间（异常次数）", width = 25)
    @ExcelProperty("充电剩余时间（异常次数）")
    @ColumnWidth(15)
    private String chargeTimeRemainCount;

    @Excel(name = "单次充电电量（异常次数）", width = 25)
    @ExcelProperty("单次充电电量（异常次数）")
    @ColumnWidth(15)
    private String singleChargeCapacityCount;

    @Excel(name = "当日电耗（异常次数）", width = 25)
    @ExcelProperty("当日电耗（异常次数）")
    @ColumnWidth(15)
    private String dayPowerConsumptionCount;


    @Excel(name = "动作编码（异常次数）", width = 25)
    @ExcelProperty("动作编码（异常次数）")
    @ColumnWidth(15)
    private String actionCode;

    @Excel(name = "回转时间（异常次数）", width = 25)
    @ExcelProperty("回转时间（异常次数）")
    @ColumnWidth(15)
    private String totalTimeRotation;

    @Excel(name = "怠速电耗（异常次数）", width = 25)
    @ExcelProperty("怠速电耗（异常次数）")
    @ColumnWidth(15)
    private String totalNoActionPowerConsumption;


    @Excel(name = "怠速时长&怠速油耗（异常次数）", width = 25)
    @ExcelProperty("怠速时长&怠速油耗（异常次数）")
    @ColumnWidth(15)
    private String idleTimeIdleFuel;

    @Excel(name = "工作时间&总油耗（异常次数）", width = 25)
    @ExcelProperty("工作时间&总油耗（异常次数）")
    @ColumnWidth(15)
    private String workTimeFuel;


    @Excel(name = "怠速油耗&总油耗（异常次数）", width = 25)
    @ExcelProperty("怠速油耗&总油耗（异常次数）")
    @ColumnWidth(15)
    private String idelFuelFuel;


    @Excel(name = "怠速时长&工作时长（异常次数）", width = 25)
    @ExcelProperty("怠速时长&工作时长（异常次数）")
    @ColumnWidth(15)
    private String idelTimeWorkTime;

    @Excel(name = "行驶里程&行驶速度（异常次数）", width = 25)
    @ExcelProperty("行驶里程&行驶速度（异常次数）")
    @ColumnWidth(15)
    private String mileageSpeed;


    @Excel(name = "行驶里程&设备位置（异常次数）", width = 25)
    @ExcelProperty("行驶里程&设备位置（异常次数）")
    @ColumnWidth(15)
    private String mileageLocation;


    @Excel(name = "发动机工作时长&总油耗（异常次数）", width = 25)
    @ExcelProperty("发动机工作时长&总油耗（异常次数）")
    @ColumnWidth(15)
    private String engineTimeFuel;

    @Excel(name = "固件版本", width = 25)
    @ExcelProperty("固件版本")
    @ColumnWidth(15)
    private String fwVersion;

    @Excel(name = "硬件版本", width = 25)
    @ExcelProperty("硬件版本")
    @ColumnWidth(15)
    private String hwVersion;

    @Excel(name = "认证秘钥", width = 25)
    @ExcelProperty("认证秘钥")
    @ColumnWidth(15)
    private String authToken;

    @Excel(name = "模型名称", width = 25)
    @ExcelProperty("模型名称")
    @ColumnWidth(15)
    private String modelName;

    @Excel(name = "产品组", width = 15)
    @ExcelProperty("产品组")
    @ColumnWidth(15)
    private String productGroup;



    @Excel(name = "国家", width = 15)
    @ExcelProperty("国家")
    @ColumnWidth(15)
    private String country;
}
