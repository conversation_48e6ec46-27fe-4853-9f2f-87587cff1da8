package com.rc.admin.ors.quality.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.OrsBasicDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
@Slf4j
@ResponseResult
@RestController
@RequestMapping("/ors/data")
@Api(tags = "设备台账基础数据相关接口")
public class OrsBasicController {

    @Autowired
    private OrsBasicDataService orsBasicDataService;

    @Autowired
    private DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @ApiOperation("事业部产品组树形结构")
    @GetMapping("/division/tree")
    public List<DivisionAndProductTree> findDivisionTree() {
        List<OrsBaseDeviceInfo> infos = orsBaseDeviceInfoMapper.selectList(
                new QueryWrapper<OrsBaseDeviceInfo>()
                        .lambda()
                        .select(OrsBaseDeviceInfo::getDivision, OrsBaseDeviceInfo::getDivisionCode, OrsBaseDeviceInfo::getProductGroup, OrsBaseDeviceInfo::getProductGroupCode)
                        .isNotNull(OrsBaseDeviceInfo::getDivisionCode)
                        .isNotNull(OrsBaseDeviceInfo::getProductGroupCode)
                        .groupBy(OrsBaseDeviceInfo::getDivision, OrsBaseDeviceInfo::getDivisionCode, OrsBaseDeviceInfo::getProductGroup, OrsBaseDeviceInfo::getProductGroupCode)
        );
        Map<String, List<OrsBaseDeviceInfo>> collect = infos.stream().collect(Collectors.groupingBy(OrsBaseDeviceInfo::getDivisionCode));
        List<DivisionAndProductTree> list = new ArrayList<>();
        collect.forEach((k, v) -> {
            DivisionAndProductTree tree = new DivisionAndProductTree();
            tree.setDivisionCode(k);
            tree.setDivision(v.get(0).getDivision());

            List<DivisionAndProductTree.ProductGroup> child = new ArrayList<>();
            v.forEach(x -> {
                DivisionAndProductTree.ProductGroup group = new DivisionAndProductTree.ProductGroup();
                group.setProductGroup(x.getProductGroup());
                group.setProductGroupCode(x.getProductGroupCode());
                child.add(group);
            });
            tree.setProductGroups(child);
            list.add(tree);
        });
        return list;
    }

    @ApiOperation("CRM国家基础数据")
    @GetMapping("/country")
    public List<OrsBasicData> countryList() {
        return orsBasicDataService.countryList();
    }

    @ApiOperation("CRM事业部基础数据")
    @GetMapping("/division")
    public List<OrsBasicData> division() {
        return orsBasicDataService.divisionList();
    }

    @ApiOperation("CRM大区基础数据")
    @GetMapping("/region")
    public List<OrsBasicData> region() {
        return orsBasicDataService.regionList();
    }

    @ApiOperation("CRM产品组基础数据")
    @GetMapping("/productGroup")
    public List<OrsBasicData> productGroup() {
        return orsBasicDataService.productGroupList();
    }

    @ApiOperation("新国家基础数据")
    @GetMapping("/country/new")
    public List<OrsBasicData> newCountryList() {
        return orsBasicDataService.newCountryList();
    }

    @ApiOperation("新国家基础数据d365")
    @GetMapping("/country-device/new")
    public List<OrsBasicData> newCountryDeviceList() {
        return orsBasicDataService.newCountryDeviceList();
    }

    @ApiOperation("新国区基础数据")
    @GetMapping("/countryRegion/new")
    public List<OrsBasicData> newCountryRegionList() {
        return orsBasicDataService.newCountryRegionList();
    }

    @ApiOperation("新大区基础数据")
    @GetMapping("/region/new")
    public List<OrsBasicData> newRegion() {
        return orsBasicDataService.newRegionList();
    }

    @ApiOperation("新事业部基础数据")
    @GetMapping("/division/new")
    public List<OrsBasicData> newDivision() {
        return orsBasicDataService.newDivisionList();
    }


    @ApiOperation("新事业部基础数据d365")
    @GetMapping("/division-device/new")
    public List<OrsBasicData> newDeviceDivision() {
        return orsBasicDataService.newDivisionDeviceList();
    }


    @ApiOperation("获取用户数据")
    @GetMapping("/user")
    public List<OrsBasicData> userList() {
        return orsBasicDataService.getUserList();
    }


    @ApiOperation("新产品组基础数据d365")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "syb", value = "事业部code")
    })
    @GetMapping("/productGroup-device/new")
    public List<OrsBasicData> newProductGroupDeviceList(String syb){
        return orsBasicDataService.newProductGroupDeviceList(syb);
    }

    @ApiOperation("新产品组基础数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "divisionCode", value = "事业部code")
    })
    @GetMapping("/productGroup/new")
    public List<OrsBasicData> newProductGroup(String divisionCode) {
        return orsBasicDataService.newProductGroupList(divisionCode);
    }

    @ApiOperation("物实例列表")
    @GetMapping("/assetId")
    public Page<String> assetIdList(String assetId, Page<?> page) {
        return orsBasicDataService.assetIdList(assetId, page);
    }

    @ApiOperation("根云物实例列表")
    @GetMapping("/rcAssetId")
    public Page<String> rcAssetIdList(String rcAssetId, Page<?> page) {
        return orsBasicDataService.rcAssetIdList(rcAssetId, page);
    }

    @ApiOperation("实例名称列表")
    @GetMapping("/name")
    public Page<String> nameList(String thingName, Page<?> page) {
        return orsBasicDataService.nameList(thingName, page);
    }

    @ApiOperation("物模型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "divisionCode", value = "事业部code"),
            @ApiImplicitParam(name = "productGroupCode", value = "产品组code"),
            @ApiImplicitParam(name = "key", value = "查询条件", required = false)
    })
    @GetMapping("/modelName")
    public List<String> modelNameList(String divisionCode,String productGroupCode, @RequestParam(required = false) String key) {
        return orsBasicDataService.modelNameList(divisionCode,productGroupCode,key);
    }


    @ApiOperation("物模型列表-new")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "divisionCode", value = "事业部code"),
            @ApiImplicitParam(name = "productGroupCode", value = "产品组code")
    })
    @GetMapping("/modelNameNew")
    public List<String> modelNameNewList(String divisionCode,String productGroupCode) {
        return orsBasicDataService.modelNameListNew(divisionCode,productGroupCode);
    }

    @ApiOperation("设备编号列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceNo", value = "设备编号"),
            @ApiImplicitParam(name = "size", value = "查询的数量")
    })
    @GetMapping("/deviceNo")
    public List<OrsDeviceListResp> deviceNoList(String deviceNo, Integer size) {
        return orsBasicDataService.deviceNoList(deviceNo, size);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示数量")
    })
    @ApiOperation("通过物模型id获取所有的属性")
    @GetMapping("/modelProperties/{modelId}")
    public Page<OrsModelPropertiesConfig> modelProperties(@PathVariable("modelId") String modelId, Integer current, Integer pageSize) {
        return orsBasicDataService.findModelPropertiesByModelId(modelId, current, pageSize);
    }

    /**
     * 数据完整性
     * 数量统计，指设备的数据质量检查属性，统计出现传Null值（属性从未上报）的设备，统计以设备为维度，若一台设备出现多个属性传NULL只算一次，统计其设备总数(不确定这个是哪里来的)、异常数量、剔除数量
     * 比例统计，分母为未出现异常数量（设备总数-异常数量），分子为分母排除剔除设备后（总设备数-异常数量-剔除数量），根据以上规则计算设备的正常率
     * @param query
     * @return
     */
    @ApiOperation("数据完整性")
    @GetMapping("/data/integrity")
    public List<DeviceQuelityCountDTO> countDeviceQuelityByAll(DeviceQuelityCountQuery query) {
        if (query.getNewMethod() == null) {
            query.setNewMethod(0);
        }

        return orsBasicDataService.countDeviceQuelityByAll(query);
    }

    /**
     * 数据准确性
     * 数量统计，指设备数据质量检查属性，是否出现属性值异常、逆增长等非传NULL的现象，出现这些任一异常认为不满足要求，统计以设备为维度，一台设备出现多个属性异常只算一次，统计其总设备数、异常设备数量、剔除设备数量
     * 正常比例统计，分母为总设备数量-异常设备数量，分子为总设备数量-异常设备数量-剔除设备数量
     * @param query
     * @return
     */
    @ApiOperation("数据准确性")
    @GetMapping("/data/quality")
    public List<DeviceQuelityCountDTO> countDeviceQuelityByQuelity(DeviceQuelityCountQuery query) {
        if (query.getNewMethod() == null) {
            query.setNewMethod(0);
        }
        return orsBasicDataService.countDeviceQuelityByQuelity(query);
    }

    /**
     * 返回条件内各个属性异常占比
     * @param deviceQuelityCountQuery
     * @return
     */
    @ApiOperation("属性异常占比")
    @GetMapping("/abnormalCode/rate")
    public AbnormalRate abnormalCodeRate(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        if(StrUtil.isNotBlank(deviceQuelityCountQuery.getMonth())) {
            DateTime dateTime = DateUtil.parse(deviceQuelityCountQuery.getMonth(), "yyyy-MM");
            deviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfMonth(dateTime)));
            deviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfMonth(dateTime)));
        } else if (StrUtil.isNotBlank(deviceQuelityCountQuery.getYear())) {
            DateTime dateTime = DateUtil.parse(deviceQuelityCountQuery.getYear(), "yyyy");
            deviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfYear(dateTime)));
            deviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfYear(dateTime)));
        }
        List<AbnormalCodeRateResp> abnormalCodeRateResps = deviceDataAbnormalDetailDayMapper.selectAbnormalCodeRate(deviceQuelityCountQuery);
        AbnormalRate abnormalRate = new AbnormalRate();

        deviceQuelityCountQuery.setAbnormalType("1");
        abnormalRate.setAccuracyRates(disposeData(deviceQuelityCountQuery,abnormalCodeRateResps));
        deviceQuelityCountQuery.setAbnormalType("2");
        abnormalRate.setIntegrityRate(disposeData(deviceQuelityCountQuery,abnormalCodeRateResps));
        return abnormalRate;
    }



    private List<AbnormalCodeRateResp> disposeData(DeviceQuelityCountQuery deviceQuelityCountQuery,List<AbnormalCodeRateResp> list){
        //1.准确性异常 2.完整性异常
        if("1".equals(deviceQuelityCountQuery.getAbnormalType())){
            list = list.stream().filter(item -> (!item.getAbnormalCode().equals("9008") && !item.getAbnormalCode().equals("9009"))).collect(Collectors.toList());
        }else{
            list = list.stream().filter(item -> (item.getAbnormalCode().equals("9008") || item.getAbnormalCode().equals("9009"))).collect(Collectors.toList());
        }

        List<AbnormalCodeRateResp> abnormalCodeRateResps = deviceDataAbnormalDetailDayMapper.countDevice(deviceQuelityCountQuery);
        Map<String, AbnormalCodeRateResp> countMap = abnormalCodeRateResps.stream().collect(Collectors.toMap(AbnormalCodeRateResp::getParamCode, i -> i, (k1, k2) -> k1));

        // 1. 按 paramCode 分组
        Map<String, List<AbnormalCodeRateResp>> groupByParam = list.stream()
                .collect(Collectors.groupingBy(AbnormalCodeRateResp::getParamCode));

        List<AbnormalCodeRateResp> result = new ArrayList<>();
        
        for (Map.Entry<String, List<AbnormalCodeRateResp>> entry : groupByParam.entrySet()) {
            String key = entry.getKey();
            AbnormalCodeRateResp abnormalCodeRateResp = new AbnormalCodeRateResp();
            abnormalCodeRateResp.setParamCode(key);
            if(countMap.containsKey(key)){
                abnormalCodeRateResp.setPropertyName(countMap.get(key).getPropertyName());
                abnormalCodeRateResp.setTotal(countMap.get(key).getTotal());
            }
            List<AbnormalCodeRateResp> value = entry.getValue();

            double totalRate = value.stream()
                    .mapToDouble(resp -> Double.parseDouble(resp.getAbnormalCodeRate().replace("%", "")))
                    .sum();
            String formattedTotalRate = String.format("%.2f%%", totalRate);
            abnormalCodeRateResp.setAbnormalCodeRate(formattedTotalRate);
            abnormalCodeRateResp.setAbnormalCodeRateRespList(value);
            result.add(abnormalCodeRateResp);
        }
        List<AbnormalCodeRateResp> sortedList = result.stream()
                .sorted((a, b) -> {
                    double aRate = Double.parseDouble(a.getAbnormalCodeRate().replace("%", ""));
                    double bRate = Double.parseDouble(b.getAbnormalCodeRate().replace("%", ""));
                    return Double.compare(bRate, aRate);
                })
                .collect(Collectors.toList());

        return  sortedList;
    }




    /**
     * 首页-设备总数统计
     * 统计设备总数，树根物联盒数量，华兴物联盒数量
     * @return 设备总数统计响应对象
     */
    @ApiOperation("设备总数统计")
    @GetMapping("/totalDeviceStatistics")
    public TotalDeviceStatisticsResp totalDeviceStatistics() {
        return orsBaseDeviceInfoMapper.totalDeviceStatistics();
    }

    /**
     * 首页-设备比例统计
     * 统计设备总数，树根物联盒数量，华兴物联盒数量
     * @return 设备总数统计响应对象
     */
    @ApiOperation("设备比例统计")
    @GetMapping("/deviceRatio")
    public DeviceRatioResp deviceRatio(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        initDateParam(deviceQuelityCountQuery);
        return orsBaseDeviceInfoMapper.deviceRatioChangeVersion(deviceQuelityCountQuery);
        /*return StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.deviceRatioChange(deviceQuelityCountQuery.getStartTime(), deviceQuelityCountQuery.getEndTime(),deviceQuelityCountQuery.getDoubleRateSign(),deviceQuelityCountQuery.getRulesSign()) : orsBaseDeviceInfoMapper.deviceRatioNew(deviceQuelityCountQuery.getStartTime(), deviceQuelityCountQuery.getEndTime());
         */
    }

    private static void initDateParam(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        if(StrUtil.isNotBlank(deviceQuelityCountQuery.getMonth())) {
            DateTime dateTime = DateUtil.parse(deviceQuelityCountQuery.getMonth(), "yyyy-MM");
            deviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfMonth(dateTime)));
            deviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfMonth(dateTime)));
        } else if (StrUtil.isNotBlank(deviceQuelityCountQuery.getYear())) {
            DateTime dateTime = DateUtil.parse(deviceQuelityCountQuery.getYear(), "yyyy");
            deviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfYear(dateTime)));
            deviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfYear(dateTime)));
        }
    }

    /**
     * 首页-事业部设备比例统计
     * 统计设备总数，树根物联盒数量，华兴物联盒数量
     * @return 设备总数统计响应对象
     */
    @ApiOperation("按事业部设备统计")
    @GetMapping("/division/statistics")
    public List<DeviceRatioReportEntryResp> divisionStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        initDateParam(deviceQuelityCountQuery);
        if(StringUtils.isNotBlank(deviceQuelityCountQuery.getQueryClassification()) && !"1".equals(deviceQuelityCountQuery.getQueryClassification())){
            return regionStatistics(deviceQuelityCountQuery);
        }
        if(StringUtils.isNotBlank(deviceQuelityCountQuery.getDivision())){
            return devisionStatistics(deviceQuelityCountQuery);
        }
        List<DeviceRatioReportEntryResp> divisionStatistics = StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.divisionStatisticsChange(deviceQuelityCountQuery):orsBaseDeviceInfoMapper.divisionStatisticsNew(deviceQuelityCountQuery);
        List<DeviceRatioReportEntryResp> divisionCountStatistics = StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.divisionCountStatisticsChange(deviceQuelityCountQuery) : orsBaseDeviceInfoMapper.divisionCountStatisticsNew(deviceQuelityCountQuery);
        divisionStatistics = divisionStatistics.stream().filter(entry -> entry.getTitle() != null).collect(Collectors.toList());
        updateReportNum(divisionStatistics, divisionCountStatistics);

        return divisionStatistics;
    }


    /**
     * 统计事业部下的各个-模型
     * @param deviceQuelityCountQuery
     * @return
     */
    public List<DeviceRatioReportEntryResp> devisionStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        initDateParam(deviceQuelityCountQuery);
        List<DeviceRatioReportEntryResp> divisionStatistics = orsBaseDeviceInfoMapper.divisionModel(deviceQuelityCountQuery);
        List<DeviceRatioReportEntryResp> divisionCountStatistics = orsBaseDeviceInfoMapper.divisionCountModel(deviceQuelityCountQuery);
        divisionStatistics = divisionStatistics.stream().filter(entry -> entry.getTitle() != null).collect(Collectors.toList());
        updateReportNum(divisionStatistics, divisionCountStatistics);

        return divisionStatistics;
    }

    /**
     * 首页-大区设备比例统计
     * 统计设备总数，树根物联盒数量，华兴物联盒数量
     * @return 设备总数统计响应对象
     */
    @ApiOperation("按大区设备统计")
    @GetMapping("/region/statistics")
    public List<DeviceRatioReportEntryResp> regionStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        initDateParam(deviceQuelityCountQuery);

        //List<DeviceRatioReportEntryResp> regionStatistics = new ArrayList<>();
        List<DeviceRatioReportEntryResp> regionStatistics = StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.regionStatisticsChange(deviceQuelityCountQuery):orsBaseDeviceInfoMapper.regionStatisticsNew(deviceQuelityCountQuery);

        List<DeviceRatioReportEntryResp> regionCountStatistics =   orsBaseDeviceInfoMapper.regionCountStatisticsChange(deviceQuelityCountQuery);

        regionStatistics = regionStatistics.stream().filter(entry -> !"无大区".equals(entry.getTitle())).collect(Collectors.toList());
        updateReportNum(regionStatistics, regionCountStatistics);

        return regionStatistics;
    }

    private void updateReportNum(List<DeviceRatioReportEntryResp> regionStatistics, List<DeviceRatioReportEntryResp> regionCountStatistics) {
        // 创建一个映射，键是title，值是对应的DeviceRatioReportEntryResp对象
        Map<String, DeviceRatioReportEntryResp> countMap = regionCountStatistics.stream()
            .collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, entry -> entry));

        // 更新regionStatistics中具有相同title的元素的reportNum
        for (DeviceRatioReportEntryResp entry : regionStatistics) {
            if (countMap.containsKey(entry.getTitle())) {
                entry.setReportNum(countMap.get(entry.getTitle()).getReportNum());
                entry.setAbnormalParamNum(countMap.get(entry.getTitle()).getAbnormalParamNum());
                entry.setNullParamNum(countMap.get(entry.getTitle()).getNullParamNum());
            }
        }
    }

    /**
     * 首页-日期设备比例统计
     * 统计设备总数，树根物联盒数量，华兴物联盒数量
     * @return 设备总数统计响应对象
     */
    @ApiOperation("按日期进行设备统计")
    @GetMapping("/date/statistics")
    public List<DeviceRatioReportEntryResp> dateStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery) {
        initDateParam(deviceQuelityCountQuery);

        // 构建上期查询参数
        DeviceQuelityCountQuery preDeviceQuelityCountQuery = BeanUtil.copyProperties(deviceQuelityCountQuery, DeviceQuelityCountQuery.class,
                "divisiones", "productCodes", "regionCode");
        preDeviceQuelityCountQuery.setDivisiones(Joiner.on(",").join(deviceQuelityCountQuery.getDivisiones()));
        preDeviceQuelityCountQuery.setProductCodes(Joiner.on(",").join(deviceQuelityCountQuery.getProductCodes()));
        preDeviceQuelityCountQuery.setRegionCode(Joiner.on(",").join(deviceQuelityCountQuery.getRegionCode()));
        if(StrUtil.isNotBlank(preDeviceQuelityCountQuery.getYear())) {
            DateTime dateTime = DateUtil.parse(preDeviceQuelityCountQuery.getYear(), "yyyy").offset(DateField.YEAR, -1);
            preDeviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfYear(dateTime)));
            preDeviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfYear(dateTime)));
        } else if(StrUtil.isNotBlank(preDeviceQuelityCountQuery.getMonth())){
            DateTime dateTime = DateUtil.parse(preDeviceQuelityCountQuery.getMonth(), "yyyy-MM").offset(DateField.MONTH, -1);
            preDeviceQuelityCountQuery.setStartTime(DateUtil.formatDate(DateUtil.beginOfMonth(dateTime)));
            preDeviceQuelityCountQuery.setEndTime(DateUtil.formatDate(DateUtil.endOfMonth(dateTime)));
        } else {
            preDeviceQuelityCountQuery.setStartTime(DateUtil.parseDate(deviceQuelityCountQuery.getStartTime()).offset(DateField.MONTH, -1).toString());
            preDeviceQuelityCountQuery.setEndTime(DateUtil.parseDate(deviceQuelityCountQuery.getEndTime()).offset(DateField.MONTH, -1).toString());
        }

        List<DeviceRatioReportEntryResp> deviceRatioReportEntryResps =
                StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                        orsBaseDeviceInfoMapper.dateStatisticsChange(preDeviceQuelityCountQuery):orsBaseDeviceInfoMapper.dateStatisticsNew(preDeviceQuelityCountQuery);
        // 上期数据
        Map<String, DeviceRatioReportEntryResp> preData = deviceRatioReportEntryResps
                .stream()
                .collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, Function.identity()));
        log.info("上期参数: {}\n上期数据: {}", JSONUtil.toJsonStr(preDeviceQuelityCountQuery), JSONUtil.toJsonStr(preData));

        //查询时间相关的上报数
        /*deviceQuelityCountQuery.setSign("1");
        List<DeviceRatioReportEntryResp> divisionCountStatistics = StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.divisionCountStatistics(deviceQuelityCountQuery): orsBaseDeviceInfoMapper.divisionCountStatisticsNew(deviceQuelityCountQuery);
        Map<String, DeviceRatioReportEntryResp> collect = divisionCountStatistics.stream().collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, i -> i, (k1, k2) -> k1));
*/
        // 本期数据
        List<DeviceRatioReportEntryResp> currList = StringUtils.isNotBlank(deviceQuelityCountQuery.getSwitchSign()) && "1".equals(deviceQuelityCountQuery.getSwitchSign())?
                orsBaseDeviceInfoMapper.dateStatisticsChange(deviceQuelityCountQuery) : orsBaseDeviceInfoMapper.dateStatisticsNew(deviceQuelityCountQuery);
        log.info("本期参数: {}\n本期数据: {}", JSONUtil.toJsonStr(deviceQuelityCountQuery), JSONUtil.toJsonStr(currList));
        for (DeviceRatioReportEntryResp deviceRatioReportEntryResp : currList) {

            /*if(CollUtil.isNotEmpty(collect) &&
                    collect.containsKey(deviceRatioReportEntryResp.getTitle())
                    && (StringUtils.isBlank(deviceQuelityCountQuery.getSwitchSign()) || !"1".equals(deviceQuelityCountQuery.getSwitchSign()))){
                deviceRatioReportEntryResp.setReportNum(collect.get(deviceRatioReportEntryResp.getTitle()).getReportNum());
            }*/
            String key;
            if (StrUtil.isNotBlank(preDeviceQuelityCountQuery.getYear())) {
                key = deviceRatioReportEntryResp.getTitle();
            } else {
                key = DateUtil.parse(deviceRatioReportEntryResp.getTitle(), "MM月dd日")
                        .offset(DateField.MONTH, -1)
                        .toString("MM月dd日");
            }
            DeviceRatioReportEntryResp preItem = preData.remove(key);
            log.info("获取上期值(key): {}, (value): {}", key, JSONUtil.toJsonStr(preItem));
            if (Objects.nonNull(preItem)) {
                deviceRatioReportEntryResp.setPreAccuracy(preItem.getAccuracy());
                deviceRatioReportEntryResp.setPreIntegrityRate(preItem.getIntegrityRate());
            } else {
                deviceRatioReportEntryResp.setPreAccuracy("0");
                deviceRatioReportEntryResp.setPreIntegrityRate("0");
            }

        }
        return currList;
    }

}
