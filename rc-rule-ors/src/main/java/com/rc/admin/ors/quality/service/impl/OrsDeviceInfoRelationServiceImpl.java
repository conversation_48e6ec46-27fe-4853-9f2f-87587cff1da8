package com.rc.admin.ors.quality.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoRelationMapper;
import com.rc.admin.ors.quality.entity.DeviceInfoRelationSync;
import com.rc.admin.ors.quality.entity.OrsDeviceInfoRelation;
import com.rc.admin.ors.quality.model.NcOtDeviceListResp;
import com.rc.admin.ors.quality.model.OrsRootCloudResp;
import com.rc.admin.ors.quality.service.DeviceInfoRelationSyncService;
import com.rc.admin.ors.quality.service.OrsDeviceInfoRelationService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;

/**
 * 设备基础信息(OrsDeviceInfoRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-31 16:00:20
 */
@Slf4j
@Service("orsDeviceInfoRelationService")
public class OrsDeviceInfoRelationServiceImpl extends ServiceImpl<OrsDeviceInfoRelationMapper, OrsDeviceInfoRelation> implements OrsDeviceInfoRelationService {

    private final String NORMAL_PATTRN = "yyyy-MM-dd'T'HH:mm:ss.SSSX";

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(NORMAL_PATTRN);

    @Value("${ors.rootCloud.urlPre:http://federation-openapi-gateway-zone-china.ngc.sanygroup.com}")
    private String urlPre;

    @Resource
    private DeviceInfoRelationSyncService deviceInfoRelationSyncService;

    @Resource
    private OrsSyncDeviceService orsSyncDeviceService;

    /**
     * 8小时的毫秒数
     */
    private final Long time = 8 * 60 * 60 * 1000L;

    ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("sync-ml-device-pool-%d").build();
    ExecutorService executor = new ThreadPoolExecutor(1, 5,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    /**
     * 同步ml数据 全量同步
     */
    @Override
    public void syncHistoryDevice() throws ParseException {

        // 这里默认从2024-02-01 开始查询所有设备
        String url = String.format("%s/third-apps/nc-ot-oversea-device-register/api/oversea/getDeviceList", urlPre);
        url = url + "?pageIndex=" + 1 + "&pageSize=" + 200000 + "&beginTime=1706786877000";
        String fields = "&fields=thing_id,device_ser,rc_assetid,delete_flag,model_id_40,tbox_id";
        url = url + fields;
        HttpRequest request = HttpRequest.get(url);
        request.bearerAuth(orsSyncDeviceService.getRootCloudToken());
        HttpResponse response = request.execute();

        if (StrUtil.isBlank(response.body())) {
            return;
        }

        NcOtDeviceListResp resp = JSONObject.parseObject(response.body(), NcOtDeviceListResp.class);
        if (!"200".equals(resp.getCode())) {
            log.error("同步设备信息异常【{},{}】", resp.getCode(), resp.getMessage());
            return;
        }

        JSONObject respData = resp.getData();
        if (respData == null) {
            return;
        }
        JSONArray data = respData.getJSONArray("records");
        if (data == null) {
            return;
        }
        deviceInfoRelationSyncService.remove(new QueryWrapper<>());
        DeviceInfoRelationSync relation;
        List<DeviceInfoRelationSync> list = new ArrayList<>();
        Date now = new Date();
        Map<String, DeviceInfoRelationSync> serialNumMap = new HashMap<>();
        DeviceInfoRelationSync relationTemp;
        // 对请求到的是数据入库处理
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            int deleteFlag = obj.getIntValue("deleteFlag");
            if(deleteFlag == 1) {
                continue;
            }
            String serialNum = obj.getString("deviceSer");

            if (serialNum != null && (serialNum.toLowerCase().contains("old")
                || serialNum.toLowerCase().contains("test"))) {
                continue;
            }
            relation = new DeviceInfoRelationSync();
            relation.setDeviceId(obj.getString("thingId"));
            relation.setDeviceName(obj.getString("deviceSer"));
            relation.setSerialNum(serialNum);
            relation.setThingInstanceId(obj.getString("thingId"));
            relation.setThingModelId(obj.getString("modelId40"));
            relation.setTboxId(obj.getString("tboxId"));
            relation.setRcAssetId(obj.getString("rcAssetId"));
            relation.setCreateTime(now);
            list.add(relation);
            if (list.size() == 2000) {
                deviceInfoRelationSyncService.saveBatch(list);
                list = new ArrayList<>();
            }
        }

        if (!list.isEmpty()) {
            deviceInfoRelationSyncService.saveBatch(list);
        }

        // 没有发生异常，迁移数据
        baseMapper.transferData();

    }

    private Date convert8Date(Date source) {
        long l = source.getTime() + time;
        return new Date(l);
    }
}

