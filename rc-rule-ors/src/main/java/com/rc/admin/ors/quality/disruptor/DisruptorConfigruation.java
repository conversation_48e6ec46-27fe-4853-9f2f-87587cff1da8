package com.rc.admin.ors.quality.disruptor;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.rc.admin.ors.quality.model.DeviceDataModel;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/10/24 8:53
 * @describe
 */
@Component
public class DisruptorConfigruation {

    @Resource
    private WorkIotHandler handler;

    @Bean(name = "workIotRingBuffer")
    public RingBuffer<DeviceDataModel> workIotRingBuffer(){
        EventFactory<DeviceDataModel> factory = new WorkIotEventFactory();
        int ringBufferSize = 1024 * 1024;

        Disruptor<DeviceDataModel> disruptor = new Disruptor<>(factory, ringBufferSize, r->{
           Thread t = new Thread(r);
           t.setName("rc-rule-ors-pool-" + t.getId());
           t.setDaemon(true);
           return t;
        }, ProducerType.SINGLE, new SleepingWaitStrategy());

        disruptor.handleEventsWith((e, l, b)->{
            // 这里开始接收消息，并转发给对应的处理器处理
            handler.onEvent(e, l, b);
        });

        disruptor.start();
        return disruptor.getRingBuffer();
    }
}
