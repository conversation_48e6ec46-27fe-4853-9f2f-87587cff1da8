package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 指标定义(OrsIndicator)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-09 19:34:41
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("指标定义")
@TableName("ors_indicator")
public class OrsIndicator extends Model<OrsIndicator> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "indicatorName", value = "指标名称")
    @TableField(value = "indicator_name")
    private String indicatorName;

    @ApiModelProperty(name = "indicatorRule", value = "指标规则")
    @TableField(value = "indicator_rule")
    private String indicatorRule;

    @ApiModelProperty(name = "indicatorIdentity", value = "指标类型 1=完整性 2=合理性")
    @TableField(value = "indicator_identity")
    private String indicatorIdentity;

    @ApiModelProperty(name = "indicatorDesc", value = "指标定义")
    @TableField(value = "indicator_desc")
    private String indicatorDesc;

    @ApiModelProperty(name = "paramCode", value = "字典ID")
    @TableField(value = "param_code")
    private Integer paramCode;

    private Integer abnormalCode;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

