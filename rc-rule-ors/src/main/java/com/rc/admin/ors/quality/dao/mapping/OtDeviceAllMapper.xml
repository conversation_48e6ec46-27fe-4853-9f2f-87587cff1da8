<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OtDeviceAllMapper">

    <select id="selectPageList" resultType="com.rc.admin.ors.quality.model.OtDeviceAllResp">
        SELECT
            device_ser,
            tbox_id,
            manufacturer,
            cnty_code,
            cnty_desc,
            division_code,
            division_name,
            spart,
            spart_desc,
            device_status,
            device_status_desc,
            tbox_status,
            tbox_status_desc,
            transmit_station_code,
            transmit_station_desc,
            asset_id,
            thing_id,
            model_id,
            ngc_station_id,
            ngc_station_name,
            tenant_id,
            tenant_name,
            d365_exist,
            ngc_exist,
            ml_exist,
            evi_exist,
            multi_tbox_exist,
            syb,
            syb_desc,
            bu_inner_desc,
            d365_spart,
            d365_spart_desc
        FROM
            dqm.ot_device_all
        <where>
            <if test="query.syb != null and query.syb != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.buInnerDesc != null and query.buInnerDesc != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>


            <if test="query.d365Spart != null and query.d365Spart != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null or d365_spart_desc is null )
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.d365SpartDesc != null and query.d365SpartDesc != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null or d365_spart_desc is null )
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.deviceSer != null and query.deviceSer != ''">
                AND (
                upper(device_ser) like CONCAT('%', upper(#{query.deviceSer}), '%')
                OR
                device_ser IN
                <foreach item="item" index="index" collection="query.deviceSer.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="query.tboxId != null and query.tboxId != ''">
                AND tbox_id IN
                <foreach item="item" index="index" collection="query.tboxId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.manufacturer != null and query.manufacturer != ''">
                AND manufacturer IN
                <foreach item="item" index="index" collection="query.manufacturer.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.cntyCode != null and query.cntyCode != ''">
                AND cnty_code IN
                <foreach item="item" index="index" collection="query.cntyCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.cntyDesc != null and query.cntyDesc != ''">
                AND cnty_desc IN
                <foreach item="item" index="index" collection="query.cntyDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.divisionCode != null and query.divisionCode != ''">
                AND division_code IN
                <foreach item="item" index="index" collection="query.divisionCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.divisionName != null and query.divisionName != ''">
                AND division_code IN
                <foreach item="item" index="index" collection="query.divisionName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.spart != null and query.spart != ''">
                AND spart IN
                <foreach item="item" index="index" collection="query.spart.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.spartDesc != null and query.spartDesc != ''">
                AND spart IN
                <foreach item="item" index="index" collection="query.spartDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.deviceStatus != null and query.deviceStatus != ''">
                AND device_status IN
                <foreach item="item" index="index" collection="query.deviceStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.deviceStatusDesc != null and query.deviceStatusDesc != ''">
                AND device_status_desc IN
                <foreach item="item" index="index" collection="query.deviceStatusDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tboxStatus != null and query.tboxStatus != ''">
                AND tbox_status IN
                <foreach item="item" index="index" collection="query.tboxStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tboxStatusDesc != null and query.tboxStatusDesc != ''">
                AND tbox_status_desc IN
                <foreach item="item" index="index" collection="query.tboxStatusDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.transmitStationCode != null and query.transmitStationCode != ''">
                AND transmit_station_code IN
                <foreach item="item" index="index" collection="query.transmitStationCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.transmitStationDesc != null and query.transmitStationDesc != ''">
                AND transmit_station_desc IN
                <foreach item="item" index="index" collection="query.transmitStationDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.assetId != null and query.assetId != ''">
                AND asset_id IN
                <foreach item="item" index="index" collection="query.assetId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.thingId != null and query.thingId != ''">
                AND thing_id IN
                <foreach item="item" index="index" collection="query.thingId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.modelId != null and query.modelId != ''">
                AND model_id IN
                <foreach item="item" index="index" collection="query.modelId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.ngcStationId != null and query.ngcStationId != ''">
                AND ngc_station_id IN
                <foreach item="item" index="index" collection="query.ngcStationId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.ngcStationName != null and query.ngcStationName != ''">
                AND ngc_station_name IN
                <foreach item="item" index="index" collection="query.ngcStationName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tenantId != null and query.tenantId != ''">
                AND tenant_id IN
                <foreach item="item" index="index" collection="query.tenantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tenantName != null and query.tenantName != ''">
                AND tenant_name IN
                <foreach item="item" index="index" collection="query.tenantName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.d365Exist != null">
                AND d365_exist = #{query.d365Exist}
            </if>

            <if test="query.ngcExist != null">
                AND ngc_exist = #{query.ngcExist}
            </if>

            <if test="query.mlExist != null">
                AND ml_exist = #{query.mlExist}
            </if>

            <if test="query.eviExist != null">
                AND evi_exist = #{query.eviExist}
            </if>
            <if test="query.multiTboxExist != null">
                AND multi_tbox_exist = #{query.multiTboxExist}
            </if>
            <if test="query.tboxIdCheck != null and query.tboxIdCheck != '' and query.tboxIdCheck == '1'.toString()">
                AND (tbox_id  IS  NULL  or tbox_id = '-1')
            </if>
        </where>
    </select>



    <select id="getDeviceCount" resultType="java.lang.Integer">
        SELECT
        count(distinct device_ser)
        FROM
        dqm.ot_device_all
        <where>
            <if test="query.syb != null and query.syb != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.buInnerDesc != null and query.buInnerDesc != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="query.d365Spart != null and query.d365Spart != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null or d365_spart_desc is null)
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.d365SpartDesc != null and query.d365SpartDesc != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null or d365_spart_desc is null)
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="query.deviceSer != null and query.deviceSer != ''">
                and device_ser
                IN
                <foreach item="item" index="index" collection="query.deviceSer.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tboxId != null and query.tboxId != ''">
                AND tbox_id IN
                <foreach item="item" index="index" collection="query.tboxId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.manufacturer != null and query.manufacturer != ''">
                AND manufacturer IN
                <foreach item="item" index="index" collection="query.manufacturer.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.cntyCode != null and query.cntyCode != ''">
                AND cnty_code IN
                <foreach item="item" index="index" collection="query.cntyCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.cntyDesc != null and query.cntyDesc != ''">
                AND cnty_desc IN
                <foreach item="item" index="index" collection="query.cntyDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.divisionCode != null and query.divisionCode != ''">
                AND division_code IN
                <foreach item="item" index="index" collection="query.divisionCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.divisionName != null and query.divisionName != ''">
                AND division_name IN
                <foreach item="item" index="index" collection="query.divisionName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.spart != null and query.spart != ''">
                AND spart IN
                <foreach item="item" index="index" collection="query.spart.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.spartDesc != null and query.spartDesc != ''">
                AND spart_desc IN
                <foreach item="item" index="index" collection="query.spartDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.deviceStatus != null and query.deviceStatus != ''">
                AND device_status IN
                <foreach item="item" index="index" collection="query.deviceStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.deviceStatusDesc != null and query.deviceStatusDesc != ''">
                AND device_status_desc IN
                <foreach item="item" index="index" collection="query.deviceStatusDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tboxStatus != null and query.tboxStatus != ''">
                AND tbox_status IN
                <foreach item="item" index="index" collection="query.tboxStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tboxStatusDesc != null and query.tboxStatusDesc != ''">
                AND tbox_status_desc IN
                <foreach item="item" index="index" collection="query.tboxStatusDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.transmitStationCode != null and query.transmitStationCode != ''">
                AND transmit_station_code IN
                <foreach item="item" index="index" collection="query.transmitStationCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.transmitStationDesc != null and query.transmitStationDesc != ''">
                AND transmit_station_desc IN
                <foreach item="item" index="index" collection="query.transmitStationDesc.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.assetId != null and query.assetId != ''">
                AND asset_id IN
                <foreach item="item" index="index" collection="query.assetId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.thingId != null and query.thingId != ''">
                AND thing_id IN
                <foreach item="item" index="index" collection="query.thingId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.modelId != null and query.modelId != ''">
                AND model_id IN
                <foreach item="item" index="index" collection="query.modelId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.ngcStationId != null and query.ngcStationId != ''">
                AND ngc_station_id IN
                <foreach item="item" index="index" collection="query.ngcStationId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.ngcStationName != null and query.ngcStationName != ''">
                AND ngc_station_name IN
                <foreach item="item" index="index" collection="query.ngcStationName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tenantId != null and query.tenantId != ''">
                AND tenant_id IN
                <foreach item="item" index="index" collection="query.tenantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.tenantName != null and query.tenantName != ''">
                AND tenant_name IN
                <foreach item="item" index="index" collection="query.tenantName.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.d365Exist != null">
                AND d365_exist = #{query.d365Exist}
            </if>

            <if test="query.ngcExist != null">
                AND ngc_exist = #{query.ngcExist}
            </if>

            <if test="query.mlExist != null">
                AND ml_exist = #{query.mlExist}
            </if>

            <if test="query.eviExist != null">
                AND evi_exist = #{query.eviExist}
            </if>
            <if test="query.multiTboxExist != null">
                AND multi_tbox_exist = #{query.multiTboxExist}
            </if>
            <if test="query.tboxIdCheck != null and query.tboxIdCheck != '' and query.tboxIdCheck == '1'.toString()">
                AND (tbox_id  IS  NULL  or tbox_id = '-1')
            </if>
        </where>
    </select>




    <select id="selectPageReportList" resultType="com.rc.admin.ors.quality.model.OtDeviceAllReportResp">
        SELECT
            case when bu_inner_desc is null then '1' else bu_inner_desc end as syb,
            case when bu_inner_desc is null then '无事业部' else bu_inner_desc end as bu_inner_desc,
            case when d365_spart is null then '1' else d365_spart end as d365_spart,
            case when d365_spart_desc is null then '无产品组' else d365_spart_desc end as d365_spart_desc,
            count (DISTINCT  oda.device_ser) as totalDeviceCount,
            count (DISTINCT CASE WHEN d365_exist IS TRUE THEN oda.device_ser END) AS d365DeviceCount,
            count (DISTINCT CASE WHEN d365_exist IS FALSE AND ngc_exist IS TRUE THEN oda.device_ser END) AS newCNotInD365Count,
            count (DISTINCT CASE WHEN ngc_exist IS TRUE THEN oda.device_ser END) AS newCDeviceCount,
            count (DISTINCT CASE WHEN ngc_exist IS FALSE AND d365_exist IS TRUE THEN oda.device_ser END) AS d365NotInNewCCount,
            count (DISTINCT CASE WHEN ml_exist IS TRUE THEN oda.device_ser END) AS ml2DeviceCount,
            count (DISTINCT CASE WHEN evi_exist IS TRUE THEN oda.device_ser END) AS eviDeviceCount,
            count (DISTINCT CASE WHEN (tbox_id IS NULL or tbox_id='-1')  THEN oda.device_ser END) AS noTboxIdDeviceCount,
            count (DISTINCT CASE WHEN (tbox_id IS NULL or tbox_id='-1' ) AND ngc_exist IS TRUE  THEN oda.device_ser END) AS noTboxIdNewCInstanceCount,
            count (DISTINCT CASE WHEN multi_tbox_exist IS TRUE THEN oda.device_ser END) AS multiTboxDeviceCount
        FROM
            (SELECT
        device_ser,
        tbox_id,
        manufacturer,
        cnty_code,
        cnty_desc,
        division_code,
        division_name,
        spart,
        spart_desc,
        device_status,
        device_status_desc,
        tbox_status,
        tbox_status_desc,
        transmit_station_code,
        transmit_station_desc,
        asset_id,
        thing_id,
        model_id,
        ngc_station_id,
        ngc_station_name,
        tenant_id,
        tenant_name,
        d365_exist,
        ngc_exist,
        ml_exist,
        evi_exist,
        multi_tbox_exist,
        last_update_time,
        syb,
        syb_desc,
        bu_inner_desc,
        d365_spart as tt,
        case when d365_spart_desc is null then null else d365_spart end as d365_spart,
        d365_spart_desc
        FROM
        dqm.ot_device_all
        ) oda
        <where>
            <if test="query.syb != null and query.syb != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.buInnerDesc != null and query.buInnerDesc != ''">
                <choose>
                    <when test="query.sybCheck != null and query.sybCheck != '' and query.sybCheck == '1'.toString()">
                        and (bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or bu_inner_desc is null)
                    </when>
                    <otherwise>
                        and bu_inner_desc IN
                        <foreach item="item" index="index" collection="query.buInnerDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="query.d365Spart != null and query.d365Spart != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null or d365_spart_desc is null)
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365Spart.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="query.d365SpartDesc != null and query.d365SpartDesc != ''">
                <choose>
                    <when test="query.d365SpartCheck != null and query.d365SpartCheck != '' and query.d365SpartCheck == '1'.toString()">
                        and (d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or d365_spart is null  or d365_spart_desc is null)
                    </when>
                    <otherwise>
                        and d365_spart IN
                        <foreach item="item" index="index" collection="query.d365SpartDesc.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY
            bu_inner_desc,
            d365_spart,
            d365_spart_desc
    </select>
</mapper>

