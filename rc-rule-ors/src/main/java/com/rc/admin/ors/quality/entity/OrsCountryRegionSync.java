package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;




@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("国家与国区信息")
@TableName("ors_country_region_sync")
public class OrsCountryRegionSync extends Model<OrsCountryRegionSync> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "countryCode", value = "国家code")
    @TableField(value = "country_code")
    private String countryCode;

    @ApiModelProperty(name = "countryName", value = "国家名称")
    @TableField(value = "country_name")
    private String countryName;

    @ApiModelProperty(name = "countryRegionCode", value = "国区code")
    @TableField(value = "country_region_code")
    private String countryRegionCode;

    @ApiModelProperty(name = "countryRegion", value = "国区名称")
    @TableField(value = "country_region")
    private String countryRegion;

    @ApiModelProperty(name = "regionCode", value = "大区code")
    @TableField(value = "region_code")
    private String regionCode;

    @ApiModelProperty(name = "region", value = "大区名称")
    @TableField(value = "region")
    private String region;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

