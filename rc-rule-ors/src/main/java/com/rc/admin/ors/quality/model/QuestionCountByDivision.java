package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/15 14:06
 * @describe
 */
@Getter
@Setter
public class QuestionCountByDivision {

    @ApiModelProperty(value = "事业部编号")
    private String divisionCode;

    @ApiModelProperty(value = "事业部")
    private String division;

    @ApiModelProperty(value = "转回树根处理总数")
    private int returnRootcloudNum;

    @ApiModelProperty(value = "截止昨日转换树根处理总数")
    private int yesterdayReturnRootcloudNum;

    @ApiModelProperty(value = "转换树根叫昨日差值")
    private int diffReturnRootcloudNum;

    @ApiModelProperty(value = "在途设备总数")
    private int runingDeviceNum;

    @ApiModelProperty(value = "截止昨日在途设备总数")
    private int yesterdayRuningDeviceNum;

    @ApiModelProperty(value = "在途设备较昨日差值")
    private int diffRuningDeviceNum;

    @ApiModelProperty(value = "无信号总数")
    private int noSingleNum;

    @ApiModelProperty(value = "截止昨日无信号数量")
    private int yesterdayNoSingleNum;

    @ApiModelProperty(value = "无信号数量较昨日差值")
    private int diffNoSingleNum;

    @ApiModelProperty(value = "设备不支持总数")
    private int unSurportNum;

    @ApiModelProperty(value = "设备不支持截止昨日数量")
    private int yesterdayUnSurportNum;

    @ApiModelProperty(value = "设备不支持较昨日差值")
    private int diffUnSurportNum;

    @ApiModelProperty(value = "待排除/待起机")
    private int sceneNum;

    @ApiModelProperty(value = "待排除截止昨日数量")
    private int yesterdaySceneNum;

    @ApiModelProperty(value = "待排除较昨日数量差值")
    private int diffSceneNum;

    @ApiModelProperty(value = "其他")
    private int otherNum;

    @ApiModelProperty(value = "截止昨日其他统计")
    private int yesterdayOtherNum;

    @ApiModelProperty(value = "其他较昨日差值")
    private int diffOtherNum;

    @ApiModelProperty(value = "研究院未反馈总数")
    private int unhandleNum;

    @ApiModelProperty(value = "截止研究院未反馈总数")
    private int yesterdayUnhandleNum;

    @ApiModelProperty(value = "未反馈较昨日差值")
    private int diffUnhandleNum;

    @ApiModelProperty(value = "待启机/待剔除总数")
    private int waitStartNum;

    @ApiModelProperty(value = "待启机/待剔除截止昨日数量")
    private int yesterdayWaitStartNum;

    @ApiModelProperty(value = "待启机/待剔除较昨日差值")
    private int diffWaitStartNum;

    @ApiModelProperty(value = "事业部编号")
    private int total;

    public int getDiffReturnRootcloudNum() {
        return returnRootcloudNum - yesterdayReturnRootcloudNum;
    }

    public int getDiffRuningDeviceNum() {
        return runingDeviceNum - yesterdayRuningDeviceNum;
    }

    public int getDiffNoSingleNum() {
        return noSingleNum - yesterdayNoSingleNum;
    }

    public int getDiffUnSurportNum() {
        return unSurportNum - yesterdayUnSurportNum;
    }

    public int getDiffSceneNum() {
        return sceneNum - yesterdaySceneNum;
    }

    public int getDiffOtherNum() {
        return otherNum - yesterdayOtherNum;
    }

    public int getDiffUnhandleNum() {
        return unhandleNum - yesterdayUnhandleNum;
    }

    public int getDiffWaitStartNum() {
        return waitStartNum - yesterdayUnhandleNum;
    }

    public int getTotal() {
        return waitStartNum + unhandleNum;
    }
}
