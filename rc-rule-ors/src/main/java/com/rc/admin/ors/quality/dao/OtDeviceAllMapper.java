package com.rc.admin.ors.quality.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.OtDeviceAll;
import com.rc.admin.ors.quality.model.OtDeviceAllReportResp;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;
import com.rc.admin.ors.quality.model.OtDeviceAllResp;
import com.rc.admin.ors.quality.model.OtDevicePage;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface OtDeviceAllMapper extends BaseMapper<OtDeviceAll> {


    OtDevicePage<OtDeviceAllResp> selectPageList(OtDevicePage<OtDeviceAllResp> page, @Param("query")OtDeviceAllReq otDeviceAllReq);
    List<OtDeviceAllResp> selectPageList(@Param("query")OtDeviceAllReq otDeviceAllReq);


    int getDeviceCount(@Param("query")OtDeviceAllReq otDeviceAllReq);




    List<OtDeviceAllReportResp> selectPageReportList(@Param("query")OtDeviceAllReq otDeviceAllReq);

}
