package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsDeviceCheckConfigMapper;
import com.rc.admin.ors.quality.dao.SanydsCoreParamDefMapper;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.SanydsCoreParamDef;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigImportExcel;
import com.rc.admin.ors.quality.model.DeviceRule;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.util.ShiroUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-23 15:42:00
 */
@Service("orsDeviceCheckConfigService")
public class OrsDeviceCheckConfigServiceImpl extends ServiceImpl<OrsDeviceCheckConfigMapper, OrsDeviceCheckConfig> implements OrsDeviceCheckConfigService {

    @Value("${device.location.model-name:GPS_Longitude,GPS_Latitude}")
    private String DEVICE_LOCATION;

    @Resource
    SanydsCoreParamDefMapper sanydsCoreParamDefMapper;


    @Override
    public Page<DeviceRule> findRuleByDeviceCode(Page<ModleIndicatorAndExclude> page, Wrapper wrapper) {
        return baseMapper.findRuleByDeviceCode(page, wrapper);
    }




    @Override
    public String getParamNameModel(String modelId, String paramCode,Map<String, SanydsCoreParamDef> map) {
        if(StringUtils.isNotBlank(modelId) && StringUtils.isNotBlank(paramCode)) {
            String key = modelId + "_" + paramCode;
            if ("8501".equals(paramCode)) {
                return DEVICE_LOCATION;
            } else {
                if (map.containsKey(key)) {
                    return map.get(key).getParamNameModel();
                }
            }
        }
        return "";
    }

    @Override
    public Map<String, SanydsCoreParamDef> getSanydsCoreParamDef() {
        List<SanydsCoreParamDef> sanydsCoreParamDefs = sanydsCoreParamDefMapper.selectList(new QueryWrapper<SanydsCoreParamDef>());
        Map<String, SanydsCoreParamDef> map = sanydsCoreParamDefs.stream()
                .collect(Collectors.toMap(
                        def -> def.getModelId() + "_" + def.getParamCode(),
                        def -> def, (k1, k2) -> k1
                ));
        return map;
    }

    @Override
    public void importDelete(List<OrsDeviceCheckConfigImportExcel> list) {
        this.baseMapper.batchDelete(list);
    }

    @Override
    public void importDeleteInsertHistroy(List<OrsDeviceCheckConfigImportExcel> list,String createUser) {
        this.baseMapper.insertHistory(list, createUser);
    }

    @Override
    public void insertHistoryById(Long id,String createUser) {
        this.baseMapper.insertHistoryById(id,createUser);
    }
}

