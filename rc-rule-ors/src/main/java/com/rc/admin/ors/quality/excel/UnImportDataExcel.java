package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/1 15:05
 * @describe
 */
@Getter
@Setter
@Builder
public class UnImportDataExcel {

    @Excel(name = "设备编号", width = 15)
    private String code;

    @Excel(name = "剔除类型", width = 15)
    private String name;

    @Excel(name = "剔除原因", width = 20)
    private String detail;

    @Excel(name = "失败原因", width = 20)
    private String resean;
}
