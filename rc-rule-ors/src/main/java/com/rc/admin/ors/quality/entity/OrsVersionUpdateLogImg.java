package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Base64;

@Data
@TableName("dqm.ors_version_update_log_img")
public class OrsVersionUpdateLogImg implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键，自增ID")
    private Integer id;

    @TableField("version_update_id")
    @ApiModelProperty(value = "版本更新记录id")
    private Integer versionUpdateId;

    @TableField("image_data")
    @ApiModelProperty(value = "图片二进制数据")
    private byte[] imageData;

    @TableField("image_desc")
    @ApiModelProperty(value = "图片描述")
    private String imageDesc;


    // 新增字段来存储Base64编码的图片数据
    @TableField(exist = false) // 这个字段不存在于数据库中
    @ApiModelProperty(value = "Base64编码的图片数据")
    private String imageBase64;

    public String getImageBase64() {
        if (imageData != null) {
            return "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(imageData);

        }
        return null;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }
}
