package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 字典(Dict)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-07 08:58:36
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("字典")
@TableName("sys_dict")
public class Dict extends Model<Dict> {

    @TableId(type = IdType.AUTO)
    private String id;


    @ApiModelProperty(name = "name", value = "名称", required = true)
    @NotNull(message = "名称不能为空")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "code", value = "编码", required = true)
    @NotNull(message = "编码不能为空")
    @TableField(value = "code")
    private String code;

    @ApiModelProperty(name = "parentCode", value = "父code")
    @TableField(value = "parent_code")
    private String parentCode;

    @ApiModelProperty(name = "dictType", value = "字典类型", required = true)
    @NotNull(message = "字典类型不能为空")
    @TableField(value = "dict_type")
    private String dictType;

    @ApiModelProperty(name = "icon", value = "图标")
    @TableField(value = "icon")
    private String icon;

    @ApiModelProperty(name = "displayType", value = "显示方式")
    @TableField(value = "display_type")
    private String displayType;

    @ApiModelProperty(name = "status", value = "状态")
    @TableField(value = "status")
    private String status;

    @ApiModelProperty(name = "remarks", value = "备注")
    @TableField(value = "remarks")
    private String remarks;

    @ApiModelProperty(name = "orderNo", value = "排序值")
    @TableField(value = "order_no")
    private Integer orderNo;

    @ApiModelProperty(name = "version", value = "乐观锁")
    @TableField(value = "version")
    private Integer version;

    @ApiModelProperty(name = "createUser", value = "创建人")
    @TableField(value = "create_user")
    private String createUser;

    @ApiModelProperty(name = "createDate", value = "创建时间")
    @TableField(value = "create_date")
    private Date createDate;

    @ApiModelProperty(name = "editUser", value = "编辑人")
    @TableField(value = "edit_user")
    private String editUser;

    @ApiModelProperty(name = "editDate", value = "编辑时间")
    @TableField(value = "edit_date")
    private Date editDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

