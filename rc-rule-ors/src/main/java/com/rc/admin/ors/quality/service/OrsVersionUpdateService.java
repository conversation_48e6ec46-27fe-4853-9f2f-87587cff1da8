package com.rc.admin.ors.quality.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsVersionUpdateLogImgMapper;
import com.rc.admin.ors.quality.dao.OrsVersionUpdateLogMapper;
import com.rc.admin.ors.quality.dao.OrsVersionUpdateLogRateMapper;
import com.rc.admin.ors.quality.entity.OrsVersionUpdateLog;
import com.rc.admin.ors.quality.entity.OrsVersionUpdateLogImg;
import com.rc.admin.ors.quality.entity.OrsVersionUpdateLogRate;
import com.rc.admin.ors.quality.model.DeviceQuelityCountQuery;
import com.rc.admin.ors.quality.model.DeviceRatioResp;
import com.rc.admin.util.ShiroUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.Arrays;

@Service
public class OrsVersionUpdateService extends ServiceImpl<OrsVersionUpdateLogMapper, OrsVersionUpdateLog> {

    @Resource
    private OrsVersionUpdateLogImgMapper orsVersionUpdateLogImgMapper;

    @Resource
    private OrsVersionUpdateLogRateMapper orsVersionUpdateLogRateMapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource
    private Executor asyncExecutor;


    public void removeByVersionUpdateId(Integer id){
        orsVersionUpdateLogImgMapper.delete(new QueryWrapper<OrsVersionUpdateLogImg>().lambda().eq(OrsVersionUpdateLogImg::getVersionUpdateId,id));
        orsVersionUpdateLogRateMapper.delete(new QueryWrapper<OrsVersionUpdateLogRate>().lambda().eq(OrsVersionUpdateLogRate::getVersionUpdateId,id));
        this.baseMapper.deleteById(id);
    }


    public OrsVersionUpdateLog createVersionUpdateLog(OrsVersionUpdateLog orsVersionUpdateLog, List<MultipartFile> images) throws IOException {
        
        OrsVersionUpdateLog versionUpdateLog = new OrsVersionUpdateLog();

        Date versionUpdateDate = orsVersionUpdateLog.getVersionUpdateDate();

        versionUpdateLog.setVersionName(orsVersionUpdateLog.getVersionName());

        versionUpdateLog.setVersionUpdateDate(versionUpdateDate);
        versionUpdateLog.setVersionUpdateDesc(orsVersionUpdateLog.getVersionUpdateDesc());
        versionUpdateLog.setUpdateTime(new Date());
        if(orsVersionUpdateLog.getId()!=null){
            versionUpdateLog.setId(orsVersionUpdateLog.getId());
            versionUpdateLog.setUpdateBy(ShiroUtil.getCurrentUser().getUsername());
            this.baseMapper.updateById(versionUpdateLog);
        }else{
            versionUpdateLog.setCreateBy(ShiroUtil.getCurrentUser().getUsername());
            versionUpdateLog.setCreateTime(new Date());
            this.baseMapper.insert(versionUpdateLog);
        }


        if(orsVersionUpdateLog.getId()!=null){
            orsVersionUpdateLogImgMapper.delete(new QueryWrapper<OrsVersionUpdateLogImg>().lambda().eq(OrsVersionUpdateLogImg::getVersionUpdateId,orsVersionUpdateLog.getId()));
            orsVersionUpdateLogRateMapper.delete(new QueryWrapper<OrsVersionUpdateLogRate>().lambda().eq(OrsVersionUpdateLogRate::getVersionUpdateId,orsVersionUpdateLog.getId()));
        }

        // 处理图片
        if (CollUtil.isNotEmpty(images)) {
            for (MultipartFile image : images) {
                OrsVersionUpdateLogImg versionUpdateImage = new OrsVersionUpdateLogImg();
                versionUpdateImage.setImageData(image.getBytes());
                versionUpdateImage.setImageDesc(image.getOriginalFilename());
                versionUpdateImage.setVersionUpdateId(versionUpdateLog.getId());
                orsVersionUpdateLogImgMapper.insert(versionUpdateImage);
            }
        }

        // 处理双率记录
        // 根据versionUpdateDate日期去调用接口
        handleDeviceRatio(versionUpdateDate,versionUpdateLog.getId());

        return versionUpdateLog;
    }



    @Async
    public void handleDeviceRatio(Date versionUpdateDate, Integer versionUpdateId) {
        // 计算前一天的时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(versionUpdateDate);
        calendar.add(Calendar.DATE, -1);
        Date previousDate = calendar.getTime();

        // 格式化日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = dateFormat.format(previousDate);
        String endDate = dateFormat.format(versionUpdateDate);

        // 调用deviceRatio方法
        // 使用多线程并行处理不同的doubleRateSign
        List<String> doubleRateSigns = Arrays.asList("1", "2", "3");

        // 创建异步任务列表
        List<CompletableFuture<Void>> futures = doubleRateSigns.stream()
            .map(doubleRateSign -> processDeviceRatioAsync(startDate, endDate, doubleRateSign, versionUpdateId))
            .collect(java.util.stream.Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );

        try {
            // 等待所有异步任务完成
            allFutures.get();
        } catch (Exception e) {
            throw new RuntimeException("处理设备比率数据时发生异常", e);
        }
    }

    /**
     * 异步处理单个doubleRateSign的设备比率数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param doubleRateSign 双率标志
     * @param versionUpdateId 版本更新ID
     * @return CompletableFuture<Void>
     */
    private CompletableFuture<Void> processDeviceRatioAsync(String startDate, String endDate,
                                                           String doubleRateSign, Integer versionUpdateId) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 创建查询对象
                DeviceQuelityCountQuery deviceQuelityCountQuery = new DeviceQuelityCountQuery();
                deviceQuelityCountQuery.setStartTime(startDate);
                deviceQuelityCountQuery.setEndTime(startDate);
                deviceQuelityCountQuery.setDoubleRateSign(doubleRateSign);

                // 查询before和after数据
                DeviceRatioResp deviceRatioBefore = orsBaseDeviceInfoMapper.deviceRatioChangeVersion(deviceQuelityCountQuery);

                deviceQuelityCountQuery.setStartTime(endDate);
                deviceQuelityCountQuery.setEndTime(endDate);
                DeviceRatioResp deviceRatioAfter = orsBaseDeviceInfoMapper.deviceRatioChangeVersion(deviceQuelityCountQuery);

                // 创建OrsVersionUpdateLogRate对象并设置字段
                OrsVersionUpdateLogRate rate = new OrsVersionUpdateLogRate();
                rate.setVersionUpdateId(versionUpdateId);
                rate.setDoubleRateSign(doubleRateSign);

                if (ObjectUtils.isNotEmpty(deviceRatioBefore) || ObjectUtils.isNotEmpty(deviceRatioAfter)) {
                    if (ObjectUtils.isNotEmpty(deviceRatioBefore)) {
                        rate.setIntegrityRateBefore(deviceRatioBefore.getTotalIntegrityRate());
                        rate.setAccuracyRateBefore(deviceRatioBefore.getTotalAccuracy());
                    } else {
                        rate.setIntegrityRateBefore("0");
                        rate.setAccuracyRateBefore("0");
                    }

                    if (ObjectUtils.isNotEmpty(deviceRatioAfter)) {
                        rate.setIntegrityRateAfter(deviceRatioAfter.getTotalIntegrityRate());
                        rate.setAccuracyRateAfter(deviceRatioAfter.getTotalAccuracy());
                    } else {
                        rate.setIntegrityRateAfter("0");
                        rate.setAccuracyRateAfter("0");
                    }

                    // 保存数据
                    orsVersionUpdateLogRateMapper.insert(rate);
                }
            } catch (Exception e) {
                throw new RuntimeException("处理doubleRateSign=" + doubleRateSign + "的设备比率数据时发生异常", e);
            }
        }, asyncExecutor);
    }


    // 获取版本更新记录及其关联的图片列表和双率记录列表
    public OrsVersionUpdateLog getVersionUpdateLogWithDetails(Integer versionUpdateId) {
        OrsVersionUpdateLog versionUpdateLog = this.baseMapper.selectById(versionUpdateId);
        if (versionUpdateLog != null) {
            List<OrsVersionUpdateLogImg> logImgs = orsVersionUpdateLogImgMapper.selectList(new QueryWrapper<OrsVersionUpdateLogImg>().lambda().eq(OrsVersionUpdateLogImg::getVersionUpdateId,versionUpdateId));
            versionUpdateLog.setLogImgs(logImgs);
        }
        return versionUpdateLog;
    }


    public List<OrsVersionUpdateLogRate> getRateData(Integer versionUpdateId, String doubleRateSign) {
        return orsVersionUpdateLogRateMapper.selectList(new QueryWrapper<OrsVersionUpdateLogRate>()
                .lambda()
                .eq(OrsVersionUpdateLogRate::getVersionUpdateId,versionUpdateId)
                .eq(OrsVersionUpdateLogRate::getDoubleRateSign,doubleRateSign));
    }



    public Page<OrsVersionUpdateLog> selectVersionUpdateLog(Page<OrsVersionUpdateLog> page, @Param("query")OrsVersionUpdateLog versionUpdateLog){
        QueryWrapper<OrsVersionUpdateLog> queryWrapper = new QueryWrapper<>();
        if (versionUpdateLog.getVersionName() != null) {
            queryWrapper.lambda().like(OrsVersionUpdateLog::getVersionName, versionUpdateLog.getVersionName());
        }
        if (versionUpdateLog.getVersionUpdateDate() != null) {
            queryWrapper.lambda().eq(OrsVersionUpdateLog::getVersionUpdateDate, versionUpdateLog.getVersionUpdateDate());
        }
        // 按创建时间倒序排序
        queryWrapper.lambda().orderByDesc(OrsVersionUpdateLog::getUpdateTime);

        Page<OrsVersionUpdateLog> orsVersionUpdateLogPage = this.baseMapper.selectPage(page, queryWrapper);
        orsVersionUpdateLogPage.getRecords().forEach(item->{
            List<OrsVersionUpdateLogImg> logImgs = orsVersionUpdateLogImgMapper.selectList(new QueryWrapper<OrsVersionUpdateLogImg>().lambda().eq(OrsVersionUpdateLogImg::getVersionUpdateId,item.getId()));
            item.setLogImgs(logImgs);
        });


        // 其他条件可以继续添加
        return orsVersionUpdateLogPage;
    }
}
