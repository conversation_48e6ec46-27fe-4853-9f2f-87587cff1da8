package com.rc.admin.ors.quality.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsCoreParamStatLatestMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsCoreParamStatLatest;
import com.rc.admin.ors.quality.excel.DeviceLastIotExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerNotActiveExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerOfflineExcel;
import com.rc.admin.ors.quality.model.DeviceLedgerReq;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;
import com.rc.admin.ors.quality.service.OrsDeviceInfoRelationService;
import com.rc.admin.ors.quality.service.OrsDeviceLedgerService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import com.rc.admin.ors.quality.utils.BusinessConst;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Slf4j
@ResponseResult
@RestController
@RequestMapping("/ors/ledger")
@Api(tags = "设备台账相关接口")
public class OrsDeviceLedgerController {

    @Autowired
    private OrsSyncDeviceService orsSyncDeviceService;

    @Autowired
    private OrsDeviceInfoRelationService orsDeviceInfoRelationService;

    @Autowired
    private OrsDeviceLedgerService orsDeviceLedgerService;

    @Resource
    private OrsCoreParamStatLatestMapper orsCoreParamStatLatestMapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @ApiOperation("同步物模型")
    @GetMapping("/model/sync")
    public void syncModelData() {
        orsSyncDeviceService.syncData();
    }


    @ApiOperation("同步测试")
    @GetMapping("/model/test")
    public void syncModelDataTest() {
        String s = JSON.toJSONString(orsSyncDeviceService.resultMap());
    }


    @ApiOperation("同步ncot设备")
    @GetMapping("/nc-ot/device/sync")
    public void syncNcDeviceData() {
        try {
            orsDeviceInfoRelationService.syncHistoryDevice();
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation("同步指定物模型的属性")
    @GetMapping("/model/property/sync")
    public void syncModelProperty(String modelId) {
        orsSyncDeviceService.syncProperty(null, modelId);
    }

    @ApiOperation("刷新核心工况")
    @GetMapping("/updateRealTime")
    public void updateRealTime(String modelIds, String paramCodes) {
        // 指定模型与异常编码刷新核心工况
        orsSyncDeviceService.updateRealTime(modelIds, paramCodes);
    }

    @ApiOperation("同步指定物模型下的所有设备信息")
    @GetMapping("/device/sync")
    public void syncDeviceData(@RequestParam String modelId) {
        orsSyncDeviceService.syncDevice(null, modelId);
    }

    @ApiOperation("设备台账-列表")
    @GetMapping("/page")
    public Page<DeviceLedgerResp> page(Page<DeviceLedgerResp> page, DeviceLedgerReq req) {
        return orsDeviceLedgerService.page(page, req);
    }

    @ApiOperation("长期离线设备-列表")
    @GetMapping("/offline/page")
    public Page<DeviceLedgerResp> offlinePage(Page<DeviceLedgerResp> page, DeviceLedgerReq req) {
        req.setQueryModel("offline_list");
        return orsDeviceLedgerService.page(page, req);
    }

    @ApiOperation("长期离线设备-总数")
    @GetMapping("/offline/count")
    public Long offlineCount() {
        return orsDeviceLedgerService.deviceOfflineCount();
    }

    @ApiOperation("长期未激活设备-列表")
    @GetMapping("/notActivat/page")
    public Page<DeviceLedgerResp> notActivatPage(Page<DeviceLedgerResp> page, DeviceLedgerReq req) {
        req.setQueryModel("not_activat_list");
        return orsDeviceLedgerService.page(page, req);
    }

    @ApiOperation("长期未激活设备-总数")
    @GetMapping("/notActivat/count")
    public Long notActivatCount() {
        DeviceLedgerReq req = new DeviceLedgerReq();
        req.setQueryModel("not_activat_list");
        Page<DeviceLedgerResp> page = new Page<>();
        page.setCurrent(1);
        page.setSize(1);
        return orsDeviceLedgerService.page(page, req).getTotal();
    }

    @ApiOperation("设备台账-详情")
    @GetMapping("/{id}")
    public DeviceLedgerResp findOne(@PathVariable("id") String id) {
        return orsDeviceLedgerService.detail(id);
    }

//     @ApiOperation("设备台账导出")
//     @GetMapping("/export")
//     public void export(DeviceLedgerReq req, HttpServletResponse response){
//         AtomicInteger ai = new AtomicInteger(1);
//
//         ExportParams exportParams = new ExportParams("设备台账", "设备台账明细");
//         exportParams.setCreateHeadRows(true);
// //        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, DeviceLedgerExcel.class, data);
//         Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, DeviceLedgerExcel.class, (o, i) -> {
//             Page<DeviceLedgerResp> page = orsDeviceLedgerService.page(new Page<>(i, 5000), req);
//
//             List<Object> data = new ArrayList<>();
//             page.getRecords().forEach(x->{
//                 DeviceLedgerExcel excel = new DeviceLedgerExcel();
//                 BeanUtils.copyProperties(x, excel);
//                 excel.setDataCenter(BusinessConst.dataCenterMap.get(x.getDataCenterId()));
//                 excel.setDeviceStatus(BusinessConst.deviceStatusMap.get(x.getDeviceStatus()));
//                 //离线时长
//                 Date offlineTime = x.getOfflineTime();
//                 excel.setOfflineTime(orsDeviceLedgerService.getOfflineTimeString(offlineTime));
//
//                 excel.setSort(ai.getAndIncrement());
//                 excel.setCtime(sdf.format(x.getCreated()));
//                 String s = x.getFactoryDuration();
//                 switch (s) {
//                     case "1":
//                         excel.setFactoryDuration("1年内");
//                         break;
//                     case "2":
//                         excel.setFactoryDuration("2-3年内");
//                         break;
//                     case "3":
//                         excel.setFactoryDuration("4-5年内");
//                         break;
//                     case "4":
//                         excel.setFactoryDuration("大于6年");
//                         break;
//                     default:
//                         excel.setFactoryDuration("其他");
//                         break;
//                 }
//                 excel.setProblemFollow("");
//                 excel.setCrmRegister(String.valueOf(x.getCrmRegister()));
//                 if (StringUtils.isBlank(excel.getCrmRegister()) || "null".equals(excel.getCrmRegister())) {
//                     excel.setCrmRegister("0");
//                 }
//                 if (StringUtils.isBlank(excel.getFwVersion()) || "null".equals(excel.getFwVersion())) {
//                     excel.setFwVersion("");
//                 }
//
//                 data.add(excel);
//             });
//             return data;
//         }, 0);
//         if (workbook != null) {
//             EasyPoiUtils.downLoadExcel("设备台账.xlsx", response, workbook);
//         }
//     }

    @ApiOperation("设备台账导出")
    @GetMapping("/export")
    public void export(DeviceLedgerReq req, HttpServletResponse response){
        AtomicInteger ai = new AtomicInteger(1);
        log.info("设备台账导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        Page<DeviceLedgerResp> countPage = orsDeviceLedgerService.page(new Page<>(1, 1), req);
        int totalCount = (int) countPage.getTotal();
        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("设备台账", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 300000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 10 的线程池
            //ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备台账明细" + (sheetIndex + 1))
                            .head(DeviceLedgerExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceLedgerResp> page = orsDeviceLedgerService.page(new Page<>(currentPage, writeDataRows), req);
                            List<DeviceLedgerExcel> data = new ArrayList<>();
                            page.getRecords().forEach(x->{
                                DeviceLedgerExcel excel = orsDeviceLedgerService.buildDeviceLedgerExcelVO(x, ai);
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     log.info("关闭线程池");
                //     executorService.shutdown();
                // }
            }
            log.info("设备台账导出结束");
            log.info("设备台账导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出设备台账失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }
    }

    @ApiOperation("长期离线设备导出")
    @GetMapping("/offline/export")
    public void offlineExport(DeviceLedgerReq req, HttpServletResponse response){
        req.setQueryModel("offline_list");
        AtomicInteger ai = new AtomicInteger(1);
        log.info("长期离线设备导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        Page<DeviceLedgerResp> countPage = orsDeviceLedgerService.page(new Page<>(1, 1), req);
        int totalCount = (int) countPage.getTotal();
        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("长期离线设备台账", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 10 的线程池
            //ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备台账明细" + (sheetIndex + 1))
                            .head(DeviceLedgerOfflineExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceLedgerResp> page = orsDeviceLedgerService.page(new Page<>(currentPage, writeDataRows), req);
                            List<DeviceLedgerOfflineExcel> data = new ArrayList<>();
                            page.getRecords().forEach(x->{
                                DeviceLedgerOfflineExcel excel = orsDeviceLedgerService.buildDeviceLedgerOfflineExcelVO(x, ai);
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     log.info("关闭线程池");
                //     executorService.shutdown();
                // }
            }
            log.info("长期离线设备导出结束");
            log.info("长期离线设备导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出长期离线设备失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }
    }

    @ApiOperation("长期未激活设备导出")
    @GetMapping("/notActivat/export")
    public void notActivatExport(DeviceLedgerReq req, HttpServletResponse response){
        req.setQueryModel("not_activat_list");
        AtomicInteger ai = new AtomicInteger(1);
        log.info("长期未激活设备导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        Page<DeviceLedgerResp> countPage = orsDeviceLedgerService.page(new Page<>(1, 1), req);
        int totalCount = (int) countPage.getTotal();
        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("长期未激活设备台账", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 10 的线程池
            //ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备台账明细" + (sheetIndex + 1))
                            .head(DeviceLedgerNotActiveExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceLedgerResp> page = orsDeviceLedgerService.page(new Page<>(currentPage, writeDataRows), req);
                            List<DeviceLedgerNotActiveExcel> data = new ArrayList<>();
                            page.getRecords().forEach(x->{
                                DeviceLedgerNotActiveExcel excel = orsDeviceLedgerService.buildDeviceLedgerNotActiveExcelVO(x, ai);
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     log.info("关闭线程池");
                //     executorService.shutdown();
                // }
            }
            log.info("长期未激活设备导出结束");
            log.info("长期未激活设备导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出长期未激活设备失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }
    }

    @Deprecated
    @ApiOperation("最后一次工况导出")
    @PostMapping("/iot/latest/export")
    public void queryLastIotData(@RequestPart("file") MultipartFile file, HttpServletResponse response){
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        try {
            List<DeviceLastIotExcel> his = ExcelImportUtil.importExcel(file.getInputStream(), DeviceLastIotExcel.class, params);
            if (his.isEmpty()) {
                throw new IllegalArgumentException("请上传文件类容");
            }
            List<String> collect = his.stream().map(DeviceLastIotExcel::getDeviceCode).collect(Collectors.toList());
            List<OrsBaseDeviceInfo> infos = orsBaseDeviceInfoMapper.selectList(
                    new QueryWrapper<OrsBaseDeviceInfo>()
                            .lambda()
                            .in(OrsBaseDeviceInfo::getDeviceCode, collect)
            );
            List<String> assetIds = infos.stream().map(OrsBaseDeviceInfo::getAssetId).collect(Collectors.toList());
            List<OrsCoreParamStatLatest> latests = orsCoreParamStatLatestMapper.selectList(
                    new QueryWrapper<OrsCoreParamStatLatest>()
                            .lambda()
                            .in(OrsCoreParamStatLatest::getDeviceName, assetIds)
            );
            Map<String, List<OrsCoreParamStatLatest>> map = latests.stream().collect(Collectors.groupingBy(OrsCoreParamStatLatest::getDeviceName));
            List<DeviceLastIotExcel> data = new ArrayList<>();
            DeviceLastIotExcel excel;
            for (DeviceLastIotExcel h : his) {
                excel = new DeviceLastIotExcel();
                excel.setDeviceCode(h.getDeviceCode());
                excel.setProductGroup(h.getProductGroup());
                excel.setModelType(h.getModelType());

                OrsBaseDeviceInfo info = infos.stream().filter(x -> x.getDeviceCode().equals(h.getDeviceCode())).findFirst().orElse(null);
                if (null != info) {
                    List<OrsCoreParamStatLatest> last = map.get(info.getAssetId());
                    if (null != last && !last.isEmpty()) {
                        for (OrsCoreParamStatLatest statLatest : last) {
                            if (statLatest.getParamCode() == 8504) {
                                excel.setGpsLongitude8504("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8505) {
                                excel.setGpsLatitude8505("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8503) {
                                excel.setDeviceStatus8503("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8105) {
                                excel.setEngineWorktime8105("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8102) {
                                excel.setWorkingTime8102("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8201) {
                                excel.setTotalFuelConsumption8201("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                            if (statLatest.getParamCode() == 8403) {
                                excel.setDrivingMileage8403("值：" + statLatest.getParamValue() + " 时间：" + sdf.format(statLatest.getParamValueLatestTime()));
                            }
                        }
                    }
                }
                data.add(excel);
            }
            ExportParams exportParams = new ExportParams("设备最后一次工况上报情况", "最后一次工况");
            exportParams.setCreateHeadRows(true);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, DeviceLastIotExcel.class, data);
            if (workbook != null) {
                EasyPoiUtils.downLoadExcel("设备最后一次工况上报情况.xlsx", response, workbook);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
