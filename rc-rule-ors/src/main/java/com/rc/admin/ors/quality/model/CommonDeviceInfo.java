package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/3 14:10
 * @describe
 */
@Getter
@Setter
public class CommonDeviceInfo {

    @ApiModelProperty(name = "assetId", value = "物标识")
    private String assetId;

    @ApiModelProperty(name = "thingId", value = "物实例")
    private String thingId;

    @ApiModelProperty(name = "modelId", value = "模型ID")
    private String modelId;

    @ApiModelProperty(name = "modelName", value = "模型名称")
    private String modelName;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(name = "deviceName", value = "实例名称")
    private String deviceName;

    @ApiModelProperty(name = "division", value = "事业部")
    private String division;

    @ApiModelProperty(name = "productGroup", value = "产品组")
    private String productGroup;

    @ApiModelProperty(name = "divisionCode", value = "事业部编号")
    private String divisionCode;

    @ApiModelProperty(name = "productGroupCode", value = "产品组编号")
    private String productGroupCode;

    @ApiModelProperty(name = "statDate", value = "统计日期")
    private Date statDate;

    @ApiModelProperty(name = "abnormalCode", value = "异常项code")
    private Integer abnormalCode;
}
