package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.SanydsCoreParamDef;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigImportExcel;
import com.rc.admin.ors.quality.model.DeviceRule;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-23 15:41:57
 */
public interface OrsDeviceCheckConfigService extends IService<OrsDeviceCheckConfig> {

    /**
     * 查询剔除设备的关联信息
     * @param page
     * @param wrapper
     * @return
     */
    Page<DeviceRule> findRuleByDeviceCode(Page<ModleIndicatorAndExclude> page, Wrapper wrapper);




    String getParamNameModel(String modelId, String paramCode, Map<String, SanydsCoreParamDef> map);


    Map<String, SanydsCoreParamDef>  getSanydsCoreParamDef();

    /**
     * 导入批量删除
     * @param list
     */
    void importDelete(List<OrsDeviceCheckConfigImportExcel> list);


    void importDeleteInsertHistroy(List<OrsDeviceCheckConfigImportExcel> list,String createUser);




    void insertHistoryById(@Param("id")Long id,String createUser);
}

