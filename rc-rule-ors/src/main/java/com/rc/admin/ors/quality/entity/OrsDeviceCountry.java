package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备国家关系表")
@TableName("ors_device_country")
public class OrsDeviceCountry extends Model<OrsDeviceCountry> {

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(name = "prodId", value = "设备code")
    @TableField(value = "prod_id")
    private String prodId;

    @ApiModelProperty(name = "cntyCode", value = "国家code")
    @TableField(value = "cnty_code")
    private String cntyCode;

    @ApiModelProperty(name = "cntyDesc", value = "国家描述")
    @TableField(value = "cnty_desc")
    private String cntyDesc;


    @ApiModelProperty(name = "bicZioSbzt", value = "设备状态")
    @TableField(value = "bic_zio_sbzt")
    private String bicZioSbzt;

    @ApiModelProperty(name = "bicZioJjrq", value = "交机日期")
    @TableField(value = "bic_zio_jjrq")
    private String bicZioJjrq;


    @ApiModelProperty(name = "createDate", value = "创建时间")
    @TableField(value = "create_date")
    private Date createDate;

    @ApiModelProperty(name = "updateDate", value = "修改时间")
    @TableField(value = "update_date")
    private Date updateDate;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

