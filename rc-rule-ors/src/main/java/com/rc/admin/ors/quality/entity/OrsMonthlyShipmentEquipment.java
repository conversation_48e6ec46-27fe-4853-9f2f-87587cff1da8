package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * (OrsMonthlyShipmentEquipment)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-30 15:47:15
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("")
@TableName("ors_monthly_shipment_equipment")
public class OrsMonthlyShipmentEquipment extends Model<OrsMonthlyShipmentEquipment> {


    @NotNull(message = "${column.comment}不能为空")
    @TableField(value = "device_name")
    private String deviceName;

    @TableField(value = "import_time")
    private String importTime;

    @ApiModelProperty(name = "region", value = "大区")
    @TableField(value = "region")
    private String region;

    @ApiModelProperty(name = "division", value = "事业部")
    @TableField(value = "division")
    private String division;

    @ApiModelProperty(name = "productGroup", value = "产品组")
    @TableField(value = "product_group")
    private String productGroup;

    @ApiModelProperty(name = "regionCode", value = "大区编码")
    @TableField(value = "region_code")
    private String regionCode;

    @ApiModelProperty(name = "divisionCode", value = "事业部编码")
    @TableField(value = "division_code")
    private String divisionCode;

    @ApiModelProperty(name = "productGroupCode", value = "产品组编码")
    @TableField(value = "product_group_code")
    private String productGroupCode;

    @TableField(value = "asset_id")
    private String assetId;

    @TableField(value = "model_id")
    private String modelId;

}

