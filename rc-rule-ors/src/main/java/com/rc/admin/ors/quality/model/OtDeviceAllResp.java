package com.rc.admin.ors.quality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OtDeviceAllResp implements Serializable {


    @ExcelIgnore
    @ApiModelProperty(value = "事业部编码")
    private String syb;

    @ExcelProperty(value = "事业部")
    @ApiModelProperty(value = "事业部描述")
    private String buInnerDesc;


    @ExcelIgnore
    @ApiModelProperty(value = "产品组编码")
    private String d365Spart;


    @ExcelProperty(value = "产品组")
    @ApiModelProperty(value = "产品组描述")
    private String d365SpartDesc;


    @ExcelProperty(value = "设备编码")
    @ApiModelProperty(value = "设备编码")
    private String deviceSer;

    /**
     * 设备状态
     */
    @ExcelIgnore
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;

    /**
     * 设备状态描述
     */
    @ExcelProperty(value = "设备状态")
    @ApiModelProperty(value = "设备状态描述")
    private String deviceStatusDesc;


    @ExcelIgnore
    @ApiModelProperty(value = "国家编码")
    private String cntyCode;


    @ExcelProperty(value = "国家")
    @ApiModelProperty(value = "国家描述")
    private String cntyDesc;

    @ExcelProperty(value = "物联盒厂商")
    @ApiModelProperty(value = "物联盒子厂商")
    private String manufacturer;


    @ExcelProperty(value = "物联盒ID")
    @ApiModelProperty(value = "物联盒ID")
    private String tboxId;


    @ExcelProperty(value = "租户")
    @ApiModelProperty(value = "租户名称")
    private String divisionName;

    @ExcelIgnore
    @ApiModelProperty(value = "租户code")
    private String divisionCode;

    @ExcelProperty(value = "产品组(模型)")
    @ApiModelProperty(value = "产品组(模型)名称")
    private String spartDesc;

    @ExcelIgnore
    @ApiModelProperty(value = "产品组(模型)Code")
    private String spart;


    @ExcelIgnore
    @ApiModelProperty(value = "盒子状态")
    private String tboxStatus;

    @ExcelIgnore
    @ApiModelProperty(value = "盒子状态描述")
    private String tboxStatusDesc;

    @ExcelIgnore
    @ApiModelProperty(value = "转发站点（多个站点，逗号分隔）")
    private String transmitStationCode;

    @ExcelIgnore
    @ApiModelProperty(value = "转发站点描述（多个站点，逗号分隔）")
    private String transmitStationDesc;

    /**
     * 模型id
     */
    @ExcelProperty(value = "模型ID")
    @ApiModelProperty(value = "模型id")
    private String modelId;

    @ExcelProperty(value = "物标识")
    @ApiModelProperty(value = "物标识")
    private String assetId;

    /**
     * thing_id/UUID
     */
    @ExcelProperty(value = "UUID")
    @ApiModelProperty(value = "thing_id/UUID")
    private String thingId;



    /**
     * 新C站点id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "新C站点id")
    private String ngcStationId;

    /**
     * 新C站点名称
     */
    @ExcelIgnore
    @ApiModelProperty(value = "新C站点名称")
    private String ngcStationName;

    /**
     * 新C租户id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "新C租户id")
    private String tenantId;

    /**
     * 新C租户名
     */
    @ExcelIgnore
    @ApiModelProperty(value = "新C租户名")
    private String tenantName;

    /**
     * d365台账是否存在
     */
    @ExcelIgnore
    @ApiModelProperty(value = "D365台账")
    private Boolean d365Exist;

    @ExcelProperty(value = "D365台账")
    @ApiModelProperty(value = "d365台账是否存在(str)")
    private String d365ExistStr;

    /**
     * 新C是否存在
     */
    @ExcelIgnore
    @ApiModelProperty(value = "新C台账")
    private Boolean ngcExist;


    @ExcelProperty(value = "新C台账")
    @ApiModelProperty(value = "新C是否存在(str)")
    private String ngcExistStr;

    /**
     * machinLink是否存在
     */
    @ExcelIgnore
    @ApiModelProperty(value = "ML2.0台账")
    private Boolean mlExist;


    @ExcelProperty(value = "ML2.0台账")
    @ApiModelProperty(value = "machinLink是否存在(str)")
    private String mlExistStr;

    /**
     * evi是否存在
     */
    @ExcelIgnore
    @ApiModelProperty(value = "EVI台账")
    private Boolean eviExist;


    @ExcelProperty(value = "EVI台账")
    @ApiModelProperty(value = "evi是否存在(str)")
    private String eviExistStr;


    /**
     * 是否多物联盒设备
     */
    @ExcelIgnore
    @ApiModelProperty(value = "多物联盒设备")
    private Boolean multiTboxExist;


    @ExcelProperty(value = "多物联盒设备")
    @ApiModelProperty(value = "是否多物联盒设备(str)")
    private String multiTboxExistStr;

    // 自定义getter方法
    public String getD365ExistStr() {
        return this.d365Exist != null ? (this.d365Exist ? "是" : "否") : "否";
    }

    public String getNgcExistStr() {
        return this.ngcExist != null ? (this.ngcExist ? "是" : "否") : "否";
    }

    public String getMlExistStr() {
        return this.mlExist != null ? (this.mlExist ? "是" : "否") : "否";
    }

    public String getEviExistStr() {
        return this.eviExist != null ? (this.eviExist ? "是" : "否") : "否";
    }

    public String getMultiTboxExistStr() {
        return this.multiTboxExist != null ? (this.multiTboxExist ? "是" : "否") : "否";
    }


}
