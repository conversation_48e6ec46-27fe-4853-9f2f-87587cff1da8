package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/28 13:24
 * @describe
 */
@Getter
@Setter
public class Device9007ExceptInfoDetailExcel {

    @Excel(name = "序号", width = 10)
    private int sort;

    @Excel(name = "事业部", width = 15)
    private String division;

    @Excel(name = "大区", width = 25)
    private String region;

    @Excel(name = "国家", width = 25)
    private String country;

    @Excel(name = "产品组", width = 15)
    private String productGroup;

    @Excel(name = "设备编号", width = 25)
    private String deviceCode;

    @Excel(name = "物实例", width = 25)
    private String assetId;

    @Excel(name = "漂移发生时间", width = 25)
    private String excetionTime;

    @Excel(name = "漂移前后经纬度", width = 30)
    private String location;

    @Excel(name = "是否经纬度异常", width = 15)
    private String isLatAndLngExce;

    @Excel(name = "漂移距离（km）", width = 25)
    private String distance;
}
