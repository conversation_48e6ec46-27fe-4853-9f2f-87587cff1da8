package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.ModelInfoSyncMapper;
import com.rc.admin.ors.quality.entity.ModelInfoSync;
import com.rc.admin.ors.quality.service.ModelInfoSyncService;
import org.springframework.stereotype.Service;

/**
 * 物模型同步表(ModelInfoSync)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:18:05
 */
@Service("modelInfoSyncService")
public class ModelInfoSyncServiceImpl extends ServiceImpl<ModelInfoSyncMapper, ModelInfoSync> implements ModelInfoSyncService {

}

