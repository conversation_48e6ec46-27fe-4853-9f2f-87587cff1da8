package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/08/004
 */
@Setter
@Getter
@ApiModel("设备MQTT连接信息")
public class DeviceMqttConnectInfo {

    @ApiModelProperty(name = "thingId", value = "thingId")
    private String thingId;

    @ApiModelProperty(name = "username", value = "设备连接MQTT Broker时的用户名")
    private String username;

    @ApiModelProperty(name = "authToken", value = "设备连接MQTT Broker时的密码")
    private String authToken;

}
