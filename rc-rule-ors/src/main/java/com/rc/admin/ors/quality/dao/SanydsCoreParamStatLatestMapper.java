package com.rc.admin.ors.quality.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.SanydsCoreParamStatLatest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("sany_data_service")
public interface SanydsCoreParamStatLatestMapper extends BaseMapper<SanydsCoreParamStatLatest> {

  int insertSanydsCoreParamStatLatestDay(@Param("modelIdList") List<String> modelIdList,
                                         @Param("paramCodeList") List<String> paramCodeList);
}