package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.OrsDeviceInfo;
import com.rc.admin.ors.quality.model.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
public interface OrsDeviceInfoMapper extends BaseMapper<OrsDeviceInfo> {

    /**
     * 查询台账分页
     * 
     * @param page
     *            分页对象
     * @param req
     *            查询条件
     * @return 分页的数据
     */
    Page<DeviceLedgerResp> ledgerPage(Page<DeviceLedgerResp> page, @Param("req") DeviceLedgerReq req);


    int deviceOfflineCount();

    /**
     * 通过id获取详情数据
     * @param id    主键
     * @return  设备台账信息
     */
    DeviceLedgerResp findLedger(@Param("id") String id, @Param("deviceNo") String deviceNo);

    /**
     * 国家基础数据列表
     * @return 国家list
     */
    List<OrsBasicData> countryList();

    /**
     * 事业部基础数据列表
     * @return  事业部list
     */
    List<OrsBasicData> divisionList();

    /**
     * 大区基础数据列表
     * @return  大区list
     */
    List<OrsBasicData> regionList();

    /**
     * 产品组基础数据列表
     * @return  产品组list
     */
    List<OrsBasicData> productGroupList();

    /**
     * 国家基础数据列表
     * @return 国家list
     */
    List<OrsBasicData> newCountryList();
    List<OrsBasicData> newCountryDeviceList();

    /**
     * 国区基础数据列表
     * @return 国家list
     */
    List<OrsBasicData> newCountryRegionList();

    /**
     * 事业部基础数据列表
     * @return  事业部list
     */
    List<OrsBasicData> newDivisionList();


    /**
     * 全量设备的事业部查询
     * @return
     */
    List<OrsBasicData> newDivisionDeviceList();


    List<OrsBasicData> getUserList();


    /**
     * 全量设备的产品组查询
     * @return
     */
    List<OrsBasicData> newProductGroupDeviceList(@Param("syb")String syb,@Param("sybCheck")String sybCheck);
    /**
     * 新大区基础数据列表
     * @return  大区list
     */
    List<OrsBasicData> newRegionList();

    /**
     * 产品组基础数据列表
     * @return  产品组list
     */
    List<OrsBasicData> newProductGroupList(@Param("divisionCode")String divisionCode);
    /**
     * 查询所有的物实例
     * @return  物实例列表
     */
    Page<String> assetIdList(Page<?> page, @Param("assetId") String assetId);

    /**
     * 查询所有的实例名称
     * @return  实例名称列表
     */
    Page<String> nameList(Page<?> page, @Param("thingName") String thingName);

    /**
     * 查询所有的物模型名称
     * @return  物模型名称列表
     */
    List<String> modelNameList(@Param("divisionCode")String divisionCode,@Param("productGroupCode")String productGroupCode,@Param("key")String key);


    List<String> modelNameListNew(@Param("divisionCode")String divisionCode,@Param("productGroupCode")String productGroupCode);

    /**
     * 查询所有的设备编号
     * @param deviceNo  设备编号
     * @param size  查询数量
     * @return  设备编号列表
     */
    List<OrsDeviceListResp> deviceNoList(@Param("deviceNo") String deviceNo, @Param("size") Integer size);

    /**
     * 同步指定数据
     */
    void transferData();

    void createPartitionTable(@Param("tableName")String tableName,@Param("startYear")String startYear,@Param("endYear")String endYear);

    void mergeDeviceInfo();
    void upNoDivision();

    List<DeviceCountModel> countDeviceByCondition(DataQualityReportReq req);
    List<DeviceCountModel> countDeviceByConditionChange(DataQualityReportReq req);
    List<DeviceCountModel> countDeviceByConditionDivision(DataQualityReportReq req);
    List<DeviceCountModel> countDeviceByParamCode(DataQualityReportReq req);
    List<DeviceCountModel> countDeviceByParamCodeChange(DataQualityReportReq req);


    List<DeviceCountModelNew> countDeviceByConditionNew(DataQualityReportReq req);
    List<DeviceCountModelNew> countDeviceByParamCodeNew(DataQualityReportReq req);


    List<PropertyModel> getPropertyModel(DataQualityReportReq req);
    List<DeviceModel> getDeviceModel(DataQualityReportReq req);

    List<DeviceModel> getDeviceModelByDay(DataQualityReportReq req);


    void insertDeviceRateDay(@Param("bizDate") Date bizDate);

    /**
     * 查询所有的根云物实例
     * @return  物实例列表
     */
    Page<String> rcAssetIdList(Page<?> page, @Param("rcAssetId") String rcAssetId);


    /**
     * 通过条件看需要查询发送哪些设备
     * @param checkDate
     * @return
     */
    List<OrsDeviceInfo> getDeviceInfoByCheck(@Param("checkDate") String checkDate,@Param("assetId") String assetId,@Param("modelId") String modelId);


    /**
     * 获取华兴模型id
     * @return
     */
    String getHXModelStr();
}
