package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OrsDeviceDataAbnormalDetailMapper;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.service.OrsDeviceDataAbnormalDetailService;
import org.springframework.stereotype.Service;

/**
 * 设备数据异常明细(OrsDeviceDataAbnormalDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-27 14:51:55
 */
@Service("orsDeviceDataAbnormalDetailService")
public class OrsDeviceDataAbnormalDetailServiceImpl extends ServiceImpl<OrsDeviceDataAbnormalDetailMapper, OrsDeviceDataAbnormalDetail> implements OrsDeviceDataAbnormalDetailService {

}

