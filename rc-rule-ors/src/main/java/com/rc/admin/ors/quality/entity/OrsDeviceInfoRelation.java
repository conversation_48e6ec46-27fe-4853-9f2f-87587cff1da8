package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备基础信息(OrsDeviceInfoRelation)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-01 09:14:30
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_ml_device_info_relation", description = "设备基础信息")
public class OrsDeviceInfoRelation extends Model<OrsDeviceInfoRelation> {

    @TableId(type = IdType.INPUT)
    private String deviceId;


    @ApiModelProperty(name = "deviceName", value = "设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    @TableField(value = "serial_num")
    private String serialNum;

    @TableField(value = "device_create_date")
    private Date deviceCreateDate;

    @TableField(value = "device_update")
    private Date deviceUpdate;

    @TableField(value = "thing_instance_id")
    private String thingInstanceId;

    @TableField(value = "thing_model_id")
    private String thingModelId;

    @TableField(value = "host_id")
    private String hostId;

    @TableField(value = "tbox_id")
    private String tboxId;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "bind_create_date")
    private Date bindCreateDate;

    @TableField(value = "bind_update")
    private Date bindUpdate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.deviceId;
    }
}

