package com.rc.admin.ors.quality.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.auth.dao.SysUserMapper;
import com.rc.admin.auth.model.SysUser;
import com.rc.admin.ors.quality.dao.BaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.DeviceQuestionMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.entity.*;
import com.rc.admin.ors.quality.excel.DeviceHisQuestionExcel;
import com.rc.admin.ors.quality.excel.DeviceQuestionExcel;
import com.rc.admin.ors.quality.excel.UnimportQuestionExcel;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.*;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import com.rc.admin.util.ShiroUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备问题跟进(DeviceQuestion)表控制层
 *
 * <AUTHOR>
 * @since 2023-11-30 16:55:30
 */
@Slf4j
@RestController
@RequestMapping("deviceQuestion")
@Api(tags = {"设备问题跟进"})
public class DeviceQuestionController {

    private final String CLOSE_STAT = "close";
    /**
     * 服务对象
     */
    @Resource
    private DeviceQuestionService deviceQuestionService;

    @Resource
    private OrsModelPropertiesConfigService orsModelPropertiesConfigService;

    @Resource
    private DictService dictService;

    @Resource
    private QuestionLogService questionLogService;

    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource
    private QuestionStepUserService questionStepUserService;

    @Resource
    private DeviceDataAbnormalStatDayService deviceDataAbnormalStatDayService;

    @Resource
    private DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;

    @Resource
    private DeviceQuestionMapper deviceQuestionMapper;

    @Resource
    private BaseDeviceInfoMapper baseDeviceInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 分页查询所有数据
     *
     * @param page           分页对象
     * @param req 查询实体
     * @return 所有数据
     */
    @ApiOperation("查询问题跟进列表")
    @GetMapping
    public Page<DeviceLedgerResp> selectAll(Page<DeviceLedgerResp> page, DeviceQuestionReq req) {
        return deviceQuestionService.questionList(
                page,
                Wrappers
                        .query()
                        .eq("odq.del_flag", 0)
                        .in(StringUtils.isNotBlank(req.getDivision()), "obdi.division_code", StringUtils.isNotBlank(req.getDivision()) ? req.getDivision().split(",") : null)
                        .in(StringUtils.isNotBlank(req.getRegion()), "obdi.region_code", StringUtils.isNotBlank(req.getRegion()) ? req.getRegion().split(",") : null)
                        .in(StringUtils.isNotBlank(req.getProductGroup()), "obdi.product_group_code", StringUtils.isNotBlank(req.getProductGroup()) ? req.getProductGroup().split(",") : null)
                        .and(StringUtils.isNotBlank(req.getDeviceCode()),
                                w->w
                                        .like("obdi.device_code", req.getDeviceCode())
                                        .or()
                                        .in(StringUtils.isNotBlank(req.getDeviceCode()), "obdi.device_code", req.getDeviceCode().split(","))
                        )
                        .in(StringUtils.isNotBlank(req.getTboxId()), "obdi.tbox_id", StringUtils.isNotBlank(req.getTboxId()) ? req.getTboxId().split(",") : null)
                        .in(StringUtils.isNotBlank(req.getCurStep()), "odq.cur_step", StringUtils.isNotBlank(req.getCurStep()) ? req.getCurStep().split(",") : null)
                        .like(StringUtils.isNotBlank(req.getUserName()),"odq.user_name", req.getUserName())
                        .ge(null != req.getStartTime(), "odq.create_time", req.getStartTime())
                        .le(null != req.getEndTime(), "odq.create_time", req.getEndTime())
                        .ge(null != req.getQStart(), "odq.q_start", req.getQStart())
                        .le(null != req.getQEnd(), "odq.q_end", req.getQEnd())
                        .eq(StringUtils.isNotBlank(req.getQueryAccess()) && "wait".equals(req.getQueryAccess()), "odq.user_account", ShiroUtil.getCurrentUser().getUsername())
                        .eq(StringUtils.isNotBlank(req.getQueryAccess()) && "handled".equals(req.getQueryAccess()), "oql.user_account", ShiroUtil.getCurrentUser().getUsername())
                        .in(StringUtils.isNotBlank(req.getName()), "obdi.device_name", StringUtils.isNotBlank(req.getName()) ? req.getName().split(",") : null)
                        .and(StringUtils.isNotBlank(req.getCheckItem()),
                                w-> {
                                    String[] split = req.getCheckItem().split(",");
                                    for (int i = 0; i < split.length; i++) {
                                        w.like("odq.check_item_code", split[i]);
                                        if (i < split.length-1) {
                                            w.or();
                                        }
                                    }
                                }
                        )
                        .and(StringUtils.isNotBlank(req.getExceItem()),
                                w-> {
                                    String[] split = req.getExceItem().split(",");
                                    for (int i = 0; i < split.length; i++) {
                                        w.like("odq.exce_item_code", split[i]);
                                        if (i < split.length-1) {
                                            w.or();
                                        }
                                    }
                                }
                        )
                        .orderByDesc("odq.create_time")
                ,
                StringUtils.isNotBlank(req.getQueryAccess()) && "handled".equals(req.getQueryAccess()) ? ShiroUtil.getCurrentUser().getUsername() : null
        );
    }

    @ApiOperation("问题跟进参数查询")
    @GetMapping("/query/filter")
    public List<String> queryFilter(DeviceQuestionReq req){
        return deviceQuestionMapper.queryFilter(req, ShiroUtil.getCurrentUser().getUsername());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("查询某一个问题跟进详情")
    @GetMapping("/{id}")
    public DeviceLedgerResp selectOne(@PathVariable Long id) {
        DeviceQuestion question = deviceQuestionService.getById(id);
        DeviceLedgerResp ledger = orsDeviceInfoMapper.findLedger(question.getAssetId(), null);
        ledger.setQuesLevel(question.getQuesLevel());
        ledger.setCheckItem(question.getCheckItem());
        ledger.setExceItem(question.getExceItem());
        return ledger;
    }

    /**
     * 新增数据
     *
     * @param questions 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增问题跟进")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping
    public Object insert(@RequestBody @Validated List<DeviceQuestion> questions, String comfirm) {
        // 先根据设备编号去重
        questions = questions.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(DeviceQuestion::getDeviceCode))), ArrayList::new)
        );

        for (DeviceQuestion question : questions) {
            List<DeviceDataAbnormalDetailDay> abnormalDetails = deviceDataAbnormalDetailDayMapper.selectList(
                    new QueryWrapper<DeviceDataAbnormalDetailDay>()
                            .lambda()
                            .select(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
                            .eq(DeviceDataAbnormalDetailDay::getDeviceName, question.getAssetId())
                            .apply("abnormal_time::DATE >= {0}::DATE", question.getQStart())
                            .apply("abnormal_time::DATE <= {0}::DATE", question.getQEnd())
                            .groupBy(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
            );
            Assert.isTrue(!abnormalDetails.isEmpty(), "设备"+question.getDeviceCode()+"在当前时间区间内没有异常产生，无需跟进");
            String abnormalItem = abnormalDetails.stream().map(DeviceDataAbnormalDetailDay::getAbnormalName).distinct().collect(Collectors.joining(","));
            String checkItem = abnormalDetails.stream().map(DeviceDataAbnormalDetailDay::getPropertyName).distinct().collect(Collectors.joining(","));
            String abnormalCodes = abnormalDetails.stream().map(x -> String.valueOf(x.getAbnormalCode())).distinct().collect(Collectors.joining(","));
            String checkItemCodes = abnormalDetails.stream().map(x -> String.valueOf(x.getParamCode())).distinct().collect(Collectors.joining(","));

            // 查询有没有已有记录
            DeviceQuestion one = deviceQuestionService.getOne(
                    new QueryWrapper<DeviceQuestion>()
                            .lambda()
                            .eq(DeviceQuestion::getDeviceCode, question.getDeviceCode())
                            .eq(DeviceQuestion::getQStart, question.getQStart())
                            .eq(DeviceQuestion::getQEnd, question.getQEnd())
                            .eq(DeviceQuestion::getCheckItem, checkItem)
                            .eq(DeviceQuestion::getExceItem, abnormalItem)
                            .ne(DeviceQuestion::getCurStep, CLOSE_STAT)
                            .orderByDesc(DeviceQuestion::getUpdateTime)
                            .last("limit 1")
            );
            Assert.isNull(one, "设备"+question.getDeviceCode()+"在当前问题发生时间内已有跟进中，无需重复加入");

            // 查询检查项
            List<OrsModelPropertiesConfig> list = orsModelPropertiesConfigService.list(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .eq(OrsModelPropertiesConfig::getModelId, question.getModelId())
            );
            Assert.isTrue(!list.isEmpty(), "该设备没有对应的检查项，无需跟进");

            // 检查是否有未关闭的跟进记录
            long count = deviceQuestionService.count(
                    new QueryWrapper<DeviceQuestion>()
                            .lambda()
                            .eq(DeviceQuestion::getDeviceCode, question.getDeviceCode())
                            .ne(DeviceQuestion::getCurStep, CLOSE_STAT)
            );
            if (count>0 && (StringUtils.isBlank(comfirm) || "N".equals(comfirm))) {
                return 601;
//                throw new IllegalStateException("已有未关闭的跟进记录");
            }

            // 检查确认是否增补
            DeviceQuestion one1 = deviceQuestionService.getOne(
                    new QueryWrapper<DeviceQuestion>()
                            .lambda()
                            .eq(DeviceQuestion::getDeviceCode, question.getDeviceCode())
                            .apply("create_time::DATE = CURRENT_DATE")
                            .orderByAsc(DeviceQuestion::getCreateTime)
                            .last("limit 1")
            );
            boolean needMerge = false;
            if (one1 != null && !CLOSE_STAT.equals(one1.getCurStep())) {
                List<QuestionLog> logs = questionLogService.list(
                        new QueryWrapper<QuestionLog>()
                                .lambda()
                                .eq(QuestionLog::getQuestionId, one1.getId())
                );

                //
                //在同一天内，多次选择相同编号的设备加入问题跟进（不论是批量加入的还是单个加入的），并且：
                // 1.当天第一个加入的跟进记录处于开始跟进后的第二个节点，且第二个节点未完成
                // 2.后续选择的优先级、处理节点和处理人，与当天第一个加入的跟进记录是一样的
                //系统弹窗提醒“当前选中记录可以与当天已存在的跟进记录合并，是否继续？”，用户确认则合并到当天第一个加入的跟进记录中
                if (logs.size() == 1) {
                    if (one1.getCurStep().equals(question.getCurStep()) && one1.getQuesLevel().equals(question.getQuesLevel()) && one1.getUserAccount().equals(question.getUserAccount())) {
                        if (StringUtils.isBlank(comfirm) || "N".equals(comfirm)) {
                            return 602;
//                        throw new IllegalStateException("当前选中记录可以与当天已存在的跟进记录合并，是否继续");
                        }
                        if ("Y".equals(comfirm)) {
                            needMerge = true;
                        }
                    }
                }

            }

            if (needMerge) {
                String s = one1.getExceItem() + "," + abnormalItem;
                String s1 = Arrays.stream(s.split(",")).distinct().collect(Collectors.joining(","));
                one1.setExceItem(s1);
                deviceQuestionService.updateById(one1);
            } else {
                question.setCheckItem(checkItem);
                question.setExceItem(abnormalItem);
                question.setCheckItemCode(checkItemCodes);
                question.setExceItemCode(abnormalCodes);

                question.setCreateTime(new Date());
                question.setUpdateTime(question.getCreateTime());
                question.setDelFlag(0);

            }
        }

        deviceQuestionService.saveBatch(questions);

        List<QuestionLog> logs  = new ArrayList<>();
        QuestionLog log;
        for (DeviceQuestion question : questions) {
            // 增加处理日志
            log = new QuestionLog();
            log.setCreateTime(new Date());
            log.setCurStep("start");
            log.setCurStepName("开始跟进");
            log.setUserAccount(ShiroUtil.getCurrentUser().getUsername());
            log.setUserName(ShiroUtil.getCurrentUser().getNickname());
            log.setQuestionId(question.getId());
            log.setDeviceCode(question.getDeviceCode());
            log.setAssetId(question.getAssetId());
            log.setNextStep(question.getCurStep());
            log.setNextStepName(question.getCurStepName());
            log.setNextUser(question.getUserAccount());
            log.setNextUserName(question.getUserName());
            log.setHandleState(2);
            log.setQuestionResean(question.getQuestionResean());
            logs.add(log);

            log = new QuestionLog();
            log.setCreateTime(new Date());
            log.setCurStep(question.getCurStep());
            log.setCurStepName(question.getCurStepName());
            log.setUserAccount(question.getUserAccount());
            log.setUserName(question.getUserName());
            log.setQuestionId(question.getId());
            log.setDeviceCode(question.getDeviceCode());
            log.setAssetId(question.getAssetId());
            log.setHandleState(1);
            logs.add(log);

            deviceDataAbnormalStatDayService.update(
                    new UpdateWrapper<DeviceDataAbnormalStatDay>()
                            .lambda()
                            .eq(DeviceDataAbnormalStatDay::getDeviceName, question.getAssetId())
                            .ge(DeviceDataAbnormalStatDay::getStatDate, question.getQStart())
                            .le(DeviceDataAbnormalStatDay::getStatDate, question.getQEnd())
                            .set(DeviceDataAbnormalStatDay::getDeviceQuestionId, question.getId())
            );
        }

        questionLogService.saveBatch(logs);

        return true;
    }


    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation("删除一个问题跟进")
    @DeleteMapping
    public boolean delete(@RequestParam("id") Long id) {
        return deviceQuestionService.removeById(id);
    }

    @ApiOperation("查询处理环节和意见分类")
    @ApiImplicitParam(name = "parent", value = "opinion_classify=意见分类 question_step=问题处理环节")
    @GetMapping("/dict")
    public List<Dict> findDict(String parent) {
        return dictService.list(
                new QueryWrapper<Dict>()
                        .lambda()
                        .eq(Dict::getDictType, parent)
        );
    }

    @ApiOperation("获取某一环节对应的处理人")
    @GetMapping("/handl/user")
    public List<QuestionStepUser> getUser(String stepCode, String divisionCode){
//        return null;
        List<QuestionStepUser> list = questionStepUserService.list(
                new QueryWrapper<QuestionStepUser>()
                        .lambda()
                        .eq(QuestionStepUser::getStepCode, stepCode)
                        .eq(StringUtils.isNotBlank(divisionCode) && !"platfrom".equals(stepCode), QuestionStepUser::getDivisionCode, divisionCode)
                        .isNull("platfrom".equals(stepCode), QuestionStepUser::getDivisionCode)
        );
        if(list.size() < 1)
        {
            return list;
        }

        // 根据账户查询用户名称，确保用户名称的准确性
        List<String> collect = list.stream().map(QuestionStepUser::getUserAccount).distinct().collect(Collectors.toList());
        List<SysUser> sysUsers = sysUserMapper.selectList(
                new QueryWrapper<SysUser>()
                        .lambda()
                        .in(SysUser::getUsername, collect)
        );
        list.forEach(x->{
            SysUser user = sysUsers.stream().filter(u -> u.getUsername().equals(x.getUserAccount())).findFirst().orElse(null);
            if (null != user) {
                x.setUserName(user.getNickname());
            }
        });
        return list;
    }

    @ApiOperation("提交问题处理信息")
    @PostMapping("/handle")
    public String postHandleLog(@RequestPart("file") MultipartFile file, @RequestPart("log") @Validated QuestionLog log) throws IOException {
        QuestionLog data;
        for (QuestionLog.QuestionDevices q : log.getQuestions()) {
            DeviceQuestion question = deviceQuestionService.getById(q.getQuestionId());
            Assert.notNull(question, "没有找到对应的问题");

            if (!(ShiroUtil.getCurrentUser().getUsername().equals(question.getUserAccount()))) {
                throw new RuntimeException("无权限处理此问题");
            }

            if (CLOSE_STAT.equals(question.getCurStep())) {
                throw new RuntimeException("当前跟进任务已结束，无需处理");
            }

            if (!CLOSE_STAT.equals(log.getNextStep()) && StringUtils.isBlank(log.getNextUserName())) {
                throw new RuntimeException("请指定下一环节处理人");
            }
            data = new QuestionLog();
            BeanUtils.copyProperties(log, data);
            data.setFileName(file.getOriginalFilename());
            data.setFileSize(file.getSize());
            data.setRawDateFile(file.getBytes());
            data.setQuestionId(q.getQuestionId());

            // 查找当前任务待处理的信息
            QuestionLog one = questionLogService.getOne(
                    new QueryWrapper<QuestionLog>()
                            .lambda()
                            .eq(QuestionLog::getQuestionId, q.getQuestionId())
                            .eq(QuestionLog::getHandleState, 1)
            );
            data.setId(one.getId());
            data.setUpdateTime(new Date());
            data.setHandleState(2);
            questionLogService.updateById(data);

            Date date = new Date();

            // 更新问题的下一处理信息
            question.setCurStep(log.getNextStep());
            question.setCurStepName(log.getNextStepName());
            question.setUserAccount(log.getNextUser());
            question.setUserName(log.getNextUserName());
            question.setUpdateTime(date);
            deviceQuestionService.updateById(question);


            data = new QuestionLog();
            data.setCreateTime(date);
            data.setCurStep(question.getCurStep());
            data.setCurStepName(question.getCurStepName());
            data.setUserAccount(question.getUserAccount());
            data.setUserName(question.getUserName());
            data.setQuestionId(q.getQuestionId());
            data.setDeviceCode(question.getDeviceCode());
            data.setAssetId(question.getAssetId());
            data.setHandleState(1);

            if (CLOSE_STAT.equals(log.getNextStep())) {
                data.setUserAccount(ShiroUtil.getCurrentUser().getUsername());
                data.setUserName(ShiroUtil.getCurrentUser().getNickname());
                data.setHandleState(2);
            }
            questionLogService.save(data);
        }
        return "提交处理成功";
    }

    @ApiOperation("获得问题处理日志")
    @GetMapping("/question/log")
    public List<QuestionLog> getLog(@RequestParam("questionId") Long questionId){
        List<QuestionLog> list = questionLogService.list(
                new QueryWrapper<QuestionLog>()
                        .lambda()
                        .eq(QuestionLog::getQuestionId, questionId)
                        .orderByAsc(QuestionLog::getCreateTime)
                        .orderByAsc(QuestionLog::getId)
        );

        for (int i = 0; i < list.size(); i++) {
            QuestionLog questionLog = list.get(i);
            if(questionLog.getHandleState() ==2 && questionLog.getUpdateTime() !=null)
            {
                questionLog.setCreateTime(questionLog.getUpdateTime());
            }
        }
        return list;
    }

    @ApiOperation("问题跟进关联的异常数据")
    @GetMapping("/abnormal/data")
    public Page<OrsDeviceDataAbnormalDetail> findAbnormalDateByDate(Page<OrsDeviceDataAbnormalDetail> page, @RequestParam("questionId") Long questionId) {
        return deviceQuestionService.findAbnormalDateByDate(page, questionId);
    }

    @ApiOperation("设备问题跟进导出")
    @GetMapping("/export")
    public void export(DeviceQuestionReq req, HttpServletResponse response){
        ExportParams exportParams = new ExportParams("设备问题跟进", "设备问题跟进记录");
        exportParams.setCreateHeadRows(true);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, DeviceQuestionExcel.class, (o, i) -> {
            Page<DeviceLedgerResp> page = selectAll(new Page<>(i, 5000), req);

            List<Object> data = new ArrayList<>();
            page.getRecords().forEach(x->{
                DeviceQuestionExcel excel = new DeviceQuestionExcel();
                excel.setDivision(x.getDivision());
                excel.setRegion(x.getRegion());
                excel.setProductGroup(x.getProductGroup());
                excel.setName(x.getName());
                excel.setDeviceNo(x.getDeviceNo());
                excel.setTboxId(x.getTboxId());
                excel.setCreateTime(x.getCreateTime());
                excel.setCheckItem(x.getCheckItem());
                excel.setExceItem(x.getExceItem());
                excel.setQuesLevel(x.getQuesLevel());
                excel.setCurStepName(x.getCurStepName());
                excel.setUserName(x.getUserName());
                data.add(excel);
            });
            return data;
        }, 0);
        if (workbook != null) {
            EasyPoiUtils.downLoadExcel("设备问题跟进.xlsx", response, workbook);
        }
    }

    @ApiOperation("下载指定问题处理环节的附件")
    @GetMapping("/download")
    public ResponseEntity<byte[]> downloadFile(Integer questionLogId) throws UnsupportedEncodingException {
        QuestionLog log = questionLogService.getById(questionLogId);
        if (null == log.getRawDateFile() || log.getRawDateFile().length == 0) {
            throw new IllegalStateException("当前处理没有附件");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.add("Content-Disposition", "attachment;filename="+new String(log.getFileName().getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));

        return new ResponseEntity<>(log.getRawDateFile(), headers, HttpStatus.OK);
    }

    public static List<Date> getDateRange(Date startDate, Date endDate) {
        List<Date> dateRange = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        while (!calendar.getTime().after(endDate)) {
            Date result = calendar.getTime();
            dateRange.add(result);
            calendar.add(Calendar.DATE, 1);
        }
        return dateRange.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 统计问题各个节点的数量
     * @return
     */
    @ApiOperation("统计问题各个节点的数量")
    @GetMapping("/count")
    public QuestionCount countNum(){
        return deviceQuestionMapper.countNum();
    }

    /**
     * 根据处理环节统计待处理的数据量
     * @param curSteps 处理环节集合
     * @return
     */
    @ApiOperation("统计某个环节的时长")
    @GetMapping("/count/steptime")
    public List<QuestionCountDaysByStep> countByStepDays(@RequestParam("curSteps") String curSteps){
        return deviceQuestionMapper.countByStepDays(StringUtils.isNotBlank(curSteps) ? Arrays.asList(curSteps.split(",")) : null);
    }

    /**
     * 根据用户统计其处理数据
     * @param curSteps 处理节点
     * @param userAccount 用户账户
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @ApiOperation("统计用户在某环节的数据（硬件平台处理环节 （需传参））/曲线报表通用")
    @GetMapping("/count/user")
    public Page<QuestionCountBydUser> questionCountBydUser(Page<?> page,
                                                    String curSteps,
                                                    String userAccount,
                                                    String startTime,
                                                    String endTime) {
        return deviceQuestionMapper.questionCountBydUser(page, StringUtils.isNotBlank(curSteps) ? Arrays.asList(curSteps.split(",")) : null, userAccount, startTime, endTime);
    }

    /**
     * 根据用户统计各个环节的数量
     * @param userAccount 用户账户
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @ApiOperation("统计用户在某环节的进度（进展统计）/曲线报表通用")
    @GetMapping("/count/step/user")
    public Page<QuestionCountBydUser> questionCountByStepAndUser(Page<?> page,
                                                           String userAccount,
                                                           String startTime,
                                                           String endTime) {
        return deviceQuestionMapper.questionCountByStepAndUser(page, userAccount, startTime, endTime);
    }

    @ApiOperation("客户处理统计")
    @GetMapping("/count/division")
    public List<QuestionCountByDivision> countByDivision() {
        return deviceQuestionMapper.countByDivision();
    }

    @ApiOperation("导入历史问题")
    @PostMapping("/import/his")
    public void importQuestionDevice(@RequestPart MultipartFile file, HttpServletResponse response){
        if (file.isEmpty()) {
            Assert.isTrue(!file.isEmpty(),"请上传文件");
        }
        List<UnimportQuestionExcel> unimportDataes = new ArrayList<>();
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        try {
            List<DeviceHisQuestionExcel> his = ExcelImportUtil.importExcel(file.getInputStream(), DeviceHisQuestionExcel.class, params);
            if (his.isEmpty()) {
                throw new IllegalArgumentException("请上传文件类容");
            }

            // 取出所有设备编号，只处理已存在设备台账的设备
            List<String> deviceNos = his.stream().map(DeviceHisQuestionExcel::getDeviceNo).distinct().collect(Collectors.toList());
            List<BaseDeviceInfo> deviceInfos = baseDeviceInfoMapper.selectList(
                    new QueryWrapper<BaseDeviceInfo>()
                            .lambda()
                            .in(BaseDeviceInfo::getDeviceCode, deviceNos)
            );
            Map<String, List<BaseDeviceInfo>> map = deviceInfos.stream().collect(Collectors.groupingBy(BaseDeviceInfo::getDeviceCode));

            UnimportQuestionExcel un;
            for (DeviceHisQuestionExcel h : his) {
                List<BaseDeviceInfo> devices = map.get(h.getDeviceNo());
                if (null == devices || devices.isEmpty()) {
                    un = new UnimportQuestionExcel();
                    un.setDeviceNo(h.getDeviceNo());
                    un.setErrorMsg("设备在根云台账中不存在");
                    unimportDataes.add(un);
                    continue;
                }

                // 首先找到CRM注册的，如果没有，直接用第一个
                BaseDeviceInfo deviceInfo = devices.stream().filter(x -> x.getCrmRegister() == 1).findFirst().orElse(null);
                if (null == deviceInfo) {
                    deviceInfo = devices.get(0);
                }
                // 如果问题检查项不存在，查询对应期间内的异常信息
                if (StringUtils.isBlank(h.getCheckItem())) {
                    List<DeviceDataAbnormalDetailDay> abnormalDetails = deviceDataAbnormalDetailDayMapper.selectList(
                            new QueryWrapper<DeviceDataAbnormalDetailDay>()
                                    .lambda()
                                    .select(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
                                    .eq(DeviceDataAbnormalDetailDay::getDeviceName, deviceInfo.getAssetId())
                                    .eq(DeviceDataAbnormalDetailDay::getAbnormalCode, 9008)
                                    .apply("abnormal_time::DATE >= {0}::DATE", h.getQstar())
                                    .apply("abnormal_time::DATE <= {0}::DATE", h.getQEnd())
                                    .groupBy(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
                    );
                    if (abnormalDetails.isEmpty()) {
                        un = new UnimportQuestionExcel();
                        un.setDeviceNo(h.getDeviceNo());
                        un.setErrorMsg("当前设备在当前时间区间内没有空值异常产生，无需跟进");
                        unimportDataes.add(un);
                        continue;
                    }
                    String abnormalItem = abnormalDetails.stream().map(DeviceDataAbnormalDetailDay::getAbnormalName).distinct().collect(Collectors.joining(","));
                    String checkItem = abnormalDetails.stream().map(DeviceDataAbnormalDetailDay::getPropertyName).distinct().collect(Collectors.joining(","));
                    String abnormalCodes = abnormalDetails.stream().map(x -> String.valueOf(x.getAbnormalCode())).distinct().collect(Collectors.joining(","));
                    String checkItemCodes = abnormalDetails.stream().map(x -> String.valueOf(x.getParamCode())).distinct().collect(Collectors.joining(","));
                    h.setCheckItem(checkItem);
                    h.setExceItem(abnormalItem);
                    h.setCheckItemCode(checkItemCodes);
                    h.setExceItemCode(abnormalCodes);
                }
                // 根据约定，这里只导入处理待启机、待验证环节的设备
                switch (h.getCurStepName()) {
                    // 待启机
                    case "waitStart":
                        h.setCurStepName("wait_start");
                        String s1 = importWaitStartStateData(deviceInfo, h);
                        if (StringUtils.isNotBlank(s1)){
                            un = new UnimportQuestionExcel();
                            un.setDeviceNo(h.getDeviceNo());
                            un.setErrorMsg(s1);
                            unimportDataes.add(un);
                        }
                        break;
                    // 待验证
                    case "waitVerify":
                        h.setCurStepName("wait_verify");
                        String s = importWaitVerifytStateData(deviceInfo, h);
                        if (StringUtils.isNotBlank(s)){
                            un = new UnimportQuestionExcel();
                            un.setDeviceNo(h.getDeviceNo());
                            un.setErrorMsg(s);
                            unimportDataes.add(un);
                        }
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!unimportDataes.isEmpty()) {
            ExportParams exportParams = new ExportParams("历史问题导入失败", "导入失败项");
            exportParams.setCreateHeadRows(true);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, UnimportQuestionExcel.class, unimportDataes);
            if (workbook != null) {
                log.info("问题跟进导入失败内容【{}】", JSONObject.toJSONString(unimportDataes));
                EasyPoiUtils.downLoadExcel("历史问题导入失败.xlsx", response, workbook);
            }
        }
    }

    /**
     * 导入待启机/待剔除状态的问题
     */
    private String importWaitStartStateData(BaseDeviceInfo deviceInfo, DeviceHisQuestionExcel h) {
        // 查询待启机环节的处理人
        List<QuestionStepUser> waitStart = new ArrayList<>();
        // 如果待启机环节导入有指定处理人，则直接使用，但是需确保用户已经在系统中存在，如果没有指定则查询系统中配置的对应环境的处理人
        if (StringUtils.isBlank(h.getHandleUser())) {
            waitStart = questionStepUserService.list(
                    new QueryWrapper<QuestionStepUser>()
                            .lambda()
                            .eq(QuestionStepUser::getStepCode, "wait_start")
            );
            if (null == waitStart || waitStart.isEmpty()) {
                return "没有找到待启机环节对应的处理人";
            }
        } else {
            QuestionStepUser stepUser = new QuestionStepUser();
            stepUser.setStepName("待启机/待剔除");
            stepUser.setStepCode("wait_start");
            stepUser.setUserAccount(h.getHandleUser());

            SysUser user = sysUserMapper.getSysUserByUserName(h.getHandleUser());
            if (null == user) {
                return "选择的处理人员在系统中不存在，请先添加对应人员";
            }
            stepUser.setUserName(user.getNickname());
            waitStart.add(stepUser);
        }

        // 查询硬件处理环节的处理人
        List<QuestionStepUser> hardware = new ArrayList<>();
        if (StringUtils.isBlank(deviceInfo.getDivisionCode())) {
            QuestionStepUser stepUser = new QuestionStepUser();
            stepUser.setStepName("硬件处理");
            stepUser.setStepCode("hardware");
            stepUser.setUserAccount("ke.xiang");

            SysUser user = sysUserMapper.getSysUserByUserName(stepUser.getUserAccount());
            if (null == user) {
                return "选择的处理人员在系统中不存在，请先添加对应人员";
            }
            stepUser.setUserName(user.getNickname());
            hardware.add(stepUser);
        } else {
            hardware = questionStepUserService.list(
                    new QueryWrapper<QuestionStepUser>()
                            .lambda()
                            .eq(QuestionStepUser::getStepCode, "hardware")
                            .eq(QuestionStepUser::getDivisionCode, deviceInfo.getDivisionCode())
            );
            if (null == hardware || hardware.isEmpty()) {
                return "没有找到硬件处理环节对应的处理人";
            }
        }

        QuestionStepUser waitStartStep = waitStart.get(0);
        QuestionStepUser hardwareStep = hardware.get(0);
        hardwareStep.setStepName("硬件处理");
        waitStartStep.setStepName("待启机/待剔除");
        // 加入问题
        DeviceQuestion question = genarateQuestion(deviceInfo, h, waitStartStep);

        // 增加开始跟进处理日志
        QuestionStepUser startStep = new QuestionStepUser();
        startStep.setStepName("开始跟进");
        startStep.setStepCode("start");
        startStep.setUserAccount(ShiroUtil.getCurrentUser().getUsername());
        startStep.setUserName(ShiroUtil.getCurrentUser().getNickname());
        genarateQuestionLog(question, h, startStep, hardwareStep, 2);

        // 增加硬件处理日志
        genarateQuestionLog(question, h, hardwareStep, waitStartStep, 2);

        // 增加待启机处理日志
        genarateQuestionLog(question, h, waitStartStep, null, 1);

        return null;
    }

    /**
     * 导入待验证状态的问题
     */
    private String importWaitVerifytStateData(BaseDeviceInfo deviceInfo, DeviceHisQuestionExcel h) {
        // 查询待验证环节的处理人
        List<QuestionStepUser> waitVerify = new ArrayList<>();
        // 如果待验证环节导入有指定处理人，则直接使用，但是需确保用户已经在系统中存在，如果没有指定则查询系统中配置的对应环境的处理人
        if (StringUtils.isBlank(h.getHandleUser())) {
            waitVerify = questionStepUserService.list(
                    new QueryWrapper<QuestionStepUser>()
                            .lambda()
                            .eq(QuestionStepUser::getStepCode, "wait_verify")
                            .eq(QuestionStepUser::getDivisionCode, deviceInfo.getDivisionCode())
            );
            if (null == waitVerify || waitVerify.isEmpty()) {
                return "没有找到待验证环节对应的处理人";
            }
        } else {
            QuestionStepUser stepUser = new QuestionStepUser();
            stepUser.setStepName("待验证");
            stepUser.setStepCode("wait_verify");
            stepUser.setUserAccount(h.getHandleUser());

            SysUser user = sysUserMapper.getSysUserByUserName(h.getHandleUser());
            if (null == user) {
                return "选择的处理人员在系统中不存在，请先添加对应人员";
            }
            stepUser.setUserName(user.getNickname());
            waitVerify.add(stepUser);
        }

        List<QuestionStepUser> hardware = new ArrayList<>();
        List<QuestionStepUser> operate = new ArrayList<>();
        if (StringUtils.isBlank(deviceInfo.getDivisionCode())) {
            // 没有事业部信息  指定硬件处理责任人
            QuestionStepUser stepUser = new QuestionStepUser();
            stepUser.setStepName("硬件处理");
            stepUser.setStepCode("hardware");
            stepUser.setUserAccount("ke.xiang");

            SysUser user = sysUserMapper.getSysUserByUserName(stepUser.getUserAccount());
            if (null == user) {
                return "选择的处理人员在系统中不存在，请先添加对应人员";
            }
            stepUser.setUserName(user.getNickname());
            hardware.add(stepUser);

            // 没有事业部信息  指定运营处理责任人
            stepUser = new QuestionStepUser();
            stepUser.setStepName("运营处理");
            stepUser.setStepCode("operate");
            stepUser.setUserAccount("lei.xiao");

            user = sysUserMapper.getSysUserByUserName(stepUser.getUserAccount());
            if (null == user) {
                return "选择的处理人员在系统中不存在，请先添加对应人员";
            }
            stepUser.setUserName(user.getNickname());
            operate.add(stepUser);
        } else {
            hardware = questionStepUserService.list(
                    new QueryWrapper<QuestionStepUser>()
                            .lambda()
                            .eq(QuestionStepUser::getStepCode, "hardware")
                            .eq(QuestionStepUser::getDivisionCode, deviceInfo.getDivisionCode())
            );
            if (null == hardware || hardware.isEmpty()) {
                return "没有找到硬件处理环节对应的处理人";
            }

            // 查询待启机环节的处理人
            operate = questionStepUserService.list(
                    new QueryWrapper<QuestionStepUser>()
                            .lambda()
                            .eq(QuestionStepUser::getStepCode, "operate")
                            .eq(QuestionStepUser::getDivisionCode, deviceInfo.getDivisionCode())
            );
            if (null == operate || operate.isEmpty()) {
                return "没有找到运营处理环节对应的处理人";
            }
        }

        QuestionStepUser waitVerifyStep = waitVerify.get(0);
        QuestionStepUser hardwareStep = hardware.get(0);
        QuestionStepUser operateStep = operate.get(0);
        hardwareStep.setStepName("硬件处理");
        waitVerifyStep.setStepName("待验证");
        operateStep.setStepName("运营处理");
        // 加入问题
        DeviceQuestion question = genarateQuestion(deviceInfo, h, waitVerifyStep);

        // 增加开始跟进处理日志
        QuestionStepUser startStep = new QuestionStepUser();
        startStep.setStepName("开始跟进");
        startStep.setStepCode("start");
        startStep.setUserAccount(ShiroUtil.getCurrentUser().getUsername());
        startStep.setUserName(ShiroUtil.getCurrentUser().getNickname());
        genarateQuestionLog(question, h, startStep, hardwareStep, 2);

        // 增加硬件处理日志
        genarateQuestionLog(question, h, hardwareStep, operateStep, 2);

        // 运营处理
        genarateQuestionLog(question, h, operateStep, waitVerifyStep, 2);

        // 增加待启机处理日志
        genarateQuestionLog(question, h, waitVerifyStep, null, 1);

        return null;
    }

    private DeviceQuestion genarateQuestion(BaseDeviceInfo deviceInfo, DeviceHisQuestionExcel h, QuestionStepUser stepUser) {
        // 生成问题
        DeviceQuestion question = new DeviceQuestion();
        question.setQStart(h.getQstar());
        question.setQEnd(h.getQEnd());
        question.setDeviceCode(deviceInfo.getDeviceCode());
        question.setCurStep(stepUser.getStepCode());
        question.setAssetId(deviceInfo.getAssetId());
        question.setCheckItem(h.getCheckItem());
        question.setCurStepName(stepUser.getStepName());
        question.setModelId(deviceInfo.getModelId());
        question.setQuesLevel(h.getQuesLevel());
        question.setExceItem(h.getExceItem());
        question.setCheckItemCode(h.getCheckItemCode());
        question.setExceItemCode(h.getExceItemCode());

        question.setUserAccount(stepUser.getUserAccount());
        question.setUserName(stepUser.getUserName());
        question.setCreateTime(new Date());
        question.setUpdateTime(question.getCreateTime());
        question.setDelFlag(0);
        deviceQuestionMapper.insert(question);

        // 关联指定的问题数据，并标记问题
        deviceDataAbnormalStatDayService.update(
                new UpdateWrapper<DeviceDataAbnormalStatDay>()
                        .lambda()
                        .eq(DeviceDataAbnormalStatDay::getDeviceName, question.getAssetId())
                        .ge(DeviceDataAbnormalStatDay::getStatDate, question.getQStart())
                        .le(DeviceDataAbnormalStatDay::getStatDate, question.getQEnd())
                        .set(DeviceDataAbnormalStatDay::getDeviceQuestionId, question.getId())
        );
        return question;
    }

    /**
     * 生成问题处理日志
     * @param question 问题跟进信息
     * @param h 导入信息
     * @param upStep 当前处理环节信息
     * @param nextStep 下一步处理环节信息
     * @param handleState 处理状态
     */
    private void genarateQuestionLog(DeviceQuestion question,DeviceHisQuestionExcel h, QuestionStepUser upStep, QuestionStepUser nextStep, int handleState) {
        QuestionLog log = new QuestionLog();
        log.setCreateTime(new Date());
        log.setCurStep(upStep.getStepCode());
        log.setCurStepName(upStep.getStepName());
        log.setUserAccount(upStep.getUserAccount());
        log.setUserName(upStep.getUserName());
        log.setQuestionId(question.getId());
        log.setDeviceCode(question.getDeviceCode());
        log.setAssetId(question.getAssetId());
        if (null != nextStep) {
            log.setNextStepName(nextStep.getStepName());
            log.setNextStep(nextStep.getStepCode());
            log.setNextUser(nextStep.getUserAccount());
            log.setNextUserName(nextStep.getUserName());
        }
        if (null != h && "hardware".equals(upStep.getStepCode())) {
            log.setHandlIdea(h.getHandlIdea());
            log.setQuestionResean(h.getQuestionResean());
        }
        log.setHandleState(handleState);
        questionLogService.save(log);
    }
}

