<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsBigdataEquipmentBaseInfoAllMapper">

    <select id="getDeviceInfoAll" resultType="com.rc.admin.ors.quality.model.BigdataEquipmentBaseInfoAll">
        select *
        from bigdata_equipment_base_info_all
        where ZEHDFSV_COUNTRY != 'CN' and  ZEHDFSV_COUNTRY != '' and  ZEHDFSV_COUNTRY is not null and  ZEEXDATE > #{startTime};
    </select>
    <insert id="saveDeviceInfo">
        insert into ors_crm_bigdata_equipment_base_info_all(product_id,  zehd_spart, zehd_spartdesc,
                                                         zeexdate, zejjdate, zekjdate,
                                                        sybbh, sybbh_desc, agent, agent_desc, srvagt, zsrv_agtdesc,

                                                         zehdfsv_country,
                                                        zehdfsv_reg, zehdfsv_country_desc, zehdfsv_reg_desc,
                                                        create_date)
        values (#{item.productId},  #{item.zehdSpart}, #{item.zehdSpartdesc},
                 #{item.zeexdate}, #{item.zejjdate}, #{item.zekjdate},
                #{item.sybbh}, #{item.sybbhDesc}, #{item.agent}, #{item.agentDesc}, #{item.srvagt},
                #{item.zsrvAgtdesc},
                #{item.zehdfsvCountry}, #{item.zehdfsvReg}, #{item.zehdfsvCountryDesc}, #{item.zehdfsvRegDesc},

                now()) on conflict(product_id) do
        update set

            zehd_spart= #{item.zehdSpart} ,
            zehd_spartdesc= #{item.zehdSpartdesc} ,
            zeexdate= #{item.zeexdate} ,
            zejjdate= #{item.zejjdate} ,
            zekjdate= #{item.zekjdate} ,
            sybbh= #{item.sybbh} ,
            sybbh_desc= #{item.sybbhDesc} ,
            agent= #{item.agent} ,
            agent_desc= #{item.agentDesc} ,
            srvagt= #{item.srvagt} ,
            zsrv_agtdesc= #{item.zsrvAgtdesc} ,
            zehdfsv_country= #{item.zehdfsvCountry} ,
            zehdfsv_reg= #{item.zehdfsvReg} ,
            zehdfsv_country_desc=#{item.zehdfsvCountryDesc} ,
            zehdfsv_reg_desc= #{item.zehdfsvRegDesc} ,
            create_date= now()

    </insert>

    <select id="getDqmDeviceDataAbnormalStatDay" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataAbnormalStatDay">
        select
            oddadd.model_id as modelId,
            oddadd.device_name as deviceName,
            oddadd.param_code as paramCode ,
            oddadd.abnormal_code as abnormalCode,
            oddadd.abnormal_count as abnormalCount,
            oddadd.abnormal_data as abnormalData,
            oddadd.stat_date as statDate
        from
            ors_device_data_abnormal_detail_day oddadd where oddadd.stat_date = #{statDate}
    </select>

    <select id="getDeviceDataExceptionsInfo" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp">
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope == 'new'">
            SELECT
                t4.thing_id  as thingId,
                t4.asset_id as deviceCode,
                t4.device_code as deviceName,
                t4.model_id as modelId,
                t4.model_name as modelName,
                t1.abnormal_name as abnormalName,
                t1.abnormal_data as abnormalData,
                t1.abnormal_time as abnormalTime,
                t1.property ,
                t1.property_name as propertyName,
                t4.product_group_code as zehdSpart,
                t4.product_group as zehdSpartdesc,
                t4.division as sybbh,
                t4.region as zehdsvReg,
                t4.hw_version as hwVersion,
                t4.auth_token as authToken,
                t4.fw_version as fwVersion
            from (
                SELECT
                    *
                from ors_device_data_abnormal_detail_day where (id ) in  (
                    select t2.id from (
                        SELECT
                            device_name,property,param_code,abnormal_code,
                            max(s.id) as id
                        FROM
                            ors_device_data_abnormal_detail_day s
                        WHERE
                            s.stat_date &gt;= #{req.createTime_start}
                            AND s.stat_date &lt;= #{req.createTime_end}
                            <if test="req.paramCodes != null and req.paramCodes != ''">
                                AND s.param_code in
                                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="list != null and list != ''">
                                AND s.abnormal_code in
                                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                                    #{item}
                                </foreach>

                            </if>
                        GROUP BY device_name,property,param_code,abnormal_code
                    )t2 left join (select * from ors_base_device_info ) bdi on t2.device_name = bdi.asset_id
                    where bdi.active_statu is true
                    <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                        AND bdi.model_id not in
                        <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.sybbh != null and req.sybbh != ''">
                        AND bdi.division_code in
                        <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                        AND bdi.product_group_code in
                        <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                        AND bdi.region_code in
                        <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.agentName != null and req.agentName!=''">
                        AND bdi.agent like CONCAT('%',#{req.agentName},'%')
                    </if>
                    <if test="req.country != null and req.country != ''">
                        AND bdi.country_code like CONCAT('%',#{req.country},'%')
                    </if>
                    <if test="req.deviceName != null and req.deviceName != ''">
                        AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
                    </if>

                    <if test="req.deviceNameList != null and req.deviceNameList != ''">
                        AND bdi.device_code in
                        <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                        AND bdi.asset_id in
                        <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="req.modelId != null and req.modelId != ''">
                        AND bdi.model_id in
                        <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.deviceCode != null and req.deviceCode != ''">
                        AND bdi.asset_id like CONCAT('%',#{req.deviceCode},'%')
                    </if>

                    <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                        AND bdi.asset_id in
                        <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="req.userName != null and req.userName != ''">
                        AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
                    </if>
                    <if test="req.installType != null and req.installType != ''">
                        AND bdi.install_type = #{req.installType}
                    </if>
                )
            )  t1
            left join ors_base_device_info AS t4 ON t1.device_name = t4.asset_id
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND t4.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            limit #{req.pageSize} OFFSET #{req.current}
        </if>
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope != 'new'">
            select
            bdi.thing_id  as thingId,
            bdi.asset_id as deviceCode,
            bdi.device_code as deviceName,
            bdi.model_id as modelId,
            bdi.model_name as modelName,
            oddad.abnormal_name as abnormalName,
            oddad.abnormal_data as abnormalData,
            oddad.abnormal_time as abnormalTime,
            oddad.property ,
            oddad.property_name as propertyName,
            bdi.product_group_code as zehdSpart,
            bdi.product_group as zehdSpartdesc,
            bdi.division as sybbh,
            bdi.region as zehdsvReg,
            bdi.hw_version as hwVersion,
            bdi.auth_token as authToken,
            bdi.fw_version as fwVersion

            from ors_device_data_abnormal_detail as oddad
            left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name and  bdi.active_statu is true and oddad.abnormal_effective=1
            WHERE bdi.active_statu is true
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND bdi.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>

            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id =#{req.deviceCode}
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND oddad.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="list != null and list != ''">
                AND oddad.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND oddad.stat_date &gt;= #{req.createTime_start}
                AND oddad.stat_date &lt;= #{req.createTime_end}
            </if>
            limit #{req.pageSize} OFFSET #{req.current}
        </if>
    </select>

    <select id="getHistoryDeviceDataExceptionsInfoForOne" resultType="com.rc.admin.ors.quality.model.DqmHistoryDeviceDataExceptionResp">
        with
            ors_device_question_data as
                (SELECT DISTINCT device_code, 1 AS hasFollowUp FROM dqm.ors_device_question WHERE cur_step != 'close' and del_flag = 0 group by device_code),
            ors_device_location_data as
                (
                select asset_id, device_location, stat_date
                from dqm.ors_device_location
                where stat_date::date BETWEEN #{req.createTime_start} AND #{req.createTime_end}
                )
        SELECT
        CASE WHEN o.hasFollowUp IS NOT NULL THEN 1 ELSE 0 END hasFollowUp,
        t1.endStatDate,
        coalesce(t2.device_location,'国外') as device_location,
        t1.startStatDate,
        omd.product_group_code AS zehdSpart,
        omd.product_group_name AS zehdSpartdesc,
        od.name  AS sybbh,
        t1.division_code AS sybbhCode,
        aa.data_center_id as dataCenterId,
        bdi.thing_id AS thingId,
        bdi.rc_asset_id AS deviceCode,
        bdi.device_code AS deviceName,
        bdi.model_id AS modelId,
        bdi.model_name AS modelName,
        bdi.hw_version AS hwVersion,
        bdi.auth_token AS authToken,
        bdi.fw_version AS fwVersion,
        bdi.country as country,
        t1.device_status_9008,
        t1.device_status_9009,
        t1.device_location_9008,
        t1.device_location_9001,
        t1.device_location_9002,
        t1.device_location_9007,
        t1.device_location_9009,
        t1.engine_worktime_9008,
        t1.engine_worktime_9001,
        t1.engine_worktime_9004,
        t1.engine_worktime_9009,
        t1.working_time_9008,
        t1.working_time_9001,
        t1.working_time_9004,
        t1.working_time_9009,
        t1.total_fuel_consumption_9008,
        t1.total_fuel_consumption_9001,
        t1.total_fuel_consumption_9004,
        t1.total_fuel_consumption_9009,
        t1.pumping_volume_9008,
        t1.pumping_volume_9001,
        t1.pumping_volume_9004,
        t1.pumping_volume_9009,
        t1.driving_mileage_9008,
        t1.driving_mileage_9001,
        t1.driving_mileage_9004,
        t1.driving_mileage_9009,

        t1.fuel_level_9008,
        t1.fuel_level_9001,
        t1.fuel_level_9009,
        t1.engine_speed_9008,
        t1.engine_speed_9001,
        t1.engine_speed_9009,
        t1.water_temperature_9008,
        t1.water_temperature_9001,
        t1.water_temperature_9009,
        t1.SOC_stateofcharge_9008,
        t1.SOC_stateofcharge_9001,
        t1.SOC_stateofcharge_9009,
        t1.travel_speed_9008,
        t1.travel_speed_9001,
        t1.travel_speed_9009,
        t1.total_electric_consumption_9008,
        t1.total_electric_consumption_9001,
        t1.total_electric_consumption_9004,
        t1.total_electric_consumption_9009,

        t1.total_idle_fuel_consumption_9001,
        t1.total_idle_fuel_consumption_9004,
        t1.total_idle_fuel_consumption_9005,
        t1.total_idle_fuel_consumption_9008,
        t1.total_idle_fuel_consumption_9009,
        t1.total_idle_time_9001,
        t1.total_idle_time_9004,
        t1.total_idle_time_9005,
        t1.gear_9008,
        t1.gear_9009,
        t1.gear_9001,
        t1.total_idle_time_9008,
        t1.total_idle_time_9009,
        t1.total_time_left_moving_9001,
        t1.total_time_left_moving_9004,
        t1.total_time_left_moving_9005,
        t1.total_time_left_moving_9008,
        t1.total_time_left_moving_9009,
        t1.total_time_right_moving_9001,
        t1.total_time_right_moving_9004,
        t1.total_time_right_moving_9005,
        t1.total_time_right_moving_9008,
        t1.total_time_right_moving_9009,
        t1.oil_pressure_9001,
        t1.oil_pressure_9008,
        t1.oil_pressure_9009,
        t1.pump_total_absorbed_torque_9001,
        t1.pump_total_absorbed_torque_9008,
        t1.pump_total_absorbed_torque_9009,
<!-- 2024-12-30 -->
        t1.pump_motor_rotate_speed_9001,
        t1.pump_motor_rotate_speed_9008,
        t1.pump_motor_rotate_speed_9009,
        t1.charging_status_9008,
        t1.charging_status_9009,
        t1.charging_status_9001,
        t1.charge_time_remain_9001,
        t1.charge_time_remain_9008,
        t1.charge_time_remain_9009,
        t1.single_charge_capacity_9001,
        t1.single_charge_capacity_9008,
        t1.single_charge_capacity_9009,
        t1.day_power_consumption_9001,
        t1.day_power_consumption_9008,
<!-- 2025-01-17 -->
        t1.action_code_9001,
        t1.action_code_9008,
        t1.action_code_9009,
        t1.total_time_rotation_9001,
        t1.total_time_rotation_9004,
        t1.total_time_rotation_9005,
        t1.total_time_rotation_9008,
        t1.total_time_rotation_9009,

        t1.total_no_action_power_consumption_9001,
        t1.total_no_action_power_consumption_9004,
        t1.total_no_action_power_consumption_9008,
        t1.total_no_action_power_consumption_9009,

        t1.driving_mileage_9005,
        t1.total_fuel_consumption_9005,
        t1.total_electric_consumption_9005,
        t1.working_time_9005,
        t1.engine_worktime_9005,
        t1.travel_speed_9006,
        t1.engine_speed_9006,
        t1.water_temperature_9006,
        t1.fuel_level_9006,

        t1.hasRecord,
        t1.notRecord,
        t1.deviceLocationCnt,

        t1.idle_time_idle_fuel_9101,
        t1.idle_time_idle_fuel_9102,
        t1.idle_time_idle_fuel_9100,
        t1.work_time_fuel_9103,
        t1.work_time_fuel_9104,
        t1.work_time_fuel_9100,

        t1.driving_mileage_9003,
        t1.total_fuel_consumption_9003,
        t1.total_electric_consumption_9003,
        t1.working_time_9003,
        t1.engine_worktime_9003,

        t1.idel_fuel_fuel_9105,
        t1.idel_fuel_fuel_9100,
        t1.idel_time_work_time_9106,
        t1.idel_time_work_time_9100,
        t1.mileage_speed_9107,
        t1.mileage_speed_9108,
        t1.mileage_speed_9100,
        t1.mileage_location_9109,
        t1.mileage_location_9100,
        t1.engine_time_fuel_9100


        FROM
        (
        SELECT
        s.device_name AS deviceName,
        max(s.division_code) as division_code,
        SUM ( s.device_status_9008 ) AS device_status_9008,
        SUM ( s.device_status_9009 ) AS device_status_9009,
        SUM ( s.device_location_9008 ) AS device_location_9008,
        SUM ( s.device_location_9001 ) AS device_location_9001,
        SUM ( s.device_location_9002 ) AS device_location_9002,
        SUM ( s.device_location_9007 ) AS device_location_9007,
        SUM ( s.device_location_9009 ) AS device_location_9009,
        SUM ( s.engine_worktime_9008 ) AS engine_worktime_9008,
        SUM ( s.engine_worktime_9001 ) AS engine_worktime_9001,
        SUM ( s.engine_worktime_9004 ) AS engine_worktime_9004,
        SUM ( s.engine_worktime_9009 ) AS engine_worktime_9009,
        SUM ( s.working_time_9008 ) AS working_time_9008,
        SUM ( s.working_time_9001 ) AS working_time_9001,
        SUM ( s.working_time_9004 ) AS working_time_9004,
        SUM ( s.working_time_9009 ) AS working_time_9009,
        SUM ( s.total_fuel_consumption_9008 ) AS total_fuel_consumption_9008,
        SUM ( s.total_fuel_consumption_9001 ) AS total_fuel_consumption_9001,
        SUM ( s.total_fuel_consumption_9004 ) AS total_fuel_consumption_9004,
        SUM ( s.total_fuel_consumption_9009 ) AS total_fuel_consumption_9009,
        SUM ( s.pumping_volume_9008 ) AS pumping_volume_9008,
        SUM ( s.pumping_volume_9001 ) AS pumping_volume_9001,
        SUM ( s.pumping_volume_9004 ) AS pumping_volume_9004,
        SUM ( s.pumping_volume_9009 ) AS pumping_volume_9009,
        SUM ( s.driving_mileage_9008 ) AS driving_mileage_9008,
        SUM ( s.driving_mileage_9001 ) AS driving_mileage_9001,
        SUM ( s.driving_mileage_9004 ) AS driving_mileage_9004,
        SUM ( s.driving_mileage_9009 ) AS driving_mileage_9009,

        SUM ( s.fuel_level_9008 ) AS fuel_level_9008,
        SUM ( s.fuel_level_9001 ) AS fuel_level_9001,
        SUM ( s.fuel_level_9009 ) AS fuel_level_9009,
        SUM ( s.engine_speed_9008 ) AS engine_speed_9008,
        SUM ( s.engine_speed_9001 ) AS engine_speed_9001,
        SUM ( s.engine_speed_9009 ) AS engine_speed_9009,
        SUM ( s.water_temperature_9008 ) AS water_temperature_9008,
        SUM ( s.water_temperature_9001 ) AS water_temperature_9001,
        SUM ( s.water_temperature_9009 ) AS water_temperature_9009,
        SUM ( s.SOC_stateofcharge_9008 ) AS SOC_stateofcharge_9008,
        SUM ( s.SOC_stateofcharge_9001 ) AS SOC_stateofcharge_9001,
        SUM ( s.SOC_stateofcharge_9009 ) AS SOC_stateofcharge_9009,
        SUM ( s.travel_speed_9008 ) AS travel_speed_9008,
        SUM ( s.travel_speed_9001 ) AS travel_speed_9001,
        SUM ( s.travel_speed_9009 ) AS travel_speed_9009,
        SUM ( s.total_electric_consumption_9008 ) AS total_electric_consumption_9008,
        SUM ( s.total_electric_consumption_9001 ) AS total_electric_consumption_9001,
        SUM ( s.total_electric_consumption_9004 ) AS total_electric_consumption_9004,
        SUM ( s.total_electric_consumption_9009 ) AS total_electric_consumption_9009,



        SUM ( s.total_idle_fuel_consumption_9001 ) AS total_idle_fuel_consumption_9001,
        SUM ( s.total_idle_fuel_consumption_9004 ) AS total_idle_fuel_consumption_9004,
        SUM ( s.total_idle_fuel_consumption_9004 ) AS total_idle_fuel_consumption_9005,
        SUM ( s.total_idle_fuel_consumption_9008 ) AS total_idle_fuel_consumption_9008,
        SUM ( s.total_idle_fuel_consumption_9009 ) AS total_idle_fuel_consumption_9009,

        SUM ( s.total_idle_time_9001 ) AS total_idle_time_9001,
        SUM ( s.total_idle_time_9004 ) AS total_idle_time_9004,
        SUM ( s.total_idle_time_9005 ) AS total_idle_time_9005,
        SUM ( s.total_idle_time_9008 ) AS total_idle_time_9008,
        SUM ( s.total_idle_time_9009 ) AS total_idle_time_9009,

        SUM ( s.gear_9008 ) AS gear_9008,
        SUM ( s.gear_9009 ) AS gear_9009,
        SUM ( s.gear_9001 ) AS gear_9001,

        SUM ( s.total_time_left_moving_9001 ) AS total_time_left_moving_9001,
        SUM ( s.total_time_left_moving_9004 ) AS total_time_left_moving_9004,
        SUM ( s.total_time_left_moving_9005 ) AS total_time_left_moving_9005,
        SUM ( s.total_time_left_moving_9008 ) AS total_time_left_moving_9008,
        SUM ( s.total_time_left_moving_9009 ) AS total_time_left_moving_9009,

        SUM ( s.total_time_right_moving_9001 ) AS total_time_right_moving_9001,
        SUM ( s.total_time_right_moving_9004 ) AS total_time_right_moving_9004,
        SUM ( s.total_time_right_moving_9005 ) AS total_time_right_moving_9005,
        SUM ( s.total_time_right_moving_9008 ) AS total_time_right_moving_9008,
        SUM ( s.total_time_right_moving_9009 ) AS total_time_right_moving_9009,

        SUM ( s.oil_pressure_9001 ) AS oil_pressure_9001,
        SUM ( s.oil_pressure_9008 ) AS oil_pressure_9008,
        SUM ( s.oil_pressure_9009 ) AS oil_pressure_9009,

        SUM ( s.pump_total_absorbed_torque_9001 ) AS pump_total_absorbed_torque_9001,
        SUM ( s.pump_total_absorbed_torque_9008 ) AS pump_total_absorbed_torque_9008,
        SUM ( s.pump_total_absorbed_torque_9009 ) AS pump_total_absorbed_torque_9009,
<!-- 2024-12-30 -->
        SUM ( s.pump_motor_rotate_speed_9001 ) AS pump_motor_rotate_speed_9001,
        SUM ( s.pump_motor_rotate_speed_9008 ) AS pump_motor_rotate_speed_9008,
        SUM ( s.pump_motor_rotate_speed_9009 ) AS pump_motor_rotate_speed_9009,

        SUM ( s.charging_status_9008 ) AS charging_status_9008,
        SUM ( s.charging_status_9009 ) AS charging_status_9009,
        SUM ( s.charging_status_9001 ) AS charging_status_9001,

        SUM ( s.charge_time_remain_9001 ) AS charge_time_remain_9001,
        SUM ( s.charge_time_remain_9008 ) AS charge_time_remain_9008,
        SUM ( s.charge_time_remain_9009 ) AS charge_time_remain_9009,

        SUM ( s.single_charge_capacity_9001 ) AS single_charge_capacity_9001,
        SUM ( s.single_charge_capacity_9008 ) AS single_charge_capacity_9008,
        SUM ( s.single_charge_capacity_9009 ) AS single_charge_capacity_9009,

        SUM ( s.day_power_consumption_9001 ) AS day_power_consumption_9001,
        SUM ( s.day_power_consumption_9008 ) AS day_power_consumption_9008,
        SUM ( s.day_power_consumption_9009 ) AS day_power_consumption_9009,
<!-- 2025-01-17 -->
        SUM ( s.action_code_9001 ) AS action_code_9001,
        SUM ( s.action_code_9008 ) AS action_code_9008,
        SUM ( s.action_code_9009 ) AS action_code_9009,

        SUM ( s.total_time_rotation_9001 ) AS total_time_rotation_9001,
        SUM ( s.total_time_rotation_9004 ) AS total_time_rotation_9004,
        SUM ( s.total_time_rotation_9005 ) AS total_time_rotation_9005,
        SUM ( s.total_time_rotation_9008 ) AS total_time_rotation_9008,
        SUM ( s.total_time_rotation_9009 ) AS total_time_rotation_9009,

        SUM ( s.total_no_action_power_consumption_9001 ) AS total_no_action_power_consumption_9001,
        SUM ( s.total_no_action_power_consumption_9004 ) AS total_no_action_power_consumption_9004,
        SUM ( s.total_no_action_power_consumption_9008 ) AS total_no_action_power_consumption_9008,
        SUM ( s.total_no_action_power_consumption_9009 ) AS total_no_action_power_consumption_9009,

        SUM ( s.driving_mileage_9005 ) AS driving_mileage_9005,
        SUM ( s.total_fuel_consumption_9005 ) AS total_fuel_consumption_9005,
        SUM ( s.total_electric_consumption_9005 ) AS total_electric_consumption_9005,
        SUM ( s.working_time_9005 ) AS working_time_9005,
        SUM ( s.engine_worktime_9005 ) AS engine_worktime_9005,
        SUM ( s.travel_speed_9006 ) AS travel_speed_9006,
        SUM ( s.engine_speed_9006 ) AS engine_speed_9006,
        SUM ( s.water_temperature_9006 ) AS water_temperature_9006,
        SUM ( s.fuel_level_9006 ) AS fuel_level_9006,

        MAX ( s.stat_date ) AS endStatDate,
        MIN ( s.stat_date ) AS startStatDate,
        SUM ( CASE WHEN s.device_question_id IS NOT NULL THEN 1 ELSE 0 END ) AS hasRecord,
        SUM ( CASE WHEN s.device_question_id IS NULL THEN 1 ELSE 0 END ) AS notRecord,
        SUM ( CASE WHEN s.device_location_cnt IS NOT NULL THEN s.device_location_cnt ELSE 0 END ) AS deviceLocationCnt,


        SUM ( s.idle_time_idle_fuel_9101 ) AS idle_time_idle_fuel_9101,
        SUM ( s.idle_time_idle_fuel_9102 ) AS idle_time_idle_fuel_9102,
        SUM ( s.idle_time_idle_fuel_9100 ) AS idle_time_idle_fuel_9100,
        SUM ( s.work_time_fuel_9103 ) AS work_time_fuel_9103,
        SUM ( s.work_time_fuel_9104 ) AS work_time_fuel_9104,
        SUM ( s.work_time_fuel_9100 ) AS work_time_fuel_9100,

        SUM ( s.driving_mileage_9003 ) AS driving_mileage_9003,
        SUM ( s.total_fuel_consumption_9003 ) AS total_fuel_consumption_9003,
        SUM ( s.total_electric_consumption_9003 ) AS total_electric_consumption_9003,
        SUM ( s.working_time_9003 ) AS working_time_9003,
        SUM ( s.engine_worktime_9003 ) AS engine_worktime_9003,
        SUM ( s.idel_fuel_fuel_9105 ) AS idel_fuel_fuel_9105,
        SUM ( s.idel_fuel_fuel_9100 ) AS idel_fuel_fuel_9100,
        SUM ( s.idel_time_work_time_9106 ) AS idel_time_work_time_9106,
        SUM ( s.idel_time_work_time_9100 ) AS idel_time_work_time_9100,
        SUM ( s.mileage_speed_9107 ) AS mileage_speed_9107,
        SUM ( s.mileage_speed_9108 ) AS mileage_speed_9108,
        SUM ( s.mileage_speed_9100 ) AS mileage_speed_9100,
        SUM ( s.mileage_location_9109 ) AS mileage_location_9109,
        SUM ( s.mileage_location_9100 ) AS mileage_location_9100,
        SUM ( s.engine_time_fuel_9100 ) AS engine_time_fuel_9100

        FROM
        dqm.ors_device_data_abnormal_stat_day s
        WHERE
        s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        group by s.device_name
        ) t1
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on t1.division_code = od.value
        INNER JOIN dqm.ors_base_device_info bdi ON t1.deviceName = bdi.asset_id AND bdi.device_code is not null
                                                        AND (bdi.exce_flag != 1 or bdi.exce_flag is null)

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.asset_id  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.asset_id in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code IN
            <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.rc_asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.rc_asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        INNER join dqm.ors_model_division omd on omd.model_id = bdi.model_id
        <if test="req.sybbh != null and req.sybbh != ''">
            AND t1.division_code = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND t1.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND omd.product_group_code = #{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND omd.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        left join (
            select asset_id, device_location, stat_date
            from dqm.ors_device_location
            where stat_date::date BETWEEN #{req.createTime_start} AND #{req.createTime_end}
        ) t2 on bdi.asset_id = t2.asset_id AND t2.stat_date::date = t1.startStatDate
        LEFT JOIN (SELECT DISTINCT device_code, 1 AS hasFollowUp FROM dqm.ors_device_question WHERE cur_step != 'close' and del_flag = 0 group by device_code) o ON bdi.device_code = o.device_code

        left join (select DISTINCT ON (device_name) device_name,data_center_id from dqm.ors_device_data_abnormal_stat_day WHERE
            stat_date &gt;= #{req.createTime_start}
            and
            stat_date &lt;= #{req.createTime_end}
            ORDER BY
            device_name,stat_date DESC
        )aa on t1.deviceName = aa.device_name
        <where>
            <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
                t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
            </if>
            <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
                AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
            </if>
            <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
                AND o.hasFollowUp is null
            </if>
            <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
                AND o.hasFollowUp is not null
            </if>
            <if test="req.dataCenterId != null">
                AND aa.data_center_id  =#{req.dataCenterId}
            </if>
        </where>

        order by t1.endStatDate desc
    </select>

    <select id="getDeviceHistoryAbnormalData" resultType="com.rc.admin.ors.quality.model.DqmHistoryDeviceDataExceptionResp">
        select t1.* from (
        select s.device_name as deviceCode ,
        s.param_code as paramCode,
        s.abnormal_code as abnormalCode,
        s.abnormal_data as abnormalData
        from  (select s.device_name ,s.param_code ,s.abnormal_code ,max(s.stat_date) as stat_date from ors_device_data_abnormal_detail_day s
        where s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        and s.param_code is not null
        and s.abnormal_code is not null
        and s.abnormal_code != 9008
        group by s.device_name ,s.param_code ,s.abnormal_code ) t inner join ors_device_data_abnormal_detail_day s
        on s.device_name = t.device_name and s.param_code = t.param_code and s.abnormal_code = t.abnormal_code and s.stat_date = t.stat_date) t1

        left join ors_base_device_info bdi on t1.deviceCode = bdi.asset_id
        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        <if test="req.sybbh != null and req.sybbh != ''">
            AND bdi.division_code  =#{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND bdi.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code  =#{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
        </if>

        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryDeviceDataExceptionsInfoForOneAll" resultType="com.rc.admin.ors.quality.model.DqmHistoryDeviceDataExceptionResp">
        select
        t1.endStatDate ,
        t1.startStatDate ,
        bdi.product_group_code as zehdSpart,
        bdi.product_group as zehdSpartdesc,
        bdi.division as sybbh,
        bdi.region as zehdsvReg,
        bdi.thing_id  as thingId,
        bdi.asset_id as deviceCode,
        bdi.device_code as deviceName,
        bdi.model_id as modelId,
        bdi.model_name as modelName,
        bdi.hw_version as hwVersion,
        bdi.auth_token as authToken,
        bdi.fw_version as fwVersion,
        t1.device_status_9008,
        t1.device_location_9008,
        t1.device_location_9001,
        t1.device_location_9002,
        t1.device_location_9007,
        t1.engine_worktime_9008,
        t1.engine_worktime_9001,
        t1.engine_worktime_9004,
        t1.working_time_9008,
        t1.working_time_9001,
        t1.working_time_9004,
        t1.total_fuel_consumption_9008,
        t1.total_fuel_consumption_9001,
        t1.total_fuel_consumption_9004,
        t1.pumping_volume_9008,
        t1.pumping_volume_9001,
        t1.pumping_volume_9004,
        t1.driving_mileage_9008,
        t1.driving_mileage_9001,
        t1.driving_mileage_9004,
        t1.hasRecord,
        t1.notRecord
        from (select s.device_name as deviceName,
            sum(s.device_status_9008) as device_status_9008
            ,sum(s.device_location_9008) as device_location_9008
            ,sum(s.device_location_9001) as device_location_9001
            ,sum(s.device_location_9002) as device_location_9002
            ,sum(s.device_location_9007) as device_location_9007
            ,sum(s.engine_worktime_9008) as engine_worktime_9008
            ,sum(s.engine_worktime_9001) as engine_worktime_9001
            ,sum(s.engine_worktime_9004) as engine_worktime_9004
            ,sum(s.working_time_9008) as working_time_9008
            ,sum(s.working_time_9001) as working_time_9001
            ,sum(s.working_time_9004) as working_time_9004
            ,sum(s.total_fuel_consumption_9008) as total_fuel_consumption_9008
            ,sum(s.total_fuel_consumption_9001) as total_fuel_consumption_9001
            ,sum(s.total_fuel_consumption_9004) as total_fuel_consumption_9004
            ,sum(s.pumping_volume_9008) as pumping_volume_9008
            ,sum(s.pumping_volume_9001) as pumping_volume_9001
            ,sum(s.pumping_volume_9004) as pumping_volume_9004
            ,sum(s.driving_mileage_9008) as driving_mileage_9008
            ,sum(s.driving_mileage_9001) as driving_mileage_9001
            ,sum(s.driving_mileage_9004) as driving_mileage_9004
            ,max(s.stat_date) as endStatDate,
            min(s.stat_date) as startStatDate,
            sum( case when s.device_question_id is not null then 1 else 0 end ) as hasRecord,
            sum( case when s.device_question_id is null then 1 else 0 end ) as notRecord
        from ors_device_data_abnormal_stat_day s
        where s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        group by s.device_name) t1
        left join ors_base_device_info bdi on t1.deviceName = bdi.asset_id
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' and del_flag = 0 group by o.device_code) o on bdi.device_code = o.device_code

        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
            AND t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
            AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
            AND o.hasFollowUp is null
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
            AND o.hasFollowUp is not null
        </if>
        <if test="req.sybbh != null and req.sybbh != ''">
            AND bdi.division_code  =#{req.sybbh}
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND bdi.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code  =#{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by t1.endStatDate desc
    </select>

    <select id="getHistoryDeviceDataExceptionsInfoForOneCount" resultType="java.lang.Integer">
        select count(t1.* )
        from (select s.device_name as deviceName,
        sum( case when s.device_question_id is not null then 1 else 0 end ) as hasRecord,
        sum( case when s.device_question_id is null then 1 else 0 end ) as notRecord
        from ors_device_data_abnormal_stat_day s

        where s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        group by s.device_name) t1
        inner join ors_base_device_info bdi on t1.deviceName = bdi.asset_id and bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  =#{req.deviceName}
            </if>
            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id =#{req.modelId}
            </if>
            <if test="req.modelIdList != null and req.modelIdList != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.modelName != null and req.modelName != ''">
                AND bdi.model_name =#{req.modelName}
            </if>
            <if test="req.modelNameList != null and req.modelNameList != ''">
                AND bdi.model_name in
                <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id =#{req.deviceCode}
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND bdi.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
                AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
        INNER join dqm.ors_model_division omd on omd.model_id = bdi.model_id
        <if test="req.sybbh != null and req.sybbh != ''">
            AND omd.division_code = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND omd.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND omd.product_group_code = #{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND omd.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' and del_flag = 0 group by o.device_code) o on bdi.device_code = o.device_code

        left join (select DISTINCT ON (device_name) device_name,data_center_id from dqm.ors_device_data_abnormal_stat_day WHERE
        stat_date &gt;= #{req.createTime_start}
        and
        stat_date &lt;= #{req.createTime_end}
        ORDER BY
        device_name,stat_date DESC
        )aa on t1.deviceName = aa.device_name
        <where>
            <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
                t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
            </if>
            <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
                AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
            </if>
            <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
                AND o.hasFollowUp is null
            </if>
            <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
                AND o.hasFollowUp is not null
            </if>
            <if test="req.dataCenterId != null">
                AND aa.data_center_id  =#{req.dataCenterId}
            </if>
        </where>
    </select>

    <select id="getHistoryDeviceDataExceptionsInfoForListAll" resultType="com.rc.admin.ors.quality.model.DqmHistoryDeviceDataExceptionResp">
        select
            t1.endStatDate ,
            t1.startStatDate ,
            bdi.product_group_code as zehdSpart,
            bdi.product_group as zehdSpartdesc,
            bdi.division as sybbh,
            bdi.region as zehdsvReg,
            bdi.thing_id  as thingId,
            bdi.asset_id as deviceCode,
            bdi.device_code as deviceName,
            bdi.model_id as modelId,
            bdi.model_name as modelName,
            bdi.hw_version as hwVersion,
            bdi.auth_token as authToken,
            bdi.fw_version as fwVersion,
            t1.device_status_9008,
            t1.device_location_9008,
            t1.device_location_9001,
            t1.device_location_9002,
            t1.device_location_9007,
            t1.engine_worktime_9008,
            t1.engine_worktime_9001,
            t1.engine_worktime_9004,
            t1.working_time_9008,
            t1.working_time_9001,
            t1.working_time_9004,
            t1.total_fuel_consumption_9008,
            t1.total_fuel_consumption_9001,
            t1.total_fuel_consumption_9004,
            t1.pumping_volume_9008,
            t1.pumping_volume_9001,
            t1.pumping_volume_9004,
            t1.driving_mileage_9008,
            t1.driving_mileage_9001,
            t1.driving_mileage_9004,
            t1.hasRecord,
            t1.notRecord,
            t1.device_location_9001_data,
            t1.device_location_9002_data,
            t1.device_location_9007_data,
            t1.engine_worktime_9001_data,
            t1.engine_worktime_9004_data,
            t1.working_time_9001_data,
            t1.working_time_9004_data,
            t1.total_fuel_consumption_9001_data,
            t1.total_fuel_consumption_9004_data,
            t1.pumping_volume_9001_data,
            t1.pumping_volume_9004_data,
            t1.driving_mileage_9001_data,
            t1.driving_mileage_9004_data,
            t1.device_location_9001_data,
            t1.device_location_9002_data,
            t1.device_location_9007_data,
            t1.engine_worktime_9001_data,
            t1.engine_worktime_9004_data,
            t1.working_time_9001_data,
            t1.working_time_9004_data,
            t1.total_fuel_consumption_9001_data,
            t1.total_fuel_consumption_9004_data,
            t1.pumping_volume_9001_data,
            t1.pumping_volume_9004_data,
            t1.driving_mileage_9001_data,
            t1.driving_mileage_9004_data
        from (
            select
                s.device_name as deviceName
                ,s.device_status_9008
                ,s.device_location_9008
                ,s.device_location_9001
                ,s.device_location_9002
                ,s.device_location_9007
                ,s.engine_worktime_9008
                ,s.engine_worktime_9001
                ,s.engine_worktime_9004
                ,s.working_time_9008
                ,s.working_time_9001
                ,s.working_time_9004
                ,s.total_fuel_consumption_9008
                ,s.total_fuel_consumption_9001
                ,s.total_fuel_consumption_9004
                ,s.pumping_volume_9008
                ,s.pumping_volume_9001
                ,s.pumping_volume_9004
                ,s.driving_mileage_9008
                ,s.driving_mileage_9001
                ,s.driving_mileage_9004
                ,s.device_location_9001_data
                ,s.device_location_9002_data
                ,s.device_location_9007_data
                ,s.engine_worktime_9001_data
                ,s.engine_worktime_9004_data
                ,s.working_time_9001_data
                ,s.working_time_9004_data
                ,s.total_fuel_consumption_9001_data
                ,s.total_fuel_consumption_9004_data
                ,s.pumping_volume_9001_data
                ,s.pumping_volume_9004_data
                ,s.driving_mileage_9001_data
                ,s.driving_mileage_9004_data
                ,s.stat_date as endStatDate,
                s.stat_date as startStatDate,
                 case when s.device_question_id is not null then 1 else 0 end  hasRecord,
                 case when s.device_question_id is null then 1 else 0 end  notRecord
            from ors_device_data_abnormal_stat_day s
            where s.stat_date &gt;= #{req.createTime_start}
            AND s.stat_date &lt;= #{req.createTime_end}
        ) t1
        left join ors_base_device_info bdi on t1.deviceName = bdi.asset_id
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' and del_flag=0 group by o.device_code) o on bdi.device_code = o.device_code

        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
            AND t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
            AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
            AND o.hasFollowUp is null
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
            AND o.hasFollowUp is not null
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.sybbh != null and req.sybbh != ''">
            AND bdi.division_code  =#{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND bdi.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code  =#{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by bdi.region_code,bdi.product_group_code,t1.deviceName ,t1.endStatDate desc
    </select>


    <select id="getModelList" resultType="java.lang.String">
        select
            pc.param_code
        from ors_device_data_abnormal_stat_day s
        left join ors_base_device_info bdi on s.device_name = bdi.asset_id
        INNER join dqm.ors_model_division omd on omd.model_id = bdi.model_id
        <if test="req.sybbh != null and req.sybbh != ''">
            AND omd.division_code = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND omd.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND omd.product_group_code = #{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND omd.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        left join dqm.ors_model_properties_config pc
        on pc.model_id = bdi.model_id
        left join (
        SELECT
        asset_id,
        CASE
        WHEN MAX(CASE WHEN device_location = '国外' THEN 1 ELSE 0 END) = 1 THEN '国外'
        ELSE '国内'
        END AS device_location
        FROM
        dqm.ors_device_location
        WHERE
        stat_date::DATE BETWEEN #{req.createTime_start} AND #{req.createTime_end}
        GROUP BY
        asset_id
        ) t2 on bdi.asset_id = t2.asset_id
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' group by o.device_code) o on bdi.device_code = o.device_code
        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        and
        s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        <if test="req.dataCenterId != null">
            AND s.data_center_id  =#{req.dataCenterId}
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
            AND o.hasFollowUp is null
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
            AND o.hasFollowUp is not null
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code IN
            <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.rc_asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.rc_asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by pc.param_code
    </select>





    <select id="getHistoryDeviceDataExceptionsInfoForList" resultType="com.rc.admin.ors.quality.model.DqmHistoryDeviceDataExceptionResp">
        select
            case when o.hasFollowUp is not null then 1 else 0 end hasFollowUp,
            t1.endStatDate,
            t1.startStatDate,
            coalesce(t2.device_location,'国外') as device_location,
            omd.product_group_code as zehdSpart,
            omd.product_group_name as zehdSpartdesc,
            od.name as sybbh,
            t1.division_code as sybbhCode,
            t1.data_center_id as dataCenterId,
            bdi.thing_id  as thingId,
            bdi.rc_asset_id as deviceCode,
            bdi.device_code as deviceName,
            bdi.model_id as modelId,
            bdi.model_name as modelName,
            bdi.hw_version as hwVersion,
            bdi.auth_token as authToken,
            bdi.fw_version as fwVersion,
            bdi.country as country,
            t1.device_status_9008,
            t1.device_status_9009,
            t1.device_location_9008,
            t1.device_location_9001,
            t1.device_location_9002,
            t1.device_location_9007,
            t1.device_location_9009,
            t1.engine_worktime_9008,
            t1.engine_worktime_9001,
            t1.engine_worktime_9004,
            t1.engine_worktime_9009,
            t1.working_time_9008,
            t1.working_time_9001,
            t1.working_time_9004,
            t1.working_time_9009,
            t1.total_fuel_consumption_9008,
            t1.total_fuel_consumption_9001,
            t1.total_fuel_consumption_9004,
            t1.total_fuel_consumption_9009,
            t1.pumping_volume_9008,
            t1.pumping_volume_9001,
            t1.pumping_volume_9004,
            t1.pumping_volume_9009,
            t1.driving_mileage_9008,
            t1.driving_mileage_9001,
            t1.driving_mileage_9004,
            t1.driving_mileage_9009,

            t1.fuel_level_9008,
            t1.fuel_level_9001,
            t1.fuel_level_9009,
            t1.engine_speed_9008,
            t1.engine_speed_9001,
            t1.engine_speed_9009,
            t1.water_temperature_9008,
            t1.water_temperature_9001,
            t1.water_temperature_9009,
            t1.SOC_stateofcharge_9008,
            t1.SOC_stateofcharge_9001,
            t1.SOC_stateofcharge_9009,
            t1.travel_speed_9008,
            t1.travel_speed_9001,
            t1.travel_speed_9009,
            t1.total_electric_consumption_9008,
            t1.total_electric_consumption_9001,
            t1.total_electric_consumption_9004,
            t1.total_electric_consumption_9009,


            t1.total_idle_fuel_consumption_9001,
            t1.total_idle_fuel_consumption_9004,
            t1.total_idle_fuel_consumption_9005,
            t1.total_idle_fuel_consumption_9008,
            t1.total_idle_fuel_consumption_9009,
            t1.total_idle_time_9001,
            t1.total_idle_time_9004,
            t1.total_idle_time_9005,
            t1.gear_9008,
            t1.gear_9009,
            t1.gear_9001,
            t1.total_idle_time_9008,
            t1.total_idle_time_9009,
            t1.total_time_left_moving_9001,
            t1.total_time_left_moving_9004,
            t1.total_time_left_moving_9005,
            t1.total_time_left_moving_9008,
            t1.total_time_left_moving_9009,
            t1.total_time_right_moving_9001,
            t1.total_time_right_moving_9004,
            t1.total_time_right_moving_9005,
            t1.total_time_right_moving_9008,
            t1.total_time_right_moving_9009,
            t1.oil_pressure_9001,
            t1.oil_pressure_9008,
            t1.oil_pressure_9009,
            t1.pump_total_absorbed_torque_9001,
            t1.pump_total_absorbed_torque_9008,
            t1.pump_total_absorbed_torque_9009,
<!-- 2024-12-30 -->
            t1.pump_motor_rotate_speed_9001,
            t1.pump_motor_rotate_speed_9008,
            t1.pump_motor_rotate_speed_9009,
            t1.charging_status_9008,
            t1.charging_status_9009,
            t1.charging_status_9001,
            t1.charge_time_remain_9001,
            t1.charge_time_remain_9008,
            t1.charge_time_remain_9009,
            t1.single_charge_capacity_9001,
            t1.single_charge_capacity_9008,
            t1.single_charge_capacity_9009,
            t1.day_power_consumption_9001,
            t1.day_power_consumption_9008,
            t1.day_power_consumption_9009,

<!-- 2025-01-17 -->
            t1.action_code_9001,
            t1.action_code_9008,
            t1.action_code_9009,
            t1.total_time_rotation_9001,
            t1.total_time_rotation_9004,
            t1.total_time_rotation_9005,
            t1.total_time_rotation_9008,
            t1.total_time_rotation_9009,

            t1.total_no_action_power_consumption_9001,
            t1.total_no_action_power_consumption_9004,
            t1.total_no_action_power_consumption_9008,
            t1.total_no_action_power_consumption_9009,


            t1.driving_mileage_9005,
            t1.total_fuel_consumption_9005,
            t1.total_electric_consumption_9005,
            t1.working_time_9005,
            t1.engine_worktime_9005,
            t1.travel_speed_9006,
            t1.engine_speed_9006,
            t1.water_temperature_9006,
            t1.fuel_level_9006,

            t1.hasRecord,
            t1.notRecord,
            t1.deviceLocationCnt,

            t1.idle_time_idle_fuel_9101,
            t1.idle_time_idle_fuel_9102,
            t1.idle_time_idle_fuel_9100,
            t1.work_time_fuel_9103,
            t1.work_time_fuel_9104,
            t1.work_time_fuel_9100,

            t1.driving_mileage_9003,
            t1.total_fuel_consumption_9003,
            t1.total_electric_consumption_9003,
            t1.working_time_9003,
            t1.engine_worktime_9003,

            t1.idel_fuel_fuel_9105,
            t1.idel_fuel_fuel_9100,
            t1.idel_time_work_time_9106,
            t1.idel_time_work_time_9100,
            t1.mileage_speed_9107,
            t1.mileage_speed_9108,
            t1.mileage_speed_9100,
            t1.mileage_location_9109,
            t1.mileage_location_9100,
            t1.engine_time_fuel_9100
        from (
            select s.device_name as deviceName,
                   max(s.data_center_id) as data_center_id,
                   max(s.division_code) as division_code,
                sum(s.device_status_9008) as device_status_9008
                ,sum(s.device_status_9009) as device_status_9009
                ,sum(s.device_location_9008) as device_location_9008
                ,sum(s.device_location_9001) as device_location_9001
                ,sum(s.device_location_9002) as device_location_9002
                ,sum(s.device_location_9007) as device_location_9007
                ,sum(s.device_location_9009) as device_location_9009
                ,sum(s.engine_worktime_9008) as engine_worktime_9008
                ,sum(s.engine_worktime_9001) as engine_worktime_9001
                ,sum(s.engine_worktime_9004) as engine_worktime_9004
                ,sum(s.engine_worktime_9009) as engine_worktime_9009
                ,sum(s.working_time_9008) as working_time_9008
                ,sum(s.working_time_9001) as working_time_9001
                ,sum(s.working_time_9004) as working_time_9004
                ,sum(s.working_time_9009) as working_time_9009
                ,sum(s.total_fuel_consumption_9008) as total_fuel_consumption_9008
                ,sum(s.total_fuel_consumption_9001) as total_fuel_consumption_9001
                ,sum(s.total_fuel_consumption_9004) as total_fuel_consumption_9004
                ,sum(s.total_fuel_consumption_9009) as total_fuel_consumption_9009
                ,sum(s.pumping_volume_9008) as pumping_volume_9008
                ,sum(s.pumping_volume_9001) as pumping_volume_9001
                ,sum(s.pumping_volume_9004) as pumping_volume_9004
                ,sum(s.pumping_volume_9009) as pumping_volume_9009
                ,sum(s.driving_mileage_9008) as driving_mileage_9008
                ,sum(s.driving_mileage_9001) as driving_mileage_9001
                ,sum(s.driving_mileage_9004) as driving_mileage_9004
                ,sum(s.driving_mileage_9009) as driving_mileage_9009,

        SUM ( s.fuel_level_9008 ) AS fuel_level_9008,
        SUM ( s.fuel_level_9001 ) AS fuel_level_9001,
        SUM ( s.fuel_level_9009 ) AS fuel_level_9009,
        SUM ( s.engine_speed_9008 ) AS engine_speed_9008,
        SUM ( s.engine_speed_9001 ) AS engine_speed_9001,
        SUM ( s.engine_speed_9009 ) AS engine_speed_9009,
        SUM ( s.water_temperature_9008 ) AS water_temperature_9008,
        SUM ( s.water_temperature_9001 ) AS water_temperature_9001,
        SUM ( s.water_temperature_9009 ) AS water_temperature_9009,
        SUM ( s.SOC_stateofcharge_9008 ) AS SOC_stateofcharge_9008,
        SUM ( s.SOC_stateofcharge_9001 ) AS SOC_stateofcharge_9001,
        SUM ( s.SOC_stateofcharge_9009 ) AS SOC_stateofcharge_9009,
        SUM ( s.travel_speed_9008 ) AS travel_speed_9008,
        SUM ( s.travel_speed_9001 ) AS travel_speed_9001,
        SUM ( s.travel_speed_9009 ) AS travel_speed_9009,
        SUM ( s.total_electric_consumption_9008 ) AS total_electric_consumption_9008,
        SUM ( s.total_electric_consumption_9001 ) AS total_electric_consumption_9001,
        SUM ( s.total_electric_consumption_9004 ) AS total_electric_consumption_9004,
        SUM ( s.total_electric_consumption_9009 ) AS total_electric_consumption_9009,

                SUM ( s.total_idle_fuel_consumption_9001 ) AS total_idle_fuel_consumption_9001,
                SUM ( s.total_idle_fuel_consumption_9004 ) AS total_idle_fuel_consumption_9004,
                SUM ( s.total_idle_fuel_consumption_9005 ) AS total_idle_fuel_consumption_9005,
                SUM ( s.total_idle_fuel_consumption_9008 ) AS total_idle_fuel_consumption_9008,
                SUM ( s.total_idle_fuel_consumption_9009 ) AS total_idle_fuel_consumption_9009,

                SUM ( s.total_idle_time_9001 ) AS total_idle_time_9001,
                SUM ( s.total_idle_time_9004 ) AS total_idle_time_9004,
                SUM ( s.total_idle_time_9005 ) AS total_idle_time_9005,
                SUM ( s.total_idle_time_9008 ) AS total_idle_time_9008,
                SUM ( s.total_idle_time_9009 ) AS total_idle_time_9009,

                SUM ( s.gear_9008 ) AS gear_9008,
                SUM ( s.gear_9009 ) AS gear_9009,
                SUM ( s.gear_9001 ) AS gear_9001,

                SUM ( s.total_time_left_moving_9001 ) AS total_time_left_moving_9001,
                SUM ( s.total_time_left_moving_9004 ) AS total_time_left_moving_9004,
                SUM ( s.total_time_left_moving_9005 ) AS total_time_left_moving_9005,
                SUM ( s.total_time_left_moving_9008 ) AS total_time_left_moving_9008,
                SUM ( s.total_time_left_moving_9009 ) AS total_time_left_moving_9009,

                SUM ( s.total_time_right_moving_9001 ) AS total_time_right_moving_9001,
                SUM ( s.total_time_right_moving_9004 ) AS total_time_right_moving_9004,
                SUM ( s.total_time_right_moving_9005 ) AS total_time_right_moving_9005,
                SUM ( s.total_time_right_moving_9008 ) AS total_time_right_moving_9008,
                SUM ( s.total_time_right_moving_9009 ) AS total_time_right_moving_9009,

                SUM ( s.oil_pressure_9001 ) AS oil_pressure_9001,
                SUM ( s.oil_pressure_9008 ) AS oil_pressure_9008,
                SUM ( s.oil_pressure_9009 ) AS oil_pressure_9009,
<!-- 2024-12-30 -->
            SUM ( s.pump_total_absorbed_torque_9001 ) AS pump_total_absorbed_torque_9001,
            SUM ( s.pump_total_absorbed_torque_9008 ) AS pump_total_absorbed_torque_9008,
            SUM ( s.pump_total_absorbed_torque_9009 ) AS pump_total_absorbed_torque_9009,

            SUM ( s.pump_motor_rotate_speed_9001 ) AS pump_motor_rotate_speed_9001,
            SUM ( s.pump_motor_rotate_speed_9008 ) AS pump_motor_rotate_speed_9008,
            SUM ( s.pump_motor_rotate_speed_9009 ) AS pump_motor_rotate_speed_9009,

            SUM ( s.charging_status_9008 ) AS charging_status_9008,
            SUM ( s.charging_status_9009 ) AS charging_status_9009,
            SUM ( s.charging_status_9001 ) AS charging_status_9001,

            SUM ( s.charge_time_remain_9001 ) AS charge_time_remain_9001,
            SUM ( s.charge_time_remain_9008 ) AS charge_time_remain_9008,
            SUM ( s.charge_time_remain_9009 ) AS charge_time_remain_9009,

            SUM ( s.single_charge_capacity_9001 ) AS single_charge_capacity_9001,
            SUM ( s.single_charge_capacity_9008 ) AS single_charge_capacity_9008,
            SUM ( s.single_charge_capacity_9009 ) AS single_charge_capacity_9009,

            SUM ( s.day_power_consumption_9001 ) AS day_power_consumption_9001,
            SUM ( s.day_power_consumption_9008 ) AS day_power_consumption_9008,
            SUM ( s.day_power_consumption_9009 ) AS day_power_consumption_9009,

<!-- 2025-01-17 -->
            SUM ( s.action_code_9001 ) AS action_code_9001,
            SUM ( s.action_code_9008 ) AS action_code_9008,
            SUM ( s.action_code_9009 ) AS action_code_9009,

            SUM ( s.total_time_rotation_9001 ) AS total_time_rotation_9001,
            SUM ( s.total_time_rotation_9004 ) AS total_time_rotation_9004,
            SUM ( s.total_time_rotation_9005 ) AS total_time_rotation_9005,
            SUM ( s.total_time_rotation_9008 ) AS total_time_rotation_9008,
            SUM ( s.total_time_rotation_9009 ) AS total_time_rotation_9009,

        SUM ( s.total_no_action_power_consumption_9001 ) AS total_no_action_power_consumption_9001,
        SUM ( s.total_no_action_power_consumption_9004 ) AS total_no_action_power_consumption_9004,
        SUM ( s.total_no_action_power_consumption_9008 ) AS total_no_action_power_consumption_9008,
        SUM ( s.total_no_action_power_consumption_9009 ) AS total_no_action_power_consumption_9009,

        SUM ( s.driving_mileage_9005 ) AS driving_mileage_9005,
        SUM ( s.total_fuel_consumption_9005 ) AS total_fuel_consumption_9005,
        SUM ( s.total_electric_consumption_9005 ) AS total_electric_consumption_9005,
        SUM ( s.working_time_9005 ) AS working_time_9005,
        SUM ( s.engine_worktime_9005 ) AS engine_worktime_9005,
        SUM ( s.travel_speed_9006 ) AS travel_speed_9006,
        SUM ( s.engine_speed_9006 ) AS engine_speed_9006,
        SUM ( s.water_temperature_9006 ) AS water_temperature_9006,
        SUM ( s.fuel_level_9006 ) AS fuel_level_9006,

        SUM ( s.driving_mileage_9003 ) AS driving_mileage_9003,
        SUM ( s.total_fuel_consumption_9003 ) AS total_fuel_consumption_9003,
        SUM ( s.total_electric_consumption_9003 ) AS total_electric_consumption_9003,
        SUM ( s.working_time_9003 ) AS working_time_9003,
        SUM ( s.engine_worktime_9003 ) AS engine_worktime_9003,
        SUM ( s.idel_fuel_fuel_9105 ) AS idel_fuel_fuel_9105,
        SUM ( s.idel_fuel_fuel_9100 ) AS idel_fuel_fuel_9100,
        SUM ( s.idel_time_work_time_9106 ) AS idel_time_work_time_9106,
        SUM ( s.idel_time_work_time_9100 ) AS idel_time_work_time_9100,
        SUM ( s.mileage_speed_9107 ) AS mileage_speed_9107,
        SUM ( s.mileage_speed_9108 ) AS mileage_speed_9108,
        SUM ( s.mileage_speed_9100 ) AS mileage_speed_9100,
        SUM ( s.mileage_location_9109 ) AS mileage_location_9109,
        SUM ( s.mileage_location_9100 ) AS mileage_location_9100,

        max(s.stat_date) as endStatDate,
                min(s.stat_date) as startStatDate,
                sum( case when s.device_question_id is not null then 1 else 0 end ) as hasRecord,
                sum( case when s.device_question_id is null then 1 else 0 end ) as notRecord,
                SUM ( CASE WHEN s.device_location_cnt IS NOT NULL THEN s.device_location_cnt ELSE 0 END ) AS deviceLocationCnt,

                SUM ( s.idle_time_idle_fuel_9101 ) AS idle_time_idle_fuel_9101,
                SUM ( s.idle_time_idle_fuel_9102 ) AS idle_time_idle_fuel_9102,
                SUM ( s.idle_time_idle_fuel_9100 ) AS idle_time_idle_fuel_9100,
                SUM ( s.work_time_fuel_9103 ) AS work_time_fuel_9103,
                SUM ( s.work_time_fuel_9104 ) AS work_time_fuel_9104,
                SUM ( s.work_time_fuel_9100 ) AS work_time_fuel_9100,
                SUM ( s.engine_time_fuel_9100 ) AS engine_time_fuel_9100
        from ors_device_data_abnormal_stat_day s

            where s.stat_date &gt;= #{req.createTime_start}
            AND s.stat_date &lt;= #{req.createTime_end}
            group by s.device_name,s.stat_date
        ) t1
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name)od on t1.division_code = od.value
        left join ors_base_device_info bdi on t1.deviceName = bdi.asset_id
        INNER join dqm.ors_model_division omd on omd.model_id = bdi.model_id
        <if test="req.sybbh != null and req.sybbh != ''">
            AND t1.division_code = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND t1.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND omd.product_group_code = #{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND omd.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        left join (
            SELECT
                asset_id,
                CASE
                    WHEN MAX(CASE WHEN device_location = '国外' THEN 1 ELSE 0 END) = 1 THEN '国外'
                ELSE '国内'
                    END AS device_location
            FROM
                dqm.ors_device_location
            WHERE
                stat_date::DATE BETWEEN #{req.createTime_start} AND #{req.createTime_end}
            GROUP BY
                asset_id
        ) t2 on bdi.asset_id = t2.asset_id
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' group by o.device_code) o on bdi.device_code = o.device_code
        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        <if test="req.dataCenterId != null">
            AND t1.data_center_id  =#{req.dataCenterId}
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
            AND t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
            AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
            AND o.hasFollowUp is null
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
            AND o.hasFollowUp is not null
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code IN
            <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.rc_asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.rc_asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by bdi.region_code,bdi.product_group_code,t1.deviceName ,t1.endStatDate desc
        limit #{req.pageSize} OFFSET #{req.current}
    </select>

    <select id="getHistoryDeviceDataExceptionsInfoForListCount" resultType="java.lang.Integer">
        select count(t1.* )
        from (select s.device_name as deviceName,
        s.stat_date as endStatDate,
        s.stat_date as startStatDate,
        max(s.division_code) as division_code,
        max(s.data_center_id) as data_center_id,
        sum( case when s.device_question_id is not null then 1 else 0 end ) as hasRecord,
        sum( case when s.device_question_id is null then 1 else 0 end ) as notRecord
        from ors_device_data_abnormal_stat_day s

        where s.stat_date &gt;= #{req.createTime_start}
        AND s.stat_date &lt;= #{req.createTime_end}
        group by s.device_name,s.stat_date) t1
        left join ors_base_device_info bdi on t1.deviceName = bdi.asset_id
        left join (select o.device_code,'1' as hasFollowUp  from ors_device_question o where o.cur_step != 'close' and del_flag=0 group by o.device_code) o on bdi.device_code = o.device_code
        INNER join dqm.ors_model_division omd on omd.model_id = bdi.model_id
        <if test="req.sybbh != null and req.sybbh != ''">
            AND t1.division_code = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbhList != ''">
            AND t1.division_code in
            <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND omd.product_group_code = #{req.zehdSpartdesc}
        </if>
        <if test="req.zehdSpartdescList != null and req.zehdSpartdescList != ''">
            AND omd.product_group_code in
            <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        left join (
            SELECT
                asset_id,
            CASE
                WHEN MAX(CASE WHEN device_location = '国外' THEN 1 ELSE 0 END) = 1 THEN '国外'
            ELSE '国内'
                END AS device_location
            FROM
                dqm.ors_device_location
            WHERE
                stat_date::DATE BETWEEN #{req.createTime_start} AND #{req.createTime_end}
            GROUP BY
                asset_id
        ) t2 on bdi.asset_id = t2.asset_id
        where bdi.device_code is not null and  (bdi.exce_flag != 1 or bdi.exce_flag is null)
        <if test="req.dataCenterId != null">
            AND t1.data_center_id  =#{req.dataCenterId}
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '1'.toString()">
            AND t1.hasRecord &gt; 0 and t1.notRecord &gt;= 0
        </if>
        <if test="req.handle != null and req.handle != '' and req.handle == '3'.toString()">
            AND t1.hasRecord &gt;= 0 and t1.notRecord &gt; 0
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '0'.toString()">
            AND o.hasFollowUp is null
        </if>
        <if test="req.hasFollowUp != null and req.hasFollowUp != '' and req.hasFollowUp == '1'.toString()">
            AND o.hasFollowUp is not null
        </if>

        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code  =#{req.deviceName}
        </if>
        <if test="req.deviceNameList != null and req.deviceNameList != ''">
            AND bdi.device_code in
            <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code IN
            <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.modelId != null and req.modelId != ''">
            AND bdi.model_id =#{req.modelId}
        </if>
        <if test="req.modelIdList != null and req.modelIdList != ''">
            AND bdi.model_id in
            <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.modelName != null and req.modelName != ''">
            AND bdi.model_name =#{req.modelName}
        </if>
        <if test="req.modelNameList != null and req.modelNameList != ''">
            AND bdi.model_name in
            <foreach collection="req.modelNameList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.deviceCode != null and req.deviceCode != ''">
            AND bdi.asset_id =#{req.deviceCode}
        </if>

        <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
            AND bdi.asset_id in
            <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getDeviceDataExceptionsInfoForMSE" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp">
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope == 'new'">
            SELECT
            t4.thing_id  as thingId,
            t4.asset_id as deviceCode,
            t4.device_code as deviceName,
            t4.model_id as modelId,
            t4.model_name as modelName,
            t1.abnormal_name as abnormalName,
            t1.abnormal_data as abnormalData,
            t1.abnormal_time as abnormalTime,
            t1.property ,
            t1.property_name as propertyName,
            t4.product_group_code as zehdSpart,
            t4.product_group as zehdSpartdesc,
            t4.division as sybbh,
            t4.region as zehdsvReg,
            t4.hw_version as hwVersion,
            t4.auth_token as authToken,
            t4.fw_version as fwVersion
            from (
            SELECT
            *
            from ors_device_data_abnormal_detail_day where (id ) in  (
            select t2.id from (
            SELECT
            device_name,property,param_code,abnormal_code,
            max(s.id) as id
            FROM
            ors_device_data_abnormal_detail_day s
            WHERE
            1 = 1
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND s.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list != null and list != ''">
                AND s.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            AND s.stat_date &gt;= #{req.createTime_start}
            AND s.stat_date &lt;= #{req.createTime_end}
            AND s.abnormal_code !='9004' AND s.abnormal_code != '9007'
            GROUP BY device_name,property,param_code,abnormal_code
            )t2 left join ors_base_device_info bdi on t2.device_name = bdi.asset_id
            left join ors_monthly_shipment_equipment mse on bdi.device_code = mse.device_name and mse.import_time = #{req.yearMonthTime}
            where bdi.active_statu is true and mse.device_name is not null
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>

            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id like CONCAT('%',#{req.deviceCode},'%')
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            )
            )  t1
            left join ors_base_device_info AS t4 ON t1.device_name = t4.asset_id
            limit #{req.pageSize} OFFSET #{req.current}
        </if>
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope != 'new'">
            select
            bdi.thing_id  as thingId,
            bdi.asset_id as deviceCode,
            bdi.device_code as deviceName,
            bdi.model_id as modelId,
            bdi.model_name as modelName,
            oddad.abnormal_name as abnormalName,
            oddad.abnormal_data as abnormalData,
            oddad.abnormal_time as abnormalTime,
            oddad.property ,
            oddad.property_name as propertyName,
            bdi.product_group_code as zehdSpart,
            bdi.product_group as zehdSpartdesc,
            bdi.division as sybbh,
            bdi.region as zehdsvReg,
            bdi.hw_version as hwVersion,
            bdi.auth_token as authToken,
            bdi.fw_version as fwVersion

            from ors_device_data_abnormal_detail as oddad
            left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name and  bdi.active_statu is true and oddad.abnormal_effective=1
            left join ors_monthly_shipment_equipment mse on bdi.device_code = mse.device_name and mse.import_time = #{req.yearMonthTime}
            WHERE bdi.active_statu is true and mse.device_name is not null and oddad.abnormal_code != '9004' and oddad.abnormal_code != '9007'
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>

            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id =#{req.deviceCode}
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND oddad.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="list != null and list != ''">
                AND oddad.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND oddad.stat_date &gt;= #{req.createTime_start}
                AND oddad.stat_date &lt;= #{req.createTime_end}
            </if>
            limit #{req.pageSize} OFFSET #{req.current}
        </if>
    </select>

    <select id="getDeviceDataExceptionsInfoCount" resultType="java.lang.Integer">
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope == 'new'">
            SELECT
             count(1)
              from (
            SELECT
            *
            from ors_device_data_abnormal_detail_day where (id ) in  (
            select t2.id from (
            SELECT
            device_name,property,param_code,abnormal_code,
            max(s.id) as id
            FROM
            ors_device_data_abnormal_detail_day s
            WHERE
            1 = 1
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND s.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list != null and list != ''">
                AND s.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
                AND s.stat_date &gt;= #{req.createTime_start}
                AND s.stat_date &lt;= #{req.createTime_end}
            GROUP BY device_name,property,param_code,abnormal_code
            )t2 left join (select
            *
            from ors_base_device_info ) bdi on t2.device_name = bdi.asset_id
            where bdi.active_statu is true
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND bdi.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>
            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id like CONCAT('%',#{req.deviceCode},'%')
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            )
            )  t1
        </if>

        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope != 'new'">
            select
            count(1)
            from ors_device_data_abnormal_detail as oddad
            left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name and  bdi.active_statu is true and oddad.abnormal_effective=1
            WHERE bdi.active_statu is true
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND bdi.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>
            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id =#{req.deviceCode}
            </if>
            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND oddad.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="list != null and list != ''">
                AND oddad.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND oddad.stat_date &gt;= #{req.createTime_start}
                AND oddad.stat_date &lt;= #{req.createTime_end}
            </if>

        </if>


    </select>

    <select id="getDeviceDataExceptionsInfoCountForMSE" resultType="java.lang.Integer">
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope == 'new'">
            SELECT
            count(t1.*)

            from (
            SELECT
            *
            from ors_device_data_abnormal_detail_day where (id ) in  (
            select t2.id from (
            SELECT
            device_name,property,param_code,abnormal_code,
            max(s.id) as id
            FROM
            ors_device_data_abnormal_detail_day s
            WHERE
            1 = 1
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND s.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list != null and list != ''">
                AND s.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            AND s.stat_date &gt;= #{req.createTime_start}
            AND s.stat_date &lt;= #{req.createTime_end}
            AND s.abnormal_code !='9004' AND s.abnormal_code != '9007'
            GROUP BY device_name,property,param_code,abnormal_code
            )t2 left join ors_base_device_info bdi on t2.device_name = bdi.asset_id
            left join ors_monthly_shipment_equipment mse on bdi.device_code = mse.device_name and mse.import_time = #{req.yearMonthTime}
            where bdi.active_statu is true and mse.device_name is not null

            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>

            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id like CONCAT('%',#{req.deviceCode},'%')
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            )
            )  t1
            left join ors_base_device_info AS t4 ON t1.device_name = t4.asset_id

        </if>
        <if test="req.dataScope != null and req.dataScope != '' and req.dataScope != 'new'">
            select
            count(oddad.*)

            from ors_device_data_abnormal_detail as oddad
            left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name and  bdi.active_statu is true and oddad.abnormal_effective=1
            left join ors_monthly_shipment_equipment mse on bdi.device_code = mse.device_name and mse.import_time = #{req.yearMonthTime}
            WHERE bdi.active_statu is true and mse.device_name is not null and oddad.abnormal_code != '9004' and oddad.abnormal_code != '9007'
            <if test="req.sybbh != null and req.sybbh != ''">
                AND bdi.division_code in
                <foreach collection="req.sybbhList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND bdi.region_code in
                <foreach collection="req.zehdsvRegList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.agentName != null and req.agentName!=''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code  like CONCAT('%',#{req.deviceName},'%')
            </if>

            <if test="req.deviceNameList != null and req.deviceNameList != ''">
                AND bdi.device_code in
                <foreach collection='req.deviceNameList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalDeviceKey != null and req.abnormalDeviceKey != ''">
                AND bdi.asset_id in
                <foreach collection="listName" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.modelId != null and req.modelId != ''">
                AND bdi.model_id in
                <foreach collection="req.modelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null and req.deviceCode != ''">
                AND bdi.asset_id =#{req.deviceCode}
            </if>

            <if test="req.deviceCodeList != null and req.deviceCodeList != ''">
                AND bdi.asset_id in
                <foreach collection='req.deviceCodeList' item='item' separator=',' open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND oddad.param_code in
                <foreach collection="paramCode" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="list != null and list != ''">
                AND oddad.abnormal_code in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installType}
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND oddad.stat_date &gt;= #{req.createTime_start}
                AND oddad.stat_date &lt;= #{req.createTime_end}
            </if>

        </if>

    </select>


    <select id="getDataQualityReport" resultType="java.util.Map">

        SELECT distinct
         bdi.asset_id
        ,bdi.model_id 	--物模型
        ,bdi.asset_id as device_name			--设备编号
        ,CASE WHEN bdi.crm_register =0 OR bdi.crm_register is null THEN '0' ELSE '1'  END AS auto_regist_new --注册
        ,CASE WHEN ddad.device_name IS NULL THEN '0' ELSE '1'  END AS is_abnormal
        ,CASE WHEN bdi.division_code IS NULL THEN '其它' else bdi.division_code  END as sybbh								--事业部编号
        ,CASE WHEN bdi.division IS NULL THEN '其它' else bdi.division  END as sybbh_desc								--事业部名称
        ,CASE WHEN bdi.product_group_code IS NULL THEN '其它' else bdi.product_group_code  END as zehd_spart								--产品组编号
        ,CASE WHEN bdi.product_group IS NULL THEN '其它' else bdi.product_group  END as zehd_spartdesc								--产品组名称
        ,CASE WHEN bdi.region_code IS NULL THEN '其它' else bdi.region_code  END as zehdfsv_reg								--区域
        ,CASE WHEN bdi.region IS NULL THEN '其它' else bdi.region  END as name								--大区名称
        ,bdi.install_type			--安装类型
        ,CASE WHEN position('WHOLE' IN dcc.exclude_type)>0 THEN '1' ELSE '0'  END AS exclude_type 			--是否整机剔除
        ,CASE WHEN bdi.active_statu is true THEN '1'  ELSE '0' END  AS is_active    					--是否激活
        FROM
        ors_base_device_info as bdi
        LEFT JOIN (select distinct device_name from ors_device_data_abnormal_detail_day
                   where stat_date  &gt;=  #{req.createTime_start}
                   AND stat_date  &lt;=  #{req.createTime_end}
                  <if test="req.list !=null and req.list !=''">
                      AND abnormal_code in
                      <foreach collection="req.list" item="item" index="index" separator="," open="(" close=")">
                          #{item}
                      </foreach>
                  </if>

        ) ddad on ddad.device_name = bdi.asset_id
        LEFT JOIN (select asset_id,STRING_AGG(exclude_type , ',') as exclude_type   from ors_device_check_config  where del_flag = '0'
            AND create_time  &gt;=  #{req.createTime_start}
            AND create_time  &lt;=  #{req.createTime_end}
        GROUP BY asset_id) AS dcc ON  bdi.asset_id = dcc.asset_id

        where bdi.exce_flag != 1
            <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
                AND bdi.model_id not in
                <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.region != null and req.region != ''">
                AND bdi.region_code like CONCAT('%',#{req.region},'%')
            </if>
            <if test="req.agentName != null and req.agentName != ''">
                AND bdi.agent like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
            </if>
            <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
                AND bdi.division_code in
                <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND bdi.product_group_code in
                <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installTypes}
            </if>
    </select>

    <select id="getExcludeItems" resultType="java.util.Map">
        select DISTINCT
        bdi.asset_id as product_id
        ,case when bdi.division_code is null then '其它' else bdi.division_code END as sybbh
        ,case when bdi.product_group_code is null then '其它' else bdi.product_group_code END as zehd_spart
        ,case when bdi.product_group is null then '其它' else bdi.product_group END as zehd_spartdesc
        ,case when bdi.region is null then '其它' else bdi.region END as zehdfsv_regDesc
        ,case when bdi.region_code is null then '其它' else bdi.region_code END as zehdfsv_reg
        ,case when bdi.country is null then '其它' else bdi.country END as countryDesc
        ,case when bdi.country_code is null then '其它' else bdi.country_code END as country_code
        ,CASE WHEN bdi.region IS NULL THEN '其它' else bdi.region  END as name								--大区名称
        ,concat(odcc.param_code)  AS exclude_type
        ,odcc.exclude_resean
        from (
        select *, row_number() over (partition by s.exclude_type order by s.exclude_type desc) as group_idx
        from ors_device_check_config s
        ) odcc
        left join ors_base_device_info as bdi on bdi.asset_id = odcc.asset_id
        where   odcc.param_code > 0 and bdi.exce_flag != 1
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.region != null and req.region != ''">
            AND bdi.region_code like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND bdi.agent like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND bdi.division_code in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>
        <if test="req.startTime != null and req.startTime != ''">
            AND odcc.create_time  &gt;=  #{req.createTime_start}
            AND odcc.create_time  &lt;=  #{req.createTime_end}
        </if>
        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
    </select>




    <!--月度发货-->

    <select id="getDataQualityReportMonthly" resultType="java.util.Map">

        SELECT DISTINCT
        bdi.asset_id
        ,bdi.model_id 	--物模型
        ,CASE WHEN mse.device_name IS NULL THEN '其它' else mse.device_name  END as device_name								--设备编号
        ,CASE WHEN bdi.crm_register =0 OR bdi.crm_register is null THEN '0' ELSE '1'  END AS auto_regist_new --注册
        ,CASE WHEN ddad.device_name IS NULL THEN '0' ELSE '1'  END AS is_abnormal
        ,CASE WHEN mse.division IS NULL THEN '其它' else mse.division  END as sybbh								--事业部编号
        ,CASE WHEN mse.division IS NULL THEN '其它' else mse.division  END as sybbh_desc								--事业部名称
        ,CASE WHEN mse.product_group IS NULL THEN '其它' else mse.product_group  END as zehd_spart								--产品组编号
        ,CASE WHEN mse.product_group IS NULL THEN '其它' else mse.product_group  END as zehd_spartdesc								--产品组名称
        ,CASE WHEN mse.region IS NULL THEN '其它' else mse.region END as zehdfsv_reg								--区域
        ,CASE WHEN mse.region IS NULL THEN '其它' else mse.region  END as name								--大区名称
        ,bdi.install_type			--安装类型
        ,CASE WHEN position('WHOLE' IN dcc.exclude_type)>0 THEN '1' ELSE '0'  END AS exclude_type 			--是否整机剔除
        ,CASE WHEN bdi.active_statu is true THEN '1'  ELSE '0' END  AS is_active    					--是否激活
        FROM
        ors_monthly_shipment_equipment as mse
        left join ors_base_device_info as bdi on mse.device_name = bdi.device_code
        LEFT JOIN ors_device_data_abnormal_detail_day AS ddad
                ON bdi.asset_id = ddad.device_name
                AND ddad.stat_date  &gt;=  #{req.createTime_start}
                AND ddad.stat_date  &lt;=  #{req.createTime_end}
        LEFT JOIN (select asset_id,STRING_AGG(exclude_type , ',') as exclude_type   from ors_device_check_config  where del_flag = '0'
         AND create_time  &gt;=  #{req.createTime_start}
         AND create_time  &lt;=  #{req.createTime_end}
        GROUP BY asset_id) AS dcc ON  bdi.asset_id = dcc.asset_id
        <where>
            <if test="req.region != null and req.region != ''">
                AND mse.region like CONCAT('%',#{req.region},'%')
            </if>
            <if test="req.agentName != null and req.agentName != ''">
                AND mse.product_group like CONCAT('%',#{req.agentName},'%')
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                AND mse.device_name like CONCAT('%',#{req.deviceName},'%')
            </if>
            <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
                AND mse.division in
                <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND mse.product_group in
                <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
            <if test="req.country != null and req.country != ''">
                AND bdi.country_code like CONCAT('%',#{req.country},'%')
            </if>
            <if test="req.userName != null and req.userName != ''">
                AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND mse.import_time  =  #{req.startTime}
            </if>
            <if test="req.installType != null and req.installType != ''">
                AND bdi.install_type = #{req.installTypes}
            </if>
        </where>
    </select>

    <select id="getExcludeItemsMonthly" resultType="java.util.Map">
        select DISTINCT
        mse.device_name as product_id
        ,case when mse.division is null then '其它' else mse.division END as sybbh
        ,case when mse.product_group is null then '其它' else mse.product_group END as zehd_spartdesc
        ,case when mse.product_group is null then '其它' else mse.product_group END as zehd_spart
        ,case when mse.region is null then '其它' else mse.region END as zehdfsv_regDesc
        ,case when mse.region is null then '其它' else mse.region END as zehdfsv_reg
        ,case when bdi.country is null then '其它' else bdi.country END as countryDesc
        ,case when bdi.country_code is null then '其它' else bdi.country_code END as country_code
        ,CASE WHEN mse.region IS NULL THEN '其它' else mse.region  END as name								--大区名称
        ,CASE WHEN concat(odcc.param_code) IS NULL THEN '其它' else concat(odcc.param_code)  END as exclude_type
        ,odcc.exclude_resean
        from
        ors_monthly_shipment_equipment as mse
        left join ors_base_device_info as bdi on mse.device_name = bdi.device_code
        left join (
        select *, row_number() over (partition by s.exclude_type order by s.exclude_type desc) as group_idx
        from ors_device_check_config s
        ) odcc on bdi.asset_id = odcc.asset_id
        where   odcc.param_code > 0
        <if test="req.region != null and req.region != ''">
            AND mse.region like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND mse.product_group like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND mse.device_name like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND mse.division in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND mse.product_group in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>
        <if test="req.startTime != null and req.startTime != ''">
            AND mse.import_time  =  #{req.startTime}
        </if>
        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
    </select>

    <select id="queryAbnormal" resultType="java.util.HashMap">
        SELECT DISTINCT
            dict_desc,
            dict_id
        FROM
            sany_data_service.sanyds_dict
        WHERE
            dict_type in ('single_abnormal_code','multiple_abnormal_code')
        <if test="dictType !=null and dictType !=''">
            <choose>
                <when test="dictType == 'single_param'">
                    and dict_type = 'single_abnormal_code'
                </when>
                <otherwise>
                    and dict_type = 'multiple_abnormal_code'
                </otherwise>
            </choose>
        </if>
        <if test="paramCode !=null and paramCode !=''">
            and dict_id in (select abnormal_code from dqm.ors_indicator where param_code in
            <foreach item="item" index="index" collection="paramCode.split(',')"  open="(" separator="," close=")">
                #{item}::INTEGER
            </foreach>
            )
        </if>

        ORDER BY
            dict_id ASC
    </select>



    <select id="queryParamCode" resultType="java.util.HashMap">
        SELECT DISTINCT dict_id,
                        dict_desc
        FROM sany_data_service.sanyds_dict
        WHERE
        dict_type in ('single_param','multiple_param')
        <if test="dictType !=null and dictType !=''">
            and dict_type = #{dictType}
        </if>
        <if test="(modelId != null and modelId != '')  or (divisionCode != null and divisionCode != '')">
            AND dict_id IN(
            SELECT
            distinct pc.param_code
            FROM
            dqm.ors_model_properties_config pc
            LEFT JOIN dqm.ors_model_division md ON pc.model_id = md.model_id
            <where>
                <if test="modelId != null and modelId != ''">
                    and pc.model_id
                    IN
                    <foreach item="item" index="index" collection="modelId.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="divisionCode != null and divisionCode != ''">
                    and md.division_code
                    IN
                    <foreach item="item" index="index" collection="divisionCode.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            )
        </if>
        order by dict_id asc
    </select>



    <select id="queryMergeDetails" resultType="java.util.HashMap">
        select
            t1.model_id,
            t1.device_name,
            count(1) as abnormal_count,
            t1.property,
            t1.property_name,
            t1.abnormal_name,
            t1.stat_date,
            max(t2.abnormal_time) as abnormal_time,
            max(t2.abnormal_data) as abnormal_data,
            t1.abnormal_code,
            t1.param_code
        from
            ors_device_data_abnormal_detail t1
        left join (
            select
                abnormal_data,
                abnormal_time,
                device_name,
                property,
                property_name,
                abnormal_code,
                abnormal_name,
                param_code,
                stat_date,
                model_id,
                row_number() over(
                    partition by model_id,device_name,property,property_name,abnormal_code,abnormal_name,param_code,stat_date
                    order by abnormal_time desc
                ) as num
            from ors_device_data_abnormal_detail
            where stat_date = #{date} and abnormal_effective=1
        ) t2 on t2.num = 1 and t1.model_id = t2.model_id and t1.device_name = t2.device_name and t1.property = t2.property and t1.property_name = t2.property_name
             and t1.abnormal_code = t2.abnormal_code and t1.abnormal_name = t2.abnormal_name and t1.param_code = t2.param_code and t1.stat_date = t2.stat_date
            and t1.abnormal_effective=1
             left join ors_base_device_info bdi on bdi.asset_id = t1.device_name
        where t1.stat_date = #{date}
          and t1.model_id is not null and bdi.exce_flag != 1
        group by
            t1.model_id,
            t1.device_name,
            t1.property,
            t1.property_name,
            t1.abnormal_code,
            t1.abnormal_name,
            t1.param_code,
            t1.stat_date
    </select>

    <insert id="insertDataAbnormalStatDays">
        <foreach collection="list" item="item" separator=";">
        insert into ors_device_data_abnormal_stat_day
        (
        model_id,
        device_name,
        stat_date,
        device_status_9008,
        device_location_9008,
        device_location_9001,
        device_location_9002,
        device_location_9007,
        engine_worktime_9008,
        engine_worktime_9001,
        engine_worktime_9004,
        working_time_9008,
        working_time_9001,
        working_time_9004,
        total_fuel_consumption_9008,
        total_fuel_consumption_9001,
        total_fuel_consumption_9004,
        pumping_volume_9008,
        pumping_volume_9001,
        pumping_volume_9004,
        driving_mileage_9008,
        driving_mileage_9001,
        driving_mileage_9004,
            device_location_9001_data,
            device_location_9002_data,
            device_location_9007_data,
            engine_worktime_9001_data,
            engine_worktime_9004_data,
            working_time_9001_data,
            working_time_9004_data,
            total_fuel_consumption_9001_data,
            total_fuel_consumption_9004_data,
            pumping_volume_9001_data,
            pumping_volume_9004_data,
            driving_mileage_9001_data,
            driving_mileage_9004_data
        )
        values(
        #{item.modelId},
        #{item.deviceName},
        #{item.statDate},
        #{item.deviceStatus9008},
        #{item.deviceLocation9008},
        #{item.deviceLocation9001},
        #{item.deviceLocation9002},
        #{item.deviceLocation9007},
        #{item.engineWorktime9008},
        #{item.engineWorktime9001},
        #{item.engineWorktime9004},
        #{item.workingTime9008},
        #{item.workingTime9001},
        #{item.workingTime9004},
        #{item.totalFuelConsumption9008},
        #{item.totalFuelConsumption9001},
        #{item.totalFuelConsumption9004},
        #{item.pumpingVolume9008},
        #{item.pumpingVolume9001},
        #{item.pumpingVolume9004},
        #{item.drivingMileage9008},
        #{item.drivingMileage9001},
        #{item.drivingMileage9004},
            #{item.deviceLocation9001Data},
            #{item.deviceLocation9002Data},
            #{item.deviceLocation9007Data},
            #{item.engineWorktime9001Data},
            #{item.engineWorktime9004Data},
            #{item.workingTime9001Data},
            #{item.workingTime9004Data},
            #{item.totalFuelConsumption9001Data},
            #{item.totalFuelConsumption9004Data},
            #{item.pumpingVolume9001Data},
            #{item.pumpingVolume9004Data},
            #{item.drivingMileage9001Data},
            #{item.drivingMileage9004Data}
        ) on conflict(model_id,device_name,stat_date) do
        update set device_status_9008 = #{item.deviceStatus9008},
        device_location_9008 = #{item.deviceLocation9008},
        device_location_9001 = #{item.deviceLocation9001},
        device_location_9002 = #{item.deviceLocation9002},
        device_location_9007 = #{item.deviceLocation9007},
        engine_worktime_9008 = #{item.engineWorktime9008},
        engine_worktime_9001 = #{item.engineWorktime9001},
        engine_worktime_9004 = #{item.engineWorktime9004},
        working_time_9008 = #{item.workingTime9008},
        working_time_9001 = #{item.workingTime9001},
        working_time_9004 = #{item.workingTime9004},
        total_fuel_consumption_9008 = #{item.totalFuelConsumption9008},
        total_fuel_consumption_9001 = #{item.totalFuelConsumption9001},
        total_fuel_consumption_9004 = #{item.totalFuelConsumption9004},
        pumping_volume_9008 = #{item.pumpingVolume9008},
        pumping_volume_9001 = #{item.pumpingVolume9001},
        pumping_volume_9004 = #{item.pumpingVolume9004},
        driving_mileage_9008 = #{item.drivingMileage9008},
        driving_mileage_9001 = #{item.drivingMileage9001},
        driving_mileage_9004 = #{item.drivingMileage9004},
            device_location_9001_data  = #{item.deviceLocation9001Data},
            device_location_9002_data = #{item.deviceLocation9002Data},
            device_location_9007_data = #{item.deviceLocation9007Data},
            engine_worktime_9001_data = #{item.engineWorktime9001Data},
            engine_worktime_9004_data = #{item.engineWorktime9004Data},
            working_time_9001_data = #{item.workingTime9001Data},
            working_time_9004_data = #{item.workingTime9004Data},
            total_fuel_consumption_9001_data = #{item.totalFuelConsumption9001Data},
            total_fuel_consumption_9004_data = #{item.totalFuelConsumption9004Data},
            pumping_volume_9001_data = #{item.pumpingVolume9001Data},
            pumping_volume_9004_data = #{item.pumpingVolume9004Data},
            driving_mileage_9001_data = #{item.drivingMileage9001Data},
            driving_mileage_9004_data = #{item.drivingMileage9004Data}
        </foreach>
    </insert>

    <insert id="syncDetailsToDay">
        <foreach collection="list" item="item" separator=";">
            insert into ors_device_data_abnormal_detail_day
            (
                model_id,
                device_name,
                abnormal_count,
                property,
                property_name,
                abnormal_name,
                stat_date,
                abnormal_time,
                abnormal_data,
                abnormal_code,
                param_code
            )
            values(
                #{item.model_id},
                #{item.device_name},
                #{item.abnormal_count},
                #{item.property},
                #{item.property_name},
                #{item.abnormal_name},
                #{item.stat_date},
                #{item.abnormal_time},
                #{item.abnormal_data},
                #{item.abnormal_code},
                #{item.param_code}
            ) on conflict(model_id,device_name,property,property_name,abnormal_name,stat_date) do
            update set abnormal_count = #{item.abnormal_count},abnormal_time = #{item.abnormal_time},
                      abnormal_data = #{item.abnormal_data},abnormal_code = #{item.abnormal_code},param_code = #{item.param_code}
        </foreach>
    </insert>

    <select id="getGroupByDataSybbh" resultType="java.util.HashMap">
        select t1.sybbh, t1.spart as zehd_spart,t1.property, t1.property_name, concat(case when t2.count is null then 0 else t2.count end) as count
        from (
        select DISTINCT bdi.division_code as sybbh,bdi.product_group_code as spart,mpc.property,property_name,mpc.param_code from ors_base_device_info as bdi
        left join (select  model_id,property,property_name,param_code from  ors_model_properties_config GROUP BY model_id,property,property_name,param_code ORDER BY model_id) as mpc on  bdi.model_id  = mpc.model_id
        where  bdi.division_code is not null and  bdi.product_group_code is not null
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY bdi.division_code,bdi.product_group_code
        ) t1
        left join (
        select count(1),sybbh,zehd_spart,property,property_name from (
        select
        case when bdi.division_code is null then '其它' else bdi.division_code END as sybbh
        ,case when bdi.product_group_code is null then '其它' else bdi.product_group_code END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        <if test="req.list !=null and req.list !=''">
            AND abnormal_code in
            <foreach collection="req.list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        where  bdi.active_statu is true and bdi.exce_flag != 1
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.region != null and req.region != ''">
            AND bdi.region_code like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND bdi.agent like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND bdi.division_code in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name
        ) t2 on t1.sybbh = t2.sybbh and t1.spart = t2.zehd_spart and t1.property = t2.property

        union all

        select
        t5.sybbh,
        t5.zehd_spart,
        t5.property,
        t5.property_name,
        concat(t5.counts) as count
        from (
        select count(1) as counts,sybbh,zehd_spart,property,property_name from (
        select
        CASE WHEN bdi.division_code IS NULL THEN '其它' else bdi.division_code  END as sybbh
        ,case when bdi.product_group_code is null then '其它' else bdi.product_group_code END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        <if test="req.list !=null and req.list !=''">
            AND abnormal_code in
            <foreach collection="req.list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        where  bdi.active_statu is true
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.region != null and req.region != ''">
            AND bdi.region_code like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND bdi.agent like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND bdi.division_code in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name) as t5 where  t5.sybbh = '其它' or t5.zehd_spart = '其它'

    </select>

    <select id="getGroupByDataRegion" resultType="java.util.HashMap">

        select t1.sybbh, t1.spart as zehd_spart,t1.property, t1.property_name, concat(case when t2.count is null then 0 else t2.count end) as count from (
        select DISTINCT bdi.region as sybbh,bdi.product_group_code as spart,mpc.property,property_name,mpc.param_code from ors_base_device_info as bdi
        left join (select  model_id,property,property_name,param_code from  ors_model_properties_config GROUP BY model_id,property,property_name,param_code ORDER BY model_id) as mpc on  bdi.model_id  = mpc.model_id
        where  bdi.region is not null and  bdi.product_group_code is not null
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY bdi.region,bdi.product_group_code
        ) t1 left join (
        select count(1),sybbh,zehd_spart,property,property_name from (
        select
        CASE WHEN bdi.region IS NULL THEN '其它' else bdi.region  END as sybbh
        ,case when bdi.product_group_code is null then '其它' else bdi.product_group_code END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        <if test="req.list !=null and req.list !=''">
            AND abnormal_code in
            <foreach collection="req.list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        where  bdi.active_statu is true and bdi.exce_flag != 1
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.region != null and req.region != ''">
            AND bdi.region_code like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND bdi.agent like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND bdi.division_code in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name
        ) t2 on t1.sybbh = t2.sybbh and t1.spart = t2.zehd_spart and t1.property = t2.property

        union all

        select
        t5.sybbh,
        t5.zehd_spart,
        t5.property,
        t5.property_name,
        concat(t5.counts) as count
        from (
        select count(1) as counts,sybbh,zehd_spart,property,property_name from (
        select
        CASE WHEN bdi.region IS NULL THEN '其它' else bdi.region  END as sybbh
        ,case when bdi.product_group_code is null then '其它' else bdi.product_group_code END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        <if test="req.list !=null and req.list !=''">
            AND abnormal_code in
            <foreach collection="req.list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        where  bdi.active_statu is true
        <if test="req.huaXinModelIdList !=null and req.huaXinModelIdList != ''">
            AND bdi.model_id not in
            <foreach collection="req.huaXinModelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.region != null and req.region != ''">
            AND bdi.region_code like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND bdi.agent like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND bdi.device_code like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND bdi.division_code in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND bdi.product_group_code in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name) as t5 where  t5.sybbh = '其它' or t5.zehd_spart = '其它'

    </select>


    <select id="getGroupByDataMseSybbh" resultType="java.util.HashMap">
        select t1.sybbh, t1.spart as zehd_spart,t1.property, t1.property_name, concat(case when t2.count is null then 0 else t2.count end) as count
        from (
        select DISTINCT mse.division as sybbh,mse.product_group as spart,mpc.property,property_name,mpc.param_code from
        ors_monthly_shipment_equipment as mse left join ors_base_device_info as bdi on mse.device_name = bdi.device_code
        left join (select  model_id,property,property_name,param_code from  ors_model_properties_config GROUP BY model_id,property,property_name,param_code ORDER BY model_id) as mpc on  bdi.model_id  = mpc.model_id
        where  mse.division is not null and  mse.product_group is not null ORDER BY mse.division,mse.product_group
        ) t1
        left join (
        select count(1),sybbh,zehd_spart,property,property_name from (
        select
        case when mse.division is null then '其它' else mse.division END as sybbh
        ,case when mse.product_group is null then '其它' else mse.product_group END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        AND abnormal_code != '9004' AND abnormal_code != '9007'
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        left join ors_monthly_shipment_equipment as mse on mse.device_name = bdi.device_code
        where mse.region is not null and mse.product_group is not null and  bdi.active_statu is true
        <if test="req.region != null and req.region != ''">
            AND mse.region like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND mse.product_group like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND mse.device_name like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND mse.division in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND mse.product_group in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name
        ) t2 on t1.sybbh = t2.sybbh and t1.spart = t2.zehd_spart and t1.property = t2.property
        where t1.property is not null

        union all

        select
        t5.sybbh,
        t5.zehd_spart,
        t5.property,
        t5.property_name,
        concat(t5.counts) as count
        from (
        select count(1) as counts,sybbh,zehd_spart,property,property_name from (
        select
        CASE WHEN mse.region IS NULL THEN '其它' else mse.region  END as sybbh
        ,case when mse.product_group is null then '其它' else mse.product_group END as zehd_spart
        ,oddad.property
        ,oddad.property_name
        from (select DISTINCT property,property_name,device_name from ors_device_data_abnormal_detail_day where
        stat_date  &gt;=  #{req.createTime_start}
        AND stat_date  &lt;=  #{req.createTime_end}
        AND abnormal_code != '9004' AND abnormal_code != '9007'
        ) as oddad
        left join ors_base_device_info as bdi on bdi.asset_id = oddad.device_name
        left join ors_monthly_shipment_equipment as mse on mse.device_name = bdi.device_code
        where mse.region is not null and mse.product_group is not null and  bdi.active_statu is true
        <if test="req.region != null and req.region != ''">
            AND mse.region like CONCAT('%',#{req.region},'%')
        </if>
        <if test="req.agentName != null and req.agentName != ''">
            AND mse.product_group like CONCAT('%',#{req.agentName},'%')
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            AND mse.device_name like CONCAT('%',#{req.deviceName},'%')
        </if>
        <if test="req.divisionRegiondesc != null and req.divisionRegiondesc != ''">
            AND mse.division in
            <foreach collection="req.divisionRegiondescList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
            AND mse.product_group in
            <foreach collection="req.zehdSpartdescs" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>
        <if test="req.country != null and req.country != ''">
            AND bdi.country_code like CONCAT('%',#{req.country},'%')
        </if>
        <if test="req.userName != null and req.userName != ''">
            AND bdi.customer_name like CONCAT('%',#{req.userName},'%')
        </if>

        <if test="req.installType != null and req.installType != ''">
            AND bdi.install_type = #{req.installTypes}
        </if>
        )t group by t.sybbh,t.zehd_spart,t.property,t.property_name) as t5 where  t5.sybbh = '其它' or t5.zehd_spart = '其它'
        and t5.property is not null


    </select>

    <insert id="ors_up_work_device">
        truncate table dqm.ors_up_work_device;
        insert into dqm.ors_up_work_device (device_name,stat_date)
            SELECT device_name,#{bizDate}::DATE as stat_date FROM sany_data_service.sanyds_core_param_stat_latest_day WHERE stat_date = #{bizDate}::DATE GROUP BY device_name
            UNION DISTINCT
            SELECT device_name,#{bizDate}::DATE as stat_date  FROM dqm.ors_device_data_abnormal_detail WHERE stat_date = #{bizDate}::DATE and abnormal_effective=1 GROUP BY device_name
        ;
    </insert>

    <insert id="ors_device_data_abnormal_detail_day">

        ANALYZE dqm.ors_device_data_abnormal_detail;
        -- 删除有问题的从未上报异常数据
        DELETE FROM dqm.ors_device_data_abnormal_detail
        WHERE stat_date = #{bizDate}::DATE
        AND abnormal_code = 9008
        AND (device_name, param_code) IN (
            SELECT device_name, param_code
            FROM dqm.ors_device_data_abnormal_detail
            WHERE stat_date = #{bizDate}::DATE
            GROUP BY device_name, param_code
            HAVING count(1) > 1
        );

        DELETE FROM dqm.ors_device_data_abnormal_detail_day WHERE stat_date = #{bizDate}::DATE;

        -- 先存聚合数据
        truncate dqm.abnormal_total;
        INSERT INTO dqm.abnormal_total (total, abnormal_time, device_name, param_code, abnormal_code)
        SELECT COUNT(1) AS total,
               MAX(T1.abnormal_time) AS abnormal_time,
               T1.device_name,
               T1.param_code,
               T1.abnormal_code
        FROM dqm.ors_device_data_abnormal_detail T1
        WHERE T1.stat_date =  #{bizDate}::DATE
          AND T1.abnormal_effective = 1
          GROUP BY T1.device_name,
		      T1.param_code,
		      T1.abnormal_code ;

        INSERT INTO dqm.ors_device_data_abnormal_detail_day (device_name, abnormal_count, property, property_name,
                                                             abnormal_name, stat_date, create_time, abnormal_time,
                                                             abnormal_code, param_code, abnormal_data, detail_id,data_center_id,division_code)
        SELECT
            T2.device_name,
            T1.total,
            T2.property,
            T2.property_name,
            T2.abnormal_name,
            T2.stat_date,
            CURRENT_TIMESTAMP,
            T2.abnormal_time,
            T2.abnormal_code,
            T2.param_code,
            T2.abnormal_data,
            T2.detail_id,
            T2.data_center_id,
            T2.division_code
        FROM dqm.abnormal_total as T1
         INNER JOIN dqm.ors_device_data_abnormal_detail T2 ON T2.abnormal_time = T1.abnormal_time
            AND T2.device_name = T1.device_name
            AND T2.param_code = T1.param_code
            AND T2.abnormal_code = T1.abnormal_code;

        analyze dqm.ors_device_data_abnormal_detail_day;
    </insert>
    <insert id="ors_device_data_abnormal_stat_day">
        DELETE FROM dqm.ors_device_data_abnormal_stat_day WHERE stat_date = #{bizDate}::DATE;
        INSERT INTO dqm.ors_device_data_abnormal_stat_day (device_name,
                                                           stat_date,
                                                           create_time,
                                                           data_center_id,
                                                           division_code,
                                                           device_status_9008,
                                                           device_status_9009,
                                                           device_location_9008,
                                                           device_location_9001,
                                                           device_location_9002,
                                                           device_location_9007,
                                                           device_location_9009,
                                                           engine_worktime_9008,
                                                           engine_worktime_9001,
                                                           engine_worktime_9004,
                                                           engine_worktime_9009,
                                                           engine_worktime_9003,
                                                           engine_worktime_9005,
                                                           working_time_9008,
                                                           working_time_9001,
                                                           working_time_9004,
                                                           working_time_9009,
                                                           working_time_9003,
                                                           working_time_9005,
                                                           total_fuel_consumption_9008,
                                                           total_fuel_consumption_9001,
                                                           total_fuel_consumption_9004,
                                                           total_fuel_consumption_9009,
                                                           total_fuel_consumption_9003,
                                                           total_fuel_consumption_9005,
                                                           pumping_volume_9008,
                                                           pumping_volume_9001,
                                                           pumping_volume_9004,
                                                           pumping_volume_9009,
                                                           driving_mileage_9008,
                                                           driving_mileage_9001,
                                                           driving_mileage_9004,
                                                           driving_mileage_9009,
                                                           driving_mileage_9003,
                                                           driving_mileage_9005,
                                                            travel_speed_9008,
                                                            travel_speed_9001,
                                                            travel_speed_9009,
                                                            travel_speed_9006,
                                                            fuel_level_9001,
                                                            fuel_level_9008,
                                                            fuel_level_9009,
                                                            fuel_level_9006,
                                                            engine_speed_9001,
                                                            engine_speed_9008,
                                                            engine_speed_9009,
                                                            engine_speed_9006,
                                                            water_temperature_9008,
                                                            water_temperature_9001,
                                                            water_temperature_9009,
                                                            water_temperature_9006,
                                                            total_electric_consumption_9008,
                                                            total_electric_consumption_9001,
                                                            total_electric_consumption_9004,
                                                            total_electric_consumption_9009,
                                                            total_electric_consumption_9003,
                                                            total_electric_consumption_9005,
                                                            SOC_stateofcharge_9008,
                                                            SOC_stateofcharge_9001,
                                                            SOC_stateofcharge_9009,
                                                        total_idle_fuel_consumption_9001,
                                                        total_idle_fuel_consumption_9004,
                                                        total_idle_fuel_consumption_9005,
                                                        total_idle_fuel_consumption_9008,
                                                        total_idle_fuel_consumption_9009,
                                                        total_idle_time_9001,
                                                        total_idle_time_9004,
                                                        total_idle_time_9005,
                                                        total_idle_time_9008,
                                                        total_idle_time_9009,
                                                        gear_9008,
                                                        gear_9009,
                                                        gear_9001,
                                                        total_time_left_moving_9001,
                                                        total_time_left_moving_9004,
                                                        total_time_left_moving_9005,
                                                        total_time_left_moving_9008,
                                                        total_time_left_moving_9009,
                                                        total_time_right_moving_9001,
                                                        total_time_right_moving_9004,
                                                        total_time_right_moving_9005,
                                                        total_time_right_moving_9008,
                                                        total_time_right_moving_9009,
                                                        oil_pressure_9001,
                                                        oil_pressure_9008,
                                                        oil_pressure_9009,
                                                        pump_total_absorbed_torque_9001,
                                                        pump_total_absorbed_torque_9008,
                                                        pump_total_absorbed_torque_9009,
        <!-- 2024-12-30 -->
        pump_motor_rotate_speed_9001,
        pump_motor_rotate_speed_9008,
        pump_motor_rotate_speed_9009,
        charging_status_9008,
        charging_status_9009,
        charging_status_9001,
        charge_time_remain_9001,
        charge_time_remain_9008,
        charge_time_remain_9009,
        single_charge_capacity_9001,
        single_charge_capacity_9008,
        single_charge_capacity_9009,
        day_power_consumption_9001,
        day_power_consumption_9008,
        day_power_consumption_9009,
        <!-- 2025-01-17 -->
        action_code_9001,
        action_code_9008,
        action_code_9009,
        total_time_rotation_9001,
        total_time_rotation_9004,
        total_time_rotation_9005,
        total_time_rotation_9008,
        total_time_rotation_9009,

        total_no_action_power_consumption_9001,
        total_no_action_power_consumption_9004,
        total_no_action_power_consumption_9008,
        total_no_action_power_consumption_9009,


        <!-- 2025-05-14 -->
        idle_time_idle_fuel_9101,
        idle_time_idle_fuel_9102,
        idle_time_idle_fuel_9100,
        work_time_fuel_9103,
        work_time_fuel_9104,
        work_time_fuel_9100,

        idel_fuel_fuel_9105,
        idel_fuel_fuel_9100,
        idel_time_work_time_9106,
        idel_time_work_time_9100,
        mileage_speed_9107,
        mileage_speed_9108,
        mileage_speed_9100,
        mileage_location_9109,
        mileage_location_9100,
        engine_time_fuel_9100


        )
        SELECT
        T1.device_name,
        T1.stat_date,
        CURRENT_TIMESTAMP,
        max(T1.data_center_id) as data_center_id,
        max(T1.division_code) as division_code,
        <!-- 设备状态 -->
        SUM ( CASE WHEN T1.param_code = 8503 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS device_status_9008,
        SUM ( CASE WHEN T1.param_code = 8503 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS device_status_9009,
        <!-- 设备位置 -->
        SUM ( CASE WHEN T1.param_code = 8501 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS device_location_9008,
        SUM ( CASE WHEN T1.param_code = 8501 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS device_location_9001,
        SUM ( CASE WHEN T1.param_code = 8501 AND T1.abnormal_code = 9002 THEN 1 ELSE 0 END ) AS device_location_9002,
        SUM ( CASE WHEN T1.param_code = 8501 AND T1.abnormal_code = 9007 THEN 1 ELSE 0 END ) AS device_location_9007,
        SUM ( CASE WHEN T1.param_code = 8501 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS device_location_9009,
        <!-- 发动机工作时间 -->
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS engine_worktime_9008,
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS engine_worktime_9001,
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS engine_worktime_9004,
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS engine_worktime_9009,
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9003 THEN 1 ELSE 0 END ) AS engine_worktime_9003,
        SUM ( CASE WHEN T1.param_code = 8105 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS engine_worktime_9005,
        <!-- 工作时间 -->
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS working_time_9008,
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS working_time_9001,
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS working_time_9004,
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS working_time_9009,
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9003 THEN 1 ELSE 0 END ) AS working_time_9003,
        SUM ( CASE WHEN T1.param_code = 8102 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS working_time_9005,
        <!-- 总油耗 -->
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9008,
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9001,
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9004,
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9009,
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9003 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9003,
        SUM ( CASE WHEN T1.param_code = 8201 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_fuel_consumption_9005,
        <!-- 泵送方量 -->
        SUM ( CASE WHEN T1.param_code = 8401 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS pumping_volume_9008,
        SUM ( CASE WHEN T1.param_code = 8401 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS pumping_volume_9001,
        SUM ( CASE WHEN T1.param_code = 8401 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS pumping_volume_9004,
        SUM ( CASE WHEN T1.param_code = 8401 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS pumping_volume_9009,
        <!-- 行驶里程 -->
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS driving_mileage_9008,
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS driving_mileage_9001,
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS driving_mileage_9004,
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS driving_mileage_9009,
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9003 THEN 1 ELSE 0 END ) AS driving_mileage_9003,
        SUM ( CASE WHEN T1.param_code = 8403 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS driving_mileage_9005,
        <!-- 行驶速度 -->
        SUM ( CASE WHEN T1.param_code = 8510 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS travel_speed_9008,
        SUM ( CASE WHEN T1.param_code = 8510 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS travel_speed_9001,
        SUM ( CASE WHEN T1.param_code = 8510 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS travel_speed_9009,
        SUM ( CASE WHEN T1.param_code = 8510 AND T1.abnormal_code = 9006 THEN 1 ELSE 0 END ) AS travel_speed_9006,
        <!-- 油位 -->
        SUM ( CASE WHEN T1.param_code = 8506 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS fuel_level_9001,
        SUM ( CASE WHEN T1.param_code = 8506 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS fuel_level_9008,
        SUM ( CASE WHEN T1.param_code = 8506 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS fuel_level_9009,
        SUM ( CASE WHEN T1.param_code = 8506 AND T1.abnormal_code = 9006 THEN 1 ELSE 0 END ) AS fuel_level_9006,
        <!-- 发动机转速 -->
        SUM ( CASE WHEN T1.param_code = 8507 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS engine_speed_9001,
        SUM ( CASE WHEN T1.param_code = 8507 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS engine_speed_9008,
        SUM ( CASE WHEN T1.param_code = 8507 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS engine_speed_9009,
        SUM ( CASE WHEN T1.param_code = 8507 AND T1.abnormal_code = 9006 THEN 1 ELSE 0 END ) AS engine_speed_9006,
        <!-- 发动机水温 -->
        SUM ( CASE WHEN T1.param_code = 8508 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS water_temperature_9008,
        SUM ( CASE WHEN T1.param_code = 8508 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS water_temperature_9001,
        SUM ( CASE WHEN T1.param_code = 8508 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS water_temperature_9009,
        SUM ( CASE WHEN T1.param_code = 8508 AND T1.abnormal_code = 9006 THEN 1 ELSE 0 END ) AS water_temperature_9006,
        <!-- 总电耗 -->
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_electric_consumption_9008,
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_electric_consumption_9001,
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_electric_consumption_9004,
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_electric_consumption_9009,
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9003 THEN 1 ELSE 0 END ) AS total_electric_consumption_9003,
        SUM ( CASE WHEN T1.param_code = 8511 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_electric_consumption_9005,
        <!-- 当前电量 -->
        SUM ( CASE WHEN T1.param_code = 8509 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS SOC_stateofcharge_9008,
        SUM ( CASE WHEN T1.param_code = 8509 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS SOC_stateofcharge_9001,
        SUM ( CASE WHEN T1.param_code = 8509 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS SOC_stateofcharge_9009,
<!--新增属性-->
                <!-- 怠速油耗 -->
                SUM ( CASE WHEN T1.param_code = 8205 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_idle_fuel_consumption_9001,
                SUM ( CASE WHEN T1.param_code = 8205 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_idle_fuel_consumption_9004,
                SUM ( CASE WHEN T1.param_code = 8205 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_idle_fuel_consumption_9005,
                SUM ( CASE WHEN T1.param_code = 8205 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_idle_fuel_consumption_9008,
                SUM ( CASE WHEN T1.param_code = 8205 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_idle_fuel_consumption_9009,
                <!-- 怠速时长 -->
                SUM ( CASE WHEN T1.param_code = 8106 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_idle_time_9001,
                SUM ( CASE WHEN T1.param_code = 8106 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_idle_time_9004,
                SUM ( CASE WHEN T1.param_code = 8106 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_idle_time_9005,
                SUM ( CASE WHEN T1.param_code = 8106 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_idle_time_9008,
                SUM ( CASE WHEN T1.param_code = 8106 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_idle_time_9009,
                <!-- 档位 -->
                SUM ( CASE WHEN T1.param_code = 8602 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS gear_9008,
                SUM ( CASE WHEN T1.param_code = 8602 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS gear_9009,
                SUM ( CASE WHEN T1.param_code = 8602 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS gear_9001,
                <!-- 左行走工时 -->
                SUM ( CASE WHEN T1.param_code = 8108 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_time_left_moving_9001,
                SUM ( CASE WHEN T1.param_code = 8108 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_time_left_moving_9004,
                SUM ( CASE WHEN T1.param_code = 8108 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_time_left_moving_9005,
                SUM ( CASE WHEN T1.param_code = 8108 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_time_left_moving_9008,
                SUM ( CASE WHEN T1.param_code = 8108 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_time_left_moving_9009,
                <!-- 右行走工时 -->
                SUM ( CASE WHEN T1.param_code = 8107 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_time_right_moving_9001,
                SUM ( CASE WHEN T1.param_code = 8107 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_time_right_moving_9004,
                SUM ( CASE WHEN T1.param_code = 8107 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_time_right_moving_9005,
                SUM ( CASE WHEN T1.param_code = 8107 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_time_right_moving_9008,
                SUM ( CASE WHEN T1.param_code = 8107 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_time_right_moving_9009,
                <!-- 机油压力 -->
                SUM ( CASE WHEN T1.param_code = 8603 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS oil_pressure_9001,
                SUM ( CASE WHEN T1.param_code = 8603 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS oil_pressure_9008,
                SUM ( CASE WHEN T1.param_code = 8603 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS oil_pressure_9009,
                <!-- 泵吸收功率 -->
                SUM ( CASE WHEN T1.param_code = 8604 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS pump_total_absorbed_torque_9001,
                SUM ( CASE WHEN T1.param_code = 8604 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS pump_total_absorbed_torque_9008,
                SUM ( CASE WHEN T1.param_code = 8604 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS pump_total_absorbed_torque_9009,
<!--2024-12-30-->
                <!--电机转速-->
                SUM ( CASE WHEN T1.param_code = 8605 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS pump_motor_rotate_speed_9001,
                SUM ( CASE WHEN T1.param_code = 8605 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS pump_motor_rotate_speed_9008,
                SUM ( CASE WHEN T1.param_code = 8605 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS pump_motor_rotate_speed_9009,
                <!--充电状态-->
                SUM ( CASE WHEN T1.param_code = 8606 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS charging_status_9008,
                SUM ( CASE WHEN T1.param_code = 8606 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS charging_status_9009,
                SUM ( CASE WHEN T1.param_code = 8606 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS charging_status_9001,
                <!--充电剩余时间-->
                SUM ( CASE WHEN T1.param_code = 8607 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS charge_time_remain_9001,
                SUM ( CASE WHEN T1.param_code = 8607 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS charge_time_remain_9008,
                SUM ( CASE WHEN T1.param_code = 8607 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS charge_time_remain_9009,
                <!--单次充电电量-->
                SUM ( CASE WHEN T1.param_code = 8608 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS single_charge_capacity_9001,
                SUM ( CASE WHEN T1.param_code = 8608 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS single_charge_capacity_9008,
                SUM ( CASE WHEN T1.param_code = 8608 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS single_charge_capacity_9009,
                <!--当日电耗-->
                SUM ( CASE WHEN T1.param_code = 8609 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS day_power_consumption_9001,
                SUM ( CASE WHEN T1.param_code = 8609 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS day_power_consumption_9008,
                SUM ( CASE WHEN T1.param_code = 8609 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS day_power_consumption_9009,
                <!--动作编码-->
                SUM ( CASE WHEN T1.param_code = 8610 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS action_code_9001,
                SUM ( CASE WHEN T1.param_code = 8610 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS action_code_9008,
                SUM ( CASE WHEN T1.param_code = 8610 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS action_code_9009,
                <!--回转时间-->
                SUM ( CASE WHEN T1.param_code = 8611 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_time_rotation_9001,
                SUM ( CASE WHEN T1.param_code = 8611 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_time_rotation_9004,
                SUM ( CASE WHEN T1.param_code = 8611 AND T1.abnormal_code = 9005 THEN 1 ELSE 0 END ) AS total_time_rotation_9005,
                SUM ( CASE WHEN T1.param_code = 8611 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_time_rotation_9008,
                SUM ( CASE WHEN T1.param_code = 8611 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_time_rotation_9009,

                <!--怠速电耗-->
                SUM ( CASE WHEN T1.param_code = 8305 AND T1.abnormal_code = 9001 THEN 1 ELSE 0 END ) AS total_no_action_power_consumption_9001,
                SUM ( CASE WHEN T1.param_code = 8305 AND T1.abnormal_code = 9004 THEN 1 ELSE 0 END ) AS total_no_action_power_consumption_9004,
                SUM ( CASE WHEN T1.param_code = 8305 AND T1.abnormal_code = 9008 THEN 1 ELSE 0 END ) AS total_no_action_power_consumption_9008,
                SUM ( CASE WHEN T1.param_code = 8305 AND T1.abnormal_code = 9009 THEN 1 ELSE 0 END ) AS total_no_action_power_consumption_9009,

                <!--怠速时长&怠速油耗-->
                SUM ( CASE WHEN T1.param_code = 8701 AND T1.abnormal_code = 9101 THEN 1 ELSE 0 END ) AS idle_time_idle_fuel_9101,
                SUM ( CASE WHEN T1.param_code = 8701 AND T1.abnormal_code = 9102 THEN 1 ELSE 0 END ) AS idle_time_idle_fuel_9102,
                SUM ( CASE WHEN T1.param_code = 8701 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS idle_time_idle_fuel_9100,

                <!--工作时间&总油耗-->
                SUM ( CASE WHEN T1.param_code = 8702 AND T1.abnormal_code = 9103 THEN 1 ELSE 0 END ) AS work_time_fuel_9103,
                SUM ( CASE WHEN T1.param_code = 8702 AND T1.abnormal_code = 9104 THEN 1 ELSE 0 END ) AS work_time_fuel_9104,
                SUM ( CASE WHEN T1.param_code = 8702 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS work_time_fuel_9100,

                <!--怠速油耗&总油耗-->
                SUM ( CASE WHEN T1.param_code = 8703 AND T1.abnormal_code = 9105 THEN 1 ELSE 0 END ) AS idel_fuel_fuel_9105,
                SUM ( CASE WHEN T1.param_code = 8703 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS idel_fuel_fuel_9100,

                <!--怠速时长&工作时长-->
                SUM ( CASE WHEN T1.param_code = 8704 AND T1.abnormal_code = 9106 THEN 1 ELSE 0 END ) AS idel_time_work_time_9106,
                SUM ( CASE WHEN T1.param_code = 8704 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS idel_time_work_time_9100,


                <!--行驶里程&行驶速度-->
                SUM ( CASE WHEN T1.param_code = 8705 AND T1.abnormal_code = 9107 THEN 1 ELSE 0 END ) AS mileage_speed_9107,
                SUM ( CASE WHEN T1.param_code = 8705 AND T1.abnormal_code = 9108 THEN 1 ELSE 0 END ) AS mileage_speed_9108,
                SUM ( CASE WHEN T1.param_code = 8705 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS mileage_speed_9100,


                <!--行驶里程&设备位置-->
                SUM ( CASE WHEN T1.param_code = 8706 AND T1.abnormal_code = 9109 THEN 1 ELSE 0 END ) AS mileage_location_9109,
                SUM ( CASE WHEN T1.param_code = 8706 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS mileage_location_9100,

                <!--发动机工作时长&总油耗-->
                SUM ( CASE WHEN T1.param_code = 8707 AND T1.abnormal_code = 9100 THEN 1 ELSE 0 END ) AS engine_time_fuel_9100

        FROM dqm.ors_device_data_abnormal_detail T1
        WHERE T1.stat_date = #{bizDate}::DATE
          AND T1.abnormal_effective = 1
         GROUP BY T1.stat_date,T1.device_name ;

        -- 清理无用数据
        DELETE
        FROM dqm.ors_device_data_abnormal_stat_day
        WHERE device_status_9008 = 0
        AND device_status_9009 = 0
        AND device_location_9008 = 0
        AND device_location_9001 = 0
        AND device_location_9002 = 0
        AND device_location_9007 = 0
        AND device_location_9009 = 0
        AND engine_worktime_9008 = 0
        AND engine_worktime_9001 = 0
        AND engine_worktime_9004 = 0
        AND engine_worktime_9009 = 0
        AND engine_worktime_9003 = 0
        AND engine_worktime_9005 = 0
        AND working_time_9008 = 0
        AND working_time_9001 = 0
        AND working_time_9004 = 0
        AND working_time_9009 = 0
        AND working_time_9003 = 0
        AND working_time_9005 = 0
        AND total_fuel_consumption_9008 = 0
        AND total_fuel_consumption_9001 = 0
        AND total_fuel_consumption_9004 = 0
        AND total_fuel_consumption_9009 = 0
        AND total_fuel_consumption_9003 = 0
        AND total_fuel_consumption_9005 = 0
        AND pumping_volume_9008 = 0
        AND pumping_volume_9001 = 0
        AND pumping_volume_9004 = 0
        AND pumping_volume_9009 = 0
        AND driving_mileage_9008 = 0
        AND driving_mileage_9001 = 0
        AND driving_mileage_9004 = 0
        AND driving_mileage_9009 = 0
        AND driving_mileage_9003 = 0
        AND driving_mileage_9005 = 0
        AND travel_speed_9008 = 0
        AND travel_speed_9001 = 0
        AND travel_speed_9009 = 0
        AND travel_speed_9006 = 0
        AND fuel_level_9001 = 0
        AND fuel_level_9008 = 0
        AND fuel_level_9009 = 0
        AND fuel_level_9006 = 0
        AND engine_speed_9001 = 0
        AND engine_speed_9008 = 0
        AND engine_speed_9009 = 0
        AND engine_speed_9006 = 0
        AND water_temperature_9008 = 0
        AND water_temperature_9001 = 0
        AND water_temperature_9009 = 0
        AND water_temperature_9006 = 0
        AND total_electric_consumption_9008 = 0
        AND total_electric_consumption_9001 = 0
        AND total_electric_consumption_9004 = 0
        AND total_electric_consumption_9009 = 0
        AND total_electric_consumption_9003 = 0
        AND total_electric_consumption_9005 = 0
        AND SOC_stateofcharge_9008 = 0
        AND SOC_stateofcharge_9001 = 0
        AND SOC_stateofcharge_9009 = 0
        AND total_idle_fuel_consumption_9001 = 0
        AND total_idle_fuel_consumption_9004 = 0
        AND total_idle_fuel_consumption_9005 = 0
        AND total_idle_fuel_consumption_9008 = 0
        AND total_idle_fuel_consumption_9009 = 0
        AND total_idle_time_9001 = 0
        AND total_idle_time_9004 = 0
        AND total_idle_time_9005 = 0
        AND total_idle_time_9008 = 0
        AND total_idle_time_9009 = 0
        AND gear_9008 = 0
        AND gear_9009 = 0
        AND gear_9001 = 0
        AND total_time_left_moving_9001 = 0
        AND total_time_left_moving_9004 = 0
        AND total_time_left_moving_9005 = 0
        AND total_time_left_moving_9008 = 0
        AND total_time_left_moving_9009 = 0
        AND total_time_right_moving_9001 = 0
        AND total_time_right_moving_9004 = 0
        AND total_time_right_moving_9005 = 0
        AND total_time_right_moving_9008 = 0
        AND total_time_right_moving_9009 = 0
        AND oil_pressure_9001 = 0
        AND oil_pressure_9008 = 0
        AND oil_pressure_9009 = 0
        AND pump_total_absorbed_torque_9001 = 0
        AND pump_total_absorbed_torque_9008 = 0
        AND pump_total_absorbed_torque_9009 = 0
<!--2024-12-05-->
        AND pump_motor_rotate_speed_9001 =0
        AND pump_motor_rotate_speed_9008 =0
        AND pump_motor_rotate_speed_9009 =0
        AND charging_status_9008 =0
        AND charging_status_9009 =0
        AND charging_status_9001 =0
        AND charge_time_remain_9001 =0
        AND charge_time_remain_9008 =0
        AND charge_time_remain_9009 =0
        AND single_charge_capacity_9001 =0
        AND single_charge_capacity_9008 =0
        AND single_charge_capacity_9009 =0
        AND day_power_consumption_9001 =0
        AND day_power_consumption_9008 =0
        AND day_power_consumption_9009 =0
<!-- 2025-01-17 -->
        AND action_code_9001 =0
        AND action_code_9008 =0
        AND action_code_9009 =0
        AND total_time_rotation_9001 =0
        AND total_time_rotation_9004 =0
        AND total_time_rotation_9005 =0
        AND total_time_rotation_9008 =0
        AND total_time_rotation_9009 =0
        AND total_no_action_power_consumption_9001 =0
        AND total_no_action_power_consumption_9004 =0
        AND total_no_action_power_consumption_9008 =0
        AND total_no_action_power_consumption_9009 =0
<!-- 2025-05-14 -->
        AND idle_time_idle_fuel_9101 =0
        AND idle_time_idle_fuel_9102 =0
        AND idle_time_idle_fuel_9100 =0
        AND work_time_fuel_9103 =0
        AND work_time_fuel_9104 =0
        AND work_time_fuel_9100 =0

        AND idel_fuel_fuel_9105 =0
        AND idel_fuel_fuel_9100 =0
        AND idel_time_work_time_9106 =0
        AND idel_time_work_time_9100 =0
        AND mileage_speed_9107 =0
        AND mileage_speed_9108 =0
        AND mileage_speed_9100 =0
        AND mileage_location_9109 =0
        AND mileage_location_9100 =0
        AND engine_time_fuel_9100 =0;

        -- 更新位置工况条数
        UPDATE dqm.ors_device_data_abnormal_stat_day
        SET device_location_cnt = subquery.work_cnt
        FROM (
        SELECT t1.device_name, t1.stat_date, t2.work_cnt, t2.param_code
        FROM (
        SELECT device_name, stat_date
        FROM dqm.ors_device_data_abnormal_stat_day
        WHERE stat_date = #{bizDate}::DATE
        ) t1
        JOIN (
        SELECT device_name, stat_date, param_code, work_cnt
        FROM sany_data_service.sanyds_device_work_cnt
        WHERE stat_date = #{bizDate}::DATE and param_code = 8501
        ) t2
        ON t1.device_name = t2.device_name
        ) AS subquery
        WHERE dqm.ors_device_data_abnormal_stat_day.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_stat_day.device_name = subquery.device_name;

        analyze dqm.ors_device_data_abnormal_stat_day;
    </insert>

    <insert id="transferAbnormalDataToDay">
        -- 便于手动执行，这里先删除对应日期的数据
        DELETE FROM dqm.ors_device_data_abnormal_detail_day WHERE stat_date = #{date}::DATE;
        INSERT INTO dqm.ors_device_data_abnormal_detail_day (device_name, abnormal_count, property, property_name,
                                                             abnormal_name, stat_date, create_time, abnormal_time,
                                                             abnormal_code, param_code, abnormal_data, detail_id)
        SELECT
            T2.device_name,
            T1.total,
            T2.property,
            T2.property_name,
            T2.abnormal_name,
            T2.stat_date,
            CURRENT_TIMESTAMP,
            T2.abnormal_time,
            T2.abnormal_code,
            T2.param_code,
            T2.abnormal_data,
            T1.detail_id
        FROM (
             SELECT COUNT(1) AS total,
                    MAX(T1.detail_id) AS detail_id
             FROM dqm.ors_device_data_abnormal_detail T1
             WHERE T1.stat_date = #{date}::DATE
               AND T1.device_name NOT IN (SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE')
             GROUP BY T1.device_name,
                      T1.abnormal_code,
                      T1.param_code
         ) T1
         INNER JOIN dqm.ors_device_data_abnormal_detail T2 ON T2.detail_id = T1.detail_id;

        DELETE FROM dqm.ors_device_data_abnormal_stat_day WHERE stat_date = #{date}::DATE;
        INSERT INTO dqm.ors_device_data_abnormal_stat_day (device_name,
                                                           stat_date,
                                                           create_time,
                                                           device_status_9008,
                                                           device_location_9008,
                                                           device_location_9001,
                                                           device_location_9002,
                                                           device_location_9007,
                                                           engine_worktime_9008,
                                                           engine_worktime_9001,
                                                           engine_worktime_9004,
                                                           working_time_9008,
                                                           working_time_9001,
                                                           working_time_9004,
                                                           total_fuel_consumption_9008,
                                                           total_fuel_consumption_9001,
                                                           total_fuel_consumption_9004,
                                                           pumping_volume_9008,
                                                           pumping_volume_9001,
                                                           pumping_volume_9004,
                                                           driving_mileage_9008,
                                                           driving_mileage_9001,
                                                           driving_mileage_9004)
        SELECT T1.device_name,
               T1.stat_date,
               CURRENT_TIMESTAMP,
               SUM(CASE
                       WHEN T1.param_code = 8503 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8503%') THEN 1
                       ELSE 0 END) AS device_status_9008,
               SUM(CASE
                       WHEN T1.param_code = 8501 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8501%') THEN 1
                       ELSE 0 END) AS device_location_9008,
               SUM(CASE
                       WHEN T1.param_code = 8501 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8501%') THEN 1
                       ELSE 0 END) AS device_location_9001,
               SUM(CASE
                       WHEN T1.param_code = 8501 AND T1.abnormal_code = 9002 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8501%') THEN 1
                       ELSE 0 END) AS device_location_9002,
               SUM(CASE
                       WHEN T1.param_code = 8501 AND T1.abnormal_code = 9007 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8501%') THEN 1
                       ELSE 0 END) AS device_location_9007,
               SUM(CASE
                       WHEN T1.param_code = 8105 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8105%') THEN 1
                       ELSE 0 END) AS engine_worktime_9008,
               SUM(CASE
                       WHEN T1.param_code = 8105 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8105%') THEN 1
                       ELSE 0 END) AS engine_worktime_9001,
               SUM(CASE
                       WHEN T1.param_code = 8105 AND T1.abnormal_code = 9004 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8105%') THEN 1
                       ELSE 0 END) AS engine_worktime_9004,
               SUM(CASE
                       WHEN T1.param_code = 8102 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8102%') THEN 1
                       ELSE 0 END) AS working_time_9008,
               SUM(CASE
                       WHEN T1.param_code = 8102 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8102%') THEN 1
                       ELSE 0 END) AS working_time_9001,
               SUM(CASE
                       WHEN T1.param_code = 8102 AND T1.abnormal_code = 9004 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8102%') THEN 1
                       ELSE 0 END) AS working_time_9004,
               SUM(CASE
                       WHEN T1.param_code = 8201 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8201%') THEN 1
                       ELSE 0 END) AS total_fuel_consumption_9008,
               SUM(CASE
                       WHEN T1.param_code = 8201 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8201%') THEN 1
                       ELSE 0 END) AS total_fuel_consumption_9001,
               SUM(CASE
                       WHEN T1.param_code = 8201 AND T1.abnormal_code = 9004 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8201%') THEN 1
                       ELSE 0 END) AS total_fuel_consumption_9004,
               SUM(CASE
                       WHEN T1.param_code = 8401 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8401%') THEN 1
                       ELSE 0 END) AS pumping_volume_9008,
               SUM(CASE
                       WHEN T1.param_code = 8401 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8401%') THEN 1
                       ELSE 0 END) AS pumping_volume_9001,
               SUM(CASE
                       WHEN T1.param_code = 8401 AND T1.abnormal_code = 9004 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8401%') THEN 1
                       ELSE 0 END) AS pumping_volume_9004,
               SUM(CASE
                       WHEN T1.param_code = 8403 AND T1.abnormal_code = 9008 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8403%') THEN 1
                       ELSE 0 END) AS driving_mileage_9008,
               SUM(CASE
                       WHEN T1.param_code = 8403 AND T1.abnormal_code = 9001 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8403%') THEN 1
                       ELSE 0 END) AS driving_mileage_9001,
               SUM(CASE
                       WHEN T1.param_code = 8403 AND T1.abnormal_code = 9004 AND
                            (T2.param_codes IS NULL OR T2.param_codes NOT LIKE '%8403%') THEN 1
                       ELSE 0 END) AS driving_mileage_9004
        FROM dqm.ors_device_data_abnormal_detail T1
                 LEFT JOIN (SELECT asset_id, string_agg(param_code::TEXT, ',') AS param_codes
                            FROM dqm.ors_device_check_config
                            WHERE param_code IS NOT NULL
                              AND exclude_type != 'WHOLE'
                            GROUP BY asset_id) T2 ON T2.asset_id = T1.device_name
        WHERE T1.stat_date = #{date}::DATE
          AND T1.device_name NOT IN (SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE')
        GROUP BY T1.device_name, T1.stat_date;

        -- 清理无用数据
        DELETE
        FROM dqm.ors_device_data_abnormal_stat_day
        WHERE device_status_9008 = 0
          AND device_location_9008 = 0
          AND device_location_9001 = 0
          AND device_location_9002 = 0
          AND device_location_9007 = 0
          AND engine_worktime_9008 = 0
          AND engine_worktime_9001 = 0
          AND engine_worktime_9004 = 0
          AND working_time_9008 = 0
          AND working_time_9001 = 0
          AND working_time_9004 = 0
          AND total_fuel_consumption_9008 = 0
          AND total_fuel_consumption_9001 = 0
          AND total_fuel_consumption_9004 = 0
          AND pumping_volume_9008 = 0
          AND pumping_volume_9001 = 0
          AND pumping_volume_9004 = 0
          AND driving_mileage_9008 = 0
          AND driving_mileage_9001 = 0
          AND driving_mileage_9004 = 0;

        -- 更新位置工况条数
        UPDATE dqm.ors_device_data_abnormal_stat_day
        SET device_location_cnt = subquery.work_cnt
        FROM (
        SELECT t1.device_name, t1.stat_date, t2.work_cnt, t2.param_code
        FROM (
        SELECT device_name, stat_date
        FROM dqm.ors_device_data_abnormal_stat_day
        WHERE stat_date = #{date}::DATE
        ) t1
        JOIN (
        SELECT device_name, stat_date, param_code, work_cnt
        FROM sany_data_service.sanyds_device_work_cnt
        WHERE stat_date = #{date}::DATE and param_code = 8501
        ) t2
        ON t1.device_name = t2.device_name
        ) AS subquery
        WHERE dqm.ors_device_data_abnormal_stat_day.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_stat_day.stat_date = subquery.stat_date;

    </insert>

    <insert id="aggDeviceHourWorkCnt">
        DELETE FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE;
        INSERT INTO sany_data_service.sanyds_device_work_cnt
        SELECT
            device_name,
            stat_date,
            param_code,
            SUM ( work_cnt ) work_cnt
        FROM
            sany_data_service.sanyds_device_hour_work_cnt
        WHERE
            stat_date = #{bizDate}::DATE
        GROUP BY
            device_name,
            stat_date,
            param_code;
        DELETE FROM sany_data_service.sanyds_device_hour_work_cnt WHERE stat_date = (#{bizDate}::DATE - INTERVAL '7 day')::DATE;
        DELETE FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = (#{bizDate}::DATE - INTERVAL '7 day')::DATE;
    </insert>
</mapper>
