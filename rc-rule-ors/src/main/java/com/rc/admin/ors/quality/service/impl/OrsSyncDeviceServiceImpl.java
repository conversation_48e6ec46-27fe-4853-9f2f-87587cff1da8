package com.rc.admin.ors.quality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.rc.admin.ors.quality.dao.*;
import com.rc.admin.ors.quality.entity.*;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.*;
import com.rc.admin.ors.quality.utils.DeviceCenterIdList;
import com.rc.admin.ors.quality.utils.DeviceResponse;
import com.rc.admin.ors.quality.utils.DeviceSource;
import com.rc.admin.ors.quality.utils.InstallType;
import com.rc.admin.sys.model.SysRedisVO;
import com.rc.admin.sys.service.SysRedisService;
import com.rc.admin.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <P>
 * 同步设备信息service实现类
 * </P>
 * 
 * <AUTHOR>
 * @since 2023/10/23
 */
@Slf4j
@Service
public class OrsSyncDeviceServiceImpl implements OrsSyncDeviceService {

    @Value("${ors.rootCloud.clientId:20250214b4e9e6c1faf930b9}")
    private String clientId;

    @Value("${ors.rootCloud.clientSecret:3868c834bf52a274ee975187038831fb}")
    private String clientSecret;

    @Value("${ors.rootCloud.grantType:client_credentials}")
    private String grantType;

    @Value("${ors.rootCloud.urlPre:http://federation-openapi-gateway-zone-china.ngc.sanygroup.com}")
    private String urlPre;

    @Value("${ors.rootCloud.organizationId:6653ec92d31141004170a5f3}")
    private String organizationId;


    @Value("${ors.rootCloud.authKey:}")
    private String authKey;


    @Value("${ors.rootCloud.esbUrl:}")
    private String esbUrl;

    private static final String TOKEN_KEY = "ors:newc:token";

    @Value("${ors.rootCloudOversea.clientId:2023110129f939d663872dd6}")
    private String clientIdOversea;

    @Value("${ors.rootCloudOversea.clientSecret:1dddf366c5e255f14b16241f8ad3ad6a}")
    private String clientSecretOversea;

    @Value("${ors.rootCloudOversea.grantType:client_credentials}")
    private String grantTypeOversea;

    @Value("${ors.rootCloudOversea.urlPre:https://openapi-oversea.rootcloud.com}")
    private String urlPreOversea;

    private static final String TOKEN_KEY_OVERSEA = "ors:oversea:token";

    @Autowired
    private SysRedisService serviceRedis;

    @Resource
    private OrsSyncModelConfigMapper orsSyncModelConfigMapper;

    @Resource
    private DeviceInfoSyncMapper deviceInfoSyncMapper;

    @Resource
    private DeviceInfoSyncService deviceInfoSyncService;

    @Resource
    private ModelInfoSyncMapper modelInfoSyncMapper;

    @Resource
    private ModelPropertySyncMapper modelPropertySyncMapper;

    @Resource
    private ModelPropertySyncService modelPropertySyncService;

    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource
    OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource
    SanydsCoreParamDefMapper sanydsCoreParamDefMapper;

    @Resource
    OrsModelInfoMapper orsModelInfoMapper;

    @Resource
    SanydsCoreParamStatLatestMapper sanydsCoreParamStatLatestMapper;

    @Resource
    SanydsCoreParamStatLatestService sanydsCoreParamStatLatestService;

    @Resource
    OrsCountryRegionSyncService orsCountryRegionSyncService;

    @Resource
    OrsDeviceCountryService orsDeviceCountryService;

    @Resource
    private OrsModelInfoServiceImpl orsModelInfoService;

    private final Integer pageSize = 1000;

    @Override
    public void syncData() {
        String token = this.getRootCloudToken();

        // 加载需要同步的物模型信息，后面的数据只同步物模型相关的数据
        List<OrsSyncModelConfig> models = orsSyncModelConfigMapper.selectList(new QueryWrapper<>());
        List<String> collect = models.stream().map(OrsSyncModelConfig::getModelId).collect(Collectors.toList());

        // 同步之前先删除同步表中的数据
        modelInfoSyncMapper.delete(new QueryWrapper<>());

        // 查询 请求满足条件的物模型数据
        Map<String, Object> param = Maps.newHashMapWithExpectedSize(4);
        param.put("status", "active");
        param.put("thingType", "device");
        param.put("modelIdList", collect);

        HttpRequest request = HttpRequest.post(urlPre + "/thing-model/v1/thing/thing-classes-query");
        request.body(JSONObject.toJSONString(param));
        request.bearerAuth(token);
        HttpResponse response = request.execute();

        if (StrUtil.isBlank(response.body())) {
            throw new RuntimeException("没有请求到物模型信息");
        }

        OrsRootCloudResp resp = JSONObject.parseObject(response.body(), OrsRootCloudResp.class);

        List<ModelInfoSync> modelList =
                JSONObject.parseArray(resp.getPayload(), ModelInfoSync.class);
        if (CollUtil.isEmpty(modelList)) {
            throw new RuntimeException("没有请求到物模型信息");
        }

        // 请求到数据，开始处理数据
        for (ModelInfoSync orsModelInfo : modelList) {
            // 物模型信息入库
            modelInfoSyncMapper.insert(orsModelInfo);
//            // 同步模型属性
            this.syncProperty(token, orsModelInfo.getModelId());

            // 同步物模型对应的设备
            this.syncDevice(token, orsModelInfo.getModelId());
        }

        // 数据同步完成，迁移数据
        orsDeviceInfoMapper.transferData();
    }

    @Override
    public void createPartitionTable() {
        //每年一月一号 创建今年的分区
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        for (int month = 1; month <= 12; month++) {
            String tableName = currentYear+String.format("%02d",month);
            String startYear = currentYear+"-"+String.format("%02d",month)+"-01";
            String endYear = (month == 12 ? currentYear + 1 : currentYear)+"-"+String.format("%02d", month == 12 ? 1 : month + 1)+"-01";
            orsDeviceInfoMapper.createPartitionTable(tableName,startYear,endYear);
        }
    }


    @Override
    public Map<String, OrsDeviceCountry> resultCountryMap() {
        List<OrsDeviceCountry> list = orsDeviceCountryService.list();
        return list.stream().collect(Collectors.toMap(OrsDeviceCountry::getCntyDesc, i -> i, (k1, k2) -> k1));
    }

    /**
     * 返回设备国家-map
     * @return
     */
    public Map<String, OrsDeviceCountry> getDeviceCountryMap(){
        List<OrsDeviceCountry> list = orsDeviceCountryService.list();
        Map<String, OrsDeviceCountry> deviceCountryMap = list.stream()
                .collect(Collectors.toMap(OrsDeviceCountry::getProdId, i -> i, (k1, k2) -> k1));
        return deviceCountryMap;
    }

    @Override
    public Map<String, CountryData> resultMap() {

        Map<String, Object> params = new HashMap<>();
        Map<String, Object> isParams = new HashMap<>();
        params.put("inParam",isParams);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("authKey", authKey);

        List<CountryData> resultList = CollUtil.newArrayList();

        log.info("请求地址：{}，请求参数：{}",esbUrl,JSONObject.toJSONString(params));
        try {
            int page = 1;
            while (true){
                params.put("page", String.valueOf(page));
                String result = HttpUtil.post(esbUrl, JSONObject.toJSONString(params), headerMap);
                JSONObject responseBody = JSONObject.parseObject(result);
                // 检查响应码
                if (!"OK".equals(responseBody.get("code"))) {
                    break;
                }
                // 获取 data 数组
                JSONArray dataArr = responseBody.getJSONArray("data");
                List<CountryData> countryDataList = dataArr.toJavaList(CountryData.class);
                if (countryDataList.isEmpty()) {
                    log.info("总页码：{}",page);
                    break;
                }
                resultList.addAll(countryDataList);
                page++;
            }
            //去掉没有设备或者 没有国家code的数据
            resultList = resultList.stream()
                    .filter(i->StringUtils.isNotBlank(i.getProd_id()) && StringUtils.isNotBlank(i.getCnty_code())).collect(Collectors.toList());
            log.info("调用集成平台请求设备-国家接口返回数据条数：{}",resultList.size());

            Map<String, CountryData> map = resultList.stream().collect(Collectors.toMap(CountryData::getProd_id, i -> i, (k1, k2) -> k1));
            log.info("调用集成平台请求设备-国家接口返回数据按照设备去重条数：{}",map.size());
            syncDeviceCountry(map);
            return map;
        }catch (Exception e){
            log.info("请求集成平台错误：{}",e.getMessage(),e);
        }
        return null;
    }

    @Async
    public void syncDeviceCountry(Map<String, CountryData> map){
        List<OrsDeviceCountry> list = orsDeviceCountryService.list();
        Map<String, OrsDeviceCountry> deviceCountryMap = list.stream()
                .filter(i->StringUtils.isNotBlank(i.getProdId()))
                .collect(Collectors.toMap(OrsDeviceCountry::getProdId, i -> i, (k1, k2) -> k1));
        List<OrsDeviceCountry> deviceCountryListUpdate = CollUtil.newArrayList();
        List<OrsDeviceCountry> deviceCountryListCreate = CollUtil.newArrayList();
        if(map != null){
            for (Map.Entry<String, CountryData> entry : map.entrySet()) {
                CountryData value = entry.getValue();

                OrsDeviceCountry deviceCountry = new OrsDeviceCountry();
                deviceCountry.setProdId(value.getProd_id());
                deviceCountry.setCntyCode(value.getCnty_code());
                deviceCountry.setCntyDesc(value.getCnty_desc());
                deviceCountry.setBicZioJjrq(value.getBic_zio_jjrq());
                deviceCountry.setBicZioSbzt(value.getBic_zio_sbzt());

                if(deviceCountryMap.containsKey(value.getProd_id())){
                    if(StringUtils.isBlank(deviceCountryMap.get(value.getProd_id()).getCntyCode())
                            ||  StringUtils.isBlank(deviceCountryMap.get(value.getProd_id()).getCntyDesc())
                            ||  StringUtils.isBlank(deviceCountryMap.get(value.getProd_id()).getBicZioJjrq())
                            ||  StringUtils.isBlank(deviceCountryMap.get(value.getProd_id()).getBicZioSbzt())
                            ||  !deviceCountryMap.get(value.getProd_id()).getCntyCode().equals(deviceCountry.getCntyCode())
                            ||  !deviceCountryMap.get(value.getProd_id()).getBicZioJjrq().equals(deviceCountry.getBicZioJjrq())
                            ||  !deviceCountryMap.get(value.getProd_id()).getBicZioSbzt().equals(deviceCountry.getBicZioSbzt())){
                        deviceCountry.setId(deviceCountryMap.get(value.getProd_id()).getId());
                        deviceCountry.setUpdateDate(new Date());
                        deviceCountryListUpdate.add(deviceCountry);
                    }
                }else{
                    deviceCountry.setCreateDate(new Date());
                    deviceCountryListCreate.add(deviceCountry);
                }
            }
        }
        log.info("设备国家-新增条数：{}",deviceCountryListCreate.size());
        log.info("设备国家-修改数据条数：{}",deviceCountryListUpdate.size());
        if(CollUtil.isNotEmpty(deviceCountryListCreate)){
            orsDeviceCountryService.saveOrUpdateBatch(deviceCountryListCreate);
        }
        if(CollUtil.isNotEmpty(deviceCountryListUpdate)){
            orsDeviceCountryService.saveOrUpdateBatch(deviceCountryListUpdate);
        }

    }


    public void updateRealTime(String modelIds,String paramCodes) {
        log.info("updateRealTime 执行开始");
        QueryWrapper queryWrapper = new QueryWrapper<>();
        List<ModelInfoSync> modelInfoSyncs = modelInfoSyncMapper.selectList(queryWrapper);
        Map<String, String> modelIdToTenantIdMap = modelInfoSyncs.stream()
                .collect(Collectors.toMap(
                        ModelInfoSync::getModelId, // 键
                        ModelInfoSync::getTenantId, // 值
                        (existingValue, newValue) -> newValue // 处理重复键，保留新值
                ));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        Map<String, List<String>> modelParamMap = new HashMap();
        Map<String, Integer> paramCodeMap = new HashMap();
        List<SanydsCoreParamDef> sanydsCoreParamDefs = sanydsCoreParamDefMapper.selectList(queryWrapper);
        sanydsCoreParamDefs.forEach(sanydsCoreParamDef -> {
            String modelId = sanydsCoreParamDef.getModelId();
            String paramNameModel = sanydsCoreParamDef.getParamNameModel();
            if (StringUtils.isBlank(paramNameModel)) {
                return;
            }
            List<String> paramList = modelParamMap.get(modelId);
            if (CollectionUtil.isEmpty(paramList)) {
                paramList = new ArrayList<>();
                paramList.add(paramNameModel);
                modelParamMap.put(modelId, paramList);
            } else {
                paramList.add(paramNameModel);
            }
            Integer paramCode = paramCodeMap.get(modelId + paramNameModel);
            if (paramCode == null) {
                paramCodeMap.put(modelId + paramNameModel, sanydsCoreParamDef.getParamCode());
            }
        });
        //queryWrapper.eq("device_name", "SW9553CCF6888");
        if (StringUtils.isNotBlank(modelIds)){
            queryWrapper.in("model_id", Arrays.asList(modelIds.split(",")));
        }
        List<OrsBaseDeviceInfo> list = orsBaseDeviceInfoMapper.selectList(queryWrapper);
        list.parallelStream().forEach(orsBaseDeviceInfo -> {
            String modelId =orsBaseDeviceInfo.getModelId();
            String thingId =orsBaseDeviceInfo.getThingId();
            String deviceName = orsBaseDeviceInfo.getDeviceName();
            String tenantId = modelIdToTenantIdMap.get(modelId);

            String url = String.format("%s/realtime-manage/v1/realtime/models/%s/things/%s", urlPre, modelId, thingId);
            List<String> properties = modelParamMap.get(modelId);
            if (CollectionUtil.isEmpty(properties)) {
                return;
            }
            url = url + "?properties=" + JSONObject.toJSONString(properties);
            HttpRequest request = HttpRequest.get(url);
            request.bearerAuth(getRootCloudToken());
            HttpResponse response = request.execute();
            JSONObject responseBody = JSONObject.parseObject(response.body(), JSONObject.class);
            if (responseBody.getString("code") != null && !"200".equals(responseBody.getString("code"))) {
                log.error(orsBaseDeviceInfo.getDeviceCode() + "请求失败:" + responseBody.getString("message"));
                return;
            }
            JSONArray payload = responseBody.getJSONArray("payload");
            if(CollectionUtil.isEmpty(payload)) {
                return;
            }
            for (int i = 0; i < payload.size(); i++) {
                JSONObject jsonObject = payload.getJSONObject(i);
                JSONObject data = jsonObject.getJSONObject("data");
                if (data == null) {
                    return;
                }
                List<SanydsCoreParamStatLatest> sanydsCoreParamStatLatests = new ArrayList<>();
                properties.forEach(paramNameModel -> {
                    JSONObject paramValue = data.getJSONObject(paramNameModel);
                    if (paramValue == null) {
                        return;
                    }
                    String value = paramValue.getString("value");
                    String timeLocal = paramValue.getString("timeLocal");
                    LocalDateTime date = null;
                    try {
                        date = LocalDateTime.parse(timeLocal, formatter);
                    } catch (DateTimeParseException e) {
                        e.printStackTrace();
                        return;
                    }
                    date = date.plus(8, ChronoUnit.HOURS);
                    // 将 LocalDateTime 转换为 ZonedDateTime
                    ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());

                    // 将 ZonedDateTime 转换为 Instant
                    java.time.Instant instant = zonedDateTime.toInstant();

                    // 将 Instant 转换为 Date
                    Date newDate = Date.from(instant);
                    Integer paramCode = paramCodeMap.get(modelId + paramNameModel);

                    // 指定异常编码
                    if (StringUtils.isNotBlank(paramCodes)){
                        if(!Arrays.asList(paramCodes.split(",")).contains(paramCode.toString())){
                            return;
                        }
                    }

                    SanydsCoreParamStatLatest sanydsCoreParamStatLatest = new SanydsCoreParamStatLatest();
                    sanydsCoreParamStatLatest.setTenantId(tenantId);
                    sanydsCoreParamStatLatest.setModelId(modelId);
                    sanydsCoreParamStatLatest.setDeviceName(deviceName);
                    sanydsCoreParamStatLatest.setParamCode(paramCode);
                    sanydsCoreParamStatLatest.setIsParamValueAbnormal(false);
                    sanydsCoreParamStatLatest.setParamValueEarliestTime(newDate);
                    sanydsCoreParamStatLatest.setParamValueLatestTime(newDate);
                    sanydsCoreParamStatLatest.setCreateTime(newDate);
                    if (paramCode == 8503) {
                        sanydsCoreParamStatLatest.setParamValue(new BigDecimal(value.equals("0") ? "0" : "1"));
                    } else {
                        // 如果超长，大概是是异常数据，这里只需要保存有数据就可以，用-1代替
                        if (value.length() > 20) {
                            value = "-1";
                        }
                        sanydsCoreParamStatLatest.setParamValue(new BigDecimal(value));
                    }

                    // sanydsCoreParamStatLatests.add(sanydsCoreParamStatLatest);
                    List<SanydsCoreParamStatLatest> queryObjs = null;
                    try {
                        // 查询 tenantId，modelId，deviceName，paramCode记录是否存在
                        queryObjs = sanydsCoreParamStatLatestService.list(
                                new LambdaQueryWrapper<SanydsCoreParamStatLatest>()
                                        .eq(SanydsCoreParamStatLatest::getTenantId, tenantId)
                                        .eq(SanydsCoreParamStatLatest::getModelId, modelId)
                                        .eq(SanydsCoreParamStatLatest::getDeviceName, deviceName)
                                        .eq(SanydsCoreParamStatLatest::getParamCode, paramCode)
                        );

                        if(CollectionUtil.isEmpty(queryObjs)) {
                            sanydsCoreParamStatLatestService.save(sanydsCoreParamStatLatest);
                        }
                    } catch (Exception e) {
                        log.error("插入数据失败：" + e.getMessage());
                        e.printStackTrace();
                    }

                    if (paramCode == 8504 || paramCode == 8505) {
                        sanydsCoreParamStatLatest = new SanydsCoreParamStatLatest();
                        sanydsCoreParamStatLatest.setTenantId(tenantId);
                        sanydsCoreParamStatLatest.setModelId(modelId);
                        sanydsCoreParamStatLatest.setDeviceName(deviceName);
                        sanydsCoreParamStatLatest.setIsParamValueAbnormal(false);
                        sanydsCoreParamStatLatest.setParamValueEarliestTime(newDate);
                        sanydsCoreParamStatLatest.setParamValueLatestTime(newDate);
                        sanydsCoreParamStatLatest.setCreateTime(newDate);

                        sanydsCoreParamStatLatest.setParamCode(8501);
                        sanydsCoreParamStatLatest.setParamValue(new BigDecimal(1));
                        sanydsCoreParamStatLatests.add(sanydsCoreParamStatLatest);
                        try {
                            queryObjs = sanydsCoreParamStatLatestService.list(
                                new LambdaQueryWrapper<SanydsCoreParamStatLatest>()
                                    .eq(SanydsCoreParamStatLatest::getTenantId, tenantId)
                                    .eq(SanydsCoreParamStatLatest::getModelId, modelId)
                                    .eq(SanydsCoreParamStatLatest::getDeviceName, deviceName)
                                    .eq(SanydsCoreParamStatLatest::getParamCode, 8501)
                            );
                            if(CollectionUtil.isEmpty(queryObjs)) {
                                sanydsCoreParamStatLatestService.save(sanydsCoreParamStatLatest);
                            }
                        } catch (Exception e) {
                            log.error("插入数据失败：" + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                });

            }
        });
        // 更新 sanyds_core_param_stat_latest_day
        List<String> modelIdList = null;
        List<String> paramCodeList = null;

        if (StringUtils.isNotBlank(modelIds)){
            modelIdList = Arrays.asList(modelIds.split(","));
        }

        if (StringUtils.isNotBlank(paramCodes)){
            paramCodeList = Arrays.asList(paramCodes.split(","));
        }
        int insertCount = sanydsCoreParamStatLatestMapper.insertSanydsCoreParamStatLatestDay(modelIdList, paramCodeList);
        log.info("插入sanyds_core_param_stat_latest_day表{}条", insertCount);
        log.info("updateRealTime 执行结束");
    }


    @Override
    public void syncNationalRegion() {

        List<OrsCountryRegionSync> countryRegionSyncList = orsCountryRegionSyncService.list();
        Map<String, OrsCountryRegionSync> countryNameMap
                = countryRegionSyncList.stream().collect(Collectors.toMap(OrsCountryRegionSync::getCountryName, i -> i, (k1, k2) -> k1));

        String token = this.getRootCloudToken();

        HttpRequest request = HttpRequest.get(urlPre + "/organization-manage/v2/departments/"+organizationId);
        request.bearerAuth(token);
        HttpResponse response = request.execute();

        if (StrUtil.isBlank(response.body())) {
            throw new RuntimeException("没有找到对应的组织结构");
        }

        List<OrsCountryRegionSync> list = CollUtil.newArrayList();

        NationalRegionResp resp = JSONObject.parseObject(response.body(), NationalRegionResp.class);

        List<NationalRegionResp> regions = resp.getChildren();
        //大区
        for (NationalRegionResp region : regions) {
            if(CollUtil.isNotEmpty(region.getChildren())){
                //国区
                List<NationalRegionResp> countryRegions = region.getChildren();
                for (NationalRegionResp countryRegion : countryRegions) {
                    //国家
                    if(CollUtil.isNotEmpty(countryRegion.getChildren())){
                        List<NationalRegionResp> countrys = countryRegion.getChildren();
                        if(CollUtil.isNotEmpty(countrys)){
                            for (NationalRegionResp country : countrys) {
                                OrsCountryRegionSync orsCountryRegionSync = new OrsCountryRegionSync();
                                String regionName = region.getName().replaceAll("[^\\p{IsHan}]", "");
                                orsCountryRegionSync.setRegion(regionName);
                                orsCountryRegionSync.setRegionCode(region.getId());

                                String countryRegionName = countryRegion.getName().replaceAll("[^\\p{IsHan}]", "");
                                orsCountryRegionSync.setCountryRegion(countryRegionName);
                                orsCountryRegionSync.setCountryRegionCode(countryRegion.getId());

                                String countryName = country.getName().replaceAll("[^\\p{IsHan}]", "");
                                orsCountryRegionSync.setCountryName(countryName);
                                if(countryNameMap.containsKey(countryName)){
                                    orsCountryRegionSync.setId(countryNameMap.get(countryName).getId());
                                }
                                if(StringUtils.isNotBlank(countryName)){
                                    list.add(orsCountryRegionSync);
                                }
                            }
                        }
                    }
                }
            }
        }
        Map<String, OrsDeviceCountry> stringCountryDataMap = resultCountryMap();

        if(CollUtil.isNotEmpty(list)){
            list.forEach(item->{
                if(StringUtils.isNotBlank(item.getCountryName()) && stringCountryDataMap!=null && stringCountryDataMap.containsKey(item.getCountryName())){
                    item.setCountryCode(stringCountryDataMap.get(item.getCountryName()).getCntyCode());
                }
            });
            orsCountryRegionSyncService.saveOrUpdateBatch(list);
        }

    }




    public static void main(String[] args) {
        String url = "http://openapi.ngc.sanygroup.com/third-apps/nc-ot-oversea-device-register/api/oversea/getDeviceList";
        HttpRequest request = HttpRequest.get(url + "?beginTime=1706786877000&pageIndex=1&pageSize=200000&fields=device_ser,register_flag");
        HttpResponse response = request.execute();

        if (StrUtil.isBlank(response.body())) {
        }
        JSONObject resp = JSONObject.parseObject(response.body(), JSONObject.class);
        JSONObject data = resp.getJSONObject("data");
        JSONArray recordArr = data.getJSONArray("records");
        List<String> resultList = new ArrayList<>();
        for (int j = 0; j < recordArr.size(); j++) {

            JSONObject record = recordArr.getJSONObject(j);
            String deviceSer = record.getString("deviceSer");
            String registerFlag = record.getString("registerFlag");
            int deleteFlag = record.getIntValue("deleteFlag");
            if (deleteFlag == 1) {
                continue;
            }
            resultList.add(deviceSer);
        }
        try {
            // 创建 FileWriter 对象，指定文件路径
            FileWriter fileWriter = new FileWriter("D:\\output.txt");
            // 包装为 BufferedWriter 以提高写入效率
            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);

            // 遍历 resultList 并写入文件
            for (String s : resultList) {
                // 写入字符串并换行
                if(StrUtil.isNotBlank(s)) {
                    bufferedWriter.write(s);
                    bufferedWriter.newLine(); // 添加新行
                }
            }

            // 关闭 BufferedWriter
            bufferedWriter.close();
        } catch (IOException e) {
            // 处理可能发生的 IO 异常
            e.printStackTrace();
        }
    }

    @Override
    public void syncDevice(String token, String modelId) {
        if (StrUtil.isBlank(modelId)) {
            return;
        }
        if (StringUtils.isBlank(token)) {
            token = this.getRootCloudToken();
        }

        Map<String, OrsDeviceVersionResp> deviceVersion = getDeviceVersion(modelId, null);

        // 删除设备同步表中的数据
        deviceInfoSyncMapper.delete(new QueryWrapper<DeviceInfoSync>().lambda().eq(DeviceInfoSync::getModelId, modelId));

        // 先获得设备状态数据
        Map<String, DeviceStatuResp> map = new HashMap<>();
        // 循环 CenterId 集合
        for (String centerId : DeviceCenterIdList.deviceCenterIdList) {
            List<DeviceStatuResp> deviceStatu = getDeviceStatu(modelId, null, centerId);
            deviceStatu.forEach(x -> {
                if (null != x) {
                    map.put(x.getThingId(), x);
                }
            });
        }

        // 分页请求物模型对应的设备
        int page = 0;
        Map<String, Object> param = Maps.newHashMapWithExpectedSize(3);
        param.put("modelId", modelId);
        param.put("limit", pageSize);
        param.put("includeMetadata", true);



        //当前模型下所有设备的 MQTT 连接信息
        Map<String, DeviceMqttConnectInfo> deviceMqttConnectInfoMap = getDeviceMqttConnectInfoMap(token, modelId);

        // 通过三一集成平台获取设备国家关系
        Map<String, OrsDeviceCountry> stringCountryDataMap = getDeviceCountryMap();

        long total = 0;
        while (true) {
            param.put("skip", page * pageSize);

            OrsRootCloudResp resp = getIotData(param, token);

            List<OrsDeviceResp> deviceList =
                    JSONObject.parseArray(resp.getPayload(), OrsDeviceResp.class);
            // 由于数据在海外，数据请求时间长，可能会出现无法请求到数据的情况，这里进行重试
            if (CollUtil.isEmpty(deviceList)) {
                if (null != deviceList  && resp.getMetadata().getLong("totalCount") >  total) {
                    int i = 1;
                    do {
                        log.info("物模型【{}】第【{}】页没有获取到数据异常，第【{}】次尝试", modelId, page, i);
                        resp = getIotData(param, token);
                        deviceList = JSONObject.parseArray(resp.getPayload(), OrsDeviceResp.class);
                        i++;
                    } while (deviceList.isEmpty());
                } else {
                    return;
                }

            }


            List<DeviceInfoSync> deviceInfos = new ArrayList<>();
            // 请求到数据，对数据进行处理后准备入库
            deviceList.forEach(item -> {
                DeviceInfoSync entity = Convert.convert(DeviceInfoSync.class, item);
                if (entity.getName().toLowerCase().contains("old")
                    || entity.getName().toLowerCase().contains("test")) {
                    return;
                }

                if(StringUtils.isNotBlank(entity.getAssetId()) && stringCountryDataMap!=null){
                    if(stringCountryDataMap.containsKey(entity.getAssetId())){
                        OrsDeviceCountry countryData = stringCountryDataMap.get(entity.getAssetId());
                        entity.setCountry(countryData.getCntyDesc());
                        entity.setCountryCode(countryData.getCntyCode());
                    }else{
                        String assetId = entity.getAssetId();
                        //如果带_H 没有找到 去找一下 不带H的数据
                        if (assetId.contains("_H")) {
                            // 如果包含 "_H"，则移除它
                            assetId = assetId.replace("_H", "");
                            if(stringCountryDataMap.containsKey(assetId)){
                                OrsDeviceCountry countryData = stringCountryDataMap.get(assetId);
                                entity.setCountry(countryData.getCntyDesc());
                                entity.setCountryCode(countryData.getCntyCode());
                            }
                        }
                    }
                }

                entity.setDataSource(DeviceSource.NEWC.getValue());
                entity.setInstallType(InstallType.BEFORE.getValue());
                entity.setModelId(modelId);
                if (ObjectUtil.isNotNull(item.getConnectionConfig())) {
                    DeviceMqttConnectInfo deviceMqttConnectInfo = deviceMqttConnectInfoMap.get(item.getThingId());
                    if(ObjectUtil.isNotEmpty(deviceMqttConnectInfo)){
                        entity.setAuthToken(deviceMqttConnectInfo.getAuthToken());
                        entity.setUserName(deviceMqttConnectInfo.getUsername());
                    }
                }
                if (ObjectUtil.isNotNull(item.getThingInfo())) {
                    entity.setManufacturer(item.getThingInfo().getManufacturer());
                    entity.setModel(item.getThingInfo().getModel());
                    entity.setFwVersion(item.getThingInfo().getFwVersion());
                    entity.setHwVersion(item.getThingInfo().getHwVersion());
                    entity.setSimImsi(item.getThingInfo().getSimImsi());
                    entity.setPhoneNumber(item.getThingInfo().getPhoneNumber());
                }
                OrsDeviceVersionResp orsDeviceVersionResp = deviceVersion.get(item.getThingId());
                if(ObjectUtil.isNotEmpty(orsDeviceVersionResp)){
                    entity.setFwVersion(orsDeviceVersionResp.getFwVersion());
                    entity.setHwVersion(orsDeviceVersionResp.getHwVersion());
                }
                entity.setAssetIdNrc(entity.getAssetId().replace("RC",""));
                // 加入设备状态
                DeviceStatuResp statuResp = map.get(entity.getThingId());
                if (null != statuResp) {
                    if(Objects.isNull(statuResp.getOnline())){
                        entity.setOlineStatu(false);
                        entity.setActiveStatu(false);
                        entity.setDeviceStatus(1);
                    }else{
                        Boolean b = statuResp.getOnline();
                        entity.setOlineStatu(b);
                        entity.setActiveStatu(null != b);
                        // 设备状态 0=停用 1=未激活 2=已激活 默认未激活
                        if (item.getDeprecated() != null && item.getDeprecated()) {
                            entity.setDeviceStatus(0);
                        } else if(null == b) {
                            entity.setDeviceStatus(1);
                        } else if(null != b) {
                            entity.setDeviceStatus(2);
                        }
                        entity.setFirstDataTime(statuResp.getFirstDataTime());
                    }
                }
                // 数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站
                if (StringUtils.isBlank(item.getDataCenterId())) {
                    entity.setDataCenterId(0);
                } else if (item.getDataCenterId().endsWith("de")){
                    entity.setDataCenterId(1);
                } else if (item.getDataCenterId().endsWith("sg")){
                    entity.setDataCenterId(2);
                } else {
                    entity.setDataCenterId(0);
                }
                deviceInfos.add(entity);
            });
            deviceInfoSyncService.saveBatch(deviceInfos);
            total += deviceList.size();
            if (resp.getMetadata().getLong("totalCount") ==  total) {
                break;
            }
            page++ ;
        }
    }

    private Map<String, DeviceMqttConnectInfo> getDeviceMqttConnectInfoMap(String token, String modelId) {
        //查询该模型信息
        QueryWrapper<ModelInfoSync> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_id",modelId);
        ModelInfoSync modelInfoSync = modelInfoSyncMapper.selectOne(queryWrapper);

        Map<String, DeviceMqttConnectInfo> maps = new HashMap<>();
        //循环 CenterId 集合
        for (String centerId : DeviceCenterIdList.deviceCenterIdList) {
            while (true){

                DeviceResponse iotDeviceInfo = this.getIotDeviceInfo(token, centerId,modelInfoSync.getTenantId(),modelId);
                for (DeviceMqttConnectInfo info : iotDeviceInfo.getPayload()) {
                    if(StringUtils.isNotBlank(info.getThingId())){
                        maps.put(info.getThingId(),info);
                    }
                }
                //全部查询完成
                if(BeanUtil.isEmpty(iotDeviceInfo.getMetadata())||iotDeviceInfo.getMetadata().getTotalCount() != 100){
                    break;
                }
            }
        }
        return maps;
    }
    private DeviceResponse getIotDeviceInfo(String token, String centerId, String tenantId,String modelId){
        String url = String.format("%s/connectivity/v1/access-info/batch-query", urlPre);

        HttpRequest request = HttpRequest.post(url);
        Map<String, Object> param = new HashMap<>();
        param.put("_includeMetadata",true);
        param.put("_skip",0);
        param.put("_limit",100);
        param.put("modelIds",Arrays.asList(modelId));
        request.body(JSONObject.toJSONString(param));
        request.bearerAuth(token);
        request.header("X-RCD-Tenant-Id",tenantId);
        request.header("X-Data-Center-Id",centerId);
        HttpResponse response = request.execute();
        return DeviceResponse.getDeviceResponse(response.body());
    }

    private OrsRootCloudResp getIotData(Map<String, Object> param, String token){
        String url = String.format("%s/thing-instance/v1/thing/thing-classes/%s/instances", urlPre, param.get("modelId"));
        url = url + "?_limit=" + pageSize + "&_skip=" + (param.get("skip"));
        url = url + "&includeMetadata=" + param.get("includeMetadata");
        HttpRequest request = HttpRequest.get(url);

        request.body(JSONObject.toJSONString(param));
        request.bearerAuth(token);
        HttpResponse response = request.execute();
        return JSONObject.parseObject(response.body(), OrsRootCloudResp.class);
    }

    private OrsRootCloudResp getOverseaIotData(Map<String, Object> param, String token){
        String url = String.format("%s/thing-instance/v1/thing/thing-classes/%s/instances", urlPreOversea, param.get("modelId"));
        url = url + "?limit=" + pageSize + "&skip=" + (param.get("skip"));
        url = url + "&includeMetadata=" + param.get("includeMetadata");
        HttpRequest request = HttpRequest.get(url);

        request.bearerAuth(token);
        HttpResponse response = request.execute();
        return JSONObject.parseObject(response.body(), OrsRootCloudResp.class);
    }

    @Override
    public void syncProperty(String token, String modelId) {
        if (StrUtil.isBlank(modelId)) {
            return;
        }
        if (StringUtils.isBlank(token)) {
            token = this.getRootCloudToken();
        }
        int page = 0;
        // 删除物模型属性同步表中的数据
        modelPropertySyncMapper.delete(new QueryWrapper<ModelPropertySync>().lambda().eq(ModelPropertySync::getModelId, modelId));
        while (true) {
            // 请求物模型对应的属性信息
            String url = String.format("%s/thing-model/v1/thing/thing-classes/%s/properties", urlPre, modelId);
            HttpRequest request = HttpRequest.get(url + "?_limit=" + pageSize + "&_skip=" + (page * pageSize));
            request.bearerAuth(token);
            HttpResponse response = request.execute();

            if (StrUtil.isBlank(response.body())) {
                return;
            }
            OrsRootCloudResp resp = JSONObject.parseObject(response.body(), OrsRootCloudResp.class);

            List<ModelPropertySync> modelList =
                    JSONObject.parseArray(resp.getPayload(), ModelPropertySync.class);
            if (CollUtil.isEmpty(modelList)) {
                return;
            }

            modelList.forEach(item -> {
                item.setModelId(modelId);
            });
            modelPropertySyncService.saveBatch(modelList);
            page ++;
        }
    }

    /**
     * 海外根云数据请求token获取
     * @return
     */
    @Override
    public String getOverseaRootCloudToken() {

        // 如果redis里面存在，直接从redis里面获取
        SysRedisVO redisToken = serviceRedis.get(TOKEN_KEY_OVERSEA);
        if (Objects.nonNull(redisToken) && Objects.nonNull(redisToken.getValue())) {
            return String.valueOf(redisToken.getValue());
        }

        Map<String, String> param = Maps.newHashMapWithExpectedSize(3);
        param.put("client_id", clientIdOversea);
        param.put("client_secret", clientSecretOversea);
        param.put("grant_type", grantTypeOversea);

        try {
            HttpRequest request = HttpRequest.post(urlPreOversea + "/account-manage/v2/auth/login");
            request.body(JSONObject.toJSONString(param));
            HttpResponse response = request.execute();
            if (StrUtil.isBlank(response.body())) {
                return null;
            }
            OrsRootCloudTokenResp resp = JSONObject.parseObject(response.body(), OrsRootCloudTokenResp.class);
            String token = resp.getAccessToken();
            // 获得token  存入Redis
            if (StrUtil.isNotBlank(token)) {
                SysRedisVO sysRedisVO = new SysRedisVO();
                sysRedisVO.setKey(TOKEN_KEY_OVERSEA);
                sysRedisVO.setValue(token);
                sysRedisVO.setExpire(1800L);
                serviceRedis.save(sysRedisVO);
                return token;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取海外根云token异常" + e.getMessage());
        }
        return null;
    }

    /**
     * 根云数据请求token获取
     * @return
     */
    @Override
    public String getRootCloudToken() {

        // 如果redis里面存在，直接从redis里面获取
        SysRedisVO redisToken = serviceRedis.get(TOKEN_KEY);
        if (Objects.nonNull(redisToken) && Objects.nonNull(redisToken.getValue())) {
            return String.valueOf(redisToken.getValue());
        }

        Map<String, String> param = Maps.newHashMapWithExpectedSize(3);
        param.put("client_id", clientId);
        param.put("client_secret", clientSecret);
        param.put("grant_type", grantType);

        try {
            HttpRequest request = HttpRequest.post(urlPre + "/account-manage/v2/auth/login");
            request.body(JSONObject.toJSONString(param));
            HttpResponse response = request.execute();
            if (StrUtil.isBlank(response.body())) {
                return null;
            }
            OrsRootCloudTokenResp resp = JSONObject.parseObject(response.body(), OrsRootCloudTokenResp.class);
            String token = resp.getAccessToken();
            // 获得token  存入Redis
            if (StrUtil.isNotBlank(token)) {
                SysRedisVO sysRedisVO = new SysRedisVO();
                sysRedisVO.setKey(TOKEN_KEY);
                sysRedisVO.setValue(token);
                sysRedisVO.setExpire(1800L);
                serviceRedis.save(sysRedisVO);
                return token;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取根云token异常" + e.getMessage());
        }
        return null;
    }
    private List<DeviceStatuResp> getDeviceStatu(String modelId, String thingId, String centerId) {
        List<DeviceStatuResp> list = new ArrayList<>();
        if (StringUtils.isBlank(modelId) && StringUtils.isBlank(thingId)) {
            return list;
        }

        int page = 0;
        while (true) {
            String url = String.format("%s/thing-instance/v1/device/device-instances/status", urlPre);
            url = url + "?_limit=" + pageSize + "&_skip=" + (page * pageSize);
            if (StringUtils.isNotBlank(modelId)) {
                url =url + "&modelId=" + modelId;
            }
            if (StringUtils.isNotBlank(thingId)) {
                url = url + "&thingId=" + thingId;
            }
            // 不排序可能导致返回数据不全
            url = url + "&_sort=[{\"field\": \"name\", \"sortType\": \"ASC\"}]";
            HttpRequest request = HttpRequest.get(url);
            request.bearerAuth(getRootCloudToken());
            request.header("X-Data-Center-Id",centerId);
            HttpResponse response = request.execute();

            if (StrUtil.isBlank(response.body())) {
                break;
            }
            OrsRootCloudResp resp = new OrsRootCloudResp();
            try {

                resp = JSONObject.parseObject(response.body(), OrsRootCloudResp.class);
            } catch (Exception e) {
                log.error("modelId:{},centerId:{},异常{}",modelId, centerId, e.getMessage());
            }

            List<DeviceStatuResp> deviceinfos =
                    JSONObject.parseArray(resp.getPayload(), DeviceStatuResp.class);
            if (CollUtil.isEmpty(deviceinfos)) {
                break;
            }

            list.addAll(deviceinfos);
            page ++;
        }
        return list;
    }

    private Map<String, OrsDeviceVersionResp> getDeviceVersion(String modelId, String thingId) {

        if (StringUtils.isBlank(modelId) && StringUtils.isBlank(thingId)) {
            return new HashMap<>();
        }

        String tokenOversea = this.getOverseaRootCloudToken();
        int page = 0;
        Map<String, Object> param = Maps.newHashMapWithExpectedSize(3);
        param.put("modelId", modelId);
        param.put("limit", pageSize);
        param.put("includeMetadata", true);

        Map<String, OrsDeviceVersionResp> versionMap = new HashMap<>();
        while (true) {

            param.put("skip", page * pageSize);
            OrsRootCloudResp respOversea = getOverseaIotData(param, tokenOversea);
            List<OrsDeviceResp> deviceList =
                JSONObject.parseArray(respOversea.getPayload(), OrsDeviceResp.class);
            if (CollectionUtil.isEmpty(deviceList)) {
                break;
            }
            deviceList.forEach(item -> {

                if (item.getThingInfo() != null) {
                    OrsDeviceVersionResp deviceVersioninfo = new OrsDeviceVersionResp();
                    deviceVersioninfo.setThingId(item.getThingId());
                    deviceVersioninfo.setFwVersion(item.getThingInfo().getFwVersion());
                    deviceVersioninfo.setHwVersion(item.getThingInfo().getHwVersion());
                    versionMap.put(item.getThingId(), deviceVersioninfo);
                }

                });
            page ++;
        }
        return versionMap;
    }

//    public static void main(String[] args) {
//        Map<String, String> param = Maps.newHashMapWithExpectedSize(3);
//        param.put("client_id", "20231023f89f8a4f8efaab73");
//        param.put("client_secret", "99b727ea6618a847ceb4edea59491281");
//        param.put("grant_type", "client_credentials");
//        HttpRequest request = HttpRequest.post("https://openapi-pre.rootcloudapp.com/account-manage/v2/auth/login");
//        request.body(JSONObject.toJSONString(param));
//        HttpResponse response = request.execute();
//        System.out.println(response.body());
//    }
}
