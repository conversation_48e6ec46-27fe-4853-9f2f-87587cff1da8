package com.rc.admin.ors.quality.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/3 18:22
 * @describe
 */
@Getter
@Setter
public class UnReportDevice {

    private String assetId;
    private String modelId;
    private String thingId;
    private String deviceName;
    private String property;
    private String propertyName;
    private String propertyCode;
    private Integer paramCode;
    private String  paramValue;
    private Integer deviceNameCount;
    private Date paramValueLatestTime;

    private String dictDesc;

    private String modelName;

    private String division;

    private String productGroup;

    private String divisionCode;

    private String productGroupCode;

    private Integer dataCenterId;
}
