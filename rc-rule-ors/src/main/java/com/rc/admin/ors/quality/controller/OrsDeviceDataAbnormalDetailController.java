package com.rc.admin.ors.quality.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.dao.OrsDeviceDataAbnormalDetailMapper;
import com.rc.admin.ors.quality.entity.OrcTaskFile;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.excel.Device9007ExceptInfoDetailExcel;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsReq;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp;
import com.rc.admin.ors.quality.dao.OrcTaskFileMapper;
import com.rc.admin.ors.quality.service.OrsDeviceDataAbnormalDetailService;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 设备数据异常明细(OrsDeviceDataAbnormalDetail)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-25 10:13:37
 */
@ResponseResult
@RestController
@RequestMapping("orsDeviceDataAbnormalDetail")
@Api(tags = {"设备数据异常明细"})
public class OrsDeviceDataAbnormalDetailController {


    /**
     * 服务对象
     */
    @Resource
    private OrsDeviceDataAbnormalDetailService orsDeviceDataAbnormalDetailService;

    @Resource
    private OrsDeviceDataAbnormalDetailMapper orsDeviceDataAbnormalDetailMapper;

    /**
     * 分页查询所有数据
     *
     * @param page                        分页对象
     * @param orsDeviceDataAbnormalDetail 查询实体
     * @return 所有数据
     */
    @ApiOperation("异常数据明细")
    @GetMapping
    public Page<OrsDeviceDataAbnormalDetail> selectAll(Page<OrsDeviceDataAbnormalDetail> page, OrsDeviceDataAbnormalDetail orsDeviceDataAbnormalDetail) {
        return orsDeviceDataAbnormalDetailService.page(page, new QueryWrapper<>(orsDeviceDataAbnormalDetail));
    }

    @Resource
    private OrcTaskFileMapper orcTaskFileMapper;
    @ApiOperation("执行查询sql")
    @PostMapping("/export/sql")
    public String exportSqlData(@RequestBody String sql,@RequestParam("result_file_type") String result_file_type){
        OrcTaskFile orcTaskFile = new OrcTaskFile();
        orcTaskFile.setExecSql(sql);
        orcTaskFile.setCreateTime(new Date());
        orcTaskFile.setTaskStatus(1);
        orcTaskFile.setResultFileType(result_file_type);
        Long aLong = orcTaskFileMapper.selectCount(new QueryWrapper<OrcTaskFile>()
                .eq("task_status", 1)
                .or().eq("task_status", 2)
        );
        if(aLong > 0)
        {
            return "当前有任务正在执行";
        }
        int insert = orcTaskFileMapper.insert(orcTaskFile);
        return "插入结果:" +insert + ":"+orcTaskFile.getTaskId();
    }

    @ApiOperation("导出位置漂移的异常数据")
    @GetMapping("/export/9007")
    public void export9007Data(String start, String end, String abnormalCode,HttpServletResponse response){
        DqmDeviceDataExceptionsReq req = new DqmDeviceDataExceptionsReq();
        req.setDataScope("all");
        req.setStartTime(start);
        req.setEndTime(end);
        req.setAbnormalName(abnormalCode);

        Page<DqmDeviceDataExceptionsResp> page
                = orsDeviceDataAbnormalDetailMapper.findAbnormalDetail(new Page<>(1, 9999999999L), req);

        List<DqmDeviceDataExceptionsResp> data = page.getRecords();

        int totalPage = (data.size() / 10000) + 1;
        int pageSize = 10000;

        AtomicInteger atomic = new AtomicInteger(1);
        ExportParams params   = new ExportParams("设备位置漂移异常历史数据", "位置漂移历史数据");
        Workbook workbook = ExcelExportUtil.exportBigExcel(params, Device9007ExceptInfoDetailExcel.class, (o, p)->{
            if (p > totalPage) {
                return null;
            }
            int fromIndex = (p - 1) * pageSize;
            int toIndex = p != totalPage ? fromIndex + pageSize : data.size();

            List<DqmDeviceDataExceptionsResp> resps = data.subList(fromIndex, toIndex);
            List<Object> list = new ArrayList<>();
            resps.forEach(x->{

                Device9007ExceptInfoDetailExcel excel = new Device9007ExceptInfoDetailExcel();
                excel.setDivision(x.getSybbh());
                excel.setRegion(x.getZehdsvReg());
                excel.setProductGroup(x.getZehdSpartdesc());
                excel.setDeviceCode(x.getDeviceCode());
                excel.setAssetId(x.getAssetId());
                excel.setCountry(x.getCountry());
                excel.setExcetionTime(x.getAbnormalTime());

                JSONObject obj = JSONObject.parseObject(x.getAbnormalData());
                String newLng = obj.getString("新经度");
                String newLat = obj.getString("新纬度");
                String oldLng = obj.getString("旧经度");
                String oldLat = obj.getString("旧纬度");
                String location = "新经度:" + newLng + ",新纬度:" + newLat + "\n旧经度:" + oldLng + ",旧纬度:" + oldLat;
                excel.setLocation(location);
                excel.setDistance(obj.getString("漂移距离"));
                excel.setSort(atomic.getAndIncrement());

                String isExce = "N";
                double nlng = Double.parseDouble(newLng);
                double nlat = Double.parseDouble(newLat);
                double olng = Double.parseDouble(oldLng);
                double olat = Double.parseDouble(oldLat);

                double d = 0;
                // 经纬度任一一个等于0 标记为异常
                if (nlat == d || nlng == d || olat == d || olng == d) {
                    isExce = "Y";
                }

                double pos90 = 90;
                double neg90 = -90;
                // 经度任一一个如果不在＞90或者＜-90的范围内 标记为异常
                if (nlat > pos90 || nlat < neg90 || olat > pos90 || olat < neg90) {
                    isExce = "Y";
                }

                double pos180 = 180;
                double neg180 = -180;
                // 纬度任一一个如果不在＞180或者＜-180的范围内 标记为异常
                if (nlng > pos180 || nlng < neg180 || olng > pos180 || olng < neg180) {
                    isExce = "Y";
                }
                excel.setIsLatAndLngExce(isExce);
                list.add(excel);
            });

            return list;
        }, totalPage);
        if (workbook != null) {
            EasyPoiUtils.downLoadExcel("设备位置漂移异常历史数据.xlsx", response, workbook);
        }

    }
}

