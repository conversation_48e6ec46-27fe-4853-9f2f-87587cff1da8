package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ors_iot_model_info")
@ApiModel(value = "ors_iot_model_info对象", description = "ors物模型表")
public class OrsModelInfo {

    @ApiModelProperty(value = "设备模型行业分类索引")
    @TableField(value = "category")
    private String category;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "dept_scope")
    private String deptScope;

    @ApiModelProperty(value = "组织长名称")
    @TableField(value = "dept_scope_full_name")
    private String deptScopeFullName;

    @ApiModelProperty(value = "物模型描述")
    @TableField(value = "description")
    private String description;

    @ApiModelProperty(value = "全路径名称")
    @TableField(value = "full_group_name")
    private String fullGroupName;

    @ApiModelProperty(value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(value = "模型认证标识")
    @TableField(value = "model_key")
    private String modelKey;

    @ApiModelProperty(value = "物模型名称")
    @TableField(value = "name")
    private String name;

    private String thingType;
    private Integer offlineCheckPeriod;
    private Boolean isPublished;
    private Long timestampVaryRangeMillis;
    private String timestampUnit;
    private Date created;
    private String createdBy;
    private String creatorId;
    private Date updated;
    private String updatedBy;
    private String updatorId;
    private Boolean enableSelfReg;
    private String modelSecret;
    private Boolean enableSetGateway;
    private String version;
    private Boolean isAbstract;
    private Boolean hasActiveVersion;
    private String tenantId;
    private String publishStatus;
    private Boolean enableDiagnosis;
    private String cloudDataVisualizationName;
    private Boolean sendInputSwitch;
    private String modelStatus;
}
