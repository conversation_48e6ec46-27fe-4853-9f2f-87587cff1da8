package com.rc.admin.ors.quality.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *     设备数据源
 * </p>
 * <AUTHOR>
 * @since 2023/10/27
 */
@AllArgsConstructor
public enum DeviceSource {
    /**
     * 根云平台
     */
    ROOT_CLOUD(1, "根云"),
    /**
     * 新C
     */
    NEWC(2, "新C");

    @Getter
    private final Integer value;

    @Getter
    private final String desc;
}
