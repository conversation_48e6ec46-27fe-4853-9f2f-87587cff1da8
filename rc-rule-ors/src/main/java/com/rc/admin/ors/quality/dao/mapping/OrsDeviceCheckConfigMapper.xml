<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsDeviceCheckConfigMapper">

    <!--   查询设备的剔除信息   -->
    <select id="findRuleByDeviceCode" resultType="com.rc.admin.ors.quality.model.DeviceRule">
        SELECT
            T1.ID AS check_id,
            obdi.device_code,
            obdi.model_id,
            T1.exclude_type,
            T1.exclude_resean,
            obdi.thing_id as uuid,
            T1.create_user,
            T1.create_time,
            T1.asset_id,
            obdi.device_name,
            T1.property_name,
            T1.param_code,
            obdi.model_name,
            omd.product_group_code,
            omd.product_group_name as product_group,
            omd.division_name AS org_name,
            omd.division_code as org_code,
            obdi.country as zehdfsv_country
        FROM
            dqm.ors_device_check_config T1
                inner join dqm.ors_base_device_info obdi on obdi.asset_id = T1.asset_id
                INNER JOIN  dqm.ors_model_division omd ON omd.model_id = obdi.model_id
            ${ew.customSqlSegment}
    </select>

    <select id="selectAll" resultType="com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig">
        SELECT
            T1.id,
            T1.uuid,
            T1.exclude_type,
            T1.exclude_resean,
            T1.create_user,
            T1.create_time,
            T1.update_time,
            T1.update_user,
            T1.del_flag,
            T1.model_id,
            T1.device_code,
            T1.asset_id,
            T1.device_name,
            T1.property_name,
            T1.param_code,
            T1.param_name,
            obdi.data_center_id
        FROM
            dqm.ors_device_check_config T1
            inner join dqm.ors_base_device_info obdi on obdi.asset_id = T1.asset_id
        WHERE T1.del_flag = 0
    </select>

    <!--  查询某物模型对应的属性指标及其关联的设备对应的剔除规则  -->
    <select id="findModleIndicatorAndExclude" resultType="com.rc.admin.ors.quality.model.ModleIndicatorAndExclude">
        SELECT
            ompc.model_id,
            ompc.property,
            ompc.property_name,
            ompc.param_code,
            m.indicator_json,
            T3.device_code,
            T3.exclude_resean,
            T3.exclude_type,
            T3.create_time,
            T3.create_user
        FROM
            (
                    select  T1.model_id,
                    T1.param_code,
                    array_to_json( array_agg (row_to_json(ROW(T4.indicator_desc,T4.indicator_rule)))) AS indicator_json
                    from  dqm.ors_model_properties_config T1
                    LEFT JOIN   dqm.ors_indicator T4 ON  T1.param_code = T4.param_code
                    where model_id = #{modelId}
                    GROUP BY T1.model_id,T1.param_code
            ) m
            INNER JOIN dqm.ors_model_properties_config ompc ON ompc.model_id = m.model_id AND ompc.param_code = m.param_code AND ompc.model_id = #{modelId}
            LEFT JOIN dqm.ors_device_check_config T3 ON T3.exclude_type = ompc.property
                <if test="deviceCode != null and deviceCode != ''">
                    AND T3.device_code = #{deviceCode}
                </if>
                <if test="assetId != null and assetId != ''">
                    AND T3.asset_id = #{assetId}
                </if>
        inner join sany_data_service.sanyds_dict sd on m.param_code = sd.dict_id and sd.dict_type='single_param'
    </select>




    <select id="findModleIndicatorAndExcludeMultiple" resultType="com.rc.admin.ors.quality.model.ModleIndicatorAndExclude">
        SELECT
        ompc.model_id,
        ompc.property,
        ompc.property_name,
        ompc.param_code,
        m.indicator_json,
        T3.device_code,
        T3.exclude_resean,
        T3.exclude_type,
        T3.create_time,
        T3.create_user
        FROM
        (
        select  T1.model_id,
        T1.param_code,
        array_to_json( array_agg (row_to_json(ROW(T4.indicator_desc,T4.indicator_rule)))) AS indicator_json
        from  dqm.ors_model_properties_config T1
        LEFT JOIN   dqm.ors_indicator T4 ON  T1.param_code = T4.param_code
        where model_id = #{modelId}
        GROUP BY T1.model_id,T1.param_code
        ) m
        INNER JOIN dqm.ors_model_properties_config ompc ON ompc.model_id = m.model_id AND ompc.param_code = m.param_code AND ompc.model_id = #{modelId}
        LEFT JOIN dqm.ors_device_check_config T3 ON T3.exclude_type = ompc.property
        <if test="deviceCode != null and deviceCode != ''">
            AND T3.device_code = #{deviceCode}
        </if>
        <if test="assetId != null and assetId != ''">
            AND T3.asset_id = #{assetId}
        </if>
        inner join sany_data_service.sanyds_dict sd on m.param_code = sd.dict_id and sd.dict_type='multiple_param'
    </select>


    <insert id="insertHistory">
        insert into dqm.ors_device_check_config_history
            (id,uuid,exclude_type,exclude_resean,create_user,create_time,update_time,update_user,del_flag,model_id,device_code,asset_id,device_name,property_name,param_code,param_name,delete_time,delete_user)
        select id,uuid,exclude_type,exclude_resean,create_user,create_time,update_time,update_user,del_flag,model_id,device_code,asset_id,device_name,property_name,param_code,param_name,CURRENT_TIMESTAMP,#{username}
        from dqm.ors_device_check_config
        WHERE (device_code,property_name) IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            (#{item.deviceCode}, #{item.propertyName})
        </foreach>
    </insert>



    <insert id="insertHistoryById">
        insert into dqm.ors_device_check_config_history
        (id,uuid,exclude_type,exclude_resean,create_user,create_time,update_time,update_user,del_flag,model_id,device_code,asset_id,device_name,property_name,param_code,param_name,delete_time,delete_user)
        select id,uuid,exclude_type,exclude_resean,create_user,create_time,update_time,update_user,del_flag,model_id,device_code,asset_id,device_name,property_name,param_code,param_name,CURRENT_TIMESTAMP,#{username}
        from dqm.ors_device_check_config
        WHERE id = #{id}
    </insert>


    <delete id="batchDelete">
        delete
        from dqm.ors_device_check_config
        WHERE (device_code,property_name) IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            (#{item.deviceCode}, #{item.propertyName})
        </foreach>
    </delete>

</mapper>
