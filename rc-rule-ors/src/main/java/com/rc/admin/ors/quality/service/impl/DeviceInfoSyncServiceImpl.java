package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.DeviceInfoSyncMapper;
import com.rc.admin.ors.quality.entity.DeviceInfoSync;
import com.rc.admin.ors.quality.service.DeviceInfoSyncService;
import org.springframework.stereotype.Service;

/**
 * 根云设备数据同步表(DeviceInfoSync)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:18:05
 */
@Service("deviceInfoSyncService")
public class DeviceInfoSyncServiceImpl extends ServiceImpl<DeviceInfoSyncMapper, DeviceInfoSync> implements DeviceInfoSyncService {

}

