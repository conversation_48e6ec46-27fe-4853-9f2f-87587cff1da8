package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.QuestionStepUserMapper;
import com.rc.admin.ors.quality.entity.QuestionStepUser;
import com.rc.admin.ors.quality.service.QuestionStepUserService;
import org.springframework.stereotype.Service;

/**
 * 问题跟进处理环节负责人(QuestionStepUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-01 10:43:53
 */
@Service("questionStepUserService")
public class QuestionStepUserServiceImpl extends ServiceImpl<QuestionStepUserMapper, QuestionStepUser> implements QuestionStepUserService {

}

