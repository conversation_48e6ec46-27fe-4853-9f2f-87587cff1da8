package com.rc.admin.ors.quality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class OtDeviceAllReq implements Serializable {


    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String deviceSer;

    /**
     * 物联盒id
     */
    @ApiModelProperty(value = "物联盒id")
    private String tboxId;


    @ApiModelProperty(value = "租户名称")
    private String divisionName;

    @ApiModelProperty(value = "租户code")
    private String divisionCode;

    @ApiModelProperty(value = "产品组(模型)名称")
    private String spartDesc;

    @ApiModelProperty(value = "产品组(模型)Code")
    private String spart;


    /**
     * 物联盒子厂商
     */
    @ApiModelProperty(value = "物联盒子厂商")
    private String manufacturer;

    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码")
    private String cntyCode;

    /**
     * 国家描述
     */
    @ApiModelProperty(value = "国家描述")
    private String cntyDesc;

    /**
     * 事业部编码
     */
    @ApiModelProperty(value = "事业部编码")
    private String syb;

    /**
     * 事业部描述
     */
    @ApiModelProperty(value = "事业部描述")
    private String buInnerDesc;

    /**
     * 产品组编码
     */
    @ApiModelProperty(value = "产品组编码")
    private String d365Spart;

    /**
     * 产品组描述
     */
    @ApiModelProperty(value = "产品组描述")
    private String d365SpartDesc;

    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;

    /**
     * 设备状态描述
     */
    @ApiModelProperty(value = "设备状态描述")
    private String deviceStatusDesc;

    /**
     * 盒子状态
     */
    @ApiModelProperty(value = "盒子状态")
    private String tboxStatus;

    /**
     * 盒子状态描述
     */
    @ApiModelProperty(value = "盒子状态描述")
    private String tboxStatusDesc;

    /**
     * 转发站点（多个站点，逗号分隔）
     */
    @ApiModelProperty(value = "转发站点（多个站点，逗号分隔）")
    private String transmitStationCode;

    /**
     * 转发站点描述（多个站点，逗号分隔）
     */
    @ApiModelProperty(value = "转发站点描述（多个站点，逗号分隔）")
    private String transmitStationDesc;

    /**
     * 物标识
     */
    @ApiModelProperty(value = "物标识")
    private String assetId;

    /**
     * thing_id/UUID
     */
    @ApiModelProperty(value = "thing_id/UUID")
    private String thingId;

    /**
     * 模型id
     */
    @ApiModelProperty(value = "模型id")
    private String modelId;

    /**
     * 新C站点id
     */
    @ApiModelProperty(value = "新C站点id")
    private String ngcStationId;

    /**
     * 新C站点名称
     */
    @ApiModelProperty(value = "新C站点名称")
    private String ngcStationName;

    /**
     * 新C租户id
     */
    @ApiModelProperty(value = "新C租户id")
    private String tenantId;

    /**
     * 新C租户名
     */
    @ApiModelProperty(value = "新C租户名")
    private String tenantName;

    /**
     * d365台账是否存在
     */
    @ApiModelProperty(value = "d365台账是否存在")
    private Boolean d365Exist;

    /**
     * 新C是否存在
     */
    @ApiModelProperty(value = "新C是否存在")
    private Boolean ngcExist;

    /**
     * machinLink是否存在
     */
    @ApiModelProperty(value = "machinLink是否存在")
    private Boolean mlExist;

    /**
     * evi是否存在
     */
    @ApiModelProperty(value = "evi是否存在")
    private Boolean eviExist;

    /**
     * 是否多物联盒设备
     */
    @ApiModelProperty(value = "是否多物联盒设备")
    private Boolean multiTboxExist;

    @ApiModelProperty(value = "页码")
    private int current;

    @ApiModelProperty(value = "条数")
    private int size;

    @ApiModelProperty(value = "无物联盒子:传1 代表无 否则不传值")
    private String tboxIdCheck;


    @ApiModelProperty(value = "无事业部:传1 代表无 否则不传值")
    private String sybCheck;


    @ApiModelProperty(value = "无产品组织:传1 代表无 否则不传值")
    private String d365SpartCheck;
}
