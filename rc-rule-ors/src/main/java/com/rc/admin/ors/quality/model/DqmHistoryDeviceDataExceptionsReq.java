package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DqmHistoryDeviceDataExceptionsReq implements Serializable {
    @Setter
    @Getter
    @ApiModelProperty(value = "事业部")
    private String sybbh;
    @ApiModelProperty(value = "事业部")
    private List<String> sybbhList;
    @Setter
    @ApiModelProperty(value = "产品组")
    private String zehdSpart;
    @Setter
    @Getter
    @ApiModelProperty(value = "代理商")
    private String agent;
    @ApiModelProperty(value = "代理商")
    private String agentName;
    @Setter
    @Getter
    @ApiModelProperty(value = "国家")
    private String country;
    @Setter
    @Getter
    @ApiModelProperty(value = "客户名称")
    private String userName;
    @Getter
    @ApiModelProperty(value = "设备编号")
    private String deviceName;
    @ApiModelProperty(value = "设备编号列表")
    private List<String> deviceNameList;

    @ApiModelProperty(value = "存量分类")
    private String storeCategory;
    @Setter
    @Getter
    @ApiModelProperty(value = "检查项属性编码")
    private List<Integer> paramCode;
    @ApiModelProperty(value = "检查项属性集合")
    private String paramCodes;
    @Setter
    @Getter
    @ApiModelProperty("安装分类")
    private String installType;
    @ApiModelProperty(value = "设备编号集")
    private String abnormalDeviceList;
    @Setter
    @Getter
    @ApiModelProperty(value = "物标识")
    private String deviceCode;
    @ApiModelProperty(value = "物标识列表")
    private List<String> deviceCodeList;
    @ApiModelProperty(value = "所属大区")
    private String zehdsvReg;
    @ApiModelProperty(value = "所属大区")
    private List<String> zehdsvRegList;
    @Setter
    @Getter
    @ApiModelProperty(value = "所属大区名称")
    private String zehdSpartdesc;
    @ApiModelProperty(value = "所属大区名称")
    private List<String> zehdSpartdescList;

    @ApiModelProperty(value = "设备跟进中 0:否,1是 ")
    private String hasFollowUp;

    @ApiModelProperty("不统计华兴设备 0不统计,1统计")
    private String hasHuaXin;

    @ApiModelProperty("single_param 单属性;多属性:multiple_param")
    private String dictType;

    private List<String> huaXinModelIdList;

    @Setter
    @Getter
    @ApiModelProperty(value = "物标识")
    private String uuid;
    @Setter
    @Getter
    @ApiModelProperty(value = "实例名称")
    private String thingId;
    @Setter
    @Getter
    @ApiModelProperty(value = "模型Id")
    private String modelId;
    @ApiModelProperty(value = "模型Id")
    private List<String> modelIdList;
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    @ApiModelProperty(value = "模型名称")
    private List<String> modelNameList;

    @Setter
    @Getter
    @ApiModelProperty(value = " 数据期间 查询方式选择：可选项包括：年月、日期\\n\" +\n" +
            "            \"查询方式=年月，提供年度和月份选择输入。年份初始为2023，显示当前年份，月度默认显示当前月份\\n\" +\n" +
            "            \"查询方式=日期，提供日期范围选择，用户可以选择开始和结束日期")
    private String startTime;
    @Setter
    @Getter
    @ApiModelProperty(value = " 数据期间 查询方式选择：可选项包括：年月、日期\\n\" +\n" +
            "            \"查询方式=年月，提供年度和月份选择输入。年份初始为2023，显示当前年份，月度默认显示当前月份\\n\" +\n" +
            "            \"查询方式=日期，提供日期范围选择，用户可以选择开始和结束日期")
    private String endTime;
    @ApiModelProperty(value = "异常项")
    private String abnormalName;
    @Setter
    @Getter
    @ApiModelProperty(value = "异常项")
    private List<Integer> list;
    @ApiModelProperty(value = "设备集")
    private List<String> listName;
    @ApiModelProperty(value = "异常数")
    private String abnormalData;
    @ApiModelProperty(value = "最近在线时间")
    private String lastOnlineTime;
    @ApiModelProperty(value = "离线时长")
    private String offlineDuration;
    @Setter
    @ApiModelProperty(value = "最近位置")
    private String lastLocation;
    @Setter
    @Getter
    @ApiModelProperty(value = "行，1，2")
    private String row;
    @Setter
    @ApiModelProperty(value = "安装分类")
    private String installClassification;
    @Getter
    @Setter
    @ApiModelProperty(value = "数据范围 可选项：最新、所有，默认为“最新”\n" +
            "当选项为“最新”时，无论设备数据期间内有多少异常数据，都只显示设备在该期间内的最新一笔记录\n" +
            "当选项为“所有”时，显示设备在指定期间内的所有异常数据")
    private String dataScope;

    @Getter
    @Setter
    @ApiModelProperty(value = "页码")
    private int current;
    @Getter
    @Setter
    @ApiModelProperty(value = "条数")
    private int pageSize;
    @Getter
    @Setter
    @ApiModelProperty(value = "字段")
    private String sortField;
    @Getter
    @Setter
    @ApiModelProperty(value = "排序类型")
    private String sortOrder;
    @Getter
    @Setter
    @ApiModelProperty(value = "数据检查日期开始时间")
    private Date createTime_start;
    @Getter
    @Setter
    @ApiModelProperty(value = "数据检查日期结束时间")
    private Date createTime_end;
    @ApiModelProperty("总异常设备")
    private String abnormalDeviceKey;

    @Getter
    @Setter
    @ApiModelProperty("年月")
    private String yearMonthTime;

    @Getter
    @Setter
    @ApiModelProperty("数据中心")
    private Integer dataCenterId;

    @Getter
    @Setter
    private String handle;

    public void setDeviceName(String deviceName) {
        if (StringUtils.isNotBlank(deviceName)) {
            this.deviceName = deviceName.toUpperCase();
        }
    }
    public void setDeviceNameLast(String deviceName) {
        this.deviceName = deviceName;
    }

}
