package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/004
 */
@Setter
@Getter
@ApiModel("异常属性数量及占比")
public class AbnormalCodeRateResp {

    @ApiModelProperty("总异常数")
    private int total;

    @ApiModelProperty("属性编码")
    private String paramCode;

    @ApiModelProperty("属性名称")
    private String propertyName;

    @ApiModelProperty("异常编码")
    private String abnormalCode;

    @ApiModelProperty("异常名称")
    private String abnormalName;

    @ApiModelProperty("该属性异常出现次数")
    private String abnormalCodeCount;

    @ApiModelProperty("异常属性占总数比，abnormalCodeCount/total*100%")
    private String abnormalCodeRate;


    @ApiModelProperty("对应具体异常明细")
    private List<AbnormalCodeRateResp> abnormalCodeRateRespList;

}
