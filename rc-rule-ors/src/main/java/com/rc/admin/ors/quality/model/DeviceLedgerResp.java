package com.rc.admin.ors.quality.model;

import com.rc.admin.ors.quality.utils.FactoryDateType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Setter
@Getter
@ApiModel("设备台账响应数据")
public class DeviceLedgerResp {

    @ApiModelProperty("主键（详情时必传）")
    private String id;

    @ApiModelProperty("新事业部code")
    private String newDivisionCode;

    @ApiModelProperty("新产品组code")
    private String newProductGroupCode;

    @ApiModelProperty("新事业部名称")
    private String newDivisionName;

    @ApiModelProperty("新产品组名称")
    private String newProductGroupName;

    @ApiModelProperty("新国家名称")
    private String newCountryName;

    @ApiModelProperty("新国区名称")
    private String countryRegionName;

    @ApiModelProperty("新大区名称")
    private String newRegionName;

    @ApiModelProperty("UUID")
    private String uuid;

    @ApiModelProperty("物标识")
    private String assetId;

    @ApiModelProperty("设备编号")
    private String deviceNo;

    @ApiModelProperty("实例名称")
    private String name;

    @ApiModelProperty("物模型名称")
    private String modelName;

    @ApiModelProperty("注册时间")
    private Date created;

    @ApiModelProperty("crm注册时间（0==>否；1==>是）")
    private Integer crmRegister;

    @ApiModelProperty("设备状态 0=停用 1=未激活 2=已激活 默认未激活")
    private Integer deviceStatus;

    @ApiModelProperty("数据中心 0=亚洲主站 1=欧洲法兰克福站点 2=亚洲新加坡站点 3=非洲开普敦站点 默认亚洲主站")
    private Integer dataCenterId;

    @ApiModelProperty("根云物标识")
    private String rcAssetId;

    @ApiModelProperty("安装分类")
    private String installType;

    @ApiModelProperty("出厂时间")
    private Date factoryDate;

    @ApiModelProperty("出厂时长CODE")
    public String factoryDuration;

    public String getFactoryDuration() {
        return FactoryDateType.getDateValue(factoryDate).toString();
    }

    @ApiModelProperty("问题跟进(1=进行中 2=已完成)")
    private String problemFollow;

    @ApiModelProperty("是否剔除(0==>否；1==>是)")
    private String isEliminate;

    @ApiModelProperty("数据源")
    private String dataSource;

    @ApiModelProperty("数据源")
    private String dataSourceTenant = "设备模型池";
    //TODO 取值需要设置

    @ApiModelProperty("事业部")
    private String division;

    private String divisionCode;

    @ApiModelProperty("大区")
    private String region;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("国家Code")
    private String countryCode;

    @ApiModelProperty("产品组")
    private String productGroup;

    @ApiModelProperty("销售代理商")
    private String agent;

    @ApiModelProperty("服务代理商")
    private String service;

//    @ApiModelProperty("实际购买人")
//    private String actualPurchaser;
//
//    @ApiModelProperty("名义购买人")
//    private String nominalPurchaser;

    @ApiModelProperty("离线时长")
    private Date offlineTime;

    @ApiModelProperty("离线时长CODE")
    public String offlineTimeCode;

    @ApiModelProperty("物模型ID")
    private String modelId;

    @ApiModelProperty("硬件版本号")
    private String hwVersion;

    @ApiModelProperty("固件版本号")
    private String fwVersion;

    @ApiModelProperty("秘钥")
    private String authToken;

    @ApiModelProperty("交机日期")
    private Date deliveryDate;

    @ApiModelProperty("开机日期")
    private Date openDate;

    @ApiModelProperty("离线时长 天数")
    private Integer offlineDays;

    @ApiModelProperty("异常标识 0=无异常 1=异常")
    private String exceFlag;

    @ApiModelProperty("异常描述")
    private String exceDesc;

    @ApiModelProperty(name = "quesLevel", value = "问题跟进优先级 P0 P1 P2 P3")
    private String quesLevel;

    @ApiModelProperty(name = "checkItem", value = "检查项")
    private String checkItem;

    @ApiModelProperty(name = "exceItem", value = "异常项")
    private String exceItem;

    @ApiModelProperty(name = "curStep", value = "当前环节")
    private String curStep;

    @ApiModelProperty(name = "userAccount", value = "当前处理责任人账号")
    private String userAccount;

    @ApiModelProperty(name = "userName", value = "当前处理责任人姓名")
    private String userName;

    @ApiModelProperty(name = "curStepName", value = "当前环节名称")
    private String curStepName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    @ApiModelProperty(name = "tboxId", value = "物联盒ID")
    private String tboxId;

    @ApiModelProperty(name = "eliminateProperty", value = "剔除项")
    private String eliminateProperty;

    public String getOfflineTimeCode(){
        if (null == offlineDays) {
            return null;
        }
        if (offlineDays == 0) {
            return "1";
        }
        if (offlineDays >= 1 && offlineDays <= 7) {
            return "2";
        }
        if (offlineDays > 7 && offlineDays <= 15) {
            return "3";
        }
        if (offlineDays > 15 && offlineDays <= 30) {
            return "4";
        }
        if (offlineDays > 30 && offlineDays <= 60) {
            return "5";
        }
        if (offlineDays > 60 && offlineDays <= 180) {
            return "6";
        }
        if (offlineDays > 180) {
            return "7";
        }

        return "";
    }

    @ApiModelProperty("长期未激活时长 天数")
    private Integer notActivatDays;
}
