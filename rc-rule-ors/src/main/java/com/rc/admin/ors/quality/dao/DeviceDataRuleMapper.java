package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.DeviceDataRule;
import com.rc.admin.ors.quality.model.*;
import org.apache.ibatis.annotations.Param;


public interface DeviceDataRuleMapper extends BaseMapper<DeviceDataRule> {


    Page<DqmDeviceDataRuleExceptionsResp> findAbnormal(Page<?> page, @Param("req") DqmDeviceDataRuleExceptionsReq req);



    Page<DqmDeviceDataRuleDetailExceptionsResp> findAbnormalDetail(Page<?> page, @Param("req") DqmDeviceDataRuleDetailExceptionsReq req);
}

