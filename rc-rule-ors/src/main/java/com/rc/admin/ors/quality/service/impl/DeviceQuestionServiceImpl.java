package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.DeviceQuestionMapper;
import com.rc.admin.ors.quality.entity.DeviceQuestion;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;
import com.rc.admin.ors.quality.service.DeviceQuestionService;
import org.springframework.stereotype.Service;

/**
 * 设备问题跟进(DeviceQuestion)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-30 16:55:32
 */
@Service("deviceQuestionService")
public class DeviceQuestionServiceImpl extends ServiceImpl<DeviceQuestionMapper, DeviceQuestion> implements DeviceQuestionService {

    @Override
    public Page<DeviceLedgerResp> questionList(Page<DeviceLedgerResp> page, Wrapper wrapper, String queryAccess) {
        return baseMapper.questionList(page, wrapper, queryAccess);
    }

    @Override
    public Page<OrsDeviceDataAbnormalDetail> findAbnormalDateByDate(Page<OrsDeviceDataAbnormalDetail> page, Long questionId) {
        return baseMapper.findAbnormalDateByDate(page, questionId);
    }
}

