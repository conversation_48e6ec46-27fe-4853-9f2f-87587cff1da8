package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class OtDeviceAll implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备编码
     */
    @TableField(value = "device_ser")
    @ApiModelProperty(value = "设备编码")
    private String deviceSer;

    /**
     * 物联盒id
     */
    @TableField(value = "tbox_id")
    @ApiModelProperty(value = "物联盒id")
    private String tboxId;

    /**
     * 物联盒子厂商
     */
    @TableField(value = "manufacturer")
    @ApiModelProperty(value = "物联盒子厂商")
    private String manufacturer;

    /**
     * 国家编码
     */
    @TableField(value = "cnty_code")
    @ApiModelProperty(value = "国家编码")
    private String cntyCode;

    /**
     * 国家描述
     */
    @TableField(value = "cnty_desc")
    @ApiModelProperty(value = "国家描述")
    private String cntyDesc;

    /**
     * 事业部编码
     */
    @TableField(value = "division_code")
    @ApiModelProperty(value = "事业部编码")
    private String divisionCode;

    /**
     * 事业部描述
     */
    @TableField(value = "division_name")
    @ApiModelProperty(value = "事业部描述")
    private String divisionName;

    /**
     * 产品组编码
     */
    @TableField(value = "spart")
    @ApiModelProperty(value = "产品组编码")
    private String spart;

    /**
     * 产品组描述
     */
    @TableField(value = "spart_desc")
    @ApiModelProperty(value = "产品组描述")
    private String spartDesc;

    /**
     * 设备状态
     */
    @TableField(value = "device_status")
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;

    /**
     * 设备状态描述
     */
    @TableField(value = "device_status_desc")
    @ApiModelProperty(value = "设备状态描述")
    private String deviceStatusDesc;

    /**
     * 盒子状态
     */
    @TableField(value = "tbox_status")
    @ApiModelProperty(value = "盒子状态")
    private String tboxStatus;

    /**
     * 盒子状态描述
     */
    @TableField(value = "tbox_status_desc")
    @ApiModelProperty(value = "盒子状态描述")
    private String tboxStatusDesc;

    /**
     * 转发站点（多个站点，逗号分隔）
     */
    @TableField(value = "transmit_station_code")
    @ApiModelProperty(value = "转发站点（多个站点，逗号分隔）")
    private String transmitStationCode;

    /**
     * 转发站点描述（多个站点，逗号分隔）
     */
    @TableField(value = "transmit_station_desc")
    @ApiModelProperty(value = "转发站点描述（多个站点，逗号分隔）")
    private String transmitStationDesc;

    /**
     * 物标识
     */
    @TableField(value = "asset_id")
    @ApiModelProperty(value = "物标识")
    private String assetId;

    /**
     * thing_id/UUID
     */
    @TableField(value = "thing_id")
    @ApiModelProperty(value = "thing_id/UUID")
    private String thingId;

    /**
     * 模型id
     */
    @TableField(value = "model_id")
    @ApiModelProperty(value = "模型id")
    private String modelId;

    /**
     * 新C站点id
     */
    @TableField(value = "ngc_station_id")
    @ApiModelProperty(value = "新C站点id")
    private String ngcStationId;

    /**
     * 新C站点名称
     */
    @TableField(value = "ngc_station_name")
    @ApiModelProperty(value = "新C站点名称")
    private String ngcStationName;

    /**
     * 新C租户id
     */
    @TableField(value = "tenant_id")
    @ApiModelProperty(value = "新C租户id")
    private String tenantId;

    /**
     * 新C租户名
     */
    @TableField(value = "tenant_name")
    @ApiModelProperty(value = "新C租户名")
    private String tenantName;

    /**
     * d365台账是否存在
     */
    @TableField(value = "d365_exist")
    @ApiModelProperty(value = "d365台账是否存在")
    private boolean d365Exist;

    /**
     * 新C是否存在
     */
    @TableField(value = "ngc_exist")
    @ApiModelProperty(value = "新C是否存在")
    private boolean ngcExist;

    /**
     * machinLink是否存在
     */
    @TableField(value = "ml_exist")
    @ApiModelProperty(value = "machinLink是否存在")
    private boolean mlExist;

    /**
     * evi是否存在
     */
    @TableField(value = "evi_exist")
    @ApiModelProperty(value = "evi是否存在")
    private boolean eviExist;

    /**
     * 是否多物联盒设备
     */
    @TableField(value = "multi_tbox_exist")
    @ApiModelProperty(value = "是否多物联盒设备")
    private boolean multiTboxExist;
}
