package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/1 16:51
 * @describe 历史问题设备导入
 */
@Getter
@Setter
public class DeviceHisQuestionExcel {

    @Excel(name = "设备编号", width = 15)
    private String deviceNo;

    @Excel(name = "异常项")
    private String exceItem;

    @Excel(name = "检查项")
    private String checkItem;

    @Excel(name = "优先级")
    private String quesLevel;

    @Excel(name = "当前处理状态", addressList = true, replace = {"平台处理_platfrom", "硬件处理_hardware", "研究院处理_research_institute","待启机/待剔除_waitStart", "运营处理_operate", "待验证_waitVerify", "问题关闭_close"})
    private String curStepName;

    @Excel(name = "处理意见")
    private String handlIdea;

    @Excel(name = "问题原因")
    private String questionResean;

    @Excel(name = "对应环节处理人账户")
    private String handleUser;

    @Excel(name = "问题的开始时间", format = "yyyy-MM-dd")
    private Date qstar;

    @Excel(name = "问题的结束时间", format = "yyyy-MM-dd")
    private Date qEnd;

    private String checkItemCode;

    private String exceItemCode;

}
