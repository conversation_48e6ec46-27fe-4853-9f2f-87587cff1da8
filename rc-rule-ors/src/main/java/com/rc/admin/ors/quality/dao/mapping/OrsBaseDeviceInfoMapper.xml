<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper">
    <select id="totalDeviceStatistics" resultType="com.rc.admin.ors.quality.model.TotalDeviceStatisticsResp">
        with distinct_devices as (select distinct device_code
                                  from ors_base_device_info
                                  where device_code is not null)
        select count(device_code)                                          as totalDeviceNumber,
               sum(case when model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then 1 else 0 end)     as sgIotBox,
               sum(case when model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then 1 else 0 end) as hxIotBox
        from distinct_devices
    </select>


    <select id="deviceRatioNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioResp">
        SELECT
        round( COALESCE ( AVG ( bb.param_abnormal_rate )* 100, 0 ), 2 ) AS totalAccuracy,
        round( COALESCE ( AVG ( bb.null_abnormal_rate )* 100, 0 ), 2 ) AS totalIntegrityRate,
        round( COALESCE ( AVG ( case when omd.model_id not in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')  then bb.param_abnormal_rate else  NULL END )* 100, 0 ), 2 ) AS sgIotBoxAccuracy,
        round( COALESCE ( AVG ( case when omd.model_id not in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')  then bb.null_abnormal_rate  else  NULL END )* 100, 0 ), 2 ) AS sgIotBoxIntegrityRate,
        round( COALESCE ( AVG ( case when omd.model_id  in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')  then bb.param_abnormal_rate else  NULL END )* 100, 0 ), 2 ) AS hxIotBoxAccuracy,
        round( COALESCE ( AVG ( case when omd.model_id  in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')  then bb.null_abnormal_rate  else  NULL END )* 100, 0 ), 2 ) AS hxIotBoxIntegrityRate
        FROM
        dqm.ors_device_rate_day bb
        LEFT JOIN dqm.ors_base_device_info bdi ON bdi.asset_id = bb.asset_id
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = bdi.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location
        FROM dqm.ors_device_location
        WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON bb.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="startTime != null and startTime != ''">
                AND bb.stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND bb.stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
    </select>



    <select id="deviceRatioChangeVersion" resultType="com.rc.admin.ors.quality.model.DeviceRatioResp">
        -- 1.查询总的数量
        WITH devices_all_count AS(SELECT
        count(distinct case when T3.asset_id is not null then T3.asset_id end)  as 总上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{query.startTime}::DATE
        AND stat_date::DATE &lt;= #{query.endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{query.startTime}::date
        and T3.report_date <![CDATA[<=]]> #{query.endTime}::date
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{query.doubleRateSign})
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{query.doubleRateSign} = '3' THEN '2' ELSE #{query.doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        <if test="query.division != null and query.division != ''">
            and T3.division_code = #{query.division}
        </if>
        ),

        -- 1.根据reportlog表查询数量
        devices_count AS(SELECT
        T3.report_date,
        count(distinct case when T3.asset_id is not null then T3.asset_id end)  as 总上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{query.startTime}::DATE
        AND stat_date::DATE &lt;= #{query.endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{query.startTime}::date
        and T3.report_date <![CDATA[<=]]> #{query.endTime}::date
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{query.doubleRateSign})
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{query.doubleRateSign} = '3' THEN '2' ELSE #{query.doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        <if test="query.division != null and query.division != ''">
            and T3.division_code = #{query.division}
        </if>
        group by T3.report_date
        )

        -- 2.查询返回对应的数据
        select round((avg(a2.总数据准确率) * 100)::numeric, 2)   as totalAccuracy,
        round((avg(a2.总数据完整率) * 100)::numeric, 2)   as totalIntegrityRate,
        (select 总上数台数  from devices_all_count) as totalDeviceCount
        from (
        select a.日期,
        (a.总上数台数 - a.总未上报异常数量)*1.0 / NULLIF(a.总上数台数, 0)       as 总数据完整率,
        (a.总上数台数 - a.总参数异常数量)*1.0 / NULLIF(a.总上数台数, 0)         as 总数据准确率,
        NULLIF(a.总上数台数, 0) AS 总上数台数
        from (
        select
        aa.stat_date as 日期,
        aa.总未上报异常数量,
        aa.总参数异常数量,
        bb.总上数台数
        FROM
        (select
        dd.stat_date,
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 总未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 总参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{query.startTime}::DATE
        AND stat_date::DATE &lt;= #{query.endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{query.startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{query.endTime}::date
        <if test="query.division != null and query.division != ''">
            and dd.division_code = #{query.division}
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{query.doubleRateSign})
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="query.rulesSign != null and query.rulesSign != '' and query.rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="query.doubleRateSign != null and query.doubleRateSign != '' and query.doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>

        <if test="query.doubleRateSign != null and query.doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{query.doubleRateSign} = '3' THEN '2' ELSE #{query.doubleRateSign} END)
        </if>
        GROUP BY
        dd.stat_date)aa
        left join devices_count bb  on aa.stat_date = bb.report_date
        )a) a2
    </select>


    <select id="deviceRatioChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioResp">
        -- 1.根据reportlog表查询数量
        WITH devices_count AS(SELECT
        T3.report_date,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as 总上数台数,
        cast(COUNT ( DISTINCT CASE WHEN T3.asset_id is not null and NOT exists (select 1 from dqm.ors_double_rate_config rc
        WHERE rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN T3.asset_id END ) as float) AS 树根上数台数,
        cast(COUNT ( DISTINCT CASE WHEN T3.asset_id is not null and exists (select 1 from dqm.ors_double_rate_config rc WHERE
        rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN T3.asset_id END ) as float) AS 华兴上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        group by T3.report_date
        )

        -- 2.查询返回对应的数据
        select round((avg(a2.总数据准确率) * 100)::numeric, 2)   as totalAccuracy,
        round((avg(a2.总数据完整率) * 100)::numeric, 2)   as totalIntegrityRate,
        round((avg(a2.树根数据准确率) * 100)::numeric, 2) as sgIotBoxAccuracy,
        round((avg(a2.树根数据完整率) * 100)::numeric, 2) as sgIotBoxIntegrityRate,
        round((avg(a2.华兴数据准确率) * 100)::numeric, 2) as hxIotBoxAccuracy,
        round((avg(a2.华兴数据完整率) * 100)::numeric, 2) as hxIotBoxIntegrityRate
        from (
        select a.日期,
        (a.总上数台数 - a.总未上报异常数量) / NULLIF(a.总上数台数, 0)       as 总数据完整率,
        (a.总上数台数 - a.总参数异常数量) / NULLIF(a.总上数台数, 0)         as 总数据准确率,
        (a.树根上数台数 - a.树根未上报异常数量) / NULLIF(a.树根上数台数, 0) as 树根数据完整率,
        (a.树根上数台数 - a.树根参数异常数量) / NULLIF(a.树根上数台数, 0)   as 树根数据准确率,
        (a.华兴上数台数 - a.华兴未上报异常数量) / NULLIF(a.华兴上数台数, 0) as 华兴数据完整率,
        (a.华兴上数台数 - a.华兴参数异常数量) / NULLIF(a.华兴上数台数, 0)   as 华兴数据准确率
        from (
        select
        aa.stat_date as 日期,
        aa.总未上报异常数量,
        aa.总参数异常数量,
        aa.树根未上报异常数量,
        aa.树根参数异常数量,
        aa.华兴未上报异常数量,
        aa.华兴参数异常数量,
        bb.总上数台数,
        bb.树根上数台数,
        bb.华兴上数台数
        FROM
        (select
        dd.stat_date,
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 总未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 总参数异常数量,
        COUNT ( DISTINCT CASE WHEN  abnormal_code IN ( 9008,9009 ) and NOT exists (select 1 from dqm.ors_double_rate_config rc WHERE rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN dd.device_name END ) AS 树根未上报异常数量,
        COUNT ( DISTINCT CASE WHEN  abnormal_code not IN ( 9008,9009 ) and NOT exists (select 1 from dqm.ors_double_rate_config rc WHERE rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN dd.device_name END ) AS 树根参数异常数量,
        COUNT ( DISTINCT CASE WHEN  abnormal_code IN ( 9008,9009 ) and exists (select 1 from dqm.ors_double_rate_config rc WHERE rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN dd.device_name END ) AS 华兴未上报异常数量,
        COUNT ( DISTINCT CASE WHEN  abnormal_code not IN ( 9008,9009 ) and exists (select 1 from dqm.ors_double_rate_config rc WHERE rc.double_rate_sign = '4' and di.model_id = rc.model_id) THEN dd.device_name END ) AS 华兴参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{endTime}::date

        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="rulesSign != null and rulesSign != '' and rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>

        <if test="doubleRateSign != null and doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END)
        </if>
        GROUP BY
        dd.stat_date)aa
        left join devices_count bb  on aa.stat_date = bb.report_date
        )a) a2
    </select>

    <select id="deviceRatio" resultType="com.rc.admin.ors.quality.model.DeviceRatioResp">
        select round((avg(a2.总数据准确率) * 100)::numeric, 2)   as totalAccuracy,
               round((avg(a2.总数据完整率) * 100)::numeric, 2)   as totalIntegrityRate,
               round((avg(a2.树根数据准确率) * 100)::numeric, 2) as sgIotBoxAccuracy,
               round((avg(a2.树根数据完整率) * 100)::numeric, 2) as sgIotBoxIntegrityRate,
               round((avg(a2.华兴数据准确率) * 100)::numeric, 2) as hxIotBoxAccuracy,
               round((avg(a2.华兴数据完整率) * 100)::numeric, 2) as hxIotBoxIntegrityRate
        from (select a.日期,
                     (a.总上数台数 - a.总未上报异常数量) / NULLIF(a.总上数台数, 0)       as 总数据完整率,
                     (a.总上数台数 - a.总参数异常数量) / NULLIF(a.总上数台数, 0)         as 总数据准确率,
                     (a.树根上数台数 - a.树根未上报异常数量) / NULLIF(a.树根上数台数, 0) as 树根数据完整率,
                     (a.树根上数台数 - a.树根参数异常数量) / NULLIF(a.树根上数台数, 0)   as 树根数据准确率,
                     (a.华兴上数台数 - a.华兴未上报异常数量) / NULLIF(a.华兴上数台数, 0) as 华兴数据完整率,
                     (a.华兴上数台数 - a.华兴参数异常数量) / NULLIF(a.华兴上数台数, 0)   as 华兴数据准确率
              from (select T3.report_date                                                                        as 日期,
                           count(distinct case
                                              when T3.asset_id is not null and T3.null_param_codes is not null
                                                  then T3.asset_id
                               end)                                                                              as 总未上报异常数量,
                           count(distinct case
                                              when T3.asset_id is not null and T3.abnormal_param_codes is not null
                                                  then T3.asset_id
                               end)                                                                              as 总参数异常数量,
                           cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as 总上数台数,
                           count(distinct case
                                              when T3.asset_id is not null and T3.null_param_codes is not null
                                                  and T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then T3.asset_id
                               end)                                                                              as 树根未上报异常数量,
                           count(distinct case
                                              when T3.asset_id is not null and T3.abnormal_param_codes is not null
                                                  and T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then T3.asset_id
                               end)                                                                              as 树根参数异常数量,
                           cast(count(distinct case
                                                   when T3.asset_id is not null and T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                                                       then T1.asset_id end) as float)                           as 树根上数台数,
                           count(distinct case
                                              when T3.asset_id is not null and T3.null_param_codes is not null
                                                  and T1.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then T3.asset_id
                               end)                                                                              as 华兴未上报异常数量,
                           count(distinct case
                                              when T3.asset_id is not null and T3.abnormal_param_codes is not null
                                                  and T1.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385') then T3.asset_id
                               end)                                                                              as 华兴参数异常数量,
                           cast(count(distinct case
                                                   when T3.asset_id is not null and T1.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                                                       then T1.asset_id end) as float)                           as 华兴上数台数
                    from (select T1.asset_id,
                                 T1.product_group,
                                 T1.product_group_code,
                                 T1.crm_register,
                                 T1.active_statu,
                                 T1.country_region,
                                 T1.model_id,
                                 T1.device_code
                          from dqm.ors_base_device_info T1
                            left join (
                              select distinct asset_id, device_location from dqm.ors_device_location
                              where device_location = '国内'
                                and stat_date::DATE &gt;= #{startTime}::DATE
                                AND stat_date::DATE &lt;= #{endTime}::DATE
                              ) t3 on T1.asset_id = t3.asset_id
                          where T1.device_code is not null
                            and (t3.device_location is null)
                            <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
                                and T1.country_code
                                in
                                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
                            </if>
                            <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
                                and (T1.country_code
                                not
                                in
                                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
                                or T1.country_code is null
                                )
                            </if>
                        ) T1
                             left join dqm.ors_device_check_config T2 on
                        T2.asset_id = T1.asset_id
                            and T2.create_time <![CDATA[<=]]> #{endTime}::timestamp
        inner join

        (
        select
        <choose>
            <when test="doubleRateSign != null and doubleRateSign != ''">
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END ) )
                ) AS param_codes,
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.abnormal_param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END  ) )
                ) AS abnormal_param_codes,
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.null_param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END  ) )
                ) AS null_param_codes,
            </when>
            <otherwise>
                T3.param_codes,
                T3.null_param_codes,
                T3.abnormal_param_codes,
            </otherwise>
        </choose>
        T3.asset_id,
        T3.report_date
        from dqm.ors_device_param_report_log T3
        where T3.asset_id not in (select asset_id
        from dqm.ors_device_check_config T2
        where exclude_type = 'WHOLE'
        and T2.create_time <![CDATA[<=]]> #{endTime}::timestamp)
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and (
            string_to_array(T3.null_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            or
            string_to_array(T3.abnormal_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            or
            string_to_array(T3.report_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            )
        </if>
        ) T3
        on
            T3.asset_id = T1.asset_id
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
        INNER JOIN  dqm.ors_model_division omd ON omd.model_id = T1.model_id
            and omd.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        group by T3.report_date) a) a2
        limit 1
    </select>

    <sql id="ors_base_device_info_sub_sql">
        (select T1.asset_id,
                T1.crm_register,
                T1.active_statu,
                T1.model_id,
                T1.device_code,
                T1.country_code,
                T4.region,
                T4.region_code
         from dqm.ors_base_device_info T1
          left join dqm.ors_country_region_sync T4
             ON T1.country_code = T4.country_code
          left join (
            select distinct asset_id, device_location from dqm.ors_device_location
            where device_location = '国内'
            and stat_date::DATE &gt;= #{startTime}::DATE
            AND stat_date::DATE &lt;= #{endTime}::DATE
          ) t2 on T1.asset_id = t2.asset_id
         where T1.device_code is not null
            <if test="iotBoxType != null and iotBoxType == 'HX'">
                and T1.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="iotBoxType != null and iotBoxType == 'SG'">
                and T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
                and T1.country_code
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
            </if>
            <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
                and (T1.country_code
                not
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
                    or T1.country_code is null
                )
            </if>
            and (t2.device_location is null )
        ) T1
        left join dqm.ors_device_check_config T2 on T2.asset_id = T1.asset_id
        and T2.create_time <![CDATA[<=]]> #{endTime}::timestamp
        inner join (
        select
        <choose>
            <when test="doubleRateSign != null and doubleRateSign != ''">
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END  ) )
                ) AS param_codes,
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.abnormal_param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END  ) )
                ) AS abnormal_param_codes,
                (
                SELECT
                array_to_string( ARRAY_AGG ( DISTINCT elem ), ',' )
                FROM
                UNNEST ( string_to_array( T3.null_param_codes, ',' ) ) AS elem
                WHERE
                elem = ANY ( ARRAY ( SELECT DISTINCT param_code :: TEXT FROM dqm.ors_double_rate_config WHERE double_rate_sign = CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END  ) )
                ) AS null_param_codes,
            </when>
            <otherwise>
                T3.param_codes,
                T3.null_param_codes,
                T3.abnormal_param_codes,
            </otherwise>
        </choose>
        T3.asset_id,
        T3.report_date,
        od.name as sybbh,
        T3.division_code
        from dqm.ors_device_param_report_log T3
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        where T3.asset_id not in (select asset_id
        from dqm.ors_device_check_config T2
        where exclude_type = 'WHOLE'
        and T2.create_time <![CDATA[<=]]> #{endTime}:: timestamp)
        and T3.report_date <![CDATA[>=]]> #{startTime}:: date
        and T3.report_date <![CDATA[<=]]> #{endTime}:: date
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and (
            string_to_array(T3.null_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            or
            string_to_array(T3.abnormal_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            or
            string_to_array(T3.report_param_codes, ',') <![CDATA[&&]]> ARRAY(select DISTINCT param_code::text from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END )
            )
        </if>
        )
        T3 on T3.asset_id = T1.asset_id
        INNER JOIN  dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="divisiones != null and divisiones.size() != 0">
          and omd.division_code in
          <foreach collection="divisiones" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and omd.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign=  '2' )
        </if>
    </sql>

    <select id="divisionStatisticsNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        omd.division_name  as title,
        round( COALESCE ( AVG (bb.param_abnormal_rate )* 100, 0 ), 2 ) AS accuracy,
        round( COALESCE ( AVG (bb.null_abnormal_rate )* 100, 0 ), 2 ) AS integrityRate
        FROM
        dqm.ors_device_rate_day bb
        LEFT JOIN dqm.ors_base_device_info bdi ON bdi.asset_id = bb.asset_id
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = bdi.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location
        FROM dqm.ors_device_location
        WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON bb.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="startTime != null and startTime != ''">
                AND bb.stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND bb.stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY
        omd.division_name,
        omd.division_code
    </select>


    <select id="divisionModel" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        -- 1.根据reportlog表查询数量
        WITH devices_count AS(SELECT
        di.model_name as name,
        T3.report_date ,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as 上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        <if test="division != null and division != ''">
            and T3.division_code = #{division}
        </if>
        group by di.model_name,T3.report_date
        )

        -- 2.查询异常返回
        select a2.title,
        round((avg(a2.integrityRate) )::numeric, 2)   as integrityRate,
        round((avg(a2.accuracy) )::numeric, 2) as accuracy
        from (
        select
        NULLIF(a.上数台数, 0) as count,
        a.事业部                                                as title,
        a.日期,
        round(((a.上数台数 - a.未上报异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2) as integrityRate,
        round(((a.上数台数 - a.参数异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2)   as accuracy
        from (
        select aa.name as 事业部,aa.stat_date as 日期,aa.未上报异常数量,aa.参数异常数量,bb.上数台数
        from
        (
        select
        di.model_name as name,
        dd.stat_date,
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on dd.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{endTime}::date
        and od.name is not null

        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="rulesSign != null and rulesSign != '' and rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END)
        </if>
        <if test="division != null and division != ''">
            and dd.division_code = #{division}
        </if>
        GROUP BY
        di.model_name,dd.stat_date)aa
        left join devices_count bb on aa.name = bb.name and aa.stat_date = bb.report_date
        )a
        )a2
        where a2.title is not null
        group by a2.title
        order by integrityRate desc
    </select>



    <select id="divisionStatisticsChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        -- 1.根据reportlog表查询数量
        WITH devices_count AS(SELECT
        od.name,
        T3.report_date ,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as 上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        group by od.name,T3.report_date
        )

        -- 2.查询异常返回
        select a2.title,
        round((avg(a2.integrityRate) )::numeric, 2)   as integrityRate,
        round((avg(a2.accuracy) )::numeric, 2) as accuracy
        from (
        select
        NULLIF(a.上数台数, 0) as count,
        a.事业部                                                as title,
        a.日期,
        round(((a.上数台数 - a.未上报异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2) as integrityRate,
        round(((a.上数台数 - a.参数异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2)   as accuracy
        from (
        select aa.name as 事业部,aa.stat_date as 日期,aa.未上报异常数量,aa.参数异常数量,bb.上数台数
        from
        (
        select
        od.name,
        dd.stat_date,
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on dd.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{endTime}::date
        and od.name is not null

        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="rulesSign != null and rulesSign != '' and rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END)
        </if>
        GROUP BY
        od.name,dd.stat_date)aa
        left join devices_count bb on aa.name = bb.name and aa.stat_date = bb.report_date
        )a
        )a2 group by a2.title
        order by integrityRate desc
    </select>

    <select id="divisionStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
      select a2.title,
      round((avg(a2.integrityRate) )::numeric, 2)   as integrityRate,
      round((avg(a2.accuracy) )::numeric, 2) as accuracy
      from (select a.事业部                                                as title,
                   a.日期,
            round(((a.上数台数 - a.未上报异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2) as integrityRate,
            round(((a.上数台数 - a.参数异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2)   as accuracy
        from (select T3.sybbh                                                                           as 事业部,
                     T3.report_date                                                                        as 日期,
                     count(distinct
                           case
                               when T3.asset_id is not null
                                   and T3.null_param_codes is not null then T3.asset_id
                               end)                                                                        as 未上报异常数量,
                     count(distinct
                           case
                               when T3.asset_id is not null
                                   and T3.abnormal_param_codes is not null then T3.asset_id
                               end)                                                                        as 参数异常数量,
                     cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as 上数台数
              from <include refid="ors_base_device_info_sub_sql" />
              group by T3.sybbh , T3.report_date) a ) a2
              group by a2.title
              order by integrityRate desc
    </select>

    <select id="divisionCountStatisticsNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        <choose>
            <when test="sign != null and sign != ''">
                to_char( T3.stat_date, 'MM月DD日' ) as title,
            </when>
            <otherwise>
                omd.division_name as title,
            </otherwise>
        </choose>
        COALESCE(SUM(T3.total),0)  as reportNum
        FROM
        dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location FROM dqm.ors_device_location WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id

        left join (

        SELECT device_name,
        <choose>
            <when test="sign != null and sign != ''">
                stat_date,
            </when>
        </choose>
        1 AS total
        FROM
        sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>

        <choose>
            <when test="sign != null and sign != ''">
                group by device_name,stat_date
            </when>
            <otherwise>
                group by device_name
            </otherwise>
        </choose>
        )T3
        ON T3.device_name = T1.asset_id
        <where>
            and (T5.device_location is null)
        </where>
        <choose>
            <when test="sign != null and sign != ''">
                GROUP BY
                T3.stat_date
            </when>
            <otherwise>
                GROUP BY
                omd.division_name,
                omd.division_code
            </otherwise>
        </choose>
    </select>


    <select id="divisionCountStatisticsChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        od.name as title,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as reportNum
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info  di  on di.asset_id = T3.asset_id
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        group by od.name
    </select>


    <select id="divisionCountModel" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
            di.model_name  as title,
            cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as reportNum
        FROM
            dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info  di  on di.asset_id = T3.asset_id
        LEFT JOIN (
            SELECT DISTINCT
            asset_id,
            device_location
            FROM
            dqm.ors_device_location
            WHERE
            device_location = '国内'
            and stat_date::DATE &gt;= #{startTime}::DATE
            AND stat_date::DATE &lt;= #{endTime}::DATE
            ) T1 ON di.asset_id = T1.asset_id
        where
            T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        <if test="division != null and division != ''">
            and T3.division_code = #{division}
        </if>
            group by di.model_name
    </select>


  <select id="divisionCountStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
    select  T3.sybbh    as title,
    count(distinct
    case
    when T3.asset_id is not null
    and T3.null_param_codes is not null then T3.asset_id
    end)                                                                        as nullParamNum,
    count(distinct
    case
    when T3.asset_id is not null
    and T3.abnormal_param_codes is not null then T3.asset_id
    end)                                                                        as abnormalParamNum,
    cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as reportNum
    from <include refid="ors_base_device_info_sub_sql" />
    group by T3.sybbh
  </select>

  <select id="regionStatisticsNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
      SELECT
      (case when T4.region is null then '无大区' else T4.region end)  as title,
      round( COALESCE ( AVG (bb.param_abnormal_rate )* 100, 0 ), 2 ) AS accuracy,
      round( COALESCE ( AVG (bb.null_abnormal_rate )* 100, 0 ), 2 ) AS integrityRate
      FROM
      dqm.ors_device_rate_day bb
      LEFT JOIN dqm.ors_base_device_info bdi ON bdi.asset_id = bb.asset_id
      left join dqm.ors_country_region_sync T4 ON bdi.country_code = T4.country_code
      LEFT JOIN ( SELECT DISTINCT asset_id, device_location
      FROM dqm.ors_device_location
      WHERE device_location = '国内'
      <if test="startTime != null and startTime != ''">
          AND stat_date::DATE &gt;= #{startTime}::DATE
      </if>
      <if test="endTime != null and endTime != ''">
          AND stat_date::DATE &lt;= #{endTime}::DATE
      </if>
      ) T5 ON bb.asset_id = T5.asset_id
      <where>
          and (T5.device_location is null)
          <if test="startTime != null and startTime != ''">
              AND bb.stat_date &gt;= #{startTime}::DATE
          </if>
          <if test="endTime != null and endTime != ''">
              AND bb.stat_date &lt;= #{endTime}::DATE
          </if>
      </where>
      GROUP BY
      T4.region,
      T4.region_code
  </select>

    <select id="regionStatisticsChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        -- 1.根据reportlog表查询数量
        WITH devices_count AS(SELECT
        T4.region,
        T3.report_date ,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as 上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        left join dqm.ors_country_region_sync T4
        ON di.country_code = T4.country_code
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        group by T4.region ,T3.report_date
        )

        -- 2.查询异常返回
        select
        (case when a2.title is null then '无大区' else a2.title end)  as title,
        round((avg(a2.integrityRate) )::numeric, 2)   as integrityRate,
        round((avg(a2.accuracy) )::numeric, 2) as accuracy
        from (
        select
        NULLIF(a.上数台数, 0) as count,
        a.大区                                                as title,
        a.日期,
        round(((a.上数台数 - a.未上报异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2) as integrityRate,
        round(((a.上数台数 - a.参数异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2)   as accuracy
        from (
        select aa.region as 大区,aa.stat_date as 日期,aa.未上报异常数量,aa.参数异常数量,bb.上数台数
        from
        (
        select
        T4.region,
        dd.stat_date,
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        left join dqm.ors_country_region_sync T4
        ON di.country_code = T4.country_code
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on dd.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="rulesSign != null and rulesSign != '' and rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END)
        </if>
        GROUP BY
        T4.region,dd.stat_date)aa
        left join devices_count bb on aa.region = bb.region and aa.stat_date = bb.report_date
        )a
        )a2 group by a2.title
        order by integrityRate desc
    </select>


  <select id="regionStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
      select
      (case when a2.title is null then '无大区' else a2.title end)  as title,
      round((avg(a2.integrityRate) )::numeric, 2)   as integrityRate,
      round((avg(a2.accuracy) )::numeric, 2) as accuracy
      from (select a.大区                                                  as title,
                   a.日期,
            round(((a.上数台数 - a.未上报异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2) as integrityRate,
            round(((a.上数台数 - a.参数异常数量) / NULLIF(a.上数台数, 0) * 100)::numeric, 2)   as accuracy
        from (select T1.region                                                                             as 大区,
                     T3.report_date                                                                        as 日期,
                     count(distinct
                           case
                               when T3.asset_id is not null
                                   and T3.null_param_codes is not null then T3.asset_id
                               end)                                                                        as 未上报异常数量,
                     count(distinct
                           case
                               when T3.asset_id is not null
                                   and T3.abnormal_param_codes is not null then T3.asset_id
                               end)                                                                        as 参数异常数量,
                     cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as 上数台数
        from <include refid="ors_base_device_info_sub_sql" /> group by T1.region, T3.report_date) a) a2
        group by a2.title
        order by integrityRate desc
    </select>


    <select id="regionCountStatisticsNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        (case when T4.region is null then '无大区' else T4.region end)  as title,
        COUNT ( DISTINCT CASE WHEN T3.device_name IS NOT NULL THEN T1.asset_id END  )  as reportNum
        FROM
        dqm.ors_base_device_info T1
        inner join dqm.ors_country_region_sync T4 ON T1.country_code = T4.country_code
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location FROM dqm.ors_device_location WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id
        left join (select device_name,stat_date from sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        group by device_name,stat_date)T3
        ON T3.device_name = T1.asset_id
        <where>
            and (T5.device_location is null)
        </where>
        GROUP BY
        T4.region,
        T4.region_code
    </select>

    <select id="regionCountStatisticsChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        (case when T4.region is null then '无大区' else T4.region end)  as title,
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as reportNum
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info  di  on di.asset_id = T3.asset_id
        left join dqm.ors_country_region_sync T4
        ON di.country_code = T4.country_code
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        group by T4.region
    </select>

  <select id="regionCountStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
    select
      (case when T1.region is null then '无大区' else T1.region end)  as title,
    count(distinct
    case
    when T3.asset_id is not null
    and T3.null_param_codes is not null then T3.asset_id
    end)                                                                        as nullParamNum,
    count(distinct
    case
    when T3.asset_id is not null
    and T3.abnormal_param_codes is not null then T3.asset_id
    end)                                                                        as abnormalParamNum,
    cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as reportNum
    from <include refid="ors_base_device_info_sub_sql" /> group by T1.region
  </select>


    <select id="dateStatisticsNew" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        SELECT
        <choose>
            <when test="year != null and year != ''">
                to_char( bb.stat_date, 'MM月' ) as title,
            </when>
            <otherwise>
                to_char( bb.stat_date, 'MM月DD日' ) as title,
            </otherwise>
        </choose>
        round( COALESCE ( AVG (bb.param_abnormal_rate )* 100, 0 ), 2 ) AS accuracy,
        round( COALESCE ( AVG (bb.null_abnormal_rate )* 100, 0 ), 2 ) AS integrityRate
        FROM
        dqm.ors_device_rate_day bb
        LEFT JOIN dqm.ors_base_device_info bdi ON bdi.asset_id = bb.asset_id
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = bdi.model_id
        <if test="divisiones != null and divisiones.size() != 0">
            and omd.division_code in
            <foreach collection="divisiones" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location
        FROM dqm.ors_device_location
        WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON bb.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="iotBoxType != null and iotBoxType == 'HX'">
                and bdi.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="iotBoxType != null and iotBoxType == 'SG'">
                and bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="startTime != null and startTime != ''">
                AND bb.stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND bb.stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        <choose>
            <when test="year != null and year != ''">
                group by to_char( bb.stat_date, 'MM月' )
            </when>
            <otherwise>
                group by to_char( bb.stat_date, 'MM月DD日' )
            </otherwise>
        </choose>
    </select>


    <select id="dateStatisticsChange" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        -- 1.根据reportlog表查询数量
        WITH devices_count AS(SELECT
        <choose>
            <when test="year != null and year != ''">
                to_char(T3.report_date, 'MM月' ) as 日期,
            </when>
            <otherwise>
                to_char(T3.report_date, 'MM月DD日' ) as 日期,
            </otherwise>
        </choose>
        cast(count(distinct case when T3.asset_id is not null then T3.asset_id end) as float) as 总上数台数
        FROM
        dqm.ors_device_param_report_log T3
        inner join dqm.ors_base_device_info di on di.asset_id = T3.asset_id
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) T1 ON di.asset_id = T1.asset_id
        where
        T1.device_location IS NULL
        and T3.report_date <![CDATA[>=]]> #{startTime}::date
        and T3.report_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            and exists(select 1 from dqm.ors_double_rate_config rc where rc.double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END and
            (T3.null_param_codes like '%' || rc.param_code || '%'
            or
            T3.abnormal_param_codes like '%' || rc.param_code || '%'
            OR
            T3.report_param_codes like '%' || rc.param_code || '%'
            ))
        </if>
        <choose>
            <when test="year != null and year != ''">
                group by to_char( T3.report_date, 'MM月' )
            </when>
            <otherwise>
                group by to_char( T3.report_date, 'MM月DD日' )
            </otherwise>
        </choose>
        )

        -- 2.查询异常返回
        select
        a2.日期 as title,
        max(a2.总上数台数) as reportNum,
        round((avg(a2.数据准确率) * 100)::numeric, 2)   as accuracy,
        round((avg(a2.数据完整率) * 100)::numeric, 2)   as integrityRate
        from (
        select a.日期,
        bb.总上数台数,
        (bb.总上数台数 - a.未上报异常数量) / NULLIF(bb.总上数台数, 0) as 数据完整率,
        (bb.总上数台数 - a.参数异常数量) / NULLIF(bb.总上数台数, 0)   as 数据准确率
        from (
        select
        <choose>
            <when test="year != null and year != ''">
                to_char(dd.stat_date, 'MM月' ) as 日期,
            </when>
            <otherwise>
                to_char( dd.stat_date, 'MM月DD日' ) as 日期,
            </otherwise>
        </choose>
        COUNT ( DISTINCT CASE WHEN abnormal_code IN ( 9008,9009 ) THEN dd.device_name END ) AS 未上报异常数量,
        COUNT ( DISTINCT CASE WHEN abnormal_code NOT IN ( 9008,9009 ) THEN dd.device_name END ) AS 参数异常数量
        FROM
        dqm.ors_device_data_abnormal_detail_day  dd
        inner join dqm.ors_base_device_info  di  on di.asset_id = dd.device_name
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on dd.division_code = od.value
        LEFT JOIN (
        SELECT DISTINCT
        asset_id,
        device_location
        FROM
        dqm.ors_device_location
        WHERE
        device_location = '国内'
        and stat_date::DATE &gt;= #{startTime}::DATE
        AND stat_date::DATE &lt;= #{endTime}::DATE
        ) t3 ON di.asset_id = t3.asset_id
        WHERE
        di.device_code IS NOT NULL
        AND t3.device_location IS NULL
        and dd.stat_date <![CDATA[>=]]> #{startTime}::date
        and dd.stat_date <![CDATA[<=]]> #{endTime}::date
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '2'.toString()">
            and di.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{doubleRateSign})
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign == '3'.toString()">
            and (di.country_code
            not
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
            or di.country_code is null
            )
        </if>
        <if test="rulesSign != null and rulesSign != '' and rulesSign == '1'.toString()">
            AND dd.abnormal_code not in (9003,9100)
        </if>
        <if test="doubleRateSign != null and doubleRateSign != '' and doubleRateSign != '1'.toString()">
            and di.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <if test="doubleRateSign != null and doubleRateSign != ''">
            AND dd.param_code IN ( SELECT DISTINCT param_code FROM dqm.ors_double_rate_config WHERE double_rate_sign =  CASE WHEN #{doubleRateSign} = '3' THEN '2' ELSE #{doubleRateSign} END)
        </if>
        <choose>
            <when test="year != null and year != ''">
                group by to_char( dd.stat_date, 'MM月' )
            </when>
            <otherwise>
                group by to_char( dd.stat_date, 'MM月DD日' )
            </otherwise>
        </choose>
        )a
        left join devices_count bb  on a.日期 = bb.日期
        )a2 group by  a2.日期
    </select>

    <select id="dateStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
        select
            <choose>
                <when test="year != null and year != ''">
                    to_char( a2.日期, 'MM月' ) as title,
                </when>
                <otherwise>
                    to_char( a2.日期, 'MM月DD日' ) as title,
                </otherwise>
            </choose>
            max(a2.总上数台数) as reportNum,
            round((avg(a2.数据准确率) * 100)::numeric, 2)   as accuracy,
            round((avg(a2.数据完整率) * 100)::numeric, 2)   as integrityRate
        from (select a.日期,
                     a.总上数台数,
                     (a.总上数台数 - a.总未上报异常数量) / NULLIF(a.总上数台数, 0) as 数据完整率,
                     (a.总上数台数 - a.总参数异常数量) / NULLIF(a.总上数台数, 0)   as 数据准确率
              from (select T3.report_date                                                                        as 日期,
                           count(distinct case
                                              when T3.asset_id is not null and T3.null_param_codes is not null then T3.asset_id
                               end)                                                                              as 总未上报异常数量,
                           count(distinct case
                                              when T3.asset_id is not null and T3.abnormal_param_codes is not null
                                                  then T3.asset_id
                               end)                                                                              as 总参数异常数量,
                           cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as 总上数台数
                    from <include refid="ors_base_device_info_sub_sql" /> group by T3.report_date) a) a2
        <choose>
            <when test="year != null and year != ''">
                group by to_char( a2.日期, 'MM月' )
            </when>
            <otherwise>
                group by to_char( a2.日期, 'MM月DD日' )
            </otherwise>
        </choose>
    </select>

  <select id="dateCountStatistics" resultType="com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp">
    select
    <choose>
      <when test="year != null and year != ''">
        to_char( a2.日期, 'MM月' ) as title,
      </when>
      <otherwise>
        to_char( a2.日期, 'MM月DD日' ) as title,
      </otherwise>
    </choose>,
    count(distinct case
    when T3.asset_id is not null and T3.null_param_codes is not null then T3.asset_id
    end)                                                                              as nullParamNum,
    count(distinct case
    when T3.asset_id is not null and T3.abnormal_param_codes is not null
    then T3.asset_id
    end)                                                                              as abnormalParamNum,
    cast(count(distinct case when T3.asset_id is not null then T1.asset_id end) as float) as reportNum
    from <include refid="ors_base_device_info_sub_sql" />
    <choose>
      <when test="year != null and year != ''">
        group by to_char( T3.report_date, 'MM月' )
      </when>
      <otherwise>
        group by to_char( T3.report_date, 'MM月DD日' )
      </otherwise>
    </choose>
  </select>
</mapper>
