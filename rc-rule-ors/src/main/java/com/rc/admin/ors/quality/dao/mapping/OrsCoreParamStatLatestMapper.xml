<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsCoreParamStatLatestMapper">

    <insert id="syncSanyData">
        TRUNCATE TABLE dqm.ors_core_param_stat_latest;
        INSERT INTO dqm.ors_core_param_stat_latest
        (
            tenant_id,
            model_id,
            device_name,
            param_code,
            param_value,
            is_param_value_abnormal,
            param_value_earliest_time,
            param_value_latest_time,
            create_time
        )
        SELECT * FROM sany_data_service.sanyds_core_param_stat_latest;
        analyze dqm.ors_core_param_stat_latest;
    </insert>



    <select id="getDeviceLocationResp" resultType="com.rc.admin.ors.quality.model.DeviceLocationResp">
       select
            device_name,
            string_agg(aa.name,',') as key_value
        from (SELECT DISTINCT ON (device_name, param_code)
        case when param_code = '8504' then '经度:' || param_value when  param_code = '8505' then '纬度:' || param_value end  as name,
        device_name,
        param_code,
        param_value_latest_time,
        param_value
        FROM sany_data_service.sanyds_core_param_stat_latest_day
        WHERE param_value_latest_time::date <![CDATA[<=]]> #{bizDate}::DATE
        and param_code in ('8504','8505')
        ORDER BY
        device_name,
        param_code,
        param_value_latest_time DESC
        )aa
        group  BY device_name


    </select>


    <select id="findLongTimeNoReport" resultType="com.rc.admin.ors.quality.model.UnReportDevice">
        select * from
        (
        select
            max(aa.asset_id) as asset_id,
            max(aa.model_id) as model_id,
            max(aa.thing_id) as thing_id,
            aa.device_name,
            max(aa.model_name) as model_name,
            max(aa.property) as property,
            max(aa.property_name) as property_name,
            aa.param_code as param_code,
            max(aa.param_value_latest_time) as param_value_latest_time,
            max(aa.param_value) as param_value,
            max(aa.dict_desc) as dict_desc,
            count(odsr.device_name)  as device_name_count,
            max(division_code) as division_code,
            max(data_center_id) as data_center_id,
            sum(case when odsr.asset_id is not null then 1 else 0 end) as device_name_null_count
        from
        (
        SELECT
        T1.asset_id,
        T1.model_id,
        T1.thing_id,
        T1.device_name,
        T1.model_name,
        T2.property,
        T2.property_name,
        T2.param_code,
        T3.param_value_latest_time,
        T3.param_value,
        omd.division_code,
        T1.data_center_id,
        sd.dict_desc
        FROM
            (SELECT asset_id, model_id, thing_id, device_name,model_name,created,data_center_id FROM dqm.ors_base_device_info
             WHERE active_statu = true
               AND model_id not in ('BUFLT_5006_Model_10088','BUFLT_5006_Model_10464')
               AND (EXISTS (
                            SELECT 1
                            FROM sany_data_service.sanyds_core_param_stat_latest_day
                            WHERE stat_date = #{bizDate}::DATE
                                AND device_name = dqm.ors_base_device_info.asset_id
                        )
                 OR EXISTS (
                            SELECT 1
                            FROM dqm.ors_device_data_abnormal_detail
                            WHERE stat_date = #{bizDate}::DATE
                                AND abnormal_effective = 1
                                AND device_name = dqm.ors_base_device_info.asset_id
                        ))
            ) T1
        LEFT JOIN  dqm.ors_model_division omd ON omd.model_id = T1.model_id
        INNER JOIN dqm.ors_model_properties_config T2 ON T2.model_id = T1.model_id
        LEFT JOIN sany_data_service.sanyds_dict sd ON sd.dict_id = T2.param_code
        INNER JOIN
        (
            SELECT DISTINCT ON (device_name, param_code)
            device_name,
            param_code,
            param_value_latest_time,
            param_value
            FROM sany_data_service.sanyds_core_param_stat_latest_day
            WHERE param_value_latest_time::date <![CDATA[<=]]> #{bizDate}::DATE
            ORDER BY
            device_name,
            param_code,
            param_value_latest_time DESC
        ) T3
            ON  T3.device_name = T1.asset_id
            AND T2.param_code  = T3.param_code
        LEFT JOIN (
        SELECT
        asset_id,
        string_agg ( param_code :: TEXT, ',' ) AS param_codes
        FROM
        dqm.ors_device_check_config
        WHERE
        param_code IS NOT NULL
        AND exclude_type != 'WHOLE'
        GROUP BY
        asset_id
        ) T4 ON T4.asset_id = T1.asset_id
        WHERE
        T3.param_value_latest_time::date <![CDATA[<=]]> (#{bizDate}::DATE - INTERVAL '7 days')
        AND ( T4.param_codes IS NULL OR T4.param_codes NOT LIKE'%' || T2.param_code || '%' )
        AND T1.device_name NOT IN ( SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE' )
        )aa
        left join
        (select dsr.device_name, dsr.stat_date, (case when mvd.asset_id is null then dsr.device_name else null end)  as asset_id from  dqm.ors_device_start_record dsr
           left join
            (select asset_id,stat_date  from dqm.ors_param_max_value_day where param_code='8512' and param_value = 0) mvd
         on dsr.device_name = mvd.asset_id and dsr.stat_date = mvd.stat_date
         where  dsr.stat_date::date <![CDATA[<=]]> #{bizDate}::DATE
         ) odsr
        on odsr.device_name=  aa.device_name
        and odsr.stat_date >=  aa.param_value_latest_time
        group by aa.device_name,aa.param_code
        )bb
        where
            ((bb.param_code = 8501 and bb.device_name_null_count >= 7 and  not exists (select 1 from dqm.ors_param_max_value_day where  param_code = '8512'  and param_value = 0 and asset_id=bb.device_name and stat_date:: DATE = #{bizDate}:: DATE))

           or
            (bb.param_code != 8501 and bb.device_name_count >= 7))


        union all

        select * from
            (
                select
                    max(aa.asset_id) as asset_id,
                    max(aa.model_id) as model_id,
                    max(aa.thing_id) as thing_id,
                    aa.device_name,
                    max(aa.model_name) as model_name,
                    max(aa.property) as property,
                    max(aa.property_name) as property_name,
                    aa.param_code as param_code,
                    max(aa.param_value_latest_time) as param_value_latest_time,
                    max(aa.param_value) as param_value,
                    max(aa.dict_desc) as dict_desc,
                    count(odsr.device_name)  as device_name_count,
                    max(division_code) as division_code,
                    max(data_center_id) as data_center_id,
                    sum(case when odsr.asset_id is not null then 1 else 0 end) as device_name_null_count
                from
                    (
                        SELECT
                            T1.asset_id,
                            T1.model_id,
                            T1.thing_id,
                            T1.device_name,
                            T1.model_name,
                            T2.property,
                            T2.property_name,
                            T2.param_code,
                            T3.param_value_latest_time,
                            T3.param_value,
                            omd.division_code,
                            T1.data_center_id,
                            sd.dict_desc
                        FROM
                            (SELECT asset_id, model_id, thing_id, device_name,model_name,created,data_center_id FROM dqm.ors_base_device_info
                             WHERE active_statu = true
                               AND model_id  in ('BUFLT_5006_Model_10088','BUFLT_5006_Model_10464')
                               AND (EXISTS (
                                            SELECT 1
                                            FROM sany_data_service.sanyds_core_param_stat_latest_day
                                            WHERE stat_date = #{bizDate}::DATE
                                                AND device_name = dqm.ors_base_device_info.asset_id
                                        )
                                 OR EXISTS (
                                            SELECT 1
                                            FROM dqm.ors_device_data_abnormal_detail
                                            WHERE stat_date = #{bizDate}::DATE
                                                AND abnormal_effective = 1
                                                AND device_name = dqm.ors_base_device_info.asset_id
                                        ))
                            ) T1
                                LEFT JOIN  dqm.ors_model_division omd ON omd.model_id = T1.model_id
                                INNER JOIN dqm.ors_model_properties_config T2 ON T2.model_id = T1.model_id
                                LEFT JOIN sany_data_service.sanyds_dict sd ON sd.dict_id = T2.param_code
                                INNER JOIN
                            (
                                SELECT DISTINCT ON (device_name, param_code)
                                    device_name,
                                    param_code,
                                    param_value_latest_time,
                                    param_value
                                FROM sany_data_service.sanyds_core_param_stat_latest_day
                                WHERE param_value_latest_time::date <![CDATA[<=]]> #{bizDate}::DATE
                                ORDER BY
                                    device_name,
                                    param_code,
                                    param_value_latest_time DESC
                            ) T3
                            ON  T3.device_name = T1.asset_id
                                AND T2.param_code  = T3.param_code
                                LEFT JOIN (
                                SELECT
                                    asset_id,
                                    string_agg ( param_code :: TEXT, ',' ) AS param_codes
                                FROM
                                    dqm.ors_device_check_config
                                WHERE
                                    param_code IS NOT NULL
                                  AND exclude_type != 'WHOLE'
                                GROUP BY
                                    asset_id
                            ) T4 ON T4.asset_id = T1.asset_id
                        WHERE
                            T3.param_value_latest_time::date <![CDATA[<=]]> (#{bizDate}::DATE - INTERVAL '30 days')
                          AND ( T4.param_codes IS NULL OR T4.param_codes NOT LIKE'%' || T2.param_code || '%' )
                          AND T1.device_name NOT IN ( SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE' )
                    )aa
                        left join
                    (select dsr.device_name, dsr.stat_date, (case when mvd.asset_id is null then dsr.device_name else null end)  as asset_id from  dqm.ors_device_start_record dsr
                    left join
                    (select asset_id,stat_date from dqm.ors_param_max_value_day where param_code='8512' and param_value = 0) mvd
                    on dsr.device_name = mvd.asset_id and dsr.stat_date = mvd.stat_date
                    where  dsr.stat_date::date <![CDATA[<=]]> #{bizDate}::DATE
                    ) odsr
                on odsr.device_name=  aa.device_name
                    and odsr.stat_date >=  aa.param_value_latest_time
                group by aa.device_name,aa.param_code
            )bb
        where
            ((bb.param_code = 8501 and bb.device_name_null_count >= 30 and  not exists (select 1 from dqm.ors_param_max_value_day where  param_code = '8512'  and param_value = 0 and asset_id=bb.device_name and stat_date:: DATE = #{bizDate}:: DATE))
           or
            (bb.param_code != 8501 and bb.device_name_count >= 30))
    </select>



    <select id="findUnreportUnbnormalData" resultType="com.rc.admin.ors.quality.model.UnReportDevice">
        SELECT T1.asset_id,
               T1.model_id,
               T1.thing_id,
               T1.device_name,
               T1.model_name,
               T2.property,
               T2.property_name,
               T2.param_code,
               omd.division_code,
               T1.data_center_id,
               sd.dict_desc
        FROM (SELECT asset_id, model_id, thing_id, device_name, model_name, created,data_center_id
              FROM dqm.ors_base_device_info
              WHERE active_statu = true
                AND (EXISTS(
                             SELECT 1
                             FROM sany_data_service.sanyds_core_param_stat_latest_day
                             WHERE stat_date = #{bizDate} ::DATE
                                 AND device_name = dqm.ors_base_device_info.asset_id
                         )
                  OR EXISTS(
                             SELECT 1
                             FROM dqm.ors_device_data_abnormal_detail
                             WHERE stat_date = #{bizDate} ::DATE
                                 AND abnormal_effective = 1
                                 AND device_name = dqm.ors_base_device_info.asset_id
                         ))) T1
                 LEFT JOIN  dqm.ors_model_division omd ON omd.model_id = T1.model_id

                 INNER JOIN dqm.ors_model_properties_config T2 ON T2.model_id = T1.model_id
                        and T2.data_type != 'multiple_param'
                 INNER JOIN (select device_name,
                                    count(1)                                              as device_count,
                                    sum(case when mvd.asset_id is null then 1 else 0 end) as device_name_null_count
                             from dqm.ors_device_start_record sdr
                                      left join
                                  (select asset_id, stat_date
                                   from dqm.ors_param_max_value_day
                                   where param_code = '8512'
                                     and param_value = 0) mvd
                                  on sdr.device_name = mvd.asset_id and sdr.stat_date = mvd.stat_date
                             where sdr.stat_date::date <![CDATA[<=]]> #{bizDate}:: DATE
                             group by device_name) odsr
                            on odsr.device_name = T1.device_name
                 LEFT JOIN sany_data_service.sanyds_dict sd ON sd.dict_id = T2.param_code
                 LEFT JOIN (SELECT asset_id, string_agg(param_code::TEXT, ',') AS param_codes
                            FROM dqm.ors_device_check_config
                            WHERE param_code IS NOT NULL
                              AND exclude_type != 'WHOLE'
                            GROUP BY asset_id) T4 ON T4.asset_id = T1.asset_id
        WHERE #{bizDate}::DATE >= T1.created::DATE
            AND NOT EXISTS (select 1 from sany_data_service.sanyds_core_param_stat_latest_day  where device_name = T1.asset_id  and param_code = T2.param_code AND param_value_latest_time::date <![CDATA[<=]]> #{bizDate}:: DATE)
          AND (T4.param_codes IS NULL
           OR T4.param_codes NOT LIKE '%'||T2.param_code||'%')
          AND T1.device_name NOT IN (SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE')
          AND
            (
            (
            T2.param_code = 8501
          and
            odsr.device_name_null_count <![CDATA[>=]]>
            CASE
            WHEN T1.model_id NOT IN ('BUFLT_5006_Model_10088'
            , 'BUFLT_5006_Model_10464') THEN 7
            ELSE 30
            END
            and  not exists (select 1 from dqm.ors_param_max_value_day where  param_code = '8512'  and param_value = 0 and asset_id = T1.device_name and stat_date:: DATE = #{bizDate}:: DATE)
            )
            or
            (
            T2.param_code != 8501
          and
            odsr.device_count <![CDATA[>=]]>
            CASE
            WHEN T1.model_id NOT IN ('BUFLT_5006_Model_10088'
            , 'BUFLT_5006_Model_10464') THEN 7
            ELSE 30
            END
            )
            )
    </select>
</mapper>
