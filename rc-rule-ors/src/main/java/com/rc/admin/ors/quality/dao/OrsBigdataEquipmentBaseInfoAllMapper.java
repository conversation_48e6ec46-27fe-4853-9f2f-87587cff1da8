package com.rc.admin.ors.quality.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.ors.quality.model.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备信息数据访问层
 *
 * <AUTHOR>
 * @since 2023-10-26 10:10:55
 */
public interface OrsBigdataEquipmentBaseInfoAllMapper extends BaseMapper<OrsBigdataEquipmentBaseInfoAll> {


    List<BigdataEquipmentBaseInfoAll> getDeviceInfoAll(@Param("startTime") String startTime);


    @DS("master")
    void saveDeviceInfo(@Param("item") BigdataEquipmentBaseInfoAll item);


    /**
     * 查询历史设备数据异常(一行)
     * @param req
     * @return
     */
    List<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfoForOne(@Param("req") DqmHistoryDeviceDataExceptionsReq req);

    /**
     * 查询历史设备数据异常(一行)
     * @param req
     * @return
     */
    Page<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfoForOne(@Param("page") Page<DqmHistoryDeviceDataExceptionResp> page,
                                                                                     @Param("req") DqmHistoryDeviceDataExceptionsReq req);

    /**
     * 查询历史设备数据异常 (多行)
     * @param req
     * @return
     */
    List<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfoForOneAll(@Param("req") DqmHistoryDeviceDataExceptionsReq req);

    /**
     * 获取异常设备信息
     * @param req
     * @return
     */
    List<DqmHistoryDeviceDataExceptionResp> getDeviceHistoryAbnormalData(@Param("req") DqmHistoryDeviceDataExceptionsReq req);
    int getHistoryDeviceDataExceptionsInfoForOneCount(@Param("req") DqmHistoryDeviceDataExceptionsReq req);
    List<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfoForList(@Param("req") DqmHistoryDeviceDataExceptionsReq req);
    List<String> getModelList(@Param("req") DqmHistoryDeviceDataExceptionsReq req);
    List<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfoForListAll(@Param("req") DqmHistoryDeviceDataExceptionsReq req);
    int getHistoryDeviceDataExceptionsInfoForListCount(@Param("req") DqmHistoryDeviceDataExceptionsReq req);


    List<Map<String,String>>  queryAbnormal(@Param("dictType")String dictType,@Param("paramCode")String paramCode);

    List<Map<String,String>> queryParamCode(@Param("modelId")String modelId,@Param("divisionCode")String divisionCode,@Param("dictType")String dictType);

    List<Map<String,String>> queryMergeDetails(@Param("date") Date date);

    void syncDetailsToDay(@Param("list") List<Map<String,String>> list);

    void insertDataAbnormalStatDays(@Param("list")List<DqmDeviceDataAbnormalStatDay> list);

    @Select("select DISTINCT  code from  sys_dict where dict_type ='huaXinModelId'")
    List<String> queryHuaXinModelId();

    List<DqmDeviceDataAbnormalStatDay> getDqmDeviceDataAbnormalStatDay(@Param("statDate")Date statDate);

    void transferAbnormalDataToDay(@Param("date") Date date);

    void ors_device_data_abnormal_detail_day(@Param("bizDate") Date bizDate);
    void ors_up_work_device(@Param("bizDate") Date bizDate);

    @Select("select count(1) cnt FROM dqm.ors_device_data_abnormal_detail_day WHERE stat_date = #{bizDate}::DATE")
    Integer getAbnormalDetailDayCnt(@Param("bizDate") Date bizDate);

    void ors_device_data_abnormal_stat_day(@Param("bizDate") Date bizDate);

    @Select("select count(1) cnt FROM dqm.ors_device_data_abnormal_stat_day WHERE stat_date = #{bizDate}::DATE")
    Integer getAbnormalStatDayCnt(@Param("bizDate") Date bizDate);

    void aggDeviceHourWorkCnt(@Param("bizDate") Date bizDate);

    @DS("sany_data_service")
    @Select("select count(1) cnt from sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE")
    Integer getDeviceWorkCnt(@Param("bizDate") Date bizDate);

}
