package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@TableName("dqm.ors_version_update_log")
public class OrsVersionUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键，自增ID")
    private Integer id;

    @TableField("version_name")
    @ApiModelProperty(value = "版本名称")
    private String versionName;

    @TableField("version_update_date")
    @ApiModelProperty(value = "版本更新日期")
    private Date versionUpdateDate;

    @TableField("version_update_desc")
    @ApiModelProperty(value = "版本更新描述")
    private String versionUpdateDesc;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;


    @TableField(exist = false)
    @ApiModelProperty(value = "后端返回-关联的图片记录二进制")
    private List<OrsVersionUpdateLogImg> logImgs;


    @TableField(exist = false)
    @ApiModelProperty(value = "后端返回-版本跟新前后准确率和完整率的计算")
    private List<OrsVersionUpdateLogRate> logRate;

    @TableField(exist = false)
    @ApiModelProperty(value = "页码")
    private int current;

    @TableField(exist = false)
    @ApiModelProperty(value = "条数")
    private int size;



}
