<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsDeviceDataAbnormalDetailMapper">

    <select id="findAbnormalDetail" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp">
        SELECT
            T1.device_name AS asset_id,
            T1.stat_date,
            T2.division as sybbh,
            T2.division_code,
            T2.product_group as zehd_spartdesc,
            T2.product_group_code as zehd_spart,
            T2.country,
            T2.device_code,
            T2.device_name,
            T2.hw_version,
            T2.fw_version,
            T2.model_id,
            T2.model_name as model_name,
            T2.thing_id  as thing_id,
            T2.region as zehdsvReg,
            T2.auth_token,
            T1.property,
            T1.property_name,
            T1.abnormal_name,
            T1.abnormal_code,
            T1.param_code,
            T1.abnormal_data,
            T1.abnormal_time
        FROM
        dqm.ors_device_data_abnormal_detail T1
        INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name and T1.abnormal_effective=1
        <where>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND T2.division_code IN
                <foreach item="item" index="index" collection="req.sybbh.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND T2.product_group_code IN
                <foreach collection="req.zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdsvReg != null and req.zehdsvReg != ''">
                AND T2.region_code IN
                <foreach collection="req.zehdsvReg.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null  and req.deviceCode != ''">
                AND (
                T2.device_code LIKE CONCAT('%', #{req.deviceCode}, '%')
                OR
                T2.device_code IN
                <foreach collection="req.deviceCode.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.assetId != null  and req.assetId != ''">
                AND (
                T2.asset_id LIKE CONCAT('%', #{req.assetId}, '%')
                OR
                T2.asset_id IN
                <foreach collection="req.assetId.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND T1.param_code IN
                <foreach collection="req.paramCodes.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::int4
                </foreach>
            </if>
            <if test="req.abnormalName != null and req.abnormalName != ''">
                AND T1.abnormal_code IN
                <foreach collection="req.abnormalName.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::int4
                </foreach>
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND T1.abnormal_time &gt;= #{req.startTime}::TIMESTAMP
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND T1.abnormal_time &lt;= #{req.endTime}::TIMESTAMP
            </if>
            <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
                AND T2.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
        </where>
    </select>


    <update id="updataAbnormalEffectiveOne">
        UPDATE dqm.ors_device_data_abnormal_detail ddad
        SET abnormal_effective = 0
        FROM (
        select * from (
        SELECT
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code,
        MAX(dwc.work_cnt) AS work_cnt,
        COALESCE(COUNT(dad.abnormal_code), 0) AS abnormal_cnt
        FROM
        sany_data_service.sanyds_device_work_cnt dwc
        right JOIN dqm.ors_device_data_abnormal_detail dad
        ON dwc.stat_date = dad.stat_date
        AND dwc.param_code = dad.param_code
        AND dwc.device_name = dad.device_name
        AND dad.abnormal_code IN (9004, 9001, 9005)
        WHERE
        dwc.stat_date = #{bizDate}::DATE
        AND dwc.param_code IN (8105, 8201, 8403, 8102,8511,8106,8108,8107,8205,8610,8611,8305)
        AND dwc.work_cnt > 0
        AND dad.abnormal_code is not null
        GROUP BY
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code
        )aa  where ROUND((abnormal_cnt::NUMERIC / work_cnt::NUMERIC) * 100, 5) &lt;= 0.5
        ) subquery
        WHERE
        ddad.stat_date = subquery.stat_date
        AND ddad.device_name = subquery.device_name
        AND ddad.param_code = subquery.param_code
        AND ddad.abnormal_code  IN (9004, 9001, 9005);
    </update>


    <update id="updataAbnormalEffectiveTwo">
        UPDATE dqm.ors_device_data_abnormal_detail ddad
        SET abnormal_effective = 0
        FROM (
        select * from (
        SELECT
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code,
        MAX(dwc.work_cnt) AS work_cnt,
        COALESCE(COUNT(dad.abnormal_code), 0) AS abnormal_cnt
        FROM
        sany_data_service.sanyds_device_work_cnt dwc
        right JOIN dqm.ors_device_data_abnormal_detail dad
        ON dwc.stat_date = dad.stat_date
        AND dwc.param_code = dad.param_code
        AND dwc.device_name = dad.device_name
        AND dad.abnormal_code IN (9007, 9001, 9002)
        WHERE
        dwc.stat_date =  #{bizDate}::DATE
        AND dwc.param_code = 8501
        AND dwc.work_cnt > 0
        AND dad.abnormal_code is not null
        GROUP BY
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code
        )aa  where ROUND((abnormal_cnt::NUMERIC / work_cnt::NUMERIC) * 100, 5) &lt;= 1
        ) subquery
        WHERE
        ddad.stat_date = subquery.stat_date
        AND ddad.device_name = subquery.device_name
        AND ddad.param_code = subquery.param_code
        AND ddad.abnormal_code IN (9007, 9001, 9002);
    </update>


    <update id="updataAbnormalEffectiveThree">
        UPDATE dqm.ors_device_data_abnormal_detail ddad
        SET abnormal_effective = 0
        FROM (
        select * from (
        SELECT
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code,
        MAX(dwc.work_cnt) AS work_cnt,
        COALESCE(COUNT(dad.abnormal_code), 0) AS abnormal_cnt
        FROM
        sany_data_service.sanyds_device_work_cnt dwc
        right JOIN dqm.ors_device_data_abnormal_detail dad
        ON dwc.stat_date = dad.stat_date
        AND dwc.param_code = dad.param_code
        AND dwc.device_name = dad.device_name
        AND dad.abnormal_code in  (9001,9006)
        WHERE
        dwc.stat_date =  #{bizDate}::DATE
        AND dwc.param_code IN (8604,8510,8506,8507,8508,8509,8603,8605,8607,8608,8609,8305)
        AND dwc.work_cnt > 0
        AND dad.abnormal_code is not null
        GROUP BY
        dwc.stat_date,
        dwc.device_name,
        dwc.param_code
        )aa  where ROUND((abnormal_cnt::NUMERIC / work_cnt::NUMERIC) * 100, 5) &lt;= 0.5
        ) subquery
        WHERE
        ddad.stat_date = subquery.stat_date
        AND ddad.device_name = subquery.device_name
        AND ddad.param_code = subquery.param_code
        AND ddad.abnormal_code in  (9001,9006);
    </update>






    <update id="updataAbnormalEffective">
        ANALYZE sany_data_service.sanyds_device_work_cnt;
        -- 设备位置，针对“属性值异常”、“位置漂移”、“属性值超限”，若设备当天此类异常的数量合计超过当天上传工况数量的1%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 1 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8501 ) t1
                    JOIN (
                        SELECT
                            device_name,
                            8501 AS param_code,
                            count (concat(param_code, abnormal_code)) as abnormal_cnt,
                            stat_date
                        FROM
                            dqm.ors_device_data_abnormal_detail
                        WHERE
                            stat_date = #{bizDate}::DATE
                            AND param_code = 8501
                            AND abnormal_code in (9007, 9001, 9002)
                        GROUP BY
                            stat_date,
                            device_name
                    ) t2 ON t1.stat_date = t2.stat_date
                        AND t1.device_name = t2.device_name
                        AND t1.param_code = t2.param_code
                ) t3
        ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
            AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
            AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
            AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9007, 9001, 9002)
        ;
        -- 发动机工作时间，针对“属性值异常”、“属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8105 ) t1
            JOIN (
            SELECT
            device_name,
            8105 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8105
            AND abnormal_code in (9004, 9001)
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9004, 9001)
        ;
        -- 总油耗，针对“属性值异常”、“属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8201 ) t1
            JOIN (
            SELECT
            device_name,
            8201 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8201
            AND abnormal_code in (9004, 9001)
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9004, 9001)
        ;
        -- 行驶里程，针对“属性值异常”、“属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8403 ) t1
            JOIN (
            SELECT
            device_name,
            8403 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8403
            AND abnormal_code in (9004, 9001)
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9004, 9001)
        ;
        -- 工作时间，针对“属性值异常”、“属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8102 ) t1
            JOIN (
            SELECT
            device_name,
            8102 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8102
            AND abnormal_code in (9004, 9001)
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9004, 9001)
        ;
        -- 行驶速度，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8510 ) t1
            JOIN (
            SELECT
            device_name,
            8510 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8510
            AND abnormal_code = 9001
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        -- 油位，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8506 ) t1
            JOIN (
            SELECT
            device_name,
            8506 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8506
            AND abnormal_code = 9001
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        -- 发动机转速，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8507 ) t1
            JOIN (
            SELECT
            device_name,
            8507 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8507
            AND abnormal_code = 9001
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        -- 发动机水温，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8508 ) t1
            JOIN (
            SELECT
            device_name,
            8508 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8508
            AND abnormal_code = 9001
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        -- 总电耗，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8511 ) t1
            JOIN (
            SELECT
            device_name,
            8511 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8511
            AND abnormal_code in (9004, 9001)
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9004, 9001)
        ;
        -- 当前电量，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
            FROM
            (
                SELECT
                    *,
                    CASE
                        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
                            ELSE 1 END AS abnormal_effective
                FROM
                (
                    SELECT
                        t2.*,
                        CASE WHEN t1.work_cnt = 0 THEN NULL
                            ELSE t1.work_cnt END AS work_cnt
                    FROM
                        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8509 ) t1
            JOIN (
            SELECT
            device_name,
            8509 AS param_code,
            count (concat(param_code, abnormal_code)) as abnormal_cnt,
            stat_date
            FROM
            dqm.ors_device_data_abnormal_detail
            WHERE
            stat_date = #{bizDate}::DATE
            AND param_code = 8509
            AND abnormal_code = 9001
            GROUP BY
            stat_date,
            device_name
            ) t2 ON t1.stat_date = t2.stat_date
            AND t1.device_name = t2.device_name
            ) t3
            ) AS subquery
        WHERE
            dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
          AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
          AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
          AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
<!--新增属性-->
                <!-- 怠速油耗，针对“属性值异常、属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8205 ) t1
        JOIN (
        SELECT
        device_name,
        8205 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8205
        AND abnormal_code in (9001,9004)
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        <!-- 怠速时长，针对“属性值异常、属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8106 ) t1
        JOIN (
        SELECT
        device_name,
        8106 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8106
        AND abnormal_code in (9001,9004)
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9001,9004)
        ;
        <!-- 左行走工时，针对“属性值异常、属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8108 ) t1
        JOIN (
        SELECT
        device_name,
        8108 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8108
        AND abnormal_code in (9001,9004)
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9001,9004)
        ;
        <!-- 右行走工时，针对“属性值异常、属性值逆增长”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8107 ) t1
        JOIN (
        SELECT
        device_name,
        8107 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8107
        AND abnormal_code in (9001,9004)
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code in (9001,9004)
        ;
        <!-- 机油压力，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8603 ) t1
        JOIN (
        SELECT
        device_name,
        8603 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8603
        AND abnormal_code = 9001
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        <!-- 泵吸收功率，针对“属性值异常”，若设备当天此类异常的数量合计超过当天上传工况数量的0.5%有效 -->
        UPDATE dqm.ors_device_data_abnormal_detail
        SET abnormal_effective = subquery.abnormal_effective
        FROM
        (
        SELECT
        *,
        CASE
        WHEN ( ROUND( abnormal_cnt :: NUMERIC / work_cnt :: NUMERIC, 5 ) * 100 ) &lt;= 0.5 THEN 0
        ELSE 1 END AS abnormal_effective
        FROM
        (
        SELECT
        t2.*,
        CASE WHEN t1.work_cnt = 0 THEN NULL
        ELSE t1.work_cnt END AS work_cnt
        FROM
        ( SELECT * FROM sany_data_service.sanyds_device_work_cnt WHERE stat_date = #{bizDate}::DATE AND param_code = 8604 ) t1
        JOIN (
        SELECT
        device_name,
        8604 AS param_code,
        count (concat(param_code, abnormal_code)) as abnormal_cnt,
        stat_date
        FROM
        dqm.ors_device_data_abnormal_detail
        WHERE
        stat_date = #{bizDate}::DATE
        AND param_code = 8604
        AND abnormal_code = 9001
        GROUP BY
        stat_date,
        device_name
        ) t2 ON t1.stat_date = t2.stat_date
        AND t1.device_name = t2.device_name
        ) t3
        ) AS subquery
        WHERE
        dqm.ors_device_data_abnormal_detail.stat_date = subquery.stat_date
        AND dqm.ors_device_data_abnormal_detail.device_name = subquery.device_name
        AND dqm.ors_device_data_abnormal_detail.param_code = subquery.param_code
        AND dqm.ors_device_data_abnormal_detail.abnormal_code = 9001
        ;
        ANALYZE dqm.ors_device_data_abnormal_detail;
    </update>

    <delete id="deleteSanyDataService">
        -- 清除7天之前的数据
        DELETE FROM sany_data_service.sanyds_core_param_abnormal_detail WHERE stat_date &lt;= (#{bizDate}::DATE - interval '1 days');
        DELETE FROM sany_data_service.sanyds_core_param_stat_hour WHERE stat_date = (#{bizDate}::DATE - interval '7 days');
        DELETE FROM sany_data_service.sanyds_device_location_day WHERE stat_date = (#{bizDate}::DATE - interval '7 days');
    </delete>

    <insert id="syncAbnormalData">
        -- 设置临时文件无限制，防止数据量过大导致执行失败
        SET temp_file_limit = '-1';

        -- 删除昨天的数据，防止手动执行导致数据重复
        truncate dqm.ors_device_data_abnormal_detail;

        -- 删除索引，提高数据插入速度，再创建索引
        ALTER TABLE dqm.ors_device_data_abnormal_detail DROP CONSTRAINT ors_device_data_abnormal_detail_new_pkey;

        WITH highest_priority_abnormals AS (
            SELECT id
            FROM (
                SELECT
                    T1.id,
                    ROW_NUMBER() OVER (
                        PARTITION BY T1.device_name, T1.param_code, T1.abnormal_time
                        ORDER BY T3.abnormal_code_order ASC NULLS LAST, T1.id ASC
                    ) as rn
                FROM sany_data_service.sanyds_core_param_abnormal_detail T1
                    LEFT JOIN sany_data_service.sanyds_dict T3 ON T3.dict_id = T1.abnormal_code
                WHERE T1.stat_date = #{bizDate}::DATE
            ) ranked
            WHERE rn = 1
        ),
        temp AS (
            SELECT
                T4.thing_id,
                T4.device_code,
                T1.device_name,
                omd.division_code,
                T4.data_center_id,
                T4.model_id,
                T4.model_name,
                T3.dict_desc,
                json_strip_nulls(
                    CASE WHEN T1.abnormal_code = 9007 OR T1.param_code = 8501 THEN
                         json_build_object(
                                 '当前时间', to_char(to_timestamp((json->>'this_pt')::bigint/1000.0), 'YYYY-MM-DD HH24:MI:SS.MS')::text,
                                 '采样时差', CASE WHEN json->>'pt_inc' IS NOT NULL THEN round((json->>'pt_inc')::numeric / 1000, 2) || 's'::text ELSE NULL END,
                                 '新经度', CASE
                                               WHEN json->>'this_longitude' IS NOT NULL AND json->>'this_longitude' LIKE '0E%' THEN '0'
                                               WHEN json->>'this_longitude' IS NOT NULL THEN round((json->>'this_longitude')::numeric, 6)::text
                                               ELSE NULL END,
                                 '新纬度', CASE
                                               WHEN json->>'this_latitude' IS NOT NULL AND json->>'this_longitude' LIKE '0E%' THEN '0'
                                               WHEN json->>'this_latitude' IS NOT NULL THEN round((json->>'this_latitude')::numeric, 6)::text
                                               ELSE NULL END,
                                 '旧经度', CASE WHEN json->>'last_longitude' IS NOT NULL THEN round((json->>'last_longitude')::numeric, 6)::text ELSE NULL END,
                                 '旧纬度', CASE WHEN json->>'last_latitude' IS NOT NULL THEN round((json->>'last_latitude')::numeric, 6)::text ELSE NULL END,
                                 '里程', CASE WHEN json->>'_mileage' IS NOT NULL THEN json->>'_mileage'::text ELSE NULL END,
                                 '漂移距离', CASE WHEN json->>'distance' IS NOT NULL THEN round((json->>'distance')::numeric, 5) || 'Km'::text ELSE NULL END,
                                 '漂移速度', CASE WHEN json->>'speed' IS NOT NULL THEN round((json->>'speed')::numeric, 5) || 'Km/h'::text ELSE NULL END
                         )
                        WHEN  T1.abnormal_code = 9001 OR  T1.abnormal_code = 9006  THEN
                            json_build_object(
                                    '当前值', json->>'this_pv',
                                    '当前时间', to_char(to_timestamp((json->>'this_pt')::bigint/1000.0), 'YYYY-MM-DD HH24:MI:SS.MS')::text
                                )
                         WHEN  T1.abnormal_code in (9003,9100) THEN
                            json
                    ELSE
                        json_build_object(
                                '当前值', json->>'this_pv',
                                '当前时间', to_char(to_timestamp((json->>'this_pt')::bigint/1000.0), 'YYYY-MM-DD HH24:MI:SS.MS')::text,
                                '上一值', json->>'last_pv',
                                '上一时间', to_char(to_timestamp((json->>'last_pt')::bigint/1000.0), 'YYYY-MM-DD HH24:MI:SS.MS')::text,
                                '差值', json->>'pv_inc',
                                '采样时差', CASE WHEN json->>'pt_inc' IS NOT NULL THEN round((json->>'pt_inc')::numeric / 1000, 2) || 's'::text ELSE NULL END
                        )
                   END
                ) AS abnormal_data,
                CURRENT_TIMESTAMP,
                (regexp_split_to_array(T2.dict_desc,':'))[2],
                (regexp_split_to_array(T2.dict_desc,'[:（]'))[1],
                T1.tenant_id,
                json->>'last_pv',
                json->>'last_pt',
                json->>'this_pv',
                json->>'this_pt',
                json->>'pv_inc',
                json->>'pt_inc',
                T1.abnormal_time,
                T1.param_code,
                T1.stat_date,
                T1.abnormal_code,
                T1.abnormal_detail,
                T1.create_time,
                T1.id
            FROM
                ( SELECT
                id, tenant_id, device_name, stat_date, param_code, abnormal_code, abnormal_time, abnormal_detail, model_id, create_time,
                (case when abnormal_detail like '%{%' then abnormal_detail::json else CONCAT('{', abnormal_detail, '}')::json end ) as json
                FROM sany_data_service.sanyds_core_param_abnormal_detail
                WHERE id IN (SELECT id FROM highest_priority_abnormals)
                ) T1
                    INNER JOIN dqm.ors_model_properties_config T6 ON T1.model_id = T6.model_id AND T1.param_code = T6.param_code
                    LEFT JOIN sany_data_service.sanyds_dict T2 ON T2.dict_id = T1.param_code
                    LEFT JOIN sany_data_service.sanyds_dict T3 ON T3.dict_id = T1.abnormal_code
                    LEFT JOIN dqm.ors_base_device_info T4 ON T4.asset_id = T1.device_name
                    LEFT JOIN  dqm.ors_model_division omd ON omd.model_id = T4.model_id
                    LEFT JOIN (SELECT asset_id, string_agg(param_code::TEXT, ',') AS param_codes
                    FROM dqm.ors_device_check_config
                    WHERE param_code IS NOT NULL
                    AND exclude_type != 'WHOLE'
                        GROUP BY asset_id) T5 ON T5.asset_id = T1.device_name
                      WHERE (T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T1.param_code||'%')
                            AND T1.device_name NOT IN (SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE')
        )
        INSERT INTO dqm.ors_device_data_abnormal_detail (
            uuid                     ,
            device_code              ,
            device_name              ,
            division_code            ,
            data_center_id            ,
            model_id                 ,
            model_name               ,
            abnormal_name            ,
            abnormal_data            ,
            create_time              ,
            property                 ,
            property_name            ,
            tenant_id                ,
            last_pv                  ,
            last_pt                  ,
            cur_pv                   ,
            cur_pt                   ,
            pv_inc                   ,
            pt_inc                   ,
            abnormal_time            ,
            param_code               ,
            stat_date                ,
            abnormal_code            ,
            raw_data                 ,
            raw_time,
            detail_id
        ) SELECT * FROM temp;

        ALTER TABLE dqm.ors_device_data_abnormal_detail ADD CONSTRAINT ors_device_data_abnormal_detail_new_pkey PRIMARY KEY (id);

        analyze dqm.ors_device_data_abnormal_detail;
    </insert>
</mapper>
