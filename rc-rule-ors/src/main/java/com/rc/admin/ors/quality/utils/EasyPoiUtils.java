package com.rc.admin.ors.quality.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.afterturn.easypoi.handler.inter.IWriter;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.afterturn.easypoi.word.entity.MyXWPFDocument;
import cn.afterturn.easypoi.word.parse.ParseWord07;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2023/11/1 12:12
 * @describe
 */
public class EasyPoiUtils {

    private static final Logger log = LoggerFactory.getLogger(EasyPoiUtils.class);
    private static final int MAX_SIZE = 1000;

    private EasyPoiUtils() {
    }

    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        try (OutputStream os = response.getOutputStream())  {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(os);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static <T> void defaultExport(List<T> dataList, Class<?> clz, String fileName, HttpServletResponse response, ExportParams exportParams) {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clz, dataList);
        if (workbook != null) {
            downLoadExcel(fileName, response, workbook);
        }

    }

    public static <T> void exportExcel(List<T> dataList, String title, String sheetName, Class<?> clz, String fileName, boolean isCreateHeader, HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(dataList, clz, fileName, response, exportParams);
    }

    public static <T> Workbook exportBigExcel(String title, String sheetName, Class<?> clz, boolean isCreateHeader, IExcelExportServer server, Object queryParams) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        return ExcelExportUtil.exportBigExcel(exportParams, clz, server, queryParams);
    }

    public static <T> Workbook exportBigExcel(List<T> dataList, String title, String sheetName, Class<?> clz, boolean isCreateHeader) {
        if (null != dataList && dataList.size() > 0) {
            ExportParams exportParams = new ExportParams(title, sheetName);
            exportParams.setCreateHeadRows(isCreateHeader);
            IWriter<Workbook> workbookIWriter = ExcelExportUtil.exportBigExcel(exportParams, clz);
            if (dataList.size() > 1000) {
                int size = dataList.size();
                int point = size % 1000;
                int len = size / 1000;
                if (point > 0) {
                    ++len;
                }

                for (int i = 0; i < len; ++i) {
                    int toIndex = (i + 1) * 1000;
                    if (i + 1 == len) {
                        toIndex = size;
                    }

                    workbookIWriter.write(dataList.subList(i * 1000, toIndex));
                }
            } else {
                workbookIWriter.write(dataList);
            }

            return (Workbook) workbookIWriter.close();
        } else {
            return null;
        }
    }

    public static void downLoadBigExcel(Workbook workbook, String fileName, HttpServletResponse response) {
        if (workbook != null) {
            downLoadExcel(fileName, response, workbook);
        }

    }

    public static <T> void exportExcel(List<T> dataList, String title, String sheetName, Class<?> clz, String fileName, HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setType(ExcelType.XSSF);
        defaultExport(dataList, clz, fileName, response, exportParams);
    }

    private static void defaultExport(List<Map<String, Object>> dataList, String fileName, HttpServletResponse response) {
        Workbook workbook = ExcelExportUtil.exportExcel(dataList, ExcelType.XSSF);
        if (workbook != null) {
            downLoadExcel(fileName, response, workbook);
        }

    }

    public static void exportExcel(List<Map<String, Object>> dataList, String fileName, HttpServletResponse response) {
        defaultExport(dataList, fileName, response);
    }

    public static Workbook exportExcel(ExportParams entity, List<ExcelExportEntity> entityList, Collection<?> dataSet) {
        return ExcelExportUtil.exportExcel(entity, entityList, dataSet);
    }

    public static <T> List<T> importExcel(String filePath, Integer titleRows, Integer headerRows, Class<T> clz) {
        if (StringUtils.isBlank(filePath)) {
            return null;
        } else {
            ImportParams params = new ImportParams();
            params.setTitleRows(titleRows);
            params.setHeadRows(headerRows);

            try {
                return ExcelImportUtil.importExcel(new File(filePath), clz, params);
            } catch (Exception var6) {
                throw new RuntimeException(var6.getMessage());
            }
        }
    }

    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> clz) {
        if (file == null) {
            return null;
        } else {
            ImportParams params = new ImportParams();
            params.setTitleRows(titleRows);
            params.setHeadRows(headerRows);

            try {
                return ExcelImportUtil.importExcel(file.getInputStream(), clz, params);
            } catch (Exception var6) {
                throw new RuntimeException(var6);
            }
        }
    }

    public static <T> List<T> importExcel(MultipartFile file, Class<T> clz) {
        if (file == null) {
            return null;
        } else {
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);

            try {
                return ExcelImportUtil.importExcel(file.getInputStream(), clz, params);
            } catch (Exception var4) {
                throw new RuntimeException(var4);
            }
        }
    }

    public static void selectList(Workbook workbook, int firstRow, int lastRow, int firstCol, int lastCol, List<String> dataArray, int sheetHidden) {
        String hiddenName = "hidden_" + (int) ((Math.random() * 9.0 + 1.0) * 100.0);
        Sheet sheet = workbook.getSheetAt(0);
        Sheet hidden = workbook.createSheet(hiddenName);
        Cell cell = null;
        int i = 0;

        for (int length = dataArray.size(); i < length; ++i) {
            String name = (String) dataArray.get(i);
            Row row = hidden.createRow(i);
            cell = row.createCell(0);
            cell.setCellValue(name);
        }

        Name namedCell = workbook.createName();
        namedCell.setNameName(hiddenName);
        namedCell.setRefersToFormula(hiddenName + "!$A$1:$A$" + dataArray.size());
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidation validation = sheet.getDataValidationHelper().createValidation(sheet.getDataValidationHelper().createFormulaListConstraint(hiddenName), regions);
        workbook.setSheetHidden(sheetHidden, true);
        sheet.addValidationData(validation);
    }

    public static String exportWord(String templatePath, String temDir, String fileName, Map<String, Object> params) {
        Assert.notNull(templatePath, "模板路径不能为空");
        Assert.notNull(temDir, "临时文件路径不能为空");
        Assert.notNull(fileName, "导出文件名不能为空");
        Assert.isTrue(fileName.endsWith(".docx"), "word导出请使用docx格式");
        if (!temDir.endsWith("/")) {
            temDir = temDir + File.separator;
        }

        File dir = new File(temDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String tmpPath = "";

        try {
            XWPFDocument doc = WordExportUtil.exportWord07(templatePath, params);
            tmpPath = temDir + fileName;
            FileOutputStream fos = new FileOutputStream(tmpPath);
            doc.write(fos);
            fos.flush();
            fos.close();
        } catch (Exception var8) {
            log.error("转换异常", var8);
        }

        return tmpPath;
    }

    public static String exportWord(InputStream is, String temDir, String fileName, Map<String, Object> params) {
        Assert.notNull(temDir, "临时文件路径不能为空");
        Assert.notNull(fileName, "导出文件名不能为空");
        Assert.isTrue(fileName.endsWith(".docx"), "word导出请使用docx格式");
        if (!temDir.endsWith("/")) {
            temDir = temDir + File.separator;
        }

        File dir = new File(temDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String tmpPath = "";

        try {
            MyXWPFDocument doc = new MyXWPFDocument(is);
            (new ParseWord07()).parseWord(doc, params);
            tmpPath = temDir + fileName;
            FileOutputStream fos = new FileOutputStream(tmpPath);
            doc.write(fos);
            fos.flush();
            fos.close();
        } catch (Exception var8) {
            log.error("转换异常", var8);
        }

        return tmpPath;
    }

    public static byte[] getImageBase64(String path) throws IOException {
        InputStream input = new FileInputStream(path);
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buf = new byte[1024];

        int numBytesRead;
        while ((numBytesRead = input.read(buf)) != -1) {
            output.write(buf, 0, numBytesRead);
        }

        byte[] data = output.toByteArray();
        output.close();
        input.close();
        return data;
    }

    /**
     * 导出大数据 Sheet 页面
     *
     * @param title          表格标题
     * @param isCreateHeader 是否创建表头
     * @param batchSize      每批次写入的数据量
     * @return 导出的 Workbook 对象
     */
    public static <T> Workbook exportBigExcel(List<T> dataList, String title, String sheetName,
                                              Class<?> clz, boolean isCreateHeader, int batchSize) {

        // 参数校验
        if (dataList == null || dataList.isEmpty()) {
            return new SXSSFWorkbook(); // 返回空 Workbook
        }

        // 创建 Workbook
        Workbook workbook = new SXSSFWorkbook();

        // 设置导出参数
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);

        // 创建 IWriter
        IWriter<Workbook> workbookIWriter = ExcelExportUtil.exportBigExcel(exportParams, clz);

        try {
            // 分批次写入数据
            if (dataList.size() > batchSize) {
                int size = dataList.size();
                int len = (size + batchSize - 1) / batchSize; // 计算批次数量

                for (int i = 0; i < len; i++) {
                    int fromIndex = i * batchSize;
                    int toIndex = Math.min((i + 1) * batchSize, size);
                    workbookIWriter.write(dataList.subList(fromIndex, toIndex));
                }
            } else {
                // 数据量小于等于批次大小，直接写入
                workbookIWriter.write(dataList);
            }

            // 将当前 Sheet 添加到 Workbook
            workbook = workbookIWriter.close();
        } catch (Exception e) {
            // 异常处理
            throw new RuntimeException("导出 Excel 失败", e);
        }

        return workbook;
    }

    public static void downloadWorkbooksAsZip(HttpServletResponse response,String zipName, Workbook... workbooks) {
        OutputStream toClient = null;
        FileOutputStream fos = null;
        ZipOutputStream zipOut = null;
        File zipFile = null;

        try {
            // 创建临时文件夹
            String tempDir = FileUtil.getTmpDirPath() + File.separator + IdUtil.fastSimpleUUID();
            File tempFolder = FileUtil.mkdir(tempDir);
            // 创建临时 ZIP 文件
            zipFile = new File(tempFolder, zipName + ".zip");
            fos = new FileOutputStream(zipFile);
            zipOut = new ZipOutputStream(fos);

            // 将每个 Workbook 写入 ZIP 文件
            for (int i = 0; i < workbooks.length; i++) {
                Workbook workbook = workbooks[i];
                if (workbook == null) {
                    continue; // 跳过空的 Workbook
                }

                // 如果 Workbook 有标题，使用标题作为文件名
                if (workbook.getNumberOfSheets() > 0 && workbook.getSheetName(0) != null) {
                    String filename = workbook.getSheetName(0);

                    // 创建临时 Excel 文件
                    File tempFile = new File(tempFolder,filename + ".xlsx");
                    try (FileOutputStream tempFos = new FileOutputStream(tempFile)) {
                        workbook.write(tempFos);
                    }

                    // 将临时文件添加到 ZIP 中
                    try (FileInputStream fis = new FileInputStream(tempFile)) {
                        ZipEntry zipEntry = new ZipEntry(tempFile.getName());
                        zipOut.putNextEntry(zipEntry);
                        byte[] buffer = new byte[1024 * 8]; // 8KB 缓冲区
                        int bytesRead;
                        while ((bytesRead = fis.read(buffer)) != -1) {
                            zipOut.write(buffer, 0, bytesRead);
                        }
                        zipOut.closeEntry();
                        workbook.close();
                    }

                    // 删除临时 Excel 文件
                    if (!tempFile.delete()) {
                        log.warn("临时文件删除失败: {}", tempFile.getName());
                    }
                }
            }

            // 关闭 ZIP 输出流
            zipOut.close();
            fos.close();

            // 设置响应头，提供 ZIP 文件下载
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipName, "UTF-8") + ".zip");

            // 将 ZIP 文件写入响应输出流
            toClient = new BufferedOutputStream(response.getOutputStream());
            try (FileInputStream fis = new FileInputStream(zipFile)) {
                byte[] buffer = new byte[1024 * 8]; // 8KB 缓冲区
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, bytesRead);
                }
                toClient.flush();
            }
        } catch (Exception e) {
            log.error("生成或下载 ZIP 文件时发生异常:", e);
            throw new RuntimeException("下载失败", e);
        } finally {
            // 关闭流
            if (toClient != null) {
                try {
                    toClient.close();
                } catch (IOException e) {
                    log.error("关闭输出流失败:", e);
                }
            }
            if (zipOut != null) {
                try {
                    zipOut.close();
                } catch (IOException e) {
                    log.error("关闭 ZIP 输出流失败:", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("关闭文件输出流失败:", e);
                }
            }

            // 删除临时 ZIP 文件
            if (zipFile != null && zipFile.exists() && !zipFile.delete()) {
                log.error("删除临时 ZIP 文件失败: {}", zipFile.getName());
            }
        }
    }

    public static void downloadByteArrayOutputStreamAsZip(HttpServletResponse response,String zipName,
                                                     String[] fileNames,
                                                          ByteArrayOutputStream... byteArrayOutputStreams) {
        OutputStream toClient = null;
        FileOutputStream fos = null;
        ZipOutputStream zipOut = null;
        File zipFile = null;

        try {
            // 创建临时文件夹
            String tempDir = FileUtil.getTmpDirPath() + File.separator + IdUtil.fastSimpleUUID();
            File tempFolder = FileUtil.mkdir(tempDir);
            // 创建临时 ZIP 文件
            zipFile = new File(tempFolder, zipName + ".zip");
            fos = new FileOutputStream(zipFile);
            zipOut = new ZipOutputStream(fos);

            // 将每个 Workbook 写入 ZIP 文件
            for (int i = 0; i < byteArrayOutputStreams.length; i++) {
                ByteArrayOutputStream byteArrayOutputStream = byteArrayOutputStreams[i];
                if (byteArrayOutputStream == null) {
                    continue;
                }
                InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

                // 将临时文件添加到 ZIP 中
                ZipEntry zipEntry = new ZipEntry(fileNames[i] + ".xlsx");
                zipOut.putNextEntry(zipEntry);
                byte[] buffer = new byte[1024 * 8]; // 8KB 缓冲区
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    zipOut.write(buffer, 0, bytesRead);
                }
                zipOut.closeEntry();

            }

            // 关闭 ZIP 输出流
            zipOut.close();
            fos.close();

            // 设置响应头，提供 ZIP 文件下载
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipName, "UTF-8") + ".zip");

            // 将 ZIP 文件写入响应输出流
            toClient = new BufferedOutputStream(response.getOutputStream());
            try (FileInputStream fis = new FileInputStream(zipFile)) {
                byte[] buffer = new byte[1024 * 8]; // 8KB 缓冲区
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, bytesRead);
                }
                toClient.flush();
            }
        } catch (Exception e) {
            log.error("生成或下载 ZIP 文件时发生异常:", e);
            throw new RuntimeException("下载失败", e);
        } finally {
            // 关闭流
            if (toClient != null) {
                try {
                    toClient.close();
                } catch (IOException e) {
                    log.error("关闭输出流失败:", e);
                }
            }
            if (zipOut != null) {
                try {
                    zipOut.close();
                } catch (IOException e) {
                    log.error("关闭 ZIP 输出流失败:", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("关闭文件输出流失败:", e);
                }
            }

            // 删除临时 ZIP 文件
            if (zipFile != null && zipFile.exists() && !zipFile.delete()) {
                log.error("删除临时 ZIP 文件失败: {}", zipFile.getName());
            }
        }
    }
}
