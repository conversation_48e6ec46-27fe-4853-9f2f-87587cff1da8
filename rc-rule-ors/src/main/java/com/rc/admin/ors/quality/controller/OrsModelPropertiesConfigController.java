package com.rc.admin.ors.quality.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import com.rc.admin.ors.quality.service.OrsModelPropertiesConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 物模型与属性检查配置(OrsDevicePropertiesConfig)表控制层
 * 此功能未实际使用
 * <AUTHOR>
 * @since 2023-10-23 18:56:32
 */
@ResponseResult
@RestController
@RequestMapping("/api/orsDevicePropertiesConfig")
@Api(tags = {"物模型与属性检查配置"})
public class OrsModelPropertiesConfigController {
    /**
     * 服务对象
     */
    @Resource
    private OrsModelPropertiesConfigService orsDevicePropertiesConfigService;

    /**
     * 分页查询某个物模型配置的属性检查及其关联的指标与剔除信息
     * @param page 分页信息
     * @param deviceCode 设备编号
     * @param modelId 模型ID
     * @return
     */
    @ApiOperation("分页查询某个物模型配置的属性检查及其关联的指标与剔除信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页", defaultValue = "1"),
            @ApiImplicitParam(name = "size", value = "每夜显示数据条数", defaultValue = "10"),
            @ApiImplicitParam(name = "deviceCode", value = "设备编码"),
            @ApiImplicitParam(name = "modelId", value = "w")
    })
    @GetMapping
    public Page<ModleIndicatorAndExclude> selectAll(Page<ModleIndicatorAndExclude> page, String deviceCode, String modelId, String assetId) {
        return orsDevicePropertiesConfigService.findModleIndicatorAndExclude(page, deviceCode, modelId, assetId);
    }

    /**
     * 新增数据
     *
     * @param orsDevicePropertiesConfig 实体对象
     * @return 新增结果
     */
    @PostMapping
    public boolean insert(@RequestBody OrsModelPropertiesConfig orsDevicePropertiesConfig) {
        return orsDevicePropertiesConfigService.save(orsDevicePropertiesConfig);
    }


    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    public boolean delete(@RequestParam("id") Long id) {
        return orsDevicePropertiesConfigService.removeById(id);
    }
}

