package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.OrsCoreParamStatLatest;
import com.rc.admin.ors.quality.model.DeviceLocationResp;
import com.rc.admin.ors.quality.model.UnReportDevice;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 核心工况施工统计表-最新表(OrsCoreParamStatLatest)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-01 19:14:09
 */
public interface OrsCoreParamStatLatestMapper extends BaseMapper<OrsCoreParamStatLatest> {

    void syncSanyData(@Param("bizDate") Date bizDate);

    @Select("select count(1) cnt from dqm.ors_core_param_stat_latest WHERE cast(param_value_latest_time as date) = #{bizDate}::DATE")
    Integer getStatLatestCnt(@Param("bizDate") Date bizDate);

    @Select("select count(1) cnt from sany_data_service.sanyds_core_param_stat_latest_day WHERE stat_date = #{bizDate}::DATE")
    Integer getStatLatestDayCnt(@Param("bizDate") Date bizDate);


    /**
     * 获取未上报异常的设备信息
     *
     * 新增判断    当前时间 需要大于等于入云时间
     * 并且  每日上报数据  不存在当前时间以及之前的上报数据
     * @param
     * @return
     */
    List<UnReportDevice> findUnreportUnbnormalData(@Param("bizDate") Date bizDate);

    @Select("DELETE FROM dqm.ors_device_data_abnormal_detail WHERE (stat_date = CAST(#{yesterday} AS DATE) AND abnormal_code = #{abnormal_code})")
    void removeByDate(@Param("yesterday") String yesterday,@Param("abnormal_code") Integer abnormal_code);


    /**
     * 获取长期未上报设备信息
     * @return
     */
    List<UnReportDevice> findLongTimeNoReport(@Param("bizDate") Date bizDate);



    List<DeviceLocationResp> getDeviceLocationResp(@Param("bizDate") Date bizDate);
}

