package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/14 10:45
 * @describe
 */
@Getter
@Setter
public class QuestionCount {

    @ApiModelProperty(value = "问题总数")
    private int total;

    @ApiModelProperty(value = "已关闭的数量")
    private int closedNum;

    @ApiModelProperty(value = "待平台处理的数量")
    private int platfromNum;

    @ApiModelProperty(value = "待硬件处理的数量")
    private int hardwareNum;

    @ApiModelProperty(value = "待研究院处理的数量")
    private int researchInstituteNum;

    @ApiModelProperty(value = "待起机/剔除的数量")
    private int waitStartNum;

    @ApiModelProperty(value = "待验证的数量")
    private int waitVerifyNum;

    @ApiModelProperty(value = "待运营处理的数量")
    private int operateNum;
}
