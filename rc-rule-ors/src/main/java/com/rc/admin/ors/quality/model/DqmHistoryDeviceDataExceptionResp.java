package com.rc.admin.ors.quality.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DqmHistoryDeviceDataExceptionResp {

    @ApiModelProperty(value = "事业部")
    @Excel(name = "事业部", width = 10, orderNum = "0")
    private String sybbh;
    private String sybbhCode;
    @ApiModelProperty(value = "产品组")
    private String zehdSpart;

    @ApiModelProperty(value = "数据中心")
    private Integer dataCenterId;

    @ApiModelProperty(value = "产品组")
    @Excel(name = "产品组", width = 10, orderNum = "1")
    private String zehdSpartdesc;
    @ApiModelProperty(value = "所属大区")
    @Excel(name = "所属大区", width = 10, orderNum = "2")
    private String zehdsvReg;
    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", width = 10, orderNum = "3")
    private String deviceName;
    @ApiModelProperty(value = "物标识")
    @Excel(name = "物标识", width = 10, orderNum = "4")
    private String deviceCode;
    @ApiModelProperty(value = "实例名称")
    @Excel(name = "实例名称", width = 10, orderNum = "5")
    private String thingId;
    @ApiModelProperty(value = "固件版本号")
    @Excel(name = "固件版本", width = 10, orderNum = "6")
    private String fwVersion;
    @ApiModelProperty(value = "硬件版本")
    @Excel(name = "硬件版本", width = 10, orderNum = "7")
    private String hwVersion;
    @ApiModelProperty(value = "认证秘钥")
    @Excel(name = "认证秘钥", width = 10, orderNum = "8")
    private String authToken;
    @ApiModelProperty(value = "模型名称")
    @Excel(name = "模型ID", width = 10, orderNum = "9")
    private String modelId;
    private String modelName;

    @ApiModelProperty(value = "是否加入问题跟进 0 未处理，1：已处加入，2：已加入？, 3部分处理")
    private int handle;
    @ApiModelProperty(value = "设备跟进中 0:否,1是 ")
    private int hasFollowUp;

    private String startStatDate;
    private String endStatDate;
    @ApiModelProperty(value = "定位国家")
    private String deviceLocation;
    private String abnormalCode;
    private String abnormalCount;
    private String abnormalTime;
    private String abnormalData;
    private String paramCode;
    @ApiModelProperty(value = "国家")
    @Excel(name = "国家", width = 10, orderNum = "10")
    private String country;

    private long hasRecord;
    private long notRecord;

    private long deviceStatus9008;
    private long deviceStatus9009;
    private long deviceLocation9008;
    private long deviceLocation9001;
    private long deviceLocation9002;
    private long deviceLocation9007;
    private long deviceLocation9009;
    private long engineWorktime9008;
    private long engineWorktime9001;
    private long engineWorktime9004;
    private long engineWorktime9009;
    private long workingTime9008;
    private long workingTime9001;
    private long workingTime9004;
    private long workingTime9009;
    private long totalFuelConsumption9008;
    private long totalFuelConsumption9001;
    private long totalFuelConsumption9004;
    private long totalFuelConsumption9009;
    private long pumpingVolume9008;
    private long pumpingVolume9001;
    private long pumpingVolume9004;
    private long pumpingVolume9009;
    private long drivingMileage9008;
    private long drivingMileage9001;
    private long drivingMileage9004;
    private long drivingMileage9009;

    private long fuelLevel9008;
    private long fuelLevel9001;
    private long fuelLevel9009;
    private long engineSpeed9008;
    private long engineSpeed9001;
    private long engineSpeed9009;
    private long waterTemperature9008;
    private long waterTemperature9001;
    private long waterTemperature9009;
    private long SOCStateofcharge9008;
    private long SOCStateofcharge9001;
    private long SOCStateofcharge9009;
    private long travelSpeed9008;
    private long travelSpeed9001;
    private long travelSpeed9009;
    private long totalElectricConsumption9008;
    private long totalElectricConsumption9001;
    private long totalElectricConsumption9004;
    private long totalElectricConsumption9009;

    //新增的属性
    private long totalIdleFuelConsumption9001;
    private long totalIdleFuelConsumption9004;
    private long totalIdleFuelConsumption9005;
    private long totalIdleFuelConsumption9008;
    private long totalIdleFuelConsumption9009;
    private long totalIdleTime9001;
    private long totalIdleTime9004;
    private long totalIdleTime9005;
    private long gear9008;
    private long gear9009;
    private long gear9001;
    private long totalIdleTime9008;
    private long totalIdleTime9009;
    private long totalTimeLeftMoving9001;
    private long totalTimeLeftMoving9004;
    private long totalTimeLeftMoving9005;
    private long totalTimeLeftMoving9008;
    private long totalTimeLeftMoving9009;
    private long totalTimeRightMoving9001;
    private long totalTimeRightMoving9004;
    private long totalTimeRightMoving9005;
    private long totalTimeRightMoving9008;
    private long totalTimeRightMoving9009;
    private long oilPressure9001;
    private long oilPressure9008;
    private long oilPressure9009;
    private long pumpTotalAbsorbedTorque9001;
    private long pumpTotalAbsorbedTorque9008;
    private long pumpTotalAbsorbedTorque9009;

    //2024-12-30
    private long pumpMotorRotateSpeed9001;
    private long pumpMotorRotateSpeed9008;
    private long pumpMotorRotateSpeed9009;
    private long chargingStatus9008;
    private long chargingStatus9009;
    private long chargingStatus9001;
    private long chargeTimeRemain9001;
    private long chargeTimeRemain9008;
    private long chargeTimeRemain9009;
    private long singleChargeCapacity9001;
    private long singleChargeCapacity9008;
    private long singleChargeCapacity9009;
    private long dayPowerConsumption9001;
    private long dayPowerConsumption9008;
    private long dayPowerConsumption9009;

    //2025-01-17
    private long actionCode9001;
    private long actionCode9008;
    private long actionCode9009;
    private long totalTimeRotation9001;
    private long totalTimeRotation9004;
    private long totalTimeRotation9005;
    private long totalTimeRotation9008;
    private long totalTimeRotation9009;

    private long totalNoActionPowerConsumption9001;
    private long totalNoActionPowerConsumption9004;
    private long totalNoActionPowerConsumption9008;
    private long totalNoActionPowerConsumption9009;

    //2025-04-27
    private long  drivingMileage9005;
    private long  totalFuelConsumption9005;
    private long  totalElectricConsumption9005;
    private long  workingTime9005;
    private long  engineWorktime9005;
    private long  travelSpeed9006;
    private long  engineSpeed9006;
    private long  waterTemperature9006;
    private long  fuelLevel9006;

    private long deviceLocationCnt;

    //2025-05-14
    private long idleTimeIdleFuel9101;
    private long idleTimeIdleFuel9102;
    private long idleTimeIdleFuel9100;
    private long workTimeFuel9103;
    private long workTimeFuel9104;
    private long workTimeFuel9100;

    private long drivingMileage9003;
    private long totalFuelConsumption9003;
    private long totalElectricConsumption9003;
    private long workingTime9003;
    private long engineWorktime9003;

    private long idelFuelFuel9105;
    private long idelFuelFuel9100;
    private long idelTimeWorkTime9106;
    private long idelTimeWorkTime9100;
    private long mileageSpeed9107;
    private long mileageSpeed9108;
    private long mileageSpeed9100;
    private long mileageLocation9109;
    private long mileageLocation9100;
    private long engineTimeFuel9100;


    private String deviceLocation9001Data;
    private String deviceLocation9002Data;
    private String deviceLocation9007Data;
    private String deviceLocation9009Data;
    private String engineWorktime9001Data;
    private String engineWorktime9004Data;
    private String engineWorktime9009Data;
    private String workingTime9001Data;
    private String workingTime9004Data;
    private String workingTime9009Data;
    private String totalFuelConsumption9001Data;
    private String totalFuelConsumption9004Data;
    private String totalFuelConsumption9009Data;
    private String pumpingVolume9001Data;
    private String pumpingVolume9004Data;
    private String pumpingVolume9009Data;
    private String drivingMileage9001Data;
    private String drivingMileage9004Data;
    private String drivingMileage9009Data;

    private String fuelLevel9008Data;
    private String fuelLevel9001Data;
    private String fuelLevel9009Data;
    private String engineSpeed9008Data;
    private String engineSpeed9001Data;
    private String engineSpeed9009Data;
    private String waterTemperature9008Data;
    private String waterTemperature9001Data;
    private String waterTemperature9009Data;
    private String SOCStateofcharge9008Data;
    private String SOCStateofcharge9001Data;
    private String SOCStateofcharge9009Data;
    private String travelSpeed9008Data;
    private String travelSpeed9001Data;
    private String travelSpeed9009Data;
    private String totalElectricConsumption9008Data;
    private String totalElectricConsumption9001Data;
    private String totalElectricConsumption9004Data;
    private String totalElectricConsumption9009Data;

    //新增的属性
    private String totalIdleFuelConsumption9001Data;
    private String totalIdleFuelConsumption9004Data;
    private String totalIdleFuelConsumption9008Data;
    private String totalIdleFuelConsumption9009Data;
    private String totalIdleTime9001Data;
    private String totalIdleTime9004Data;
    private String gear9008Data;
    private String gear9009Data;
    private String totalIdleTime9008Data;
    private String totalIdleTime9009Data;
    private String totalTimeLeftMoving9001Data;
    private String totalTimeLeftMoving9004Data;
    private String totalTimeLeftMoving9008Data;
    private String totalTimeLeftMoving9009Data;
    private String totalTimeRightMoving9001Data;
    private String totalTimeRightMoving9004Data;
    private String totalTimeRightMoving9008Data;
    private String totalTimeRightMoving9009Data;
    private String oilPressure9001Data;
    private String oilPressure9008Data;
    private String oilPressure9009Data;
    private String pumpTotalAbsorbedTorque9001Data;
    private String pumpTotalAbsorbedTorque9008Data;
    private String pumpTotalAbsorbedTorque9009Data;

//2024-12-30
    private String pumpMotorRotateSpeed9001Data;
    private String pumpMotorRotateSpeed9008Data;
    private String pumpMotorRotateSpeed9009Data;
    private String chargingStatus9008Data;
    private String chargingStatus9009Data;
    private String chargeTimeRemain9001Data;
    private String chargeTimeRemain9008Data;
    private String chargeTimeRemain9009Data;
    private String singleChargeCapacity9001Data;
    private String singleChargeCapacity9008Data;
    private String singleChargeCapacity9009Data;
    private String dayPowerConsumption9001Data;
    private String dayPowerConsumption9008Data;
    private String dayPowerConsumption9009Data;

//2025-01-17
    private String actionCode9001Data;
    private String actionCode9008Data;
    private String actionCode9009Data;
    private String totalTimeRotation9001Data;
    private String totalTimeRotation9004Data;
    private String totalTimeRotation9008Data;
    private String totalTimeRotation9009Data;


//    状态            8503
    private List<DqmDeviceDataAbnormalModel> abnormal8503;
//    位置            8501
    private List<DqmDeviceDataAbnormalModel> abnormal8501;
//    发动机小时        8105
    private List<DqmDeviceDataAbnormalModel> abnormal8105;
//    总油耗        8201
    private List<DqmDeviceDataAbnormalModel> abnormal8201;
//    方量            8401
    private List<DqmDeviceDataAbnormalModel> abnormal8401;
////    里程            8403
    private List<DqmDeviceDataAbnormalModel> abnormal8403;
    //工作小时
    private List<DqmDeviceDataAbnormalModel> abnormal8102;
    //油位
    private List<DqmDeviceDataAbnormalModel> abnormal8506;
    //发动机转速
    private List<DqmDeviceDataAbnormalModel> abnormal8507;
    //发动机水温
    private List<DqmDeviceDataAbnormalModel> abnormal8508;
    //当前电量
    private List<DqmDeviceDataAbnormalModel> abnormal8509;
    //行驶速度
    private List<DqmDeviceDataAbnormalModel> abnormal8510;
    //总电耗
    private List<DqmDeviceDataAbnormalModel> abnormal8511;
//新增属性
    //怠速油耗
    private List<DqmDeviceDataAbnormalModel> abnormal8205;
    //怠速时长
    private List<DqmDeviceDataAbnormalModel> abnormal8106;
    //挡位
    private List<DqmDeviceDataAbnormalModel> abnormal8602;
    //左行走工时
    private List<DqmDeviceDataAbnormalModel> abnormal8108;
    //右行走工时
    private List<DqmDeviceDataAbnormalModel> abnormal8107;
    //油压力
    private List<DqmDeviceDataAbnormalModel> abnormal8603;
    //泵吸收功率
    private List<DqmDeviceDataAbnormalModel> abnormal8604;

//2024-12-30
    //电机转速
    private List<DqmDeviceDataAbnormalModel> abnormal8605;
    //充电状态
    private List<DqmDeviceDataAbnormalModel> abnormal8606;
    //充电剩余时间
    private List<DqmDeviceDataAbnormalModel> abnormal8607;
    //单次充电电量
    private List<DqmDeviceDataAbnormalModel> abnormal8608;
    //当日电耗
    private List<DqmDeviceDataAbnormalModel> abnormal8609;

//2025-01-17
    //动作编码
    private List<DqmDeviceDataAbnormalModel> abnormal8610;
    //回转时间
    private List<DqmDeviceDataAbnormalModel> abnormal8611;

    //怠速电耗
    private List<DqmDeviceDataAbnormalModel> abnormal8305;


//2025-05-14
    //怠速时长&怠速油耗
    private List<DqmDeviceDataAbnormalModel> abnormal8701;

    //工作时间&总油耗
    private List<DqmDeviceDataAbnormalModel> abnormal8702;

    //怠速油耗&总油耗
    private List<DqmDeviceDataAbnormalModel> abnormal8703;

    //怠速时长&工作时长
    private List<DqmDeviceDataAbnormalModel> abnormal8704;

    //行驶里程&行驶速度
    private List<DqmDeviceDataAbnormalModel> abnormal8705;

    //行驶里程&设备位置
    private List<DqmDeviceDataAbnormalModel> abnormal8706;

    //发动机工作时长&总油耗
    private List<DqmDeviceDataAbnormalModel> abnormal8707;


    public int getHandle() {
        // 0 未处理，1：已处加入，2：已加入？, 3部分处理
        // t1.hasRecord > 0 and t1.notRecord >= 0
        // t1.hasRecord >= 0 and t1.notRecord > 0
        if (hasRecord > 0 && notRecord == 0) {
            return 1;
        }
        if (hasRecord > 0 && notRecord > 0) {
            return 2;
        }
        if (hasRecord == 0 && notRecord > 0) {
            return 0;
        }
        return 0;
    }
}
