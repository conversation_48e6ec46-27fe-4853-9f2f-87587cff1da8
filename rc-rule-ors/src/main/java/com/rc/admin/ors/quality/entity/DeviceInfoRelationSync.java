package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * machineLink数据同步表(DeviceInfoRelationSync)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:11:27
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("machineLink数据同步表")
@TableName("ors_ml_device_info_relation_sync")
public class DeviceInfoRelationSync extends Model<DeviceInfoRelationSync> {

    @TableId(type = IdType.AUTO)
    private String deviceId;


    @ApiModelProperty(name = "deviceName", value = "设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "serialNum", value = "序列号")
    @TableField(value = "serial_num")
    private String serialNum;

    @ApiModelProperty(name = "deviceCreateDate", value = "创建时间")
    @TableField(value = "device_create_date")
    private Date deviceCreateDate;

    @ApiModelProperty(name = "deviceUpdate", value = "修改时间")
    @TableField(value = "device_update")
    private Date deviceUpdate;

    @ApiModelProperty(name = "thingInstanceId", value = "平台复合物实例id")
    @TableField(value = "thing_instance_id")
    private String thingInstanceId;

    @ApiModelProperty(name = "thingModelId", value = "平台复合物id")
    @TableField(value = "thing_model_id")
    private String thingModelId;

    @ApiModelProperty(name = "hostId", value = "主机ID(车辆ID)")
    @TableField(value = "host_id")
    private String hostId;

    @ApiModelProperty(name = "tboxId", value = "盒子ID")
    @TableField(value = "tbox_id")
    private String tboxId;

    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "bindCreateDate", value = "创建时间")
    @TableField(value = "bind_create_date")
    private Date bindCreateDate;

    @ApiModelProperty(name = "bindUpdate", value = "修改时间")
    @TableField(value = "bind_update")
    private Date bindUpdate;

    @ApiModelProperty(name = "mlModelId", value = "ml模型ID")
    @TableField(value = "ml_model_id")
    private String mlModelId;

    @ApiModelProperty(name = "rc_asset_id", value = "根云物标识")
    @TableField(value = "rc_asset_id")
    private String rcAssetId;
    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.deviceId;
    }
}

