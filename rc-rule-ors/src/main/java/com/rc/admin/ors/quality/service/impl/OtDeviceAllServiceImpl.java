package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.OtDeviceAllMapper;
import com.rc.admin.ors.quality.entity.OtDeviceAll;
import com.rc.admin.ors.quality.model.OtDeviceAllReportResp;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;
import com.rc.admin.ors.quality.model.OtDeviceAllResp;
import com.rc.admin.ors.quality.service.OtDeviceAllService;
import com.rc.admin.ors.quality.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@Service
@Slf4j
public class OtDeviceAllServiceImpl extends ServiceImpl<OtDeviceAllMapper, OtDeviceAll> implements OtDeviceAllService {


    @Override
    public void exportExcelOtDeviceAll(HttpServletResponse response, OtDeviceAllReq req) {
        List<OtDeviceAllResp> otDeviceAllResps = baseMapper.selectPageList(req);
        try {
            ExcelUtil.exportExcel(response, otDeviceAllResps, "海外设备管理——全量设备台账.xlsx", "sheet1", OtDeviceAllResp.class);
        } catch (IOException e) {
            log.info("海外设备管理——全量设备台账导出失败：{}",e.getMessage());
        }
    }


    @Override
    public void exportExcelOtDeviceAllReport(HttpServletResponse response, OtDeviceAllReq req) {
        List<OtDeviceAllReportResp> otDeviceAllReportResps = baseMapper.selectPageReportList(req);
        try {
            ExcelUtil.exportExcel(response, otDeviceAllReportResps, "海外设备管理——全量设备报表.xlsx", "sheet1", OtDeviceAllReportResp.class);
        } catch (IOException e) {
            log.info("海外设备管理——全量设备报表导出失败：{}",e.getMessage());
        }

    }


}
