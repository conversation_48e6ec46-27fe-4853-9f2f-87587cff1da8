package com.rc.admin.ors.quality.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 *     出厂时间
 * </p>
 * <AUTHOR>
 * @since 2023/10/25
 */
@AllArgsConstructor
public enum FactoryDateType {
    /**
     * 出厂时间小于一年内
     */
    LESS_ONE(1, "1年内"),
    /**
     * 出厂时间2年到3年
     */
    TWO_AND_THREE(2, "2-3年"),
    /**
     * 出厂时间4年到5年
     */
    FOUR_AND_FIVE(3, "4-5年"),
    /**
     * 出厂时间大于6年
     */
    GREATER_SIX(4, "大于6年"),
    /**
     * 其它
     */
    OTHER(5, "其它");

    @Getter
    private final Integer value;

    @Getter
    private final String desc;

    public static Integer getDateValue(Date date) {
        if (Objects.isNull(date)) {
            return OTHER.value;
        }

        // 时间加1年大于当前时间，就是一年内
        if (DateUtil.compare(DateUtil.offset(date, DateField.YEAR, 1), new Date()) > 0) {
            return LESS_ONE.value;
        }

        if (DateUtil.compare(DateUtil.offset(date, DateField.YEAR, 1), new Date()) <= 0
                && DateUtil.compare(DateUtil.offset(date, DateField.YEAR, 3), new Date()) > 0) {
            return TWO_AND_THREE.value;
        }

        if (DateUtil.compare(DateUtil.offset(date, DateField.YEAR, 3), new Date()) <= 0
                && DateUtil.compare(DateUtil.offset(date, DateField.YEAR, 5), new Date()) > 0) {
            return TWO_AND_THREE.value;
        }
//        long l = System.currentTimeMillis() - date.getTime();
//        long v = l - (3600 * 24 * 30 * 12 * 1000L);
//        if (v < 1) {
//            return LESS_ONE.value;
//        }

        return GREATER_SIX.value;
    }

    public static Date[] getDate(Integer value) {
        Date[] array = new Date[2];

        Date now = new Date();
        FactoryDateType instance = getInstance(value);
        if (Objects.isNull(instance)) {
            return null;
        }
        switch (instance) {
            case LESS_ONE:
                array[0] = DateUtil.offset(now, DateField.YEAR, -1);
                array[1] = now;
                break;
            case TWO_AND_THREE:
                array[0] = DateUtil.offset(now, DateField.YEAR, -3);
                array[1] = DateUtil.offset(now, DateField.YEAR, -1);;
                break;
            case FOUR_AND_FIVE:
                array[0] = DateUtil.offset(now, DateField.YEAR, -5);
                array[1] = DateUtil.offset(now, DateField.YEAR, -3);;
                break;
            default:
                array[0] = null;
                array[1] = DateUtil.offset(now, DateField.YEAR, -5);
        }


        return array;
    }

    public static FactoryDateType getInstance(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        return Arrays.stream(FactoryDateType.values()).filter(v -> v.value.equals(value)).findFirst().orElse(null);
    }
}
