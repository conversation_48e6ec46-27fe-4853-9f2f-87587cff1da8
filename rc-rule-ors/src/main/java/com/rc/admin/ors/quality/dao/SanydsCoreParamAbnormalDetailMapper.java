package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.model.SanydsCoreParamAbnormalDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 核心工况异常明细表(SanydsCoreParamAbnormalDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-23 10:06:25
 */
public interface SanydsCoreParamAbnormalDetailMapper extends BaseMapper<SanydsCoreParamAbnormalDetail> {

    /**
     * 统计有上报异常的设备数量
     * @param date
     * @return
     */
    Integer countAbnormalDevice(@Param("bizDate") Date bizDate);
}

