package com.rc.admin.ors.quality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OtDeviceAllReportResp implements Serializable {

    @ExcelProperty("事业部")
    @ApiModelProperty(value = "事业部")
    private String buInnerDesc;

    @ExcelIgnore
    @ApiModelProperty(value = "事业部Code")
    private String syb;

    @ExcelProperty("产品组")
    @ApiModelProperty(value = "产品组")
    private String d365SpartDesc;

    @ExcelIgnore
    @ApiModelProperty(value = "产品组Code")
    private String d365Spart;


    @ExcelProperty("设备总数")
    @ApiModelProperty(value = "设备总数")
    private Integer totalDeviceCount;

    @ExcelProperty("D365设备数")
    @ApiModelProperty(value = "D365设备数")
    private Integer d365DeviceCount;

    @ExcelProperty("不在D365的新C设备数")
    @ApiModelProperty(value = "不在D365的新C设备数")
    private Integer newCNotInD365Count;

    @ExcelProperty("新C设备数")
    @ApiModelProperty(value = "新C设备数")
    private Integer newCDeviceCount;

    @ExcelProperty("不在新C的D365设备数")
    @ApiModelProperty(value = "不在新C的D365设备数")
    private Integer d365NotInNewCCount;

    @ExcelProperty("ML2.0设备数")
    @ApiModelProperty(value = "ML2.0设备数")
    private Integer ml2DeviceCount;

    @ExcelProperty("EVI设备数")
    @ApiModelProperty(value = "EVI设备数")
    private Integer eviDeviceCount;


    @ExcelProperty("无物联盒ID设备数")
    @ApiModelProperty(value = "无物联盒ID设备数")
    private Integer noTboxIdDeviceCount;


    @ExcelProperty("无物联盒ID新C实例数")
    @ApiModelProperty(value = "无物联ID新C实例数")
    private Integer noTboxIdNewCInstanceCount;


    @ExcelProperty("多物联盒设备数")
    @ApiModelProperty(value = "多物联盒设备数")
    private Integer multiTboxDeviceCount;
}
