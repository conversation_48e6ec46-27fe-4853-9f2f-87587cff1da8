package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 问题跟进处理环节负责人(QuestionStepUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-01 10:43:53
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("问题跟进处理环节负责人")
@TableName("ors_question_step_user")
public class QuestionStepUser extends Model<QuestionStepUser> {

    @TableId(type = IdType.AUTO)
    private Integer id;


    @ApiModelProperty(name = "stepCode", value = "处理环节编码")
    @TableField(value = "step_code")
    private String stepCode;

    @ApiModelProperty(name = "userAccount", value = "用户账户")
    @TableField(value = "user_account")
    private String userAccount;

    @ApiModelProperty(name = "userName", value = "用户姓名")
    @TableField(value = "user_name")
    private String userName;

    @ApiModelProperty(name = "divisionCode", value = "事业部编码")
    @TableField(value = "division_code")
    private String divisionCode;

    @TableField(exist = false)
    private String stepName;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

