package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class OrsMonthlyShipmentEquipmentReq implements Serializable {
    @ApiModelProperty(value = "导入文件")
    private MultipartFile file;
    @ApiModelProperty(value = "导入时间")
    private String time;
    @ApiModelProperty(value = "大区")
    private String name;
    @ApiModelProperty(value = "大区")
    private List<String> nameList;
    @ApiModelProperty(value = "产品组")
    private String agent;
    @ApiModelProperty(value = "产品组")
    private List<String> agentList;
    @ApiModelProperty(value = "事业部")
    private String sybbh;
    @ApiModelProperty(value = "事业部")
    private List<String> sybbhList;
    @ApiModelProperty(value = "页码")
    private int current;
    @ApiModelProperty(value = "条数")
    private int pageSize;


}
