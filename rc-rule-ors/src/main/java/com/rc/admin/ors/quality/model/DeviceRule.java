package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/24 9:52
 * @describe
 */
@Getter
@Setter
public class DeviceRule {

    @ApiModelProperty(name = "checkId", value = "剔除项唯一标识")
    private Long checkId;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    private String modelId;

    @ApiModelProperty(name = "property", value = "属性")
    private String property;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    private String propertyName;

    @ApiModelProperty(name = "checkRule", value = "检查规则, groove表达式")
    private String checkRule;

    @ApiModelProperty(name = "物标识", value = "实例ID")
    private String uuid;

    @ApiModelProperty(name = "excludeType", value = "WHOLE=整机，该设备不做任何数据质量检查，只要存在该项，其他剔除类型都失效；指定设备的某一项剔除")
    private String excludeType;

    private String excludeResean;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    private String deviceCode;

    private Long ruleId;

    @ApiModelProperty(value = "物标识")
    private String assetId;

    @ApiModelProperty(value = "物实例id")
    private String thingId;

    @ApiModelProperty(value = "设备协议")
    private String protocol;

    @ApiModelProperty(value = "设备描述")
    private String deviceDesc;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "设备类型")
    private String classId;

    @ApiModelProperty(value = "物模型描述")
    private String modelDesc;

    @ApiModelProperty(value = "物模型名称")
    private String modelName;

    @ApiModelProperty(value = "模型类型")
    private String thingType;

    @ApiModelProperty(value = "模型全路径")
    private String fullGroupName;

    @ApiModelProperty(value = "模型认证标识")
    private String modelKey;

    @ApiModelProperty(value = "事业部")
    private String orgName;

    @ApiModelProperty(value = "事业部编号")
    private String orgCode;

    @ApiModelProperty(value = "产品组")
    private String productGroup;

    @ApiModelProperty(value = "产品组编号")
    private String productGroupCode;

    @ApiModelProperty(value = "产品描述")
    private String shortText;

    @ApiModelProperty(value = "国家")
    private String zehdsvCountry;

    @ApiModelProperty(value = "大区")
    private String zehdsvReg;

    @ApiModelProperty(value = "国区编码")
    private String countryRegionCode;

    @ApiModelProperty(value = "国区")
    private String countryRegionName;

    @ApiModelProperty(value = "加入人员")
    private String createUser;

    @ApiModelProperty(value = "加入时间")
    private Date createTime;

    private String division;

    private Integer installType;

    private String noRegisterDeviceList;

    private String country;

    private String agentName;

    private String userName;

    private Date startTime;

    private Date endTime;

    private String storeCategory;


    @ApiModelProperty(value = "模型名称提示")
    private String promptStr;


    private String paramCode;

}
