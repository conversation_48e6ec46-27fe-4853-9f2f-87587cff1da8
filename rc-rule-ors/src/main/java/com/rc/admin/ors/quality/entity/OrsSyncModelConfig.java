package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 需要同步台账数据的物模型配置(OrsSyncModelConfig)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-01 19:14:09
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_sync_model_config", description = "需要同步台账数据的物模型配置")
public class OrsSyncModelConfig extends Model<OrsSyncModelConfig> {

    @TableId(type = IdType.AUTO)
    private String modelId;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.modelId;
    }
}

