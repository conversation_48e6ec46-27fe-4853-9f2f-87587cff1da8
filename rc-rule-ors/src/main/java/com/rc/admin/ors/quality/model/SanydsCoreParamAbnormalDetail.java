package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 核心工况异常明细表(SanydsCoreParamAbnormalDetail)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-27 14:19:07
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel( description = "核心工况异常明细表")
public class SanydsCoreParamAbnormalDetail {

    private String tenantId;

    private String modelId;

    private String modelName;

    private String thingId;

    private String deviceName;

    private Integer paramCode;

    private String paramDesc;

    private Integer abnormalCode;

    private String abnormalDesc;

    private Date abnormalTime;

    private Date statDate;


    @ApiModelProperty(name = "abnormalDetail", value = "异常明细数据")
    private String abnormalDetail;


}

