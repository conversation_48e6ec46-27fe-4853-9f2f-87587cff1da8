package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/10/24 9:52
 * @describe
 */
@Getter
@Setter
public class OrsDeviceCheckConfigExportExcel {

    @Excel(name = "序号", width = 15)
    @ExcelProperty("序号")
    @ColumnWidth(15)
    private Integer sort;

    @Excel(name = "事业部", width = 20)
    @ExcelProperty("事业部")
    @ColumnWidth(20)
    private String orgName;

    @Excel(name = "产品组", width = 20)
    @ExcelProperty("产品组")
    @ColumnWidth(20)
    private String productGroup;

    //@Excel(name = "国区", width = 20)
    @ExcelIgnore
    private String countryRegionName;

    @Excel(name = "设备编号", width = 15)
    @ExcelProperty("设备编号")
    @ColumnWidth(15)
    private String deviceCode;

    @ExcelIgnore
    private String assetId;

    @Excel(name = "实例名称", width = 20)
    @ExcelProperty("实例名称")
    @ColumnWidth(20)
    private String deviceName;

    @Excel(name = "物模型ID", width = 20)
    @ExcelProperty("物模型ID")
    @ColumnWidth(20)
    private String modelId;

    @Excel(name = "物模型名称", width = 25)
    @ExcelProperty("物模型名称")
    @ColumnWidth(25)
    private String modelName;

    @Excel(name = "剔除类型", width = 15)
    @ExcelProperty("剔除类型")
    @ColumnWidth(15)
    private String propertyName;

    @Excel(name = "剔除原因", width = 25)
    @ExcelProperty("剔除原因")
    @ColumnWidth(25)
    private String excludeResean;

    @Excel(name = "加入人员", width = 15)
    @ExcelProperty("加入人员")
    @ColumnWidth(15)
    private String createUser;

    @Excel(name = "加入时间", width = 20)
    @ExcelProperty("加入时间")
    @ColumnWidth(20)
    private String ctime;
}
