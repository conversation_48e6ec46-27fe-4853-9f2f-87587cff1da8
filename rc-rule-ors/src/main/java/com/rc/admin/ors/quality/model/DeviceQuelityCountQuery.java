package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/21 18:59
 * @describe
 */
@Getter
@Setter
public class DeviceQuelityCountQuery {

    @ApiModelProperty("年")
    private String year;

    @ApiModelProperty("月 例如：2023-08")
    private String month;

    @ApiModelProperty("开始日期")
    private String startTime;

    @ApiModelProperty("结束日期")
    private String endTime;

    @ApiModelProperty("事业部编号，多个用逗号隔开")
    private String divisiones;


    @ApiModelProperty("事业部编号")
    private String division;

    @ApiModelProperty("产品组编号，多个用逗号隔开")
    private String productCodes;

    @ApiModelProperty("大区编号，多个用逗号隔开")
    private String regionCode;

    @ApiModelProperty("需要统计的数据类型 ALL=完整性 QUALITY=准确性")
    private String dataType;

    private String abnormalName;

    @ApiModelProperty("切换标志 1.用老版本")
    private String switchSign;

    @ApiModelProperty("双率标志 1.集团 2、mysany -不传值就是查询所有")
    private String doubleRateSign;

    @ApiModelProperty("规则: 1、1.0规则 2、2.0规则")
    private String rulesSign;

    private Integer newMethod;

    private String serialNum;

    private String sign;

    @ApiModelProperty("物联盒类型 ALL=全部 SG=树根 HX=华兴")
    private String iotBoxType;

    @ApiModelProperty("查询分类 1.事业部 2.大区")
    private String queryClassification;


    @ApiModelProperty("异常 1.准确性异常 2.完整性异常")
    private String abnormalType;


    @ApiModelProperty("物模型id")
    private String  modelId;


    public Integer getNewMethod() {
        return newMethod;
    }

    public void setNewMethod(Integer newMethod) {
        this.newMethod = newMethod;
    }

    public List<String> getDivisiones() {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(divisiones)) {
            list = Arrays.asList(divisiones.split(","));
        }
        return list;
    }

    public List<String> getProductCodes() {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(productCodes)) {
            list = Arrays.asList(productCodes.split(","));
        }
        return list;
    }

    public List<String> getRegionCode() {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(regionCode)) {
            list = Arrays.asList(regionCode.split(","));
        }
        return list;
    }
}
