package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.*;

import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
public interface OrsBasicDataService {

    /**
     * 国家基础数据列表
     * @return 国家list
     */
    List<OrsBasicData> countryList();

    /**
     * 事业部基础数据列表
     * @return  事业部list
     */
    List<OrsBasicData> divisionList();

    /**
     * 大区基础数据列表
     * @return  大区list
     */
    List<OrsBasicData> regionList();

    /**
     * 新大区基础数据列表
     * @return  大区list
     */
    List<OrsBasicData> newRegionList();

    /**
     * 产品组基础数据列表
     * @return  产品组list
     */
    List<OrsBasicData> productGroupList();

    /**
     * 国家基础数据列表
     * @return 国家list
     */
    List<OrsBasicData> newCountryList();
    List<OrsBasicData> newCountryDeviceList();

    /**
     * 国区基础数据列表
     * @return 国区list
     */
    List<OrsBasicData> newCountryRegionList();


    /**
     * 事业部基础数据列表
     * @return  事业部list
     */
    List<OrsBasicData> newDivisionList();
    List<OrsBasicData> newDivisionDeviceList();


    List<OrsBasicData> getUserList();

    /**
     * 产品组基础数据列表
     * @return  产品组list
     */
    List<OrsBasicData> newProductGroupList(String divisionCode);
    List<OrsBasicData> newProductGroupDeviceList(String syb);

    /**
     * 查询所有的物实例
     * @return  物实例列表
     */
    Page<String> assetIdList(String assetId, Page<?> page);

    /**
     * 查询所有的实例名称
     * @return  实例名称列表
     */
    Page<String> nameList(String thingName, Page<?> page);

    /**
     * 查询所有的物模型名称
     * @return  物模型名称列表
     */
    List<String> modelNameList(String divisionCode,String productGroupCode,String key);


    List<String> modelNameListNew(String divisionCode,String productGroupCode);

    /**
     * 查询所有的设备编号
     * @return  设备编号列表
     */
    List<OrsDeviceListResp> deviceNoList(String deviceNo, Integer size);

    /**
     * 通过物模型Id查询所有属性
     * @param modelId   物模型Id
     * @param current   当前页
     * @param pageSize  每页显示数
     * @return  属性List
     */
    Page<OrsModelPropertiesConfig> findModelPropertiesByModelId(String modelId, Integer current, Integer pageSize);


    /**
     * 数据完整性统计
     * @param query
     * @return
     */
    List<DeviceQuelityCountDTO> countDeviceQuelityByAll(DeviceQuelityCountQuery query);

    /**
     * 数据准确性统计
     * @param query
     * @return
     */
    List<DeviceQuelityCountDTO> countDeviceQuelityByQuelity(DeviceQuelityCountQuery query);

    /**
     * 数据质量月报接口
     * @param req
     * @return
     */
    List<DeviceCountModel> getDataQualityReport(DataQualityReportReq req);



    /**
     * 数据质量月报接口-new
     * @param req
     * @return
     */
    List<DeviceCountModelNew> getDataQualityReportNew(DataQualityReportReq req);



    /**
     * 设备准确率和完整率计算处理
     * @param bizDate
     */
    void insertDeviceRateDay(Date bizDate);

    /**
     * 查询所有的根云物实例
     * @return  物实例列表
     */
    Page<String> rcAssetIdList(String rcAssetId, Page<?> page);
}
