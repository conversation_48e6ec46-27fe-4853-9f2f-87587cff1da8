<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsDeviceLocationMapper">

    <select id="selectList" resultType="com.rc.admin.ors.quality.model.OrsDeviceLocation">
        select asset_id,stat_date,longitude, latitude,device_location,data_time,create_time
        from dqm.ors_device_location where stat_date = #{statDate};
    </select>

    <update id="updateBatch"  parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update dqm.ors_device_location
            <set>
                device_location = #{item.deviceLocation}
            </set>
            where asset_id = #{item.assetId} and stat_date = #{item.statDate}
        </foreach>
    </update>
</mapper>
