package com.rc.admin.ors.quality.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.SanydsCoreParamStatLatestMapper;
import com.rc.admin.ors.quality.entity.SanydsCoreParamStatLatest;
import com.rc.admin.ors.quality.service.SanydsCoreParamStatLatestService;
import org.springframework.stereotype.Service;

/**
 * 设备数据异常明细(OrsDeviceDataAbnormalDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-27 14:51:55
 */
@Service
@DS("sany_data_service")
public class SanydsCoreParamStatLatestServiceImpl extends ServiceImpl<SanydsCoreParamStatLatestMapper, SanydsCoreParamStatLatest> implements
    SanydsCoreParamStatLatestService {

}

