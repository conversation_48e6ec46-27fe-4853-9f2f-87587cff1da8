package com.rc.admin.ors.quality.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("数据质量月度报表出参")
public class DataQualityReportResp {


    @ApiModelProperty("事业部/大区")
    @Excel(name = "事业部/大区", width = 10, orderNum = "0")
    private String divisionRegion;
    @ApiModelProperty("事业部名称")
    private String divisionRegiondesc;
    @ApiModelProperty("产品组编号")
    private String zehdSpart;
    @ApiModelProperty("产品组名称")
    @Excel(name = "产品组名称", width = 10, orderNum = "1")
    private String zehdSpartdesc;
    @ApiModelProperty("设备台数")
    @Excel(name = "设备台数", width = 10, orderNum = "2")
    private long deviceCount;
    @ApiModelProperty("注册台数")
    @Excel(name = "注册台数", width = 10, orderNum = "3")
    private long registerCount;
    @ApiModelProperty("未注册台数")
    @Excel(name = "未注册台数", width = 10, orderNum = "4")
    private long noRegisterCount;
    @ApiModelProperty("未注册设备集合")
    private List<String> noRegisterDeviceList;
    @ApiModelProperty("未注册设备")
    private String noRegisterDeviceKey;
    @ApiModelProperty("注册率")
    @Excel(name = "注册率", width = 10, orderNum = "5")
    private double registerRate;
    @ApiModelProperty("激活台数")
    @Excel(name = "激活台数", width = 10, orderNum = "6")
    private long activeCount;
    @ApiModelProperty("设备剔除数")
    @Excel(name = "设备剔除数", width = 10, orderNum = "7")
    private long deviceEliminateCount;
    @ApiModelProperty("检查项 ，异常设备数量，属性剔除设备数，检查项准确率")
    @Excel(name = "检查项 ，异常设备数量，属性剔除设备数，检查项准确率", width = 10, orderNum = "8")
    private Map<String,Map<String,Object>> combinationProperties;
    @ApiModelProperty("总异常设备数")
    @Excel(name = "总异常设备数", width = 10, orderNum = "9")
    private long abnormalCount;
    @ApiModelProperty("总异常设备集合")
    private List<String> abnormalDeviceList;
    @ApiModelProperty("总异常设备")
    private String abnormalDeviceKey;
    @ApiModelProperty("总无异常率")
    @Excel(name = "总无异常率", width = 10, orderNum = "10")
    private double noneAbnormalCount;
    @ApiModelProperty("检查项 ，异常设备数量，属性剔除设备数，检查项准确率")
    private String inspection;
    private int exceptions;
    private int exclusion;
    private double accuracy;

}
