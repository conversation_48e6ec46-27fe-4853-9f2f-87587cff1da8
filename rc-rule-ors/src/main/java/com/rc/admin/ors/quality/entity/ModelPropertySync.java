package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 物模型属性同步表(ModelPropertySync)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:09:27
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("物模型属性同步表")
@TableName("ors_iot_model_property_sync")
public class ModelPropertySync extends Model<ModelPropertySync> {


    @ApiModelProperty(name = "name", value = "属性名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "displayName", value = "属性显示名称")
    @TableField(value = "display_name")
    private String displayName;

    @ApiModelProperty(name = "propertyType", value = "属性类型")
    @TableField(value = "property_type")
    private String propertyType;

    @ApiModelProperty(name = "persistStrategy", value = "属性存储策略")
    @TableField(value = "persist_strategy")
    private String persistStrategy;

    @ApiModelProperty(name = "period", value = "属性存储周期")
    @TableField(value = "period")
    private Integer period;

    @ApiModelProperty(name = "privilege", value = "属性读写权限")
    @TableField(value = "privilege")
    private String privilege;

    @ApiModelProperty(name = "fixed", value = "属性为Number类型时的精度")
    @TableField(value = "fixed")
    private Integer fixed;

    @ApiModelProperty(name = "createdAt", value = "属性创建时间")
    @TableField(value = "created_at")
    private Date createdAt;

    @ApiModelProperty(name = "expressionType", value = "表达式类型")
    @TableField(value = "expression_type")
    private String expressionType;

    @ApiModelProperty(name = "expression", value = "表达式")
    @TableField(value = "expression")
    private String expression;

    @ApiModelProperty(name = "fromProperty", value = "来源属性")
    @TableField(value = "from_property")
    private String fromProperty;

    @ApiModelProperty(name = "windowSizeMills", value = "window窗口大小")
    @TableField(value = "window_size_mills")
    private Integer windowSizeMills;

    @ApiModelProperty(name = "windowStepMills", value = "window窗口滑动步长")
    @TableField(value = "window_step_mills")
    private Integer windowStepMills;

    @ApiModelProperty(name = "windowAllowedLatenessMills", value = "允许等待时间，单位毫秒")
    @TableField(value = "window_allowed_lateness_mills")
    private Integer windowAllowedLatenessMills;

    @ApiModelProperty(name = "priority", value = "表达式优先级，为大于等于零的整数")
    @TableField(value = "priority")
    private Integer priority;

    @ApiModelProperty(name = "executeAfterWindow", value = "表达式是否在window后计算")
    @TableField(value = "execute_after_window")
    private Boolean executeAfterWindow;

    @ApiModelProperty(name = "triggerStrategy", value = "表达式触发规则")
    @TableField(value = "trigger_strategy")
    private String triggerStrategy;

    @ApiModelProperty(name = "useOnlineMock", value = "是否计算online工况")
    @TableField(value = "use_online_mock")
    private Boolean useOnlineMock;

    @ApiModelProperty(name = "invokeMode", value = "表达式执行模式")
    @TableField(value = "invoke_mode")
    private String invokeMode;

    @ApiModelProperty(name = "skipOutput", value = "是否跳过属性输出")
    @TableField(value = "skip_output")
    private Boolean skipOutput;

    @ApiModelProperty(name = "disabled", value = "是否禁用")
    @TableField(value = "disabled")
    private Boolean disabled;

    @ApiModelProperty(name = "modelId", value = "物模型id")
    @TableField(value = "model_id")
    private String modelId;

}

