package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.BaseDeviceInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备台账(BaseDeviceInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-28 09:30:57
 */
public interface BaseDeviceInfoMapper extends BaseMapper<BaseDeviceInfo> {


    @Select("SELECT * FROM dqm.ors_base_device_info WHERE model_id IN (SELECT DISTINCT model_id FROM dqm.ors_double_rate_config WHERE double_rate_sign='4')")
    List<BaseDeviceInfo> selectBaseDeviceInfo();
}

