package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName(value = "orc_task_file")
public class OrcTaskFile {
    @TableId(value = "task_id", type = IdType.AUTO)
    private Integer taskId;

    /**
     * 执行sql
     */
    @TableField(value = "exec_sql")
    private String execSql;

    /**
     * 执行的错误信息
     */
    @TableField(value = "exec_error")
    private String execError;

    /**
     * 文件类型
     */
    @TableField(value = "result_file_type")
    private String resultFileType;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 执行的结果文件bit
     */
    @TableField(value = "result_file")
    private Object resultFile;



    /**
     * 文件大小
     */
    @TableField(value = "result_size")
    private Double resultSize;

    /**
     * 任务状态
     */
    @TableField(value = "task_status")
    private Integer taskStatus;
}