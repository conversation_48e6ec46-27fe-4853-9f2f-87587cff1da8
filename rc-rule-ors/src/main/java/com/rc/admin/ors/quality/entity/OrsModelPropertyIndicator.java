package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 物模型属性对应的指标(OrsModelPropertyIndicator)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-30 14:10:43
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_model_property_indicator", description = "物模型属性对应的指标")
public class OrsModelPropertyIndicator extends Model<OrsModelPropertyIndicator> {

    @TableId(type = IdType.AUTO)
    private Long modelPropertyId;

    private Long indicatorId;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.modelPropertyId;
    }
}

