<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsMonthlyShipmentEquipmentMapper">

    <select id="getDeviceInfo" resultType="com.rc.admin.ors.quality.model.OrsMonthlyShipmentEquipmentResp">
        select
            mse.device_name as deviceName
             ,mse.region as region
             ,mse.region as name
             ,mse.product_group as agent
             ,mse.division as sybbh
             ,mse.import_time as time
        from ors_monthly_shipment_equipment as mse
            where mse.import_time = #{req.time}
        <if test="req.name != null and req.name != ''">
            AND mse.region = #{req.name}
        </if>
        <if test="req.nameList != null and req.nameList != ''">
            AND mse.region in
            <foreach collection='req.nameList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.agent != null and req.agent != ''">
            AND mse.product_group = #{req.agent}
        </if>
        <if test="req.agentList != null and req.agentList != ''">
            AND mse.product_group in
            <foreach collection='req.agentList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.sybbh != null and req.sybbh != ''">
            AND mse.division = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbh != ''">
            AND mse.division in
            <foreach collection='req.sybbhList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by mse.division ,mse.product_group,mse.region
        limit #{req.pageSize} OFFSET #{req.current}
    </select>
    <select id="getDeviceInfoCount" resultType="java.lang.Integer">
        select
            count(1)
        from ors_monthly_shipment_equipment as mse

            where mse.import_time = #{req.time}
        <if test="req.name != null and req.name != ''">
            AND mse.region = #{req.name}
        </if>
        <if test="req.nameList != null and req.nameList != ''">
            AND mse.region in
            <foreach collection='req.nameList' item='item' separator=',' open="(" close=")">
                    #{item}
            </foreach>
        </if>
        <if test="req.agent != null and req.agent != ''">
            AND mse.product_group = #{req.agent}
        </if>
        <if test="req.agentList != null and req.agentList != ''">
            AND mse.product_group in
            <foreach collection='req.agentList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.sybbh != null and req.sybbh != ''">
            AND mse.division = #{req.sybbh}
        </if>
        <if test="req.sybbhList != null and req.sybbh != ''">
            AND mse.division in
            <foreach collection='req.sybbhList' item='item' separator=',' open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <delete id="deleteDeviceInfo">
        delete from ors_monthly_shipment_equipment where import_time = #{req.time}
    </delete>

    <select id="getParamList" resultType="java.lang.String">
        select distinct ${paramType}
        from
        ors_monthly_shipment_equipment t
        where t.import_time = #{time} and ${paramType} is not null and ${paramType} !=''
        <if test="paramValue !=null and paramValue !=''">
            and ${paramType} like concat('%',#{paramValue},'%')
        </if>
    </select>

</mapper>
