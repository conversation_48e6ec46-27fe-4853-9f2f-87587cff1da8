package com.rc.admin.ors.quality.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ors_bigdata_equipment_base_info_all")
@ApiModel(value = "ors_bigdata_equipment_base_info_all对象", description = "海外设备信息全量表")
public class OrsBigdataEquipmentBaseInfoAll extends Model<OrsBigdataEquipmentBaseInfoAll> {

    @TableField(value = "product_id")
    @ApiModelProperty(value = "产品ID")
    private String productId;
    @TableField(value = "short_text")
    @ApiModelProperty(value = "产品描述")
    private String shortText;
    @TableField(value = "zehd_spart")
    @ApiModelProperty(value = "产品组")
    private String zehdSpart;
    @TableField(value = "zehd_spartdesc")
    @ApiModelProperty(value = "产品组名称")
    private String zehdSpartdesc;
    @TableField(value = "zehd_sblx")
    @ApiModelProperty(value = "设备类型")
    private String zehdSblx;
    @TableField(value = "zejqvo")
    @ApiModelProperty(value = "型号（机型）")
    private String zejqvo;
    @TableField(value = "zeexdate")
    @ApiModelProperty(value = " 出厂日期")
    private Date zeexdate;
    @TableField(value = "zebxjzdate")
    @ApiModelProperty(value = "主机保修截止日期")
    private Date zebxjzdate;
    @TableField(value = "zejjdate")
    @ApiModelProperty(value = "交机日期")
    private Date zejjdate;
    @TableField(value = "zekjdate")
    @ApiModelProperty(value = "开机日期")
    private Date zekjdate;
    @TableField(value = "zezbsta")
    @ApiModelProperty(value = "质保状态")
    private String zezbsta;
    @TableField(value = "sybbh")
    @ApiModelProperty(value = "事业部编号")
    private String sybbh;
    @TableField(value = "sybbh_desc")
    @ApiModelProperty(value = "事业部描述")
    private String sybbhDesc;
    @TableField(value = "agent")
    @ApiModelProperty(value = "所属代理商")
    private String agent;
    @TableField(value = "agent_desc")
    @ApiModelProperty(value = "所属代理商名称")
    private String agentDesc;
    @TableField(value = "srvagt")
    @ApiModelProperty(value = "服务代理商")
    private String srvagt;
    @TableField(value = "zsrv_agtdesc")
    @ApiModelProperty(value = "服务代理商名称")
    private String zsrvAgtdesc;
    @TableField(value = "sjgmr")
    @ApiModelProperty(value = "实际购买人编号")
    private String sjgmr;
    @TableField(value = "sjgmrmc")
    @ApiModelProperty(value = "实际购买人名称")
    private String sjgmrmc;
    @TableField(value = "sjgmr_tel")
    @ApiModelProperty(value = "实际购买人联系电话")
    private String sjgmrTel;
    @TableField(value = "sjgmr_zzorgid")
    @ApiModelProperty(value = "组织机构")
    private String sjgmrZzorgid;
    @TableField(value = "mygmr")
    @ApiModelProperty(value = "名义购买人")
    private String mygmr;
    @TableField(value = "mygmrmc")
    @ApiModelProperty(value = "名义购买人名称")
    private String mygmrmc;
    @TableField(value = "mygmr_tel")
    @ApiModelProperty(value = "名义购买人联系电话")
    private String mygmrTel;
    @TableField(value = "mygmr_zzorgid")
    @ApiModelProperty(value = "组织机构")
    private String mygmrZzorgid;
    @TableField(value = "zehd_division")
    @ApiModelProperty(value = "所属事业部")
    private String zehdDivision;
    @TableField(value = "zehdsv_country")
    @ApiModelProperty(value = "国家")
    private String zehdsvCountry;
    @TableField(value = "zehdsv_reg")
    @ApiModelProperty(value = "大区")
    private String zehdsvReg;
    @TableField(value = "zehdsv_country_desc")
    @ApiModelProperty(value = "单一数值描述")
    private String zehdsvCountryDesc;
    @TableField(value = "zehdsv_reg_desc")
    @ApiModelProperty(value = "单一数值描述")
    private String zehdsvRegDesc;
    @TableField(value = "zelx_sblxr")
    @ApiModelProperty(value = "设备联系人")
    private String zelxSblxr;
    @TableField(value = "zelx_sblxrtl")
    @ApiModelProperty(value = "设备联系人电话")
    private String zelxSblxrtl;
    @TableField(value = "zcjno")
    @ApiModelProperty(value = "车架号")
    private String zcjno;
    @TableField(value = "zfdjno")
    @ApiModelProperty(value = "发动机编号")
    private String zfdjno;
    @TableField(value = "srveng")
    @ApiModelProperty(value = "服务工程师")
    private String srveng;
    @TableField(value = "srveng_des")
    @ApiModelProperty(value = "服务工程师名称")
    private String srvengDes;
    @TableField(value = "yxdb")
    @ApiModelProperty(value = "营销代表编号")
    private String yxdb;
    @TableField(value = "yxdbmc")
    @ApiModelProperty(value = "营销代表名称")
    private String yxdbmc;
    @TableField(value = "zztotalusagetim")
    @ApiModelProperty(value = "累计运行时间（小时）")
    private String zztotalusagetim;
    @TableField(value = "zelx_sbzg")
    @ApiModelProperty(value = "设备主管")
    private String zelxSbzg;
    @TableField(value = "zelx_sbzgtel")
    @ApiModelProperty(value = "设备主管联系电话")
    private String zelxSbzgtel;
    @TableField(value = "zehd_ifsoleduty")
    @ApiModelProperty(value = "未支付仍可召请标识")
    private String zehdIfsoleduty;
    @TableField(value = "zz0022")
    @ApiModelProperty(value = "配置需求")
    private String zz0022;
    @TableField(value = "zsrvoifsoleduty")
    @ApiModelProperty(value = "专属服务类型")
    private String zsrvoifsoleduty;
    @TableField(value = "resp_srveng")
    @ApiModelProperty(value = "责任工程师")
    private String respSrveng;
    @TableField(value = "resp_srveng_des")
    @ApiModelProperty(value = "责任工程师名称")
    private String respSrvengDes;
    @TableField(value = "zz0013")
    @ApiModelProperty(value = "设备状态")
    private String zz0013;
    @TableField(value = "zz0013_des")
    @ApiModelProperty(value = "文本，40个字符长")
    private String zz0013Des;
    @TableField(value = "zz0014")
    @ApiModelProperty(value = "设备使用状态")
    private String zz0014;
    @TableField(value = "zz0014_des")
    @ApiModelProperty(value = " 文本，40个字符长")
    private String zz0014Des;
    @TableField(value = "zz0017")
    @ApiModelProperty(value = "设备异常状态")
    private String zz0017;
    @TableField(value = "zz0017_des")
    @ApiModelProperty(value = "文本，40个字符长")
    private String zz0017Des;
    @TableField(value = "sbdw")
    @ApiModelProperty(value = "吨位")
    private String sbdw;
    @TableField(value = "sbdw_type")
    @ApiModelProperty(value = " 吨位种类，当前仅起重机和履带吊有值")
    private String sbdwType;
    @TableField(value = "equip_type")
    @ApiModelProperty(value = "设备分类，空或0表示全系列；汽车起重机，按吨位分类：1:[0,16)，2:[16,50)，3:[50,100)，4:[100,220)，5:[220,)；履带起重机，按吨位分类：1:[0,250)，2:[250,630)，3:[630,)；强夯机，按机型分类：1:SQH60，2:SQH60之外；消防车，表示其产品组次级型号")
    private String equipType;
    @TableField(value = "sybbh_crm")
    @ApiModelProperty(value = " 事业部编号（CRM原始值）")
    private String sybbhCrm;
    @TableField(value = "sybbh_crm_desc")
    @ApiModelProperty(value = "事业部描述（CRM原始值）")
    private String sybbhCrmDesc;
    @TableField(value = "zcpno")
    @ApiModelProperty(value = "车牌号")
    private String zcpno;
    @TableField(value = "zif_vipcus_eqp")
    @ApiModelProperty(value = "大客户设备标记")
    private String zifVipcusEqp;
    @TableField(value = "zif_zcfw")
    @ApiModelProperty(value = "驻矿服务标记")
    private String zifZcfw;
    @TableField(value = "zif_fee_srv")
    @ApiModelProperty(value = "是否有偿服务")
    private String zifFeeSrv;
    @TableField(value = "zif_tfgc")
    @ApiModelProperty(value = "是否特服跟车")
    private String zifTfgc;
    @TableField(value = "zsbrsta")
    @ApiModelProperty(value = "设备运行状态")
    private String zsbrsta;
    @TableField(value = "zsbrsta_des")
    @ApiModelProperty(value = "设备运行状态描述")
    private String zsbrstaDes;
    @TableField(value = "bxzc")
    @ApiModelProperty(value = "保修政策")
    private int bxzc;
    @TableField(value = "zzcategory_id")
    @ApiModelProperty(value = "产品组次级类别ID")
    private String zzcategoryId;
    @TableField(value = "srvsite")
    @ApiModelProperty(value = "服务网点")
    private String srvsite;
    @TableField(value = "srvsite_des")
    @ApiModelProperty(value = "服务网点描述")
    private String srvsiteDes;
    @TableField(value = "zsblev")
    @ApiModelProperty(value = "客户等级")
    private String zsblev;
    @TableField(value = "zsblev_des")
    @ApiModelProperty(value = "客户等级描述")
    private String zsblevDes;
    @TableField(value = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    @TableField(value = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}
