package com.rc.admin.ors.quality.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:19
 * @describe
 */
@Getter
@Setter
public class DeviceStatuResp {

    /**
     * 设备物标识
     */
    private String assetId;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 在线状态，true表示在线， false表示离线
     */
    private Boolean online;

    /**
     * 物实例ID
     */
    private String thingId;

    /**
     * 工作状态
     */
    private String workingStatus;

    /**
     * 经度
     */
    private double longitude;

    /**
     * 纬度
     */
    private double latitude;

    /**
     * 国家
     */
    private String country;

    /**
     * 市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String street;

    /**
     * 省份
     */
    private String state;


    /**
     * 首次上数时间，这里认为是激活时间
     */
    private Long firstDataTime;

    /**
     * 最近上数时间
     */
    private Long lastActivityTime;


    public Date getFirstDataTime() {
        if (null == firstDataTime) {
            return null;
        }
        Date utcDate = new Date(firstDataTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(utcDate);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
        return calendar.getTime();
    }

    public Date getLastActivityTime() {
        if (null == lastActivityTime) {
            return null;
        }
        Date utcDate = new Date(lastActivityTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(utcDate);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
        return calendar.getTime();
    }
}
