package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 设备台账(OrsBaseDeviceInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-21 11:15:49
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("核心工况属性定义")
@TableName("sanyds_core_param_def")
public class SanydsCoreParamDef extends Model<SanydsCoreParamDef> {


    @TableField(value = "tenant_id")
    private String tenantId;

    @TableField(value = "model_id")
    private String modelId;

    @TableField(value = "param_code")
    private Integer paramCode;

    @TableField(value = "param_name_en")
    private String paramNameEn;

    @TableField(value = "param_name_cn")
    private String paramNameCn;

    @TableField(value = "param_unit")
    private String paramUnit;

    @ApiModelProperty(name = "productGroup", value = "产品组")
    @TableField(value = "min_value")
    private Double minValue;

    @TableField(value = "max_value")
    private Double maxValue;

    @ApiModelProperty(name = "country", value = "国家")
    @TableField(value = "min_value_daily")
    private Double minValueDaily;

    @ApiModelProperty(name = "factoryDate", value = "出厂日期")
    @TableField(value = "max_value_daily")
    private Double maxValueDaily;

    @ApiModelProperty(name = "crmRegister", value = "crm注册状态")
    @TableField(value = "param_name_model")
    private String paramNameModel;

    @ApiModelProperty(name = "installType", value = "安装类型")
    @TableField(value = "is_need_unit_convert")
    private Boolean isNeedUnitConvert;

    @ApiModelProperty(name = "hwVersion", value = "设备硬件版本号")
    @TableField(value = "convert_coefficient")
    private String convertCoefficient;

    @ApiModelProperty(name = "authToken", value = "设备连接MQTT Broker时的密码")
    @TableField(value = "convert_offset")
    private String convertOffset;

    @ApiModelProperty(name = "created", value = "时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "etlTime", value = "数据入库时间")
    @TableField(value = "update_time")
    private Date updateTime;


}

