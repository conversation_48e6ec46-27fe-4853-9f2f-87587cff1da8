package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("数据质量月度报表入参")
public class DataQualityReportReq implements Serializable {

    @ApiModelProperty("事业部/大区")
    private String sybbh;
    @ApiModelProperty("事业部")
    private String divisionRegion;
    @ApiModelProperty("事业部")
    private String divisionRegiondesc;
    @ApiModelProperty(value = "事业部/大区入参集合")
    private List<String> divisionRegiondescList;
    @ApiModelProperty("设备编号")
    private String deviceName;
    @ApiModelProperty("产品组")
    private String zehdSpart;
    @ApiModelProperty("产品组名称")
    private String zehdSpartdesc;
    @ApiModelProperty("模型ids")
    private String modelIds;
    @ApiModelProperty(value = "产品组名称集合")
    private List<String> zehdSpartdescs;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("大区")
    private String region;
    @ApiModelProperty("国家")
    private String country;
    @ApiModelProperty("国区")
    private List<String> countryRegion;
    @ApiModelProperty("代理商")
    private String agentName;
    @ApiModelProperty("客户")
    private String userName;
    @ApiModelProperty("存量分类")
    private String storeCategory;
    @ApiModelProperty(value = "检查项类型")
    private String dictTypeName;
    @ApiModelProperty("安装分类")
    private String installType;
    @ApiModelProperty("安装分类")
    private int installTypes;
    @ApiModelProperty(value = "数据检查日期开始时间")
    private Date createTime_start;
    @ApiModelProperty(value = "数据检查日期结束时间")
    private Date createTime_end;
    private String mark;

    @ApiModelProperty("不统计华兴设备 0不统计,1统计")
    private String hasHuaXin;
    @ApiModelProperty(value = "华兴模型ID集合")
    private List<String> huaXinModelIdList;

    @ApiModelProperty("切换标志 1.用老版本(按照设备来处理)")
    private String switchSign;

    @ApiModelProperty(value = "异常项")
    private String abnormalName;

    private String inspection;
    @ApiModelProperty(value = "异常项")
    private List<Integer> list;

    private List<String> devices;


    private String sign;
}
