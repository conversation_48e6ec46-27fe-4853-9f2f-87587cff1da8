package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.QuestionLogMapper;
import com.rc.admin.ors.quality.entity.QuestionLog;
import com.rc.admin.ors.quality.service.QuestionLogService;
import org.springframework.stereotype.Service;

/**
 * 问题处理结果/日志(QuestionLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-01 10:43:52
 */
@Service("questionLogService")
public class QuestionLogServiceImpl extends ServiceImpl<QuestionLogMapper, QuestionLog> implements QuestionLogService {

}

