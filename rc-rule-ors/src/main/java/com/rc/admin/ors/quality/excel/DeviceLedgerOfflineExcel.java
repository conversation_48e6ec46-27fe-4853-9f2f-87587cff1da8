package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class DeviceLedgerOfflineExcel {

    @ExcelProperty("序号")
    @ColumnWidth(10)
    private int sort;

    @ExcelProperty("事业部名称")
    @ColumnWidth(15)
    private String newDivisionName;

    @ExcelProperty("产品组名称")
    @ColumnWidth(15)
    private String newProductGroupName;

    @ExcelProperty("数据中心")
    @ColumnWidth(20)
    private String dataCenter;

    @ExcelProperty("设备编号")
    @ColumnWidth(15)
    private String deviceNo;

    @ExcelIgnore
    private String assetId;

    @ExcelProperty("实例名称")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty("设备状态")
    @ColumnWidth(15)
    private String deviceStatus;

    @ExcelProperty("物模型名称")
    @ColumnWidth(20)
    private String modelName;

    @ExcelProperty("新C注册")
    @ColumnWidth(20)
    private String ctime;

    @ExcelProperty("离线时间")
    @ColumnWidth(20)
    private String offlineTime;

    @ExcelProperty("离线时长")
    @ColumnWidth(20)
    private String offlineTimeStr;

    @ExcelProperty("离线持续时间")
    @ColumnWidth(15)
    private String offlineDays;

    @ExcelProperty("国家")
    @ColumnWidth(15)
    private String country;


}
