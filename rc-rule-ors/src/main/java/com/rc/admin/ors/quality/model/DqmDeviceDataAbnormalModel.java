package com.rc.admin.ors.quality.model;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class DqmDeviceDataAbnormalModel {
    private String abnormalCode;
    private String abnormalCount;
    private String abnormalName;
    private String deviceLocationCnt;

    public String getAbnormalCount() {
        return abnormalCount;
    }

    public void setAbnormalCount(String abnormalCount) {
        this.abnormalCount = abnormalCount;
    }

    public String getAbnormalName() {
        if (StringUtils.isNotBlank(abnormalCode)){
            switch (abnormalCode) {
                case "9001":
                    abnormalName = "属性值异常";
                    break;
                case "9002":
                    abnormalName = "属性值超限";
                    break;
                case "9003":
                    abnormalName = "属性值日变化量异常";
                    break;
                case "9004":
                    abnormalName = "属性值逆增长";
                    break;
                case "9005":
                    abnormalName = "属性值跳变";
                    break;
                case "9006":
                    abnormalName = "属性值长期定值";
                    break;
                case "9007":
                    abnormalName = "位置漂移";
                    break;
                case "9008":
                    abnormalName = "属性值从未上报";
                    break;
                case "9009":
                    abnormalName = "属性值长期未上报";
                    break;
                case "9101":
                    abnormalName = "怠速时长变化怠速油耗未变化";
                    break;
                case "9102":
                    abnormalName = "怠速油耗变化怠速时长未变化";
                    break;
                case "9103":
                    abnormalName = "工作时间变化总油耗未变化";
                    break;
                case "9104":
                    abnormalName = "总油耗变化工作时长未变化";
                    break;
                case "9105":
                    abnormalName = "当日怠速油耗增长不合理";
                    break;
                case "9106":
                    abnormalName = "当日怠速时长增长不合理";
                    break;
                case "9107":
                    abnormalName = "当日里程变化无车速";
                    break;
                case "9108":
                    abnormalName = "当日有持续车速里程未变化";
                    break;
                case "9109":
                    abnormalName = "设备里程变化定位不变";
                    break;
                case "9100":
                    abnormalName = "关联工况变化不合理";
                    break;
                default:
                    break;
            }
        }
        return abnormalName;
    }

    public String getAbnormalNameAndCount() {
        return getAbnormalName() + "（" + abnormalCount + "）";
//        if("9007".equals(abnormalCode))
//        {
//            int aCnt = Integer.parseInt(abnormalCount);
//            int tCnt = Integer.parseInt(deviceLocationCnt);
//            if(tCnt == 0)
//            {
//                return getAbnormalName() + "（" + abnormalCount + "）,被除数为0";
//            }
//            BigDecimal numeratorBD = new BigDecimal(aCnt);
//            BigDecimal denominatorBD = new BigDecimal(tCnt);
//            BigDecimal result2 = numeratorBD.divide(denominatorBD, 4, BigDecimal.ROUND_HALF_UP); // 保留2位小数，使用四舍五入方式
////            float div =  aCnt / tCnt ;
//            String str = "";
//            if(result2.floatValue() <= 0.004)
//            {
//                str = "";
//            }
//            else {
//                str = getAbnormalName() + "（" + abnormalCount + "）";
//            }
//            return str;
//        }
//        else
//        {
//            return getAbnormalName() + "（" + abnormalCount + "）";
//        }
    }
}
