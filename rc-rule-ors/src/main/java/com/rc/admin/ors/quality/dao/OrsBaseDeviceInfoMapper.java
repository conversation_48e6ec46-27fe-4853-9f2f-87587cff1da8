package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.model.DeviceQuelityCountQuery;
import com.rc.admin.ors.quality.model.DeviceRatioReportEntryResp;
import com.rc.admin.ors.quality.model.DeviceRatioResp;
import com.rc.admin.ors.quality.model.TotalDeviceStatisticsResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备台账(OrsBaseDeviceInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-08 18:20:11
 */
public interface OrsBaseDeviceInfoMapper extends BaseMapper<OrsBaseDeviceInfo> {

    /**
     * 首页-设备总数统计
     * @return 设备总数统计
     */
    TotalDeviceStatisticsResp totalDeviceStatistics();

    /**
     * 首页-设备比例统计
     * @return 设备比例统计
     */

    DeviceRatioResp deviceRatio(@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("doubleRateSign")String doubleRateSign);
    DeviceRatioResp deviceRatioChange(@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("doubleRateSign")String doubleRateSign,@Param("rulesSign")String rulesSign);
    DeviceRatioResp deviceRatioChangeVersion(@Param("query")DeviceQuelityCountQuery deviceQuelityCountQuery);
    DeviceRatioResp deviceRatioNew(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 事业部数据准确率/完整率统计
     * @param deviceQuelityCountQuery 查询参数
     * @return 据准确率/完整率统计
     */
    List<DeviceRatioReportEntryResp> divisionStatisticsChange(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> divisionModel(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> divisionStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);

    List<DeviceRatioReportEntryResp> divisionStatisticsNew(DeviceQuelityCountQuery deviceQuelityCountQuery);

    /**
     * 事业部数据统计
     * @param deviceQuelityCountQuery 查询参数
     * @return 据准确率/完整率统计
     */
    List<DeviceRatioReportEntryResp> divisionCountStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> divisionCountStatisticsChange(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> divisionCountModel(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> divisionCountStatisticsNew(DeviceQuelityCountQuery deviceQuelityCountQuery);

    /**
     * 大区数据准确率/完整率统计
     * @param deviceQuelityCountQuery 查询参数
     * @return 据准确率/完整率统计
     */
    List<DeviceRatioReportEntryResp> regionStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> regionStatisticsChange(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> regionStatisticsNew(DeviceQuelityCountQuery deviceQuelityCountQuery);

    /**
     * 大区数据统计
     * @param deviceQuelityCountQuery 查询参数
     * @return
     */
    List<DeviceRatioReportEntryResp> regionCountStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> regionCountStatisticsChange(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> regionCountStatisticsNew(DeviceQuelityCountQuery deviceQuelityCountQuery);

    /**
     * 时间数据准确率/完整率统计
     * @param deviceQuelityCountQuery 查询参数
     * @return 据准确率/完整率统计
     */
    List<DeviceRatioReportEntryResp> dateStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);
    List<DeviceRatioReportEntryResp> dateStatisticsChange(DeviceQuelityCountQuery deviceQuelityCountQuery);

    List<DeviceRatioReportEntryResp> dateStatisticsNew(DeviceQuelityCountQuery deviceQuelityCountQuery);

    /**
     * 时间数据统计
     * @param deviceQuelityCountQuery 查询参数
     * @return 据准确率/完整率统计
     */
    List<DeviceRatioReportEntryResp> dateCountStatistics(DeviceQuelityCountQuery deviceQuelityCountQuery);

}

