package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 核心工况施工统计表-最新表
    */
@Data
@NoArgsConstructor
@TableName(value = "sanyds_core_param_stat_latest")
public class SanydsCoreParamStatLatest {
    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 模型ID
     */
    @TableField(value = "model_id")
    private String modelId;

    /**
     * 设备编码
     */
    @TableField(value = "device_name")
    private String deviceName;

    /**
     * 属性编码
     */
    @TableField(value = "param_code")
    private Integer paramCode;

    /**
     * 属性值
     */
    @TableField(value = "param_value")
    private BigDecimal paramValue;

    /**
     * 属性值异常标识：true异常
     */
    @TableField(value = "is_param_value_abnormal")
    private Boolean isParamValueAbnormal;

    /**
     * 属性值最早上报时间
     */
    @TableField(value = "param_value_earliest_time")
    private Date paramValueEarliestTime;

    /**
     * 属性值最新上报时间
     */
    @TableField(value = "param_value_latest_time")
    private Date paramValueLatestTime;

    /**
     * 数据生成时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}