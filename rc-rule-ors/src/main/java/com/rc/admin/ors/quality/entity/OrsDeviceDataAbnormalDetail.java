package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备数据异常明细(OrsDeviceDataAbnormalDetail)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-03 14:58:35
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_device_data_abnormal_detail", description = "设备数据异常明细")
public class OrsDeviceDataAbnormalDetail extends Model<OrsDeviceDataAbnormalDetail> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "uuid", value = "设备实例ID，thingid")
    @TableField(value = "uuid")
    private String uuid;

    @ApiModelProperty(name = "deviceCode", value = "设备编码，物标识")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "deviceName", value = "设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "modelName", value = "物模型名称")
    @TableField(value = "model_name")
    private String modelName;

    @ApiModelProperty(name = "abnormalName", value = "异常项")
    @TableField(value = "abnormal_name")
    private String abnormalName;

    @ApiModelProperty(name = "abnormalData", value = "异常数据")
    @TableField(value = "abnormal_data")
    private String abnormalData;

    @ApiModelProperty(name = "lastOnlineTime", value = "最近在线时间")
    @TableField(value = "last_online_time")
    private Date lastOnlineTime;

    @ApiModelProperty(name = "offlineDuration", value = "离线时长")
    @TableField(value = "offline_duration")
    private String offlineDuration;

    @ApiModelProperty(name = "lastLocation", value = "最近位置")
    @TableField(value = "last_location")
    private String lastLocation;

    @ApiModelProperty(name = "inventoryClassification", value = "存量分类 NEW=本月新增 OLD=存量")
    @TableField(value = "inventory_classification")
    private String inventoryClassification;

    @ApiModelProperty(name = "installClassification", value = "安装分类 FRONT=前装 BACK=后装")
    @TableField(value = "install_classification")
    private String installClassification;

    @ApiModelProperty(name = "createTime", value = "数据检查时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "property", value = "属性")
    @TableField(value = "property")
    private String property;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    @TableField(value = "property_name")
    private String propertyName;

    @ApiModelProperty(name = "tenantId", value = "租户ID")
    @TableField(value = "tenant_id")
    private String tenantId;

    @ApiModelProperty(name = "lastPv", value = "属性上一次的值")
    @TableField(value = "last_pv")
    private String lastPv;

    @ApiModelProperty(name = "lastPt", value = "属性上一次取值时间")
    @TableField(value = "last_pt")
    private String lastPt;

    @ApiModelProperty(name = "curPv", value = "属性当前值")
    @TableField(value = "cur_pv")
    private String curPv;

    @ApiModelProperty(name = "curPt", value = "属性当前取值时间")
    @TableField(value = "cur_pt")
    private String curPt;

    @ApiModelProperty(name = "pvInc", value = "属性差值")
    @TableField(value = "pv_inc")
    private String pvInc;

    @ApiModelProperty(name = "ptInc", value = "时间差")
    @TableField(value = "pt_inc")
    private String ptInc;

    @ApiModelProperty(name = "abnormalTime", value = "异常数据产生时间")
    @TableField(value = "abnormal_time")
    private Date abnormalTime;

    @ApiModelProperty(name = "paramCode", value = "参数编码，用于和底表对应")
    @TableField(value = "param_code")
    private Integer paramCode;

    @ApiModelProperty(name = "statDate", value = "异常数据生成日期")
    @TableField(value = "stat_date")
    private Date statDate;

    @ApiModelProperty(name = "detailId", value = "对应原始表的ID")
    @TableField(value = "detail_id")
    private String detailId;

    @TableField(value = "abnormal_effective")
    private Integer abnormal_effective;

    @TableField(value = "division_code")
    private String divisionCode;

    @TableField(value = "data_center_id")
    private Integer dataCenterId;

    @TableField(value = "raw_time")
    private Date raw_time;

    private Integer abnormalCode;

    private String rawData;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

