package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalStatDayMapper;
import com.rc.admin.ors.quality.entity.DeviceDataAbnormalStatDay;
import com.rc.admin.ors.quality.service.DeviceDataAbnormalStatDayService;
import org.springframework.stereotype.Service;

/**
 * 设备历史异常统计表(DeviceDataAbnormalStatDay)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-04 16:16:15
 */
@Service("deviceDataAbnormalStatDayService")
public class DeviceDataAbnormalStatDayServiceImpl extends ServiceImpl<DeviceDataAbnormalStatDayMapper, DeviceDataAbnormalStatDay> implements DeviceDataAbnormalStatDayService {

}

