package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/08/03
 */
@Setter
@Getter
@ApiModel("设备比率统计")
public class DeviceRatioResp {

    @ApiModelProperty("总完整率")
    private String totalIntegrityRate;

    @ApiModelProperty("树根物联盒完整率")
    private String sgIotBoxIntegrityRate;

    @ApiModelProperty("华兴物联盒完整率")
    private String hxIotBoxIntegrityRate;

    @ApiModelProperty("总准确率")
    private String totalAccuracy;

    @ApiModelProperty("树根物联盒准确率")
    private String sgIotBoxAccuracy;

    @ApiModelProperty("华兴物联盒准确率")
    private String hxIotBoxAccuracy;

    @ApiModelProperty("总上数台数")
    private String  totalDeviceCount;
}
