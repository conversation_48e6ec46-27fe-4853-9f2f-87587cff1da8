package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("dqm.ors_version_update_log_rate")
public class OrsVersionUpdateLogRate implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键，自增ID")
    private Integer id;

    @TableField("version_update_id")
    @ApiModelProperty(value = "版本更新记录id")
    private Integer versionUpdateId;

    @TableField("double_rate_sign")
    @ApiModelProperty(value = "双率标志")
    private String doubleRateSign;

    @TableField("integrity_rate_before")
    @ApiModelProperty(value = "完整率前")
    private String integrityRateBefore;

    @TableField("integrity_rate_after")
    @ApiModelProperty(value = "完整率后")
    private String integrityRateAfter;

    @TableField("accuracy_rate_before")
    @ApiModelProperty(value = "准确率前")
    private String accuracyRateBefore;

    @TableField("accuracy_rate_after")
    @ApiModelProperty(value = "准确率后")
    private String accuracyRateAfter;
}
