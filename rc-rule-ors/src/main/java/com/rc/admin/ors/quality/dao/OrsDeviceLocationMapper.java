package com.rc.admin.ors.quality.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.ors.quality.entity.OrsMonthlyShipmentEquipment;
import com.rc.admin.ors.quality.model.OrsDeviceLocation;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrsDeviceLocationMapper extends BaseMapper<OrsDeviceLocation> {


    List<OrsDeviceLocation> selectList(String statDate);

    void updateBatch(@Param("list") List<OrsDeviceLocation> list);

}