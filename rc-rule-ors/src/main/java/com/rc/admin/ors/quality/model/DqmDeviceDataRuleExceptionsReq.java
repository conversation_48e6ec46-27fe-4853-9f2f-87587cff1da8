package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DqmDeviceDataRuleExceptionsReq implements Serializable {


    @ApiModelProperty(value = "数据中心")
    private Integer dataCenterId;
    @ApiModelProperty(value = "事业部")
    private String sybbh;
    @ApiModelProperty(value = "事业部")
    private List<String> sybbhList;
    @ApiModelProperty(value = "产品组")
    private String zehdSpart;
    @ApiModelProperty(value = "代理商")
    private String agent;
    @ApiModelProperty(value = "代理商")
    private String agentName;
    @ApiModelProperty(value = "国家")
    private String country;
    @ApiModelProperty(value = "客户名称")
    private String userName;
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;


    @ApiModelProperty(value = "设备name")
    private String deviceName;
    @ApiModelProperty(value = "设备编号列表")
    private List<String> deviceNameList;

    @ApiModelProperty(value = "存量分类")
    private String storeCategory;
    @ApiModelProperty(value = "检查项属性编码")
    private List<Integer> paramCode;
    @ApiModelProperty(value = "检查项属性集合")
    private String paramCodes;
    @ApiModelProperty("安装分类")
    private String installType;

    @ApiModelProperty(value = "检查规则")
    private String checkRuleCode;

    @ApiModelProperty(value = "异常现象")
    private String abnormalPhenomenonCode;

    @ApiModelProperty(value = "物联盒ID")
    private String rcAssetId;

    @ApiModelProperty(value = "模型名称")
    private String modelName;

    @ApiModelProperty(value = "设备编号集")
    private String abnormalDeviceList;
    @ApiModelProperty(value = "物标识")
    private String assetId;
    @ApiModelProperty(value = "物标识列表")
    private List<String> deviceCodeList;
    @ApiModelProperty(value = "所属大区")
    private String zehdsvReg;
    @ApiModelProperty(value = "所属大区")
    private List<String> zehdsvRegList;
    @ApiModelProperty(value = "所属大区名称")
    private String zehdSpartdesc;
    @ApiModelProperty(value = "所属大区名称")
    private List<String> zehdSpartdescList;
    @ApiModelProperty(value = "国区")
    private List<String> countryRegion;

    @ApiModelProperty(value = "物标识")
    private String uuid;
    @ApiModelProperty(value = "实例名称")
    private String thingId;
    @ApiModelProperty(value = "模型名称")
    private String modelId;
    @ApiModelProperty(value = "模型名称")
    private List<String> modelIdList;
    @ApiModelProperty(value = " 数据期间 查询方式选择：可选项包括：年月、日期\\n\" +\n" +
            "            \"查询方式=年月，提供年度和月份选择输入。年份初始为2023，显示当前年份，月度默认显示当前月份\\n\" +\n" +
            "            \"查询方式=日期，提供日期范围选择，用户可以选择开始和结束日期")
    private String startTime;
    @ApiModelProperty(value = " 数据期间 查询方式选择：可选项包括：年月、日期\\n\" +\n" +
            "            \"查询方式=年月，提供年度和月份选择输入。年份初始为2023，显示当前年份，月度默认显示当前月份\\n\" +\n" +
            "            \"查询方式=日期，提供日期范围选择，用户可以选择开始和结束日期")
    private String endTime;
    @ApiModelProperty(value = "异常项")
    private String abnormalName;
    @ApiModelProperty(value = "异常项")
    private List<Integer> list;
    @ApiModelProperty(value = "设备集")
    private List<String> listName;
    @ApiModelProperty(value = "异常数")
    private String abnormalData;
    @ApiModelProperty(value = "最近在线时间")
    private String lastOnlineTime;
    @ApiModelProperty(value = "离线时长")
    private String offlineDuration;
    @ApiModelProperty(value = "最近位置")
    private String lastLocation;
    @ApiModelProperty(value = "存量分类")
    private String inventoryClassification;
    @ApiModelProperty(value = "安装分类")
    private String installClassification;
    @ApiModelProperty(value = "数据范围 可选项：最新、所有，默认为“最新”\n" +
            "当选项为“最新”时，无论设备数据期间内有多少异常数据，都只显示设备在该期间内的最新一笔记录\n" +
            "当选项为“所有”时，显示设备在指定期间内的所有异常数据")
    private String dataScope;

    @ApiModelProperty(value = "页码")
    private int current;
    @ApiModelProperty(value = "条数")
    private int pageSize;
    @ApiModelProperty(value = "字段")
    private String sortField;
    @ApiModelProperty(value = "排序类型")
    private String sortOrder;
    @ApiModelProperty(value = "数据检查日期开始时间")
    private Date createTime_start;
    @ApiModelProperty(value = "数据检查日期结束时间")
    private Date createTime_end;
    @ApiModelProperty("总异常设备")
    private String abnormalDeviceKey;

    @ApiModelProperty("年月")
    private String yearMonthTime;

    @ApiModelProperty("不统计华兴设备 0不统计,1统计")
    private String hasHuaXin;

    private List<String> huaXinModelIdList;

    public String getStoreCategory() {
        return storeCategory;
    }

    public void setStoreCategory(String storeCategory) {
        this.storeCategory = storeCategory;
    }

    public List<Integer> getParamCode() {
        return paramCode;
    }

    public void setParamCode(List<Integer> paramCode) {
        this.paramCode = paramCode;
    }

    public String getInstallType() {
        return installType;
    }

    public void setInstallType(String installType) {
        this.installType = installType;
    }

    public String getAbnormalDeviceKey() {
        return abnormalDeviceKey;
    }

    public void setAbnormalDeviceKey(String abnormalDeviceKey) {
        this.abnormalDeviceKey = abnormalDeviceKey;
    }

    public Integer getDataCenterId() {
        return dataCenterId;
    }

    public void setDataCenterId(Integer dataCenterId) {
        this.dataCenterId = dataCenterId;
    }

    public String getSybbh() {
        return sybbh;
    }

    public void setSybbh(String sybbh) {
        this.sybbh = sybbh;
    }

    public String getZehdSpart() {
        return zehdSpart;
    }

    public void setZehdSpart(String zehdSpart) {
        this.zehdSpart = zehdSpart;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }


    public String getAbnormalDeviceList() {
        return abnormalDeviceList;
    }

    public void setAbnormalDeviceList(String abnormalDeviceList) {
        this.abnormalDeviceList = abnormalDeviceList;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getZehdsvReg() {
        return zehdsvReg;
    }

    public void setZehdsvReg(String zehdsvReg) {
        this.zehdsvReg = zehdsvReg;
    }

    public String getZehdSpartdesc() {
        return zehdSpartdesc;
    }

    public void setZehdSpartdesc(String zehdSpartdesc) {
        this.zehdSpartdesc = zehdSpartdesc;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getThingId() {
        return thingId;
    }

    public void setThingId(String thingId) {
        this.thingId = thingId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getAbnormalName() {
        return abnormalName;
    }

    public void setAbnormalName(String abnormalName) {
        this.abnormalName = abnormalName;
    }

    public List<Integer> getList() {
        return list;
    }

    public void setList(List<Integer> list) {
        this.list = list;
    }

    public List<String> getListName() {
        return listName;
    }

    public void setListName(List<String> listName) {
        this.listName = listName;
    }

    public String getAbnormalData() {
        return abnormalData;
    }

    public void setAbnormalData(String abnormalData) {
        this.abnormalData = abnormalData;
    }

    public String getLastOnlineTime() {
        return lastOnlineTime;
    }

    public void setLastOnlineTime(String lastOnlineTime) {
        this.lastOnlineTime = lastOnlineTime;
    }

    public String getOfflineDuration() {
        return offlineDuration;
    }

    public void setOfflineDuration(String offlineDuration) {
        this.offlineDuration = offlineDuration;
    }

    public String getLastLocation() {
        return lastLocation;
    }

    public void setLastLocation(String lastLocation) {
        this.lastLocation = lastLocation;
    }

    public String getInventoryClassification() {
        return inventoryClassification;
    }

    public void setInventoryClassification(String inventoryClassification) {
        this.inventoryClassification = inventoryClassification;
    }

    public String getInstallClassification() {
        return installClassification;
    }

    public void setInstallClassification(String installClassification) {
        this.installClassification = installClassification;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Date getCreateTime_start() {
        return createTime_start;
    }

    public void setCreateTime_start(Date createTime_start) {
        this.createTime_start = createTime_start;
    }

    public Date getCreateTime_end() {
        return createTime_end;
    }

    public void setCreateTime_end(Date createTime_end) {
        this.createTime_end = createTime_end;
    }

    public String getYearMonthTime() {
        return yearMonthTime;
    }

    public void setYearMonthTime(String yearMonthTime) {
        this.yearMonthTime = yearMonthTime;
    }
}
