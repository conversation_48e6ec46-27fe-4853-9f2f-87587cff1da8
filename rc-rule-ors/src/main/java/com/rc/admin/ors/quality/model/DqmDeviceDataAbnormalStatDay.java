package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DqmDeviceDataAbnormalStatDay {


    @ApiModelProperty(value = "设备编号")
    private String deviceName;

    @ApiModelProperty(value = "模型ID")
    private String modelId;

    private String abnormalCode;
    private long abnormalCount;
    private String abnormalTime;
    private String abnormalData;
    private String paramCode;

    private Date statDate;

    private Date createTime;
    private long deviceStatus9008;
    private long deviceLocation9008;
    private long deviceLocation9001;
    private long deviceLocation9002;
    private long deviceLocation9007;
    private long engineWorktime9008;
    private long engineWorktime9001;
    private long engineWorktime9004;
    private long workingTime9008;
    private long workingTime9001;
    private long workingTime9004;
    private long totalFuelConsumption9008;
    private long totalFuelConsumption9001;
    private long totalFuelConsumption9004;
    private long pumpingVolume9008;
    private long pumpingVolume9001;
    private long pumpingVolume9004;
    private long drivingMileage9008;
    private long drivingMileage9001;
    private long drivingMileage9004;
    private long deviceQuestionId;

    private String deviceLocation9001Data;
    private String deviceLocation9002Data;
    private String deviceLocation9007Data;
    private String engineWorktime9001Data;
    private String engineWorktime9004Data;
    private String workingTime9001Data;
    private String workingTime9004Data;
    private String totalFuelConsumption9001Data;
    private String totalFuelConsumption9004Data;
    private String pumpingVolume9001Data;
    private String pumpingVolume9004Data;
    private String drivingMileage9001Data;
    private String drivingMileage9004Data;

}
