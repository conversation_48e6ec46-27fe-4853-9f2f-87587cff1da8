package com.rc.admin.ors.quality.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Setter
@Getter
public class OrsDeviceResp {

    /**
     * 物实例id
     */
    private String thingId;

    /**
     * 物标识
     */
    private String assetId;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型
     */
    private String classId;

    /**
     * 设备信息
     */
    private OrsDeviceInfoResp thingInfo;

    /**
     * 设备协议
     */
    private String protocol;

    /**
     * 同步
     */
    private String phase;

    /**
     * 物模型信息
     */
    private OrsDeviceModelResp model;

    /**
     * 连接信息
     */
    private OrsConnectionConfigResp connectionConfig;

    /**
     * 是否禁用
     */
    private Boolean deprecated;

    /**
     * 数据中心
     */
    private String dataCenterId;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建用户
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updated;

    /**
     * 修改用户
     */
    private String updatedBy;

    /**
     * 连接方式
     */
    private String connectionType;
}
