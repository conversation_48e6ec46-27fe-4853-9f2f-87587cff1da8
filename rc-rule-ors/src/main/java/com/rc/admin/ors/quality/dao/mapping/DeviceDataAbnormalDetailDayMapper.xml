<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper">

    <select id="clearData" resultType="com.rc.admin.ors.quality.entity.DeviceDataAbnormalDetailDay">
        SELECT
            T1.device_name,
            T1.stat_date
        FROM
            dqm.ors_device_data_abnormal_detail_day T1
                LEFT JOIN dqm.ors_device_param_report_log T2 ON T2.asset_id = T1.device_name AND T2.report_date = T1.stat_date AND T2.null_properties IS NOT NULL AND T2.abnormal_properties IS NULL
        WHERE
            T2.asset_id IS NULL
    </select>

    <select id="findAbnormalDetail" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp">
      with omd as (select * from dqm.ors_model_division omd
          <where>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
              AND omd.product_group_code IN
              <foreach collection="req.zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
              </foreach>
            </if>
              <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign != '1'.toString()">
                  and omd.model_id
                  in
                  (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign = '2')
              </if>
          </where>)
        SELECT
             <if test="req.dataScope != null and req.dataScope == 'new'">
                 DISTINCT ON (T1.device_name) T1.device_name AS asset_id,
             </if>
             <if test="req.dataScope != null and req.dataScope == 'all'">
                 T1.device_name AS asset_id,
             </if>                                 
             T1.stat_date,
             sd.dict_type as dict_type,
            ct.name as dict_type_name,
            od.name as sybbh,
            T1.division_code,
            omd.product_group_name as zehd_spartdesc,
            omd.product_group_code as zehd_spart,
             T2.device_code,
             T2.device_name,
             T2.hw_version,
             T2.country,
             T2.fw_version,
             T2.model_id,
             T2.model_name as model_name,
             T2.thing_id  as thing_id,
             T2.auth_token,
             T1.data_center_id,
             T1.property,
             T1.property_name,
             T1.abnormal_name,
             T1.abnormal_code,
             T1.param_code,
             T1.abnormal_data,
             T1.abnormal_time,
             T1.abnormal_count as abnormalNum,
             T1.detail_id,
             T2.rc_asset_id
        FROM
            dqm.ors_device_data_abnormal_detail_day T1
                left join sany_data_service.sanyds_dict sd
                on sd.dict_id = T1.param_code
                left join sys_dict ct on ct.code = sd.dict_type and ct.dict_type = 'check_type'
                left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T1.division_code = od.value
                INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name
                INNER JOIN omd ON omd.model_id = T2.model_id
        <where>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND T1.division_code IN
                <foreach item="item" index="index" collection="req.sybbh.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.dataCenterId != null">
              AND T1.data_center_id = #{req.dataCenterId}
            </if>
            <if test="req.modelId != null and req.modelId != ''">
                AND T2.model_id IN
                <foreach item="item" index="index" collection="req.modelId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.dictTypeName != null and req.dictTypeName != ''">
                AND sd.dict_type IN
                <foreach item="item" index="index" collection="req.dictTypeName.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>



            <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '2'.toString()">
                and T2.country_code
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{req.doubleRateSign})
            </if>
            <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '3'.toString()">
                and (T2.country_code
                not
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
                or T2.country_code is null
                )
            </if>
            <if test="req.country != null and req.country != ''">
                AND T2.country_code IN
                <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.rcAssetId != null and req.rcAssetId != ''">
                AND T2.rc_asset_id IN
                <foreach item="item" index="index" collection="req.rcAssetId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.deviceCode != null  and req.deviceCode != ''">
                AND (
                    T2.device_code LIKE CONCAT('%', #{req.deviceCode}, '%')
                    OR
                    T2.device_code IN
                    <foreach collection="req.deviceCode.split(',')" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.assetId != null  and req.assetId != ''">
                AND (
                    T2.asset_id LIKE CONCAT('%', #{req.assetId}, '%')
                    OR
                    T2.asset_id IN
                    <foreach collection="req.assetId.split(',')" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.doubleRateSign != null and req.doubleRateSign != ''">
                and T1.param_code in
                (select DISTINCT param_code from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{req.doubleRateSign} = '3' THEN '2' ELSE #{req.doubleRateSign} END)
            </if>
            <if test="req.paramCodes != null and req.paramCodes != ''">
                AND T1.param_code IN
                <foreach collection="req.paramCodes.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::int4
                </foreach>
            </if>
            <if test="req.abnormalName != null and req.abnormalName != ''">
                AND T1.abnormal_code IN
                <foreach collection="req.abnormalName.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::int4
                </foreach>
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND T1.stat_date &gt;= #{req.startTime}::TIMESTAMP
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND T1.stat_date &lt;= #{req.endTime}::TIMESTAMP
            </if>
            <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
                AND T2.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
        </where>
        ORDER BY
            <if test="req.dataScope != null and req.dataScope == 'new'">
                T1.device_name,
            </if>
            T1.stat_date DESC
    </select>


    <select id="countDevice" resultType="com.rc.admin.ors.quality.model.AbnormalCodeRateResp">
        SELECT
            param_code,
            property_name,
            COUNT(distinct abnormal.device_name) AS total
        FROM dqm.ors_device_data_abnormal_detail_day abnormal
        inner join  dqm.ors_base_device_info device on abnormal.device_name = device.asset_id
        left join dqm.ors_country_region_sync T4 ON device.country_code = T4.country_code
        INNER JOIN  dqm.ors_model_division omd ON omd.model_id = device.model_id
        <if test="req.divisiones != null and req.divisiones.size() > 0 ">
            AND omd.division_code IN
            <foreach collection="req.divisiones" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.division != null and req.division != '' ">
            AND abnormal.division_code  = #{req.division}
        </if>
        <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign != '1'.toString()">
            and omd.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <where>
            <if test="req.modelId != null and req.modelId != '' ">
                AND device.model_id  = #{req.modelId}
            </if>
            <if test="req.regionCode != null and req.regionCode.size() > 0 ">
                AND T4.region_code IN
                <foreach collection="req.regionCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.rulesSign != null and req.rulesSign != '' and req.rulesSign == '1'.toString()">
                AND abnormal.abnormal_code not in (9003,9100)
            </if>
            <choose>
                <when test="req.abnormalType != null and req.abnormalType != '' and req.abnormalType == '1'.toString()">
                    AND abnormal.abnormal_code not in (9008,9009)
                </when>
                <otherwise>
                    AND abnormal.abnormal_code  in (9008,9009)
                </otherwise>
            </choose>
            <if test="req.doubleRateSign != null and req.doubleRateSign != ''">
                and abnormal.param_code in
                (select DISTINCT param_code from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{req.doubleRateSign} = '3' THEN '2' ELSE #{req.doubleRateSign} END)
            </if>
            <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '2'.toString()">
                and device.country_code
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{req.doubleRateSign})
            </if>
            <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '3'.toString()">
                and (device.country_code
                not
                in
                (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
                or device.country_code is null
                )
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND stat_date &gt;= #{req.startTime}::TIMESTAMP
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND stat_date &lt;= #{req.endTime}::TIMESTAMP
            </if>
            <if test="req.iotBoxType != null and req.iotBoxType == 'SG'.toString()">
                AND device.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="req.iotBoxType != null and req.iotBoxType == 'HX'.toString()">
                AND device.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
        </where>
        GROUP BY param_code, property_name
    </select>

    <select id="selectAbnormalCodeRate" resultType="com.rc.admin.ors.quality.model.AbnormalCodeRateResp">
      WITH total_count AS (
        SELECT COUNT(*) AS cnt
        FROM dqm.ors_device_data_abnormal_detail_day abnormal
          inner join  dqm.ors_base_device_info device on abnormal.device_name = device.asset_id
            left join dqm.ors_country_region_sync T4 ON device.country_code = T4.country_code
          INNER JOIN  dqm.ors_model_division omd ON omd.model_id = device.model_id
          <if test="req.divisiones != null and req.divisiones.size() > 0 ">
            AND omd.division_code IN
            <foreach collection="req.divisiones" item="item" open="(" separator="," close=")">
              #{item}
            </foreach>
          </if>
        <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign != '1'.toString()">
            and omd.model_id
            in
            (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
        </if>
        <where>
            <if test="req.division != null and req.division != '' ">
                AND abnormal.division_code  = #{req.division}
            </if>
            <if test="req.modelId != null and req.modelId != '' ">
                AND device.model_id  = #{req.modelId}
            </if>
            <if test="req.regionCode != null and req.regionCode.size() > 0 ">
                    AND T4.region_code IN
                    <foreach collection="req.regionCode" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
              </if>
          <if test="req.rulesSign != null and req.rulesSign != '' and req.rulesSign == '1'.toString()">
                AND abnormal.abnormal_code not in (9003,9100)
          </if>
          <if test="req.doubleRateSign != null and req.doubleRateSign != ''">
            and abnormal.param_code in
            (select DISTINCT param_code from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{req.doubleRateSign} = '3' THEN '2' ELSE #{req.doubleRateSign} END)
          </if>
          <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '2'.toString()">
            and device.country_code
            in
            (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{req.doubleRateSign})
          </if>
          <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '3'.toString()">
              and (device.country_code
              not
              in
              (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
              or device.country_code is null
              )
          </if>
          <if test="req.startTime != null and req.startTime != ''">
            AND stat_date &gt;= #{req.startTime}::TIMESTAMP
          </if>
          <if test="req.endTime != null and req.endTime != ''">
            AND stat_date &lt;= #{req.endTime}::TIMESTAMP
          </if>
          <if test="req.iotBoxType != null and req.iotBoxType == 'SG'.toString()">
            AND device.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
          </if>
          <if test="req.iotBoxType != null and req.iotBoxType == 'HX'.toString()">
            AND device.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
          </if>
        </where>
      )
      SELECT
        (SELECT cnt FROM total_count) AS total,
        param_code,
        (regexp_split_to_array(property_name,'（'))[1] as property_name,
        abnormal_code,
        abnormal_name,
        COUNT(abnormal_code) as abnormal_code_count,
        CASE
          WHEN (SELECT cnt FROM total_count) = 0 THEN '0.00%'
          ELSE round(COUNT(abnormal_code)::NUMERIC / (SELECT cnt FROM total_count) * 100, 2) || '%'
        END AS abnormal_code_rate
      FROM dqm.ors_device_data_abnormal_detail_day abnormal
      inner join  dqm.ors_base_device_info device on abnormal.device_name = device.asset_id
        left join dqm.ors_country_region_sync T4 ON device.country_code = T4.country_code
      INNER JOIN  dqm.ors_model_division omd ON omd.model_id = device.model_id
      <if test="req.divisiones != null and req.divisiones.size() > 0 ">
        AND omd.division_code IN
        <foreach collection="req.divisiones" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign != '1'.toString()">
        and omd.model_id
        in
        (select DISTINCT model_id from dqm.ors_double_rate_config where double_rate_sign= '2')
      </if>
      <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '2'.toString()">
        and device.country_code
        in
        (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= #{req.doubleRateSign})
      </if>
      <if test="req.doubleRateSign != null and req.doubleRateSign != '' and req.doubleRateSign == '3'.toString()">
          and (device.country_code
          not
          in
          (select country_code from dqm.ors_country_double_rate_config where double_rate_sign= '2')
          or device.country_code is null
          )
      </if>
      <where>
          <if test="req.division != null and req.division != '' ">
              AND abnormal.division_code  = #{req.division}
          </if>
          <if test="req.modelId != null and req.modelId != '' ">
              AND device.model_id  = #{req.modelId}
          </if>
          <if test="req.regionCode != null and req.regionCode.size() > 0 ">
              AND T4.region_code IN
              <foreach collection="req.regionCode" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
        <if test="req.rulesSign != null and req.rulesSign != '' and req.rulesSign == '1'.toString()">
          AND abnormal.abnormal_code not in (9003,9100)
        </if>
        <if test="req.doubleRateSign != null and req.doubleRateSign != ''">
              and abnormal.param_code in
              (select DISTINCT param_code from dqm.ors_double_rate_config where double_rate_sign= CASE WHEN #{req.doubleRateSign} = '3' THEN '2' ELSE #{req.doubleRateSign} END)
        </if>
        <if test="req.startTime != null and req.startTime != ''">
          AND stat_date &gt;= #{req.startTime}::TIMESTAMP
        </if>
        <if test="req.endTime != null and req.endTime != ''">
          AND stat_date &lt;= #{req.endTime}::TIMESTAMP
        </if>
        <if test="req.iotBoxType != null and req.iotBoxType == 'SG'.toString()">
          AND device.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="req.iotBoxType != null and req.iotBoxType == 'HX'.toString()">
          AND device.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
      </where>
      GROUP BY param_code, property_name, abnormal_code, abnormal_name
      order by param_code,abnormal_code
    </select>
</mapper>