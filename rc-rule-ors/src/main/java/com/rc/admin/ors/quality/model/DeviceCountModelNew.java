package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/1/9 16:49
 * @describe
 */
@Getter
@Setter
public class DeviceCountModelNew {

    @ApiModelProperty("事业部")
    private String division;

    @ApiModelProperty("事业部编码")
    private String divisionCode;

    @ApiModelProperty("产品组")
    private String productGroup;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("模型id")
    private String modelId;

    @ApiModelProperty("模型名称")
    private String modelName;

    @ApiModelProperty("总数")
    private int total;

    @ApiModelProperty("注册数量")
    private int registNum;

    @ApiModelProperty("未注册数量")
    private int unRegistNum;

    @ApiModelProperty("注册率")
    private double registRate;

    @ApiModelProperty("激活数量")
    private int activeNum;

    @ApiModelProperty("对应属性激活数量")
    private int propertyActiveNum;

    @ApiModelProperty("整机剔除数量")
    private int wholeExcludNum;

    //异常总数量
    @ApiModelProperty("总的异常数量")
    private int abnormalTotalNum;

    //对应属性的异常数量
    @ApiModelProperty("对应属性异常数量")
    private int propertyAbnormalNum;

    @ApiModelProperty("属性类型")
    private String dictType;

    @ApiModelProperty("属性类型名称")
    private String dictTypeName;

    @ApiModelProperty("属性名称")
    private String propertyName;

    @ApiModelProperty("属性Code")
    private String property;

    @ApiModelProperty("对应属性剔除数量")
    private int propertyExcludNum;

    @ApiModelProperty("对应属性整机剔除数量")
    private int propertyWholeExcludNum;

    @ApiModelProperty("总的属性剔除数量")
    private int totalPropertyExcludNum;

    @ApiModelProperty("检查项准确率")
    private double checkRate;

    @ApiModelProperty("异常率")
    private double abnormalRate;

    @ApiModelProperty("对应时间对应属性未上报数据的数量")
    private int propertyUnReportNum;

    @ApiModelProperty("对应时间对应属性上报数据的数量")
    private int propertyReportNum;

    @ApiModelProperty("对应时间上报数据的数量")
    private int totalReportNum;

    @ApiModelProperty("对应时间未上报数据的数量")
    private int totalUnReportNum;

    @ApiModelProperty("总完整性异常数量")
    private int nullAbnormalTotalNum;

    @ApiModelProperty("总准确性异常数量")
    private int paramAbnormalTotalNum;

    @ApiModelProperty("上报日期")
    private String reportDate;

    private String queryAbnormalType;

    @ApiModelProperty("准确性异常的数量")
    private int propertyParamAbnormalTotalNum;

    @ApiModelProperty("完整性异常的数量")
    private int propertyNullAbnormalTotalNum;

    @ApiModelProperty("总准确性异常率")
    private double paramAbnormalRate;

    @ApiModelProperty("总完整性异常率")
    private double nullAbnormalRate;

    @ApiModelProperty("检查项准确性异常率")
    private double checkParamAbnormalRate;

    @ApiModelProperty("检查项完整性异常率")
    private double checkNullAbnormalRate;

}
