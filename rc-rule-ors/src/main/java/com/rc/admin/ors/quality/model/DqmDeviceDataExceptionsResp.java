package com.rc.admin.ors.quality.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.rc.admin.ors.quality.utils.BusinessConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DqmDeviceDataExceptionsResp {

    @ApiModelProperty(value = "事业部")
    @Excel(name = "事业部", width = 10, orderNum = "0")
    @ExcelProperty("事业部")
    @ColumnWidth(10)
    private String sybbh;

    @ApiModelProperty(value = "模型ID")
    @Excel(name = "模型ID", width = 10, orderNum = "8")
    @ExcelProperty("模型ID")
    @ColumnWidth(10)
    private String modelId;

    @ApiModelProperty(value = "模型名称")
    @Excel(name = "模型名称", width = 10, orderNum = "9")
    @ExcelProperty("模型名称")
    @ColumnWidth(10)
    private String modelName;

    @ApiModelProperty(value = "数据中心")
    @ExcelIgnore
    private Integer dataCenterId;

    @ApiModelProperty(value = "数据中心名称")
    @ExcelProperty("数据中心")
    @ColumnWidth(10)
    private String dataCenterName;
    // 设置dataCenterName的getter方法
    public String getDataCenterName() {
        if (dataCenterId != null) {
            return BusinessConst.dataCenterMap.get(dataCenterId);
        }
        return null;
    }

    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", width = 10, orderNum = "2")
    @ExcelProperty("设备编号")
    @ColumnWidth(15)
    private String deviceCode;


    @ApiModelProperty("物联盒ID")
    @ExcelProperty("物联盒ID")
    @ColumnWidth(15)
    private String rcAssetId;


    @ExcelIgnore
    @ColumnWidth(10)
    private String dictType;


    @Excel(name = "检查项类型", width = 10)
    @ExcelProperty("检查项类型")
    @ColumnWidth(10)
    private String dictTypeName;



    @Excel(name = "检查项", width = 10, orderNum = "18")
    @ExcelProperty("检查项")
    @ColumnWidth(10)
    private String propertyName;

    @ApiModelProperty(value = "异常项")
    @Excel(name = "异常项", width = 10, orderNum = "11")
    @ExcelProperty("异常项")
    @ColumnWidth(10)
    private String abnormalName;

    @ApiModelProperty(value = "异常数量")
    @ExcelProperty("异常数量")
    @ColumnWidth(10)
    private Integer abnormalNum;

    @ApiModelProperty(value = "最后异常内容")
    @Excel(name = "最后异常内容", width = 10, orderNum = "12")
    @ExcelProperty("最后异常内容")
    @ColumnWidth(10)
    private String abnormalData;

    @ApiModelProperty(value = "数据发生时间")
    @Excel(name = "数据发生时间", width = 10, orderNum = "10")
    @ExcelProperty("数据发生时间")
    @ColumnWidth(10)
    private String abnormalTime;

    @ExcelIgnore
    private String assetId;

    @ExcelIgnore
    private String thingId;

    @ApiModelProperty(value = "实例名称")
    @Excel(name = "实例名称", width = 10, orderNum = "4")
    @ExcelProperty("实例名称")
    @ColumnWidth(10)
    private String deviceName;

    @ApiModelProperty(value = "固件版本号")
    @Excel(name = "固件版本", width = 10, orderNum = "5")
    @ExcelProperty("固件版本")
    @ColumnWidth(10)
    private String fwVersion;

    @ApiModelProperty(value = "硬件版本")
    @Excel(name = "硬件版本", width = 10, orderNum = "6")
    @ExcelProperty("硬件版本")
    @ColumnWidth(10)
    private String hwVersion;

    @ApiModelProperty(value = "认证秘钥")
    @Excel(name = "认证秘钥", width = 10, orderNum = "7")
    @ExcelProperty("认证秘钥")
    @ColumnWidth(10)
    private String authToken;

    @ApiModelProperty(value = "产品组")
    @Excel(name = "产品组", width = 10, orderNum = "1")
    @ExcelProperty("产品组")
    @ColumnWidth(10)
    private String zehdSpartdesc;

    @ApiModelProperty(value = "产品组")
    @ExcelIgnore
    private String zehdSpart;

    @ApiModelProperty(value = "所属大区")
    //@Excel(name = "所属大区", width = 10, orderNum = "2")
    @ExcelIgnore
    private String zehdsvReg;


    @ApiModelProperty(value = "最近在线时间")
    @Excel(name = "最近在线时间", width = 10, orderNum = "13")
    @ExcelIgnore
    private String lastOnlineTime;

    @ApiModelProperty(value = "离线时长")
    @Excel(name = "离线时长", width = 10, orderNum = "14")
    @ExcelIgnore
    private String offlineDuration;

    @ApiModelProperty(value = "最近位置")
    @Excel(name = "最近位置", width = 10, orderNum = "15")
    @ExcelIgnore
    private String lastLocation;

    @ApiModelProperty(value = "存量分类")
    @Excel(name = "存量分类", width = 10, orderNum = "16")
    @ExcelIgnore
    private String inventoryClassification;

    @ApiModelProperty(value = "安装分类")
    @Excel(name = "安装分类", width = 10, orderNum = "17")
    @ExcelIgnore
    private String installClassification;


    @ApiModelProperty(value = "detail_id")
    @ExcelIgnore
    private String detailId;

    @ExcelIgnore
    private String abnormalCode;

    @ExcelIgnore
    private String paramCode;

    @ApiModelProperty(value = "国家")
    @Excel(name = "国家", width = 10, orderNum = "19")
    @ExcelProperty("国家")
    @ColumnWidth(10)
    private String country;

}