package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 问题处理结果/日志(QuestionLog)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-01 12:30:24
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("问题处理结果/日志")
@TableName("ors_question_log")
public class QuestionLog extends Model<QuestionLog> {

    @TableId(type = IdType.AUTO)
    private Integer id;


    @ApiModelProperty(name = "questionId", value = "问题唯一id")
    @TableField(value = "question_id")
    private Integer questionId;

    @ApiModelProperty(name = "assetId", value = "物标识")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "userAccount", value = "处理人账户")
    @TableField(value = "user_account")
    private String userAccount;

    @ApiModelProperty(name = "userName", value = "处理人姓名")
    @TableField(value = "user_name")
    private String userName;

    @ApiModelProperty(name = "handlIdea", value = "处理意见")
    @TableField(value = "handl_idea")
    @NotNull(message = "请输入处理意见")
    @Length(message = "处理意见不能超过200字")
    private String handlIdea;

    @ApiModelProperty(name = "questionResean", value = "问题原因")
    @TableField(value = "question_resean")
    private String questionResean;

    @ApiModelProperty(name = "rawDate", value = "原始报文")
    @TableField(value = "raw_date")
    private String rawDate;

    @ApiModelProperty(name = "rawDateFile", value = "原始报文文件")
    @TableField(value = "raw_date_file")
    private byte[] rawDateFile;

    @ApiModelProperty(name = "opinionClassifyCode", value = "处理意见分类编码")
    @TableField(value = "opinion_classify_code")
    private String opinionClassifyCode;

    @ApiModelProperty(name = "opinionClassifyName", value = "处理意见分类")
    @TableField(value = "opinion_classify_name")
    private String opinionClassifyName;

    @ApiModelProperty(name = "verifyResult", value = "验证结果")
    @TableField(value = "verify_result")
    private String verifyResult;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(name = "fileName", value = "原始报文文件名称")
    @TableField(value = "file_name")
    private String fileName;

    @ApiModelProperty(name = "fileSize", value = "文件大小")
    @TableField(value = "file_size")
    private Long fileSize;

    @ApiModelProperty(name = "nextUser", value = "下一环节处理人账户")
    @TableField(value = "next_user")
    private String nextUser;

    @ApiModelProperty(name = "nextUserName", value = "下一环节处理人姓名")
    @TableField(value = "next_user_name")
    private String nextUserName;

    @ApiModelProperty(name = "nextStep", value = "下一环节")
    @TableField(value = "next_step")
    private String nextStep;

    @ApiModelProperty(name = "curStep", value = "当前处理环节")
    @TableField(value = "cur_step")
    private String curStep;

    @TableField(value = "cur_step_name")
    private String curStepName;

    @ApiModelProperty(name = "nextStepName", value = "下一环节名称")
    private String nextStepName;

    @ApiModelProperty(name = "questions", value = "需要处理的问题信息")
    @TableField(exist = false)
    private List<QuestionDevices> questions;

    @ApiModelProperty(name = "handleState", value = "处理状态 1=待处理/处理中 2=处理完成")
    @TableField(value = "handle_state")
    private Integer handleState;

    private Date updateTime;



    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Getter
    @Setter
    public static class QuestionDevices{
        @ApiModelProperty(name = "questionId", value = "问题唯一id")
        @NotNull(message = "缺少对应的问题参数")
        private Integer questionId;

        @ApiModelProperty(name = "assetId", value = "物标识")
        @NotNull(message = "缺少物标识")
        private String assetId;

        @ApiModelProperty(name = "deviceCode", value = "设备编号")
        @NotNull(message = "缺少设备编号")
        private String deviceCode;
    }
}

