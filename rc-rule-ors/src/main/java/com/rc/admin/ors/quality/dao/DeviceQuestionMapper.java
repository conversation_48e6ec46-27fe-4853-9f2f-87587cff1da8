package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.DeviceQuestion;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.model.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备问题跟进(DeviceQuestion)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-30 16:55:31
 */
public interface DeviceQuestionMapper extends BaseMapper<DeviceQuestion> {

    Page<DeviceLedgerResp> questionList(Page<DeviceLedgerResp> page, @Param(Constants.WRAPPER) Wrapper wrapper, @Param("queryAccess") String queryAccess);

    List<String> queryFilter(@Param("req") DeviceQuestionReq req, @Param("curUser") String curUser);

    Page<OrsDeviceDataAbnormalDetail> findAbnormalDateByDate(Page<OrsDeviceDataAbnormalDetail> page, @Param("questionId") Long questionId);

    /**
     * 统计问题各个节点的数量
     * @return
     */
    QuestionCount countNum();

    /**
     * 根据处理环节统计待处理的数据量
     * @param curSteps 处理环节集合
     * @return
     */
    List<QuestionCountDaysByStep> countByStepDays(@Param("curSteps") List<String> curSteps);

    /**
     * 根据用户统计其处理数据
     * @param curSteps 处理节点
     * @param userAccount 用户账户
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    Page<QuestionCountBydUser> questionCountBydUser(Page<?> page,
                                                    @Param("curSteps") List<String> curSteps,
                                                    @Param("userAccount") String userAccount,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    /**
     * 根据用户统计各个环节的数量
     * @param userAccount 用户账户
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    Page<QuestionCountBydUser> questionCountByStepAndUser(Page<?> page,
                                                          @Param("userAccount") String userAccount,
                                                          @Param("startTime") String startTime,
                                                          @Param("endTime") String endTime);

    List<QuestionCountByDivision> countByDivision();
}

