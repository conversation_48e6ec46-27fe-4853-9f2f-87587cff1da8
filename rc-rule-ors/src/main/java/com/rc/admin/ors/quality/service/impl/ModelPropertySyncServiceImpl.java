package com.rc.admin.ors.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.ors.quality.dao.ModelPropertySyncMapper;
import com.rc.admin.ors.quality.entity.ModelPropertySync;
import com.rc.admin.ors.quality.service.ModelPropertySyncService;
import org.springframework.stereotype.Service;

/**
 * 物模型属性同步表(ModelPropertySync)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-08 17:18:05
 */
@Service("modelPropertySyncService")
public class ModelPropertySyncServiceImpl extends ServiceImpl<ModelPropertySyncMapper, ModelPropertySync> implements ModelPropertySyncService {

}

