package com.rc.admin.ors.quality.service;

import com.rc.admin.ors.quality.entity.OrsDeviceCountry;
import com.rc.admin.ors.quality.model.CountryData;

import java.util.Map;

/**
 * <p>
 *     同步设备信息service
 * </p>
 * <AUTHOR>
 * @since 2023/10/23
 */
public interface OrsSyncDeviceService {

    /**
     * 同步数据
     */
    void syncData();


    /**
     * 定时任务每一年 一月一号 创建分区
     */
    void createPartitionTable();

    /**
     * 三一 集成平台获取设备对应国家和编码
     * @return
     */
    Map<String, CountryData> resultMap();


    /**
     * 三一 集成平台对应的国家编码
     * @return
     */
    Map<String, OrsDeviceCountry> resultCountryMap();


    /**
     * 同步设备实例
     * @param token
     * @param modelId
     */
    void syncDevice(String token, String modelId);

    /**
     * 同步物模型对应的属性
     * @param token
     * @param modelId
     */
    void syncProperty(String token, String modelId);

    /**
     * 获得海外新Ctoken
     * @return
     */
    String getRootCloudToken();

    /**
     * 获得海外根云token
     * @return
     */
    String getOverseaRootCloudToken();

    void updateRealTime(String modelIds,String paramCodes);

    /**
     * 同步国区处理
     */
    void syncNationalRegion();
}
