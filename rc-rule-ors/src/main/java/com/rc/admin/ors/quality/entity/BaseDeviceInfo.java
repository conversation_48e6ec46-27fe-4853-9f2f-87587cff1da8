package com.rc.admin.ors.quality.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 设备台账(BaseDeviceInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-28 09:30:57
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel("设备台账")
@TableName("ors_base_device_info")
public class BaseDeviceInfo extends Model<BaseDeviceInfo> {


    @ApiModelProperty(name = "assetId", value = "物标识")
    @TableField(value = "asset_id")
    private String assetId;

    @ApiModelProperty(name = "thingId", value = "物实例")
    @TableField(value = "thing_id")
    private String thingId;

    @ApiModelProperty(name = "modelId", value = "模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "modelName", value = "模型名称")
    @TableField(value = "model_name")
    private String modelName;

    @ApiModelProperty(name = "deviceCode", value = "设备编号")
    @TableField(value = "device_code")
    private String deviceCode;

    @ApiModelProperty(name = "deviceName", value = "实例名称")
    @TableField(value = "device_name")
    private String deviceName;

    @ApiModelProperty(name = "division", value = "事业部")
    @TableField(value = "division")
    private String division;

    @ApiModelProperty(name = "productGroup", value = "产品组")
    @TableField(value = "product_group")
    private String productGroup;

    @ApiModelProperty(name = "region", value = "大区")
    @TableField(value = "region")
    private String region;

    @ApiModelProperty(name = "country", value = "国家")
    @TableField(value = "country")
    private String country;

    @ApiModelProperty(name = "factoryDate", value = "出厂日期")
    @TableField(value = "factory_date")
    private Date factoryDate;

    @ApiModelProperty(name = "crmRegister", value = "crm注册状态")
    @TableField(value = "crm_register")
    private Integer crmRegister;

    @ApiModelProperty(name = "installType", value = "安装类型")
    @TableField(value = "install_type")
    private Integer installType;

    @ApiModelProperty(name = "created", value = "时间")
    @TableField(value = "created")
    private Date created;

    @ApiModelProperty(name = "etlTime", value = "数据入库时间")
    @TableField(value = "etl_time")
    private Date etlTime;

    @ApiModelProperty(name = "hwVersion", value = "设备硬件版本号")
    @TableField(value = "hw_version")
    private String hwVersion;

    @ApiModelProperty(name = "fwVersion", value = "设备固件版本号")
    @TableField(value = "fw_version")
    private String fwVersion;

    @ApiModelProperty(name = "authToken", value = "设备连接MQTT Broker时的密码")
    @TableField(value = "auth_token")
    private String authToken;

    @ApiModelProperty(name = "dataSource", value = "数据源（1==>根云；2==>新C）")
    @TableField(value = "data_source")
    private Integer dataSource;

    @ApiModelProperty(name = "agent", value = "所属代理商")
    @TableField(value = "agent")
    private String agent;

    @ApiModelProperty(name = "service", value = "服务代理商名称")
    @TableField(value = "service")
    private String service;

    @ApiModelProperty(name = "deliveryDate", value = "交机日期")
    @TableField(value = "delivery_date")
    private Date deliveryDate;

    @ApiModelProperty(name = "openDate", value = "开机日期")
    @TableField(value = "open_date")
    private Date openDate;

    @ApiModelProperty(name = "divisionCode", value = "事业部编号")
    @TableField(value = "division_code")
    private String divisionCode;

    @ApiModelProperty(name = "productGroupCode", value = "产品组编号")
    @TableField(value = "product_group_code")
    private String productGroupCode;

    @ApiModelProperty(name = "agentCode", value = "代理商编号")
    @TableField(value = "agent_code")
    private String agentCode;

    @ApiModelProperty(name = "countryCode", value = "国家编号")
    @TableField(value = "country_code")
    private String countryCode;

    @ApiModelProperty(name = "regionCode", value = "大区编号")
    @TableField(value = "region_code")
    private String regionCode;

    @ApiModelProperty(name = "serviceCode", value = "服务商编码")
    @TableField(value = "service_code")
    private String serviceCode;

    @ApiModelProperty(name = "customerName", value = "客户名称")
    @TableField(value = "customer_name")
    private String customerName;

    @ApiModelProperty(name = "customerCode", value = "客户编码")
    @TableField(value = "customer_code")
    private String customerCode;

    @ApiModelProperty(name = "uuidKey", value = "uuid唯一键，用于去重处理")
    @TableField(value = "uuid_key")
    private Integer uuidKey;

    @ApiModelProperty(name = "serialNum", value = "ml字段")
    @TableField(value = "serial_num")
    private String serialNum;

    @ApiModelProperty(name = "assetIdNrc", value = "海外根云asset_id剔除RC字段")
    @TableField(value = "asset_id_nrc")
    private String assetIdNrc;

    @ApiModelProperty(name = "tboxId", value = "ml字段")
    @TableField(value = "tbox_id")
    private String tboxId;

    @ApiModelProperty(name = "olineStatu", value = "在线状态 0=false 1=true")
    @TableField(value = "oline_statu")
    private Boolean olineStatu;

    @ApiModelProperty(name = "activeStatu", value = "激活状态 0=false 1=true")
    @TableField(value = "active_statu")
    private Boolean activeStatu;

    @ApiModelProperty(name = "productId", value = "对应CRMproductID")
    @TableField(value = "product_id")
    private String productId;

    @ApiModelProperty(name = "exceFlag", value = "异常标识 0=无异常 1=异常")
    @TableField(value = "exce_flag")
    private Integer exceFlag;

    @ApiModelProperty(name = "exceDesc", value = "异常描述")
    @TableField(value = "exce_desc")
    private String exceDesc;

}

