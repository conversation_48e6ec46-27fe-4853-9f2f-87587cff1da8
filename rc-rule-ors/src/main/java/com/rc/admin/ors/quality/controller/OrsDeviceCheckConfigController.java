package com.rc.admin.ors.quality.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.dao.BaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.entity.BaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.entity.SanydsCoreParamDef;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigExportExcel;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigImportExcel;
import com.rc.admin.ors.quality.excel.UnImportDataExcel;
import com.rc.admin.ors.quality.model.DeviceRule;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import com.rc.admin.util.ShiroUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-23 15:41:51
 */
@Slf4j
@ResponseResult
@RestController
@RequestMapping("/api/orsDeviceCheckConfig")
@Api(tags = {"设备剔除检查配置"})
public class OrsDeviceCheckConfigController {

    private final int MAX_IMPORT = 2000;
    private final int MIN_IMPORT = 500;
    /**
     * 服务对象
     */
    @Resource
    private OrsDeviceCheckConfigService orsDeviceCheckConfigService;

    @Resource
    private OrsModelPropertiesConfigMapper orsModelPropertiesConfigMapper;

    @Resource
    private BaseDeviceInfoMapper baseDeviceInfoMapper;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;


    @ApiOperation("给新增剔除设备-供给查询条件")
    @GetMapping("/forAdd")
    public Page<DeviceRule> selectAllForUpdate(Page<ModleIndicatorAndExclude> page, DeviceRule deviceRule){
        Page<DeviceRule> deviceRulePage = selectAll(page, deviceRule);
        Map<String, SanydsCoreParamDef> map = orsDeviceCheckConfigService.getSanydsCoreParamDef();
        deviceRulePage.getRecords().forEach(item->{
            String mId = item.getModelId();
            String paramCode = item.getParamCode();
            item.setPromptStr(this.orsDeviceCheckConfigService.getParamNameModel(mId,paramCode,map));
        });
        return deviceRulePage;
    }


    /**
     * 分页查询所有数据
     *
     * @param page                 分页对象
     * @param deviceRule 查询实体
     * @return 所有数据
     */
    @ApiOperation("查询所有的设备剔除检查配置信息")
    @GetMapping
    public Page<DeviceRule> selectAll(Page<ModleIndicatorAndExclude> page, DeviceRule deviceRule) {
        List<String> devices = new ArrayList<>();
        if (StringUtils.isNotBlank(deviceRule.getNoRegisterDeviceList())) {
            devices = Arrays.asList(deviceRule.getNoRegisterDeviceList().split(","));
        }
        String deviceCode = deviceRule.getDeviceCode();
        if (StringUtils.isNotBlank(deviceCode)) {
            deviceCode = deviceCode.toUpperCase();
        }
        String assetId = deviceRule.getAssetId();
        if (StringUtils.isNotBlank(assetId)) {
            assetId = assetId.toUpperCase();
        }
        String modelId = deviceRule.getModelId();
        String finalDeviceCode = deviceCode;
        String finalAssetId = assetId;
        String finalModelId = modelId;
        List<String> finalDevices = devices;
        Page<DeviceRule> ruleByDeviceCode = this.orsDeviceCheckConfigService.findRuleByDeviceCode(
                page,
                Wrappers
                        .query()
                        .in(StringUtils.isNotBlank(deviceRule.getProductGroup()), "omd.product_group_code", StringUtils.isNotBlank(deviceRule.getProductGroup()) ? Arrays.asList(deviceRule.getProductGroup().split(",")) : null)
                        .in(StringUtils.isNotBlank(deviceRule.getOrgName()), "omd.division_code", StringUtils.isNotBlank(deviceRule.getOrgName()) ? Arrays.asList(deviceRule.getOrgName().split(",")) : null)
                        .in(StringUtils.isNotBlank(deviceRule.getCountry()), "obdi.country_code", StringUtils.isNotBlank(deviceRule.getCountry()) ? Arrays.asList(deviceRule.getCountry().split(",")) : null)
                        .in(StringUtils.isNotBlank(deviceRule.getCountryRegionCode()), "ocr.country_region_code", StringUtils.isNotBlank(deviceRule.getCountryRegionCode()) ? Arrays.asList(deviceRule.getCountryRegionCode().split(",")) : null)
                        .like(StringUtils.isNotBlank(deviceRule.getAgentName()), "obdi.agent", deviceRule.getAgentName())
                        .like(StringUtils.isNotBlank(deviceRule.getUserName()), "obdi.customer_name", deviceRule.getUserName())
                        .and(StringUtils.isNotBlank(deviceRule.getDeviceCode()),
                                w -> w
                                        .like("obdi.device_code", finalDeviceCode)
                                        .or()
                                        .in(StringUtils.isNotBlank(deviceRule.getDeviceCode()), "obdi.device_code", deviceRule.getDeviceCode().split(","))
                        )
                        .and(StringUtils.isNoneBlank(deviceRule.getAssetId()),
                                a -> a
                                        .like(StringUtils.isNoneBlank(deviceRule.getAssetId()), "T1.asset_id", finalAssetId)
                                        .or()
                                        .in(StringUtils.isNoneBlank(deviceRule.getAssetId()), "T1.asset_id", deviceRule.getAssetId().split(","))
                        )
                        .like(StringUtils.isNotBlank(deviceRule.getUuid()), "obdi.thing_id", deviceRule.getUuid())
                        .in(StringUtils.isNotBlank(deviceRule.getModelId()), "obdi.model_id", StringUtils.isNotBlank(finalModelId) ? Arrays.asList(finalModelId.split(",")) : null)
                        .in(StringUtils.isNotBlank(deviceRule.getCreateUser()), "T1.create_user", StringUtils.isNotBlank(deviceRule.getCreateUser()) ? Arrays.asList(deviceRule.getCreateUser().split(",")) : null)

                        .in(StringUtils.isNotBlank(deviceRule.getPropertyName()), "T1.param_code", StringUtils.isNotBlank(deviceRule.getPropertyName()) ? Arrays.stream(deviceRule.getPropertyName().split(","))
                                .map(String::trim)
                                .mapToInt(Integer::parseInt)
                                .boxed()
                                .collect(Collectors.toList()) : null)
                        .like(StringUtils.isNotBlank(deviceRule.getExcludeType()), "T1.exclude_type", deviceRule.getExcludeType())
                        .in(StringUtils.isNotBlank(deviceRule.getNoRegisterDeviceList()), "T1.device_code", finalDevices)
                        .orderByDesc("T1.create_time")
        );
        return ruleByDeviceCode;
    }

    /**
     * 查找某个设备的整机剔除信息
     * @param deviceCode 设备编码
     * @return
     */
    @ApiOperation("查找某个设备的整机剔除信息")
    @GetMapping("/whole")
    public OrsDeviceCheckConfig findWhole(String deviceCode){
        return orsDeviceCheckConfigService.getOne(
                new QueryWrapper<OrsDeviceCheckConfig>()
                        .lambda()
                        .eq(OrsDeviceCheckConfig::getExcludeType, "WHOLE")
                        .eq(OrsDeviceCheckConfig::getDeviceCode, deviceCode)
        );
    }

    /**
     * 新增数据
     *
     * @param orsDeviceCheckConfig 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增或修改剔除信息")
    @PostMapping
    public String insert(@RequestBody OrsDeviceCheckConfig orsDeviceCheckConfig) {
        if (null ==orsDeviceCheckConfig.getExcludeTypes() || orsDeviceCheckConfig.getExcludeTypes().isEmpty()) {
            throw new IllegalArgumentException("请添加剔除内容");
        }

        List<BaseDeviceInfo> ledgeres = baseDeviceInfoMapper.selectList(new QueryWrapper<BaseDeviceInfo>().lambda().eq(BaseDeviceInfo::getDeviceCode, orsDeviceCheckConfig.getDeviceCode()));
        if (ledgeres.isEmpty()) {
            throw new IllegalArgumentException("设备不存在");
        }
        // 判断是否已有整机剔除
        OrsDeviceCheckConfig whole = orsDeviceCheckConfigService.getOne(
                new QueryWrapper<OrsDeviceCheckConfig>()
                        .lambda()
                        .eq(OrsDeviceCheckConfig::getDeviceCode, orsDeviceCheckConfig.getDeviceCode())
                        .eq(OrsDeviceCheckConfig::getExcludeType, "WHOLE")
        );

        if (null != whole) {
            throw new IllegalArgumentException("已有整机剔除，无需再添加剔除");
        }

        List<OrsDeviceCheckConfig> data = new ArrayList<>();

        List<String> list = orsDeviceCheckConfig.getExcludeTypes().stream().map(OrsDeviceCheckConfig.MuiltiExcludType::getExcludeType).collect(Collectors.toList());
        if (list.size()> 1 && list.contains("WHOLE")) {
            throw new IllegalArgumentException("属性和整机剔除不可同时添加");
        }

        // 判断是否
        List<OrsDeviceCheckConfig> configs = orsDeviceCheckConfigService.list(
                new QueryWrapper<OrsDeviceCheckConfig>()
                        .lambda()
                        .eq(OrsDeviceCheckConfig::getDeviceCode, orsDeviceCheckConfig.getDeviceCode())
                        .in(OrsDeviceCheckConfig::getExcludeType, list)
        );

        // 判断是否重复添加
        if (!configs.isEmpty()) {
            for (OrsDeviceCheckConfig config : configs) {
                if (list.contains(config.getExcludeType())) {
                    throw new IllegalArgumentException("不可重复添加");
                }
            }
            List<String> collect = configs.stream().map(OrsDeviceCheckConfig::getExcludeType).collect(Collectors.toList());
            if (!collect.contains("WHOLE") && list.contains("WHOLE")){
                throw new IllegalArgumentException("已有属性剔除，不能再添加整机剔除");
            }
        }

        // 对满足条件的数据 入库
        for (BaseDeviceInfo ledgere : ledgeres) {
            OrsDeviceCheckConfig odc;
            for (OrsDeviceCheckConfig.MuiltiExcludType type : orsDeviceCheckConfig.getExcludeTypes()) {
                odc = new OrsDeviceCheckConfig();
                BeanUtils.copyProperties(orsDeviceCheckConfig, odc);
                odc.setExcludeType(type.getExcludeType());
                odc.setParamCode(type.getParamCode());
                if ("WHOLE".equals(type.getExcludeType())) {
                    odc.setParamCode(8601);
                }
                odc.setPropertyName(type.getPropertyName());
                odc.setParamName(type.getPropertyName());
                odc.setCreateTime(new Date());
                odc.setCreateUser(ShiroUtil.getCurrentUser().getUsername());
                odc.setAssetId(ledgere.getAssetId());
                odc.setDeviceName(ledgere.getDeviceName());
                data.add(odc);
            }
        }
        this.orsDeviceCheckConfigService.saveBatch(data);
        return "新增成功";
    }

    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除某一项剔除信息")
    @DeleteMapping
    public boolean delete(@RequestParam("id") Long id) {
        String createUser =  ShiroUtil.getCurrentUser().getUsername();
        this.orsDeviceCheckConfigService.insertHistoryById(id,createUser);
        return this.orsDeviceCheckConfigService.removeById(id);
    }

    @ApiOperation("下载导入模版")
    @GetMapping("/template")
    public void downloadTempalte(HttpServletResponse response){
        ExportParams exportParams = new ExportParams();
        exportParams.setCreateHeadRows(true);
        List<OrsDeviceCheckConfigImportExcel> data  = new ArrayList<>();
        OrsDeviceCheckConfigImportExcel excel = new OrsDeviceCheckConfigImportExcel();
        excel.setDeviceCode("必填项，请输入完整的设备编号");
        excel.setPropertyName("必填项，请根据设备所属物模型输入属性名称，若整机剔除则输入：整机");
        excel.setExcludeResean("必填项, 请输入剔除原因，不超过200字符");
        data.add(excel);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, OrsDeviceCheckConfigImportExcel.class, data);
        if (workbook != null) {
            EasyPoiUtils.downLoadExcel("设备剔除检查配置.xlsx", response, workbook);
        }
    }


    @ApiOperation("导入设备检查项-删除处理")
    @PostMapping("/importDelete")
    public String importCheckConfigDelete(@RequestParam("file") MultipartFile file, HttpServletResponse response){
        log.info("导入设备检查项-删除处理 开始");
        if (file.isEmpty()) {
            throw new IllegalArgumentException("请上传文件");
        }
        String createUser = ShiroUtil.getCurrentUser().getUsername();
        try {
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            List<OrsDeviceCheckConfigImportExcel> objects = ExcelImportUtil.importExcel(file.getInputStream(), OrsDeviceCheckConfigImportExcel.class, params);

            log.info("同步数量:{}",objects.size());
            objects = objects.stream()
                    .filter(obj -> StringUtils.isNotEmpty(obj.getDeviceCode()) ||
                            StringUtils.isNotEmpty(obj.getPropertyName()) ||
                            StringUtils.isNotEmpty(obj.getExcludeResean()))
                    .collect(Collectors.toList());

            List<List<OrsDeviceCheckConfigImportExcel>> split = CollectionUtil.split(objects, MIN_IMPORT);
            log.info("分批后的数量:{}",split.size());

            if(CollUtil.isNotEmpty(split)){
                // 创建固定大小的线程池
                ExecutorService executorService = Executors.newFixedThreadPool(5);

                // 使用CountDownLatch来等待所有任务完成
                CountDownLatch latch = new CountDownLatch(split.size());

                for (List<OrsDeviceCheckConfigImportExcel> item : split) {
                    executorService.submit(() -> {
                        try {
                            orsDeviceCheckConfigService.importDeleteInsertHistroy(item,createUser);
                            orsDeviceCheckConfigService.importDelete(item);
                        } finally {
                            latch.countDown();
                        }
                    });
                }

                // 等待所有任务完成
                latch.await();
                executorService.shutdown();
            }else{
                throw new IllegalArgumentException("请上传文件内容");
            }
        } catch (Exception e) {
            log.error("导入设备检查项-删除处理 异常", e);
            throw new IllegalStateException("文件解析错误");
        }
        log.info("导入设备检查项-删除处理 结束");
        return "导入删除成功";
    }





    @ApiOperation("导入设备检查项")
    @PostMapping("/import")
    @Transactional(rollbackFor = Exception.class)
    public String importCheckConfig(@RequestParam("file") MultipartFile file, HttpServletResponse response){
        log.info("导入设备检查项开始");
        if (file.isEmpty()) {
            throw new IllegalArgumentException("请上传文件");
        }
        List<UnImportDataExcel> unImportDataExcels = Collections.synchronizedList(new  ArrayList<>());
        List<OrsDeviceCheckConfig> list = Collections.synchronizedList(new  ArrayList<>());
        try {
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            List<OrsDeviceCheckConfigImportExcel> objects = ExcelImportUtil.importExcel(file.getInputStream(), OrsDeviceCheckConfigImportExcel.class, params);
            if (objects.isEmpty()) {
                throw new IllegalArgumentException("请上传文件类容");
            }
            objects = objects.stream()
                .filter(obj -> StringUtils.isNotEmpty(obj.getDeviceCode()) ||
                    StringUtils.isNotEmpty(obj.getPropertyName()) ||
                    StringUtils.isNotEmpty(obj.getExcludeResean()))
                .collect(Collectors.toList());
            // 获得所有的属性配置
            List<OrsModelPropertiesConfig> configs = orsModelPropertiesConfigMapper.selectList(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .select(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
                            .groupBy(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
            );

            List<List<OrsDeviceCheckConfigImportExcel>> split = CollectionUtil.split(objects, MAX_IMPORT);

            //ExecutorService executorService = Executors.newFixedThreadPool(split.size());
            List<CompletableFuture<String>> threads = new ArrayList<>();

            // 获得当前登录用户
            String currentUserName = ShiroUtil.getCurrentUser().getUsername();

            // 开始导入 这里为了提高导入的效率，将数据拆分后使用多线程导入
            for (List<OrsDeviceCheckConfigImportExcel> excels : split) {
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    // 这里为了避免使用复杂的多线程事务，导致数据不一致的问题，这里只处理数据，不对数据入库，待所有数据处理完成无误后，统一入库，便于事务管理
                    handleImport(excels, list, unImportDataExcels, configs, currentUserName);
                    return "success";
                },asyncExecutor);
                threads.add(future);
            }
            //allOf()等待所有线程执行完毕
            CompletableFuture<Void> allFuture = CompletableFuture.allOf(threads.toArray(new CompletableFuture[threads.size()]));
            allFuture.join();
        } catch (Exception e) {
            //e.printStackTrace();
            log.error("导入设备检查项异常", e);
            throw new IllegalStateException("文件解析错误");
        }

        // 如果有处理不成功的数据，以表格的形式返回给用户数据明细
        if (!unImportDataExcels.isEmpty()) {
            ExportParams exportParams = new ExportParams("设备剔除检查导入失败项", "导入失败项");
            exportParams.setCreateHeadRows(true);
            log.info("导入设备检查项失败项数量:{}", unImportDataExcels.size());
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, UnImportDataExcel.class, unImportDataExcels);
            if (workbook != null) {
                EasyPoiUtils.downLoadExcel("设备剔除检查导入失败项.xlsx", response, workbook);
            }
            // return null;
        }
        // 数据统一入库
        if(!list.isEmpty()) {
            log.info("数据统一入库saveBatch开始");
            orsDeviceCheckConfigService.saveBatch(list);
            log.info("数据统一入库saveBatch结束");
        }
        log.info("导入设备检查项结束");
        return "导入成功";
    }

    private void handleImport(List<OrsDeviceCheckConfigImportExcel> objects,
                              List<OrsDeviceCheckConfig> list,
                              List<UnImportDataExcel> unImportDataExcels,
                              List<OrsModelPropertiesConfig> configs,
                              String currentUserName){
        for (int i = 0; i < objects.size(); i++) {

            OrsDeviceCheckConfigImportExcel x = objects.get(i);
            if (StringUtils.isBlank(x.getDeviceCode()) ) {
                unImportDataExcels.add(buildUnimportData(x, "关键字不能为空或不存在"));
                continue;
            }

            List<BaseDeviceInfo> infos = baseDeviceInfoMapper.selectList(
                    new QueryWrapper<BaseDeviceInfo>()
                            .lambda()
                            .eq(BaseDeviceInfo::getDeviceCode, x.getDeviceCode())
                            .or()
                            .eq(BaseDeviceInfo::getAssetId, x.getDeviceCode())
                            .or()
                            .eq(BaseDeviceInfo::getDeviceName, x.getDeviceCode())
            );
            if (null == infos || infos.isEmpty()) {
                unImportDataExcels.add(buildUnimportData(x, "关键字不能为空或不存在"));
                continue;
            }

            String excludType;
            if (!x.getPropertyName().equals("整机")) {
                OrsModelPropertiesConfig config = configs.stream().filter(c -> c.getPropertyName().equals(x.getPropertyName())).findFirst().orElse(null);
                if (null == config) {
                    unImportDataExcels.add(buildUnimportData(x, "剔除类型不能为空或不存在"));
                    continue;
                }
                excludType = config.getProperty();
            } else {
                excludType = "WHOLE";
            }

            // 判断是否已有整机剔除
            OrsDeviceCheckConfig whole = orsDeviceCheckConfigService.getOne(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .eq(OrsDeviceCheckConfig::getDeviceCode, x.getDeviceCode())
                            .eq(OrsDeviceCheckConfig::getExcludeType, "WHOLE")
            );
            if (null != whole) {
                if ("WHOLE".equals(excludType)) {
                    unImportDataExcels.add(buildUnimportData(x, "已有整机剔除，不可重复添加"));
                    continue;
                }else{
                    unImportDataExcels.add(buildUnimportData(x, "已有整机剔除，不可再添加属性剔除"));
                    continue;
                }
            }else{
                // 判断该code是否已存在
                OrsDeviceCheckConfig code = orsDeviceCheckConfigService.getOne(
                        new QueryWrapper<OrsDeviceCheckConfig>()
                                .lambda()
                                .eq(OrsDeviceCheckConfig::getDeviceCode, x.getDeviceCode())
                                .eq(OrsDeviceCheckConfig::getExcludeType, excludType)
                );
                if (null != code) {
                    unImportDataExcels.add(buildUnimportData(x, code.getParamName()+"已存在，不可再重复添加"));
                    continue;
                }
            }

            List<OrsDeviceCheckConfig> threadSafeList = new CopyOnWriteArrayList<>(list);
            OrsDeviceCheckConfig config = threadSafeList.stream().filter(c -> c.getDeviceCode().equals(x.getDeviceCode()) && c.getExcludeType().equals(excludType)).findFirst().orElse(null);
            if (null != config) {
                unImportDataExcels.add(buildUnimportData(x, "导入数据中已存在该关键字重复的剔除类型"));
                continue;
            }

            // 设备台账根据asset_id去重
            infos = infos.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(
                                    () -> new TreeSet<>(Comparator.comparing(BaseDeviceInfo::getAssetId))
                            )
                            , ArrayList::new)
            );

            // 数据处理
            for (BaseDeviceInfo info : infos) {
                OrsModelPropertiesConfig modelConfig = orsModelPropertiesConfigMapper.selectOne(
                        new QueryWrapper<OrsModelPropertiesConfig>()
                                .lambda()
                                .eq(OrsModelPropertiesConfig::getModelId, info.getModelId())
                                .eq(OrsModelPropertiesConfig::getProperty, excludType)
                );
                if (StringUtils.isBlank(excludType) || (!"WHOLE".equals(excludType) && null == modelConfig)) {
                    unImportDataExcels.add(buildUnimportData(x, "剔除类型不能为空或不存在"));
                    continue;
                }
                if (StringUtils.isBlank(x.getExcludeResean()) || x.getExcludeResean().length()>200){
                    unImportDataExcels.add(buildUnimportData(x, "剔除原因不能为空或输入超长"));
                    continue;
                }

                // 数据组装
                OrsDeviceCheckConfig newdata = getOrsDeviceCheckConfig(x, info, currentUserName);
                newdata.setAssetId(info.getAssetId());
                newdata.setDeviceName(info.getDeviceName());

                if (!"WHOLE".equals(excludType)) {
                    newdata.setExcludeType(modelConfig.getProperty());
                    newdata.setParamCode(modelConfig.getParamCode());
                    newdata.setPropertyName(modelConfig.getPropertyName());
                }

                if ("WHOLE".equals(excludType)) {
                    newdata.setExcludeType("WHOLE");
                    newdata.setParamCode(8601);
                    newdata.setPropertyName("整机");
                }
                newdata.setParamName(newdata.getPropertyName());
                list.add(newdata);
            }

        }
    }

    @NotNull
    private static OrsDeviceCheckConfig getOrsDeviceCheckConfig(OrsDeviceCheckConfigImportExcel x,
                                                                BaseDeviceInfo ledger,
                                                                String currentUserName) {
        OrsDeviceCheckConfig config = new OrsDeviceCheckConfig();
        config.setUuid(ledger.getThingId());
        config.setAssetId(ledger.getAssetId());
        config.setDelFlag(0);
        config.setModelId(ledger.getModelId());
        config.setDeviceCode(x.getDeviceCode());
        config.setExcludeResean(x.getExcludeResean());
        config.setCreateTime(new Date());
        config.setCreateUser(currentUserName);
//        config.setCreateUser("sys");
        config.setUpdateUser(config.getCreateUser());
        config.setUpdateTime(config.getCreateTime());
        return config;
    }

    // @ApiOperation("下载数据")
    // @GetMapping("/download")
    // public void download(DeviceRule deviceRule, HttpServletResponse response){
    //     Page<DeviceRule> rulePage = selectAll(new Page<>(1, 99999999999L), deviceRule);
    //     List<OrsDeviceCheckConfigExportExcel> exportExcels = new ArrayList<>();
    //     AtomicInteger ai = new AtomicInteger(1);
    //     rulePage.getRecords().forEach(x->{
    //         OrsDeviceCheckConfigExportExcel excel = new OrsDeviceCheckConfigExportExcel();
    //         BeanUtils.copyProperties(x, excel);
    //         excel.setCtime(sdf.format(x.getCreateTime()));
    //         excel.setSort(ai.getAndIncrement());
    //         exportExcels.add(excel);
    //     });
    //     ExportParams exportParams = new ExportParams("设备剔除检查", "设备剔除检查");
    //     exportParams.setCreateHeadRows(true);
    //     Workbook workbook = ExcelExportUtil.exportExcel(exportParams, OrsDeviceCheckConfigExportExcel.class, exportExcels);
    //     if (workbook != null) {
    //         EasyPoiUtils.downLoadExcel("设备剔除检查.xlsx", response, workbook);
    //     }
    // }

    @ApiOperation("下载数据")
    @GetMapping("/download")
    public void download(DeviceRule deviceRule, HttpServletResponse response){

        // if(StringUtils.isBlank(deviceRule.getDeviceCode())){
        //     throw new EasyException("只支持指定设备编号、最多500台、设备数据异常明细导出");
        // }

        AtomicInteger ai = new AtomicInteger(1);
        log.info("设备剔除检查导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        Page<DeviceRule> countPage = selectAll(new Page<>(1, 1), deviceRule);
        int totalCount = (int) countPage.getTotal();
        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }
        // 20250303限制调整：仅限制数据量＞5w时不支持导出
        if(totalCount > 50000){
            throw new EasyException("列表数据量过大，请通过筛选减少列表数据进行导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("设备剔除检查", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 10 的线程池
            // ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备剔除检查" + (sheetIndex + 1))
                            .head(OrsDeviceCheckConfigExportExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceRule> page = selectAll(new Page<>(currentPage, writeDataRows), deviceRule);
                            List<OrsDeviceCheckConfigExportExcel> data = new ArrayList<>();

                            page.getRecords().forEach(x->{
                                OrsDeviceCheckConfigExportExcel excel = new OrsDeviceCheckConfigExportExcel();
                                BeanUtils.copyProperties(x, excel);
                                excel.setCtime(sdf.format(x.getCreateTime()));
                                excel.setSort(ai.getAndIncrement());
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     log.info("关闭线程池");
                //     executorService.shutdown();
                // }
            }
            log.info("设备剔除检查导出结束");
            log.info("设备剔除检查导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出设备剔除检查失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }
    }

    private UnImportDataExcel buildUnimportData(OrsDeviceCheckConfigImportExcel x, String errorMsg) {
        return UnImportDataExcel
                .builder()
                .code(x.getDeviceCode())
                .name(x.getPropertyName())
                .detail(x.getExcludeResean())
                .resean(errorMsg)
                .build();
    }

}

