package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rc.admin.ors.quality.entity.OtDeviceAll;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;

import javax.servlet.http.HttpServletResponse;

public interface OtDeviceAllService extends IService<OtDeviceAll> {


    void exportExcelOtDeviceAll(HttpServletResponse response, OtDeviceAllReq req);



    void exportExcelOtDeviceAllReport(HttpServletResponse response, OtDeviceAllReq req);
}
