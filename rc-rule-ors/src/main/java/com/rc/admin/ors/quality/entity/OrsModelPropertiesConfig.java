package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 物模型与属性检查配置(OrsDevicePropertiesConfig)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-27 10:11:14
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_model_properties_config", description = "物模型与属性检查配置")
public class OrsModelPropertiesConfig extends Model<OrsModelPropertiesConfig> {

    @TableId(type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(name = "dataSource", value = "数据来源")
    @TableField(value = "data_source")
    private String dataSource;

    @ApiModelProperty(name = "tenantId", value = "租户ID")
    @TableField(value = "tenant_id")
    private String tenantId;

    @ApiModelProperty(name = "modelId", value = "物模型ID")
    @TableField(value = "model_id")
    private String modelId;

    @ApiModelProperty(name = "property", value = "属性")
    @TableField(value = "property")
    private String property;

    @ApiModelProperty(name = "propertyName", value = "属性名称")
    @TableField(value = "property_name")
    private String propertyName;

    @ApiModelProperty(name = "dataType", value = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    @ApiModelProperty(name = "propertySource", value = "属性来源")
    @TableField(value = "property_source")
    private String propertySource;

    @ApiModelProperty(name = "checkRule", value = "检查规则, groove表达式")
    @TableField(value = "check_rule")
    private String checkRule;

    @ApiModelProperty(name = "paramCode", value = "属性编码")
    @TableField(value = "param_code")
    private Integer paramCode;

    private String paramName;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

