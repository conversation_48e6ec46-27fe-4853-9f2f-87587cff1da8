<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrcTaskFileMapper">


<!--auto generated by MybatisCodeHelper on 2024-03-16-->
    <select id="findByAll" resultType="com.rc.admin.ors.quality.entity.OrcTaskFile">
        select
        "task_id", "exec_sql", "exec_error", "start_time", "end_time", "result_size", "task_status", "create_time", "result_file_type"
        from dqm.orc_task_file
        <where>
            <if test="taskId != null">
                and task_id=#{taskId}
            </if>
            <if test="execSql != null">
                and exec_sql=#{execSql}
            </if>
            <if test="execError != null">
                and exec_error=#{execError}
            </if>
            <if test="resultFileType != null">
                and result_file_type=#{resultFileType}
            </if>
            <if test="startTime != null">
                and start_time=#{startTime}
            </if>
            <if test="endTime != null">
                and end_time=#{endTime}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime}
            </if>
            <if test="resultSize != null">
                and result_size=#{resultSize}
            </if>
            <if test="taskStatus != null">
                and task_status=#{taskStatus}
            </if>
        </where>
         order by create_time desc
    </select>
</mapper>