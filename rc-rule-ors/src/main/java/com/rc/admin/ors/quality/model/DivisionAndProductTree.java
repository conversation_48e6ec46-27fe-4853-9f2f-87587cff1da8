package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/11 14:25
 * @describe
 */
@Getter
@Setter
public class DivisionAndProductTree {

    @ApiModelProperty(name = "division", value = "事业部")
    private String division;



    @ApiModelProperty(name = "divisionCode", value = "事业部编号")
    private String divisionCode;

    private List<ProductGroup> productGroups;

    @Getter
    @Setter
    public static class ProductGroup{
        @ApiModelProperty(name = "productGroup", value = "产品组")
        private String productGroup;

        @ApiModelProperty(name = "productGroupCode", value = "产品组编号")
        private String productGroupCode;
    }
}
