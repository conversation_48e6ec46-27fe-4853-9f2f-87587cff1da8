<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.DeviceDataRuleMapper">


    <select id="findAbnormal" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataRuleExceptionsResp">
        with omd as (select * from dqm.ors_model_division omd
        <where>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND omd.division_code IN
                <foreach item="item" index="index" collection="req.sybbh.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND omd.product_group_code IN
                <foreach collection="req.zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>)
        select
        max(sybbh) AS sybbh,
        max(sybbhCode) AS sybbhCode,
        max(model_id) as model_id,
        max(country) as country,
        max(data_center_id) as data_center_id,
        device_name,
        max(rc_asset_id) as rc_asset_id,
        check_rule_code as check_rule_code,
        max(check_rule_name) as check_rule_name,
        max(abnormal_phenomenon_code) as abnormal_phenomenon_code,
        max(abnormal_phenomenon_name) as abnormal_phenomenon_name,
        max(stat_date) as stat_date,
        sum(abnormalData) as abnormalData,
        max(device_code) as device_code,
        max(thing_id) as thing_id,
        max(model_name) as model_name,
        max(zehdSpartdesc) AS zehdSpartdesc,
        max(zehdSpart) AS zehdSpart,
        json_agg(
        json_build_object(
        'abnormalCode', abnormal_phenomenon_code,
        'abnormalName',split_part(abnormal_phenomenon_name, '，', 2),
        'abnormalNameAndCount',concat(split_part(abnormal_phenomenon_name, '，', 2), '（', abnormalData, '）'),
        'abnormalCount', abnormalData)
        ) AS abnormalDetails
        from
        (
        SELECT
        max(omd.division_name) AS sybbh,
        max(omd.division_code) AS sybbhCode,
        max(T2.model_id) as model_id,
        max(T2.country) as country,
        max(T1.data_center_id) as data_center_id,
        T1.device_name,
        max(T2.rc_asset_id) as rc_asset_id,
        T1.check_rule_code as check_rule_code,
        max(check_rule.name) as check_rule_name,
        T1.abnormal_phenomenon_code as abnormal_phenomenon_code,
        max(abnormal_phenomenon.name) as abnormal_phenomenon_name,
        max(T1.stat_date) as stat_date,
        count(1) as abnormalData,
        max(T2.device_code) as device_code,
        max(T2.thing_id) as thing_id,
        max(T2.model_name) as model_name,
        max(omd.product_group_name) AS zehdSpartdesc,
        max(omd.product_group_code) AS zehdSpart
        FROM
        dqm.ors_device_data_rule_abnormal_detail_day T1
        INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name
        INNER JOIN omd ON omd.model_id = T2.model_id
        left join sys_dict check_rule on check_rule.code = T1.check_rule_code and check_rule.dict_type = 'checkRuleType'
        left join sys_dict abnormal_phenomenon on abnormal_phenomenon.code = T1.abnormal_phenomenon_code and
        abnormal_phenomenon.dict_type = 'abnormalPhenomenonType'
        <where>

            <if test="req.dataCenterId != null">
                AND T1.data_center_id = #{req.dataCenterId}
            </if>
            <if test="req.deviceName != null  and req.deviceName != ''">
                AND (
                T1.device_name = #{req.deviceName}
                OR
                T1.device_name IN
                <foreach collection="req.deviceName.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.deviceCode != null  and req.deviceCode != ''">
                AND (
                T2.device_code LIKE CONCAT('%', #{req.deviceCode}, '%')
                OR
                T2.device_code IN
                <foreach collection="req.deviceCode.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.country != null and req.country != ''">
                AND T2.country_code IN
                <foreach item="item" index="index" collection="req.country.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.modelId != null and req.modelId != ''">
                AND T2.model_id IN
                <foreach item="item" index="index" collection="req.modelId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.rcAssetId != null and req.rcAssetId != ''">
                AND T2.rc_asset_id IN
                <foreach item="item" index="index" collection="req.rcAssetId.split(',')" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.checkRuleCode != null and req.checkRuleCode != ''">
                AND T1.check_rule_code IN
                <foreach item="item" index="index" collection="req.checkRuleCode.split(',')" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalPhenomenonCode != null and req.abnormalPhenomenonCode != ''">
                AND T1.abnormal_phenomenon_code IN
                <foreach item="item" index="index" collection="req.abnormalPhenomenonCode.split(',')" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.thingId != null and req.thingId != ''">
                AND T2.thing_id = #{req.thingId}
            </if>

            <if test="req.modelName != null and req.modelName != ''">
                AND T2.model_name in
                <foreach collection="req.modelName.split(',')" item="item" index="index" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
                AND T2.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387',
                'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND T1.stat_date &gt;= #{req.startTime}::DATE
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND T1.stat_date &lt;= #{req.endTime}::DATE
            </if>
        </where>
        group by T1.device_name,T1.check_rule_code,T1.abnormal_phenomenon_code
        )aa
        group by aa.device_name,aa.check_rule_code
        order by max(aa.stat_date) desc,aa.device_name desc,aa.check_rule_code desc
    </select>




    <select id="findAbnormalDetail" resultType="com.rc.admin.ors.quality.model.DqmDeviceDataRuleDetailExceptionsResp">
        with omd as (select * from dqm.ors_model_division omd
        <where>
            <if test="req.sybbh != null and req.sybbh != ''">
                AND omd.division_code IN
                <foreach item="item" index="index" collection="req.sybbh.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.zehdSpartdesc != null and req.zehdSpartdesc != ''">
                AND omd.product_group_code IN
                <foreach collection="req.zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>)
        SELECT
        omd.division_name AS sybbh,
        omd.division_code AS sybbhCode,
        T2.model_id,
        T1.data_center_id,
        T1.device_name,
        T2.rc_asset_id,
        T1.check_rule_code,
        check_rule.name as check_rule_name,
        T1.abnormal_data,
        T1.abnormal_phenomenon_code,
        abnormal_phenomenon.name as abnormal_phenomenon_name,
        T1.stat_date,
        T2.device_code,
        T2.thing_id,
        T2.country,
        T2.model_name,
        omd.product_group_name AS zehdSpartdesc,
        omd.product_group_code AS zehdSpart
        FROM
        dqm.ors_device_data_rule_abnormal_detail_day T1
        INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name
        INNER JOIN omd ON omd.model_id = T2.model_id
        left join sys_dict check_rule on check_rule.code = T1.check_rule_code and check_rule.dict_type = 'checkRuleType'
        left join sys_dict abnormal_phenomenon on abnormal_phenomenon.code = T1.abnormal_phenomenon_code and abnormal_phenomenon.dict_type = 'abnormalPhenomenonType'
        <where>
            <if test="req.dataCenterId != null">
                AND T1.data_center_id = #{req.dataCenterId}
            </if>
            <if test="req.deviceName != null  and req.deviceName != ''">
                AND (
                T1.device_name = #{req.deviceName}
                OR
                T1.device_name IN
                <foreach collection="req.deviceName.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.deviceCode != null  and req.deviceCode != ''">
                AND (
                T2.device_code LIKE CONCAT('%', #{req.deviceCode}, '%')
                OR
                T2.device_code IN
                <foreach collection="req.deviceCode.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.country != null and req.country != ''">
                AND T2.country_code IN
                <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.modelId != null and req.modelId != ''">
                AND T2.model_id IN
                <foreach item="item" index="index" collection="req.modelId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.rcAssetId != null and req.rcAssetId != ''">
                AND T2.rc_asset_id IN
                <foreach item="item" index="index" collection="req.rcAssetId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.checkRuleCode != null and req.checkRuleCode != ''">
                AND T1.check_rule_code IN
                <foreach item="item" index="index" collection="req.checkRuleCode.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.abnormalPhenomenonCode != null and req.abnormalPhenomenonCode != ''">
                AND T1.abnormal_phenomenon_code IN
                <foreach item="item" index="index" collection="req.abnormalPhenomenonCode.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.thingId != null and req.thingId != ''">
                AND T2.thing_id = #{req.thingId}
            </if>

            <if test="req.modelName != null and req.modelName != ''">
                AND T2.model_name in
                <foreach collection="req.modelName.split(',')"  item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.hasHuaXin != null and req.hasHuaXin == '0'.toString()">
                AND T2.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND T1.stat_date &gt;= #{req.startTime}::DATE
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND T1.stat_date &lt;= #{req.endTime}::DATE
            </if>
        </where>
            order by T1.stat_date desc
    </select>


</mapper>
