package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rc.admin.ors.quality.entity.OrsDeviceInfoRelation;

import java.text.ParseException;

/**
 * 设备基础信息(OrsDeviceInfoRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-31 16:00:20
 */
public interface OrsDeviceInfoRelationService extends IService<OrsDeviceInfoRelation> {

    /**
     * 同步ML数据
     * @throws ParseException
     */
    void syncHistoryDevice() throws ParseException;

}

