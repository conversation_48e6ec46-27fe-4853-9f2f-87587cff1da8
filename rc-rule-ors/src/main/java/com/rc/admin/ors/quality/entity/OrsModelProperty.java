package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@TableName(value = "ors_iot_model_property")
@ApiModel(value = "物模型属性", description = "ors物模型属性表")
public class OrsModelProperty {

        @ApiModelProperty("物模型主键")
        private String modelId;

        @ApiModelProperty("属性名")
        private String name;

        @ApiModelProperty("属性显示名称")
        private String displayName;

        @ApiModelProperty("属性类型")
        private String propertyType;

        @ApiModelProperty("属性存储策略")
        private String persistStrategy;

        @ApiModelProperty("属性存储周期")
        private Integer period;

        @ApiModelProperty("属性读写权限")
        private String privilege;

        @ApiModelProperty("属性为Number类型时的精度")
        private Integer fixed;

        @ApiModelProperty("属性创建时间")
        private Date createdAt;

        @ApiModelProperty("表达式类型")
        private String expressionType;

        @ApiModelProperty("表达式")
        private String expression;

        @ApiModelProperty("来源属性")
        private String fromProperty;

        @ApiModelProperty("window窗口大小")
        private Integer windowSizeMills;

        @ApiModelProperty("window窗口滑动步长")
        private Integer windowStepMills;

        @ApiModelProperty("允许等待时间，单位毫秒")
        private Integer windowAllowedLatenessMills;

        @ApiModelProperty("表达式优先级，为大于等于零的整数")
        private Integer priority;

        @ApiModelProperty("表达式是否在window后计算")
        private Boolean executeAfterWindow;

        @ApiModelProperty("表达式触发规则")
        private String triggerStrategy;

        @ApiModelProperty("是否计算online工况")
        private Boolean useOnlineMock;

        @ApiModelProperty("表达式执行模式")
        private String invokeMode;

        @ApiModelProperty("是否跳过属性输出")
        private Boolean skipOutput;

        @ApiModelProperty("是否禁用")
        private Boolean disabled;
}
