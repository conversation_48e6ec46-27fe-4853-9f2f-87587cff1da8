package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/1 16:51
 * @describe
 */
@Getter
@Setter
public class DeviceQuestionExcel {

    @Excel(name = "事业部", width = 15)
    private String division;

    @Excel(name = "大区", width = 15)
    private String region;

    @Excel(name = "产品组", width = 15)
    private String productGroup;

    @Excel(name = "设备名称", width = 20)
    private String name;

    @Excel(name = "设备编号", width = 15)
    private String deviceNo;

    @Excel(name = "物联盒ID", width = 20)
    private String tboxId;

    @Excel(name = "跟进开始时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "检查项")
    private String checkItem;

    @Excel(name = "异常项")
    private String exceItem;

    @Excel(name = "优先级")
    private String quesLevel;

    @Excel(name = "当前处理状态")
    private String curStepName;

    @Excel(name = "当前责任人")
    private String userName;


}
