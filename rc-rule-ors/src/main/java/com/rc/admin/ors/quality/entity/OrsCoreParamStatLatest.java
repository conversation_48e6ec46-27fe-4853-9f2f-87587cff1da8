package com.rc.admin.ors.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 核心工况施工统计表-最新表(OrsCoreParamStatLatest)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-01 19:14:09
 */
@SuppressWarnings("serial")
@Getter
@Setter
@ApiModel(value = "ors_core_param_stat_latest", description = "核心工况施工统计表-最新表")
public class OrsCoreParamStatLatest extends Model<OrsCoreParamStatLatest> {

    private String tenantId;

    private String modelId;

    private String deviceName;

    private Integer paramCode;


    @ApiModelProperty(name = "paramValue", value = "属性值")
    @TableField(value = "param_value")
    private double paramValue;

    @ApiModelProperty(name = "isParamValueAbnormal", value = "属性值异常标识：true异常")
    @TableField(value = "is_param_value_abnormal")
    private Boolean isParamValueAbnormal;

    @ApiModelProperty(name = "paramValueEarliestTime", value = "属性值最早上报时间")
    @TableField(value = "param_value_earliest_time")
    private Date paramValueEarliestTime;

    @ApiModelProperty(name = "paramValueLatestTime", value = "属性值最新上报时间")
    @TableField(value = "param_value_latest_time")
    private Date paramValueLatestTime;

    @ApiModelProperty(name = "createTime", value = "数据生成时间")
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private String dictDesc;
}

