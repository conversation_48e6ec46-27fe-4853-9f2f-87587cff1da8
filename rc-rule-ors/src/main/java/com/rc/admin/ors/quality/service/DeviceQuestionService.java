package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rc.admin.ors.quality.entity.DeviceQuestion;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;

/**
 * 设备问题跟进(DeviceQuestion)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-30 16:55:32
 */
public interface DeviceQuestionService extends IService<DeviceQuestion> {

    /**
     * 问题跟进列表
     * @param page
     * @param wrapper
     * @param queryAccess
     * @return
     */
    Page<DeviceLedgerResp> questionList(Page<DeviceLedgerResp> page, Wrapper wrapper, String queryAccess);

    /**
     * 查询问题跟进关联的异常数据
     * @param page
     * @param questionId
     * @return
     */
    Page<OrsDeviceDataAbnormalDetail> findAbnormalDateByDate(Page<OrsDeviceDataAbnormalDetail> page, Long questionId);
}

