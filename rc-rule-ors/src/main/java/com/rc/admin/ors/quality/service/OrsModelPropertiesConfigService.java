package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;

/**
 * 物模型与属性检查配置(OrsDevicePropertiesConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-23 18:56:32
 */
public interface OrsModelPropertiesConfigService extends IService<OrsModelPropertiesConfig> {

    /**
     * 查询某个物模型配置的属性检查及其关联的指标与剔除信息
     * @param page 分页信息
     * @param deviceCode 设备编号
     * @param modelId 模型ID
     * @return
     */
    Page<ModleIndicatorAndExclude> findModleIndicatorAndExclude(Page<ModleIndicatorAndExclude> page, String deviceCode, String modelId, String assetId);

}

