package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigImportExcel;
import com.rc.admin.ors.quality.model.DeviceRule;
import com.rc.admin.ors.quality.model.ModleIndicatorAndExclude;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-23 15:41:55
 */
public interface OrsDeviceCheckConfigMapper extends BaseMapper<OrsDeviceCheckConfig> {

    /**
     * 查询设备的剔除信息
     * @param wrapper
     * @return
     */
    Page<DeviceRule> findRuleByDeviceCode(Page<ModleIndicatorAndExclude> page, @Param(Constants.WRAPPER) Wrapper wrapper);

    List<OrsDeviceCheckConfig> selectAll();

    /**
     * 查询某个物模型配置的属性检查及其关联的指标与剔除信息
     * @param page 分页信息
     * @param deviceCode 设备编号
     * @param modelId 模型ID
     * @return
     */
    Page<ModleIndicatorAndExclude> findModleIndicatorAndExclude(Page<ModleIndicatorAndExclude> page, @Param("deviceCode") String deviceCode, @Param("modelId") String modelId, @Param("assetId") String assetId);


    List<ModleIndicatorAndExclude> findModleIndicatorAndExcludeMultiple(@Param("deviceCode") String deviceCode, @Param("modelId") String modelId, @Param("assetId") String assetId);





    void insertHistory(@Param("list")List<OrsDeviceCheckConfigImportExcel> list,@Param("username")String username);



    void insertHistoryById(@Param("id")Long id,@Param("username")String username);



    /**
     * 导入批量删除处理
     * @param list
     */
    void batchDelete(@Param("list")List<OrsDeviceCheckConfigImportExcel> list);


}

