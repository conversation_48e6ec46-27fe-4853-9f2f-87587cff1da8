<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.DeviceParamReportLogMapper">
    <insert id="ors_device_param_report_day">

        DROP TABLE IF EXISTS dqm.ors_device_param_report_day_partition_${tbsuffix};

        CREATE TABLE dqm.ors_device_param_report_day_partition_${tbsuffix}
            PARTITION OF dqm.ors_device_param_report_day_partition
            FOR VALUES FROM ('${minDate}') TO ('${maxDate}');

        delete from "dqm"."ors_device_param_report_day" where stat_date=#{bizDate}::DATE;
        insert into "dqm"."ors_device_param_report_day"
            ("asset_id",
             "model_id" ,
             "device_code",
             "device_name",
             "division",
             "division_code",
             "product_group",
             "product_group_code",
             "region",
             "region_code",
             "crm_register",
             "active_statu",
             "install_type",
             "exce_flag",
             "exce_desc",
             "agent",
             "agent_code",
             "param_code",
             "param_name",
             "exclude_type",
             "exclude_param_code",
             "exclude_create_time" ,
             "exclude_state",
             "property_names",
             "param_codes",
             "null_properties",
             "null_param_codes",
             "abnormal_properties" ,
             "abnormal_param_codes" ,
             "report_param_codes" ,
             "abnormal_codes" ,
             "need_report_param_codes",
             "stat_date" )
        SELECT
            t1.asset_id,
            t1.model_id,
            t1.device_code,
            t1.device_name,
            t1.division,
            t1.division_code,
            t1.product_group,
            t1.product_group_code,
            t1.region,
            t1.region_code,
            t1.crm_register,
            t1.active_statu,
            t1.install_type,
            t1.exce_flag,
            t1.exce_desc,
            t1.agent,
            t1.agent_code,
            t2.param_code,
            t2.param_name,
            t3.exclude_type,
<!--            t3.param_code as exclude_param_code,-->
            t7.exclude_param_code as exclude_param_code,
<!--            t3.create_time exclude_create_time,-->
            NULL exclude_create_time,
            case when t5.exclude_type ='WHOLE' then 2
                 when t2.param_code = t3.param_code then 1
                 when t5.exclude_type IS NULL AND t3.param_code IS NULL then 0 end as exclude_state,   --  剔除0不剔除,1属性剔除,2整机剔除
            t4.property_names,                  --  所有异常属性名称（包含未上报+上报异常数据的）
            t4.param_codes,                     --  所有异常属性编号（包含未上报+上报异常数据的）
            t4.null_properties,                 --  未上报数据的属性名称
            t4.null_param_codes,                --  未上报数据的属性编号
            t4.abnormal_properties,             --  上报异常数据的属性名称
            t4.abnormal_param_codes,            --  上报异常数据的属性字段编号
            t4.report_param_codes,              --  上报了工况数据的属性
            t4.abnormal_codes,                  --  异常类型
            t6.need_report_param_codes,         --  必须要上报的属性字段
            #{bizDate}::DATE as stat_date
        FROM
            dqm.ors_base_device_info t1
                JOIN dqm.ors_model_properties_config t2 ON t1.model_id = t2.model_id
                LEFT JOIN dqm.ors_device_check_config t3 ON t1.asset_id = t3.asset_id AND t2.param_code = t3.param_code
                LEFT JOIN dqm.ors_device_param_report_log T4 ON t1.asset_id= T4.asset_id AND report_date=#{bizDate}::DATE
                LEFT JOIN dqm.ors_device_check_config t5 ON t5.exclude_type ='WHOLE' AND t1.asset_id = t5.asset_id
                LEFT JOIN (select model_id,string_agg ( DISTINCT param_code, ',' ) need_report_param_codes from dqm.ors_model_properties_config GROUP BY model_id) t6 ON t1.model_id = t6.model_id
                LEFT JOIN (select asset_id,string_agg ( DISTINCT param_code, ',' ) exclude_param_code from dqm.ors_device_check_config GROUP BY asset_id) t7 ON t1.asset_id = t7.asset_id
    </insert>
    <insert id="generateReportLog">


        truncate dqm.report_device;
        INSERT INTO dqm.report_device (device_name, stat_date)
            (
                SELECT device_name, #{bizDate}::DATE AS stat_date FROM sany_data_service.sanyds_core_param_stat_latest_day WHERE stat_date = #{bizDate}::DATE GROUP BY device_name
                UNION DISTINCT
                SELECT device_name, #{bizDate}::DATE AS stat_date FROM dqm.ors_device_data_abnormal_detail WHERE stat_date = #{bizDate}::DATE and abnormal_effective=1 GROUP BY device_name
            ) ;

        /*
            property_names所有异常属性名称（包含未上报+上报异常数据的）
            param_codes所有异常属性编号（包含未上报+上报异常数据的）
            null_properties未上报数据的属性名称
            null_param_codes未上报数据的属性编号
            abnormal_properties上报异常数据的属性名称
            abnormal_param_codes上报异常数据的属性字段编号
            report_param_codes上报了工况数据的属性
            abnormal_codes异常类型
        */
        -- 先删除，防止手动执行重复
        DELETE FROM dqm.ors_device_param_report_log WHERE report_date = #{bizDate}::DATE;

        WITH scp AS(
            SELECT DISTINCT
                CASE
                    WHEN
                        aa.multiple_code IS NULL THEN
                        scp.param_code ELSE aa.multiple_code
                    END AS param_code,
                scp.device_name,
                scp.stat_date
            FROM
                sany_data_service.sanyds_core_param_stat_latest_day scp
                    left JOIN
                (
                    select msr.multiple_code,msr.single_code,omp.model_id from
                        dqm.multiple_single_relational msr inner JOIN
                        dqm.ors_model_properties_config omp on msr.multiple_code = omp.param_code
                    union
                    select msr.single_code, msr.single_code, omp.model_id from
                        dqm.multiple_single_relational msr inner JOIN
                        dqm.ors_model_properties_config omp on msr.multiple_code = omp.param_code
                )aa
                on aa.single_code = scp.param_code
                    left join
                dqm.ors_device_check_config odc ON odc.asset_id = scp.device_name  and odc.param_code = scp.param_code
                    AND odc.create_time <![CDATA[<=]]>  #{bizDate}::TIMESTAMP
            WHERE  odc.param_code is null
              and scp.stat_date:: DATE  =   #{bizDate}::DATE
        )



        INSERT INTO dqm.ors_device_param_report_log (
            asset_id,
            serial_num,
            property_names,
            param_codes,
            null_properties,
            null_param_codes,
            report_date,
            create_time,
            abnormal_properties,
            abnormal_param_codes,
            division,
            division_code,
            product_group,
            product_group_code,
            region,
            region_code,
            country,
            country_code,
            abnormal_codes,
            report_param_codes
        )
        SELECT * FROM (
        SELECT
            T3.asset_id,
            T3.serial_num,
            string_agg ( DISTINCT CASE WHEN T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%' THEN T2.property_name END, ',' ) AS property_names,
            string_agg ( DISTINCT CASE WHEN T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%' THEN T2.param_code :: TEXT END , ',' ) AS param_codes,
            string_agg ( DISTINCT CASE WHEN (T2.abnormal_code = 9008 or T2.abnormal_code = 9009)
                AND (T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%') THEN T2.property_name END, ',' ) AS null_properties,
            string_agg ( DISTINCT CASE WHEN (T2.abnormal_code = 9008 or T2.abnormal_code = 9009)
                AND (T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%') THEN T2.param_code :: TEXT END, ',' ) AS null_param_codes,
            T1.stat_date,
            CURRENT_TIMESTAMP,
            string_agg ( DISTINCT CASE WHEN T2.abnormal_code != 9008 and T2.abnormal_code != 9009
                AND (T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%') THEN T2.property_name END, ',' ) AS abnormal_properties,
            string_agg ( DISTINCT CASE WHEN T2.abnormal_code != 9008  and T2.abnormal_code != 9009
                AND (T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%') THEN T2.param_code :: TEXT END, ',' ) AS abnormal_param_codes,
            split_part( string_agg ( DISTINCT T3.division, ',' ), ',', 1 ) AS division,
            split_part( string_agg ( DISTINCT om.division_code, ',' ), ',', 1 ) AS division_code,
            split_part( string_agg ( DISTINCT T3.product_group, ',' ), ',', 1 ) AS product_group,
            split_part( string_agg ( DISTINCT T3.product_group_code, ',' ), ',', 1 ) AS product_group_code,
            split_part( string_agg ( DISTINCT T3.region, ',' ), ',', 1 ) AS region,
            split_part( string_agg ( DISTINCT T3.region_code, ',' ), ',', 1 ) AS region_code,
            split_part( string_agg ( DISTINCT T3.country, ',' ), ',', 1 ) AS country,
            split_part( string_agg ( DISTINCT T3.country_code, ',' ), ',', 1 ) AS country_code,
            string_agg( DISTINCT CASE WHEN T5.param_codes IS NULL OR T5.param_codes NOT LIKE '%'||T2.param_code||'%' THEN T2.abnormal_code::TEXT END, ',' ) AS abnormal_codes,
            string_agg( DISTINCT scp.param_code::TEXT, ',') AS report_param_codes
        FROM dqm.report_device as T1
                INNER JOIN dqm.ors_base_device_info T3 ON T3.asset_id = T1.device_name
                left join dqm.ors_model_division om on T3.model_id = om.model_id
                LEFT JOIN dqm.ors_device_data_abnormal_detail T2 ON T2.stat_date = T1.stat_date AND T1.device_name = T2.device_name and T2.abnormal_effective=1
                LEFT JOIN scp ON scp.stat_date :: DATE = T1.stat_date AND scp.device_name = T1.device_name
                LEFT JOIN (SELECT asset_id, string_agg(param_code::TEXT, ',') AS param_codes
                           FROM dqm.ors_device_check_config
                           WHERE param_code IS NOT NULL
                             AND exclude_type != 'WHOLE'
                           GROUP BY asset_id) T5 ON T1.device_name = T5.asset_id
                where T1.device_name NOT IN (SELECT asset_id FROM dqm.ors_device_check_config WHERE exclude_type = 'WHOLE')
        GROUP BY
            T3.asset_id,
            T3.serial_num,
            T1.stat_date
        ) T111 where property_names !='' or param_codes !='' or null_properties !='' or null_param_codes !='' or abnormal_properties !='' or abnormal_param_codes !='' or abnormal_codes !='' or report_param_codes !='';

        analyze dqm.ors_device_param_report_log;

    </insert>
    <insert id="job_analysis">
        DELETE FROM dqm.orc_mid_analysis WHERE stat_date = #{bizDate}::DATE ;
        DELETE FROM dqm.orc_mid_analysis WHERE stat_date = (#{bizDate}::DATE - INTERVAL '7 day')::DATE;
        INSERT INTO "dqm"."orc_mid_analysis" ("division", "product_group", "region", "total_cnt", "up_cnt", "abnormal_cnt", "no_check_cnt", "stat_date")
        SELECT
        omd.division_name as division,
        omd.product_group_name as product_group,
            '' as region,
            sum( case when t1.asset_id is null then 0 else 1 end) total_cnt,
            sum( case when t2.report_param_codes is null then 0 else 1 end) up_cnt,
            sum( case when t2.abnormal_param_codes is null then 0 else 1 end) abnormal_cnt,
            sum( case when t3.asset_id is null then 0 else 1 end) no_check_cnt
            ,#{bizDate}::DATE as stat_date
        FROM
            ( SELECT asset_id,model_id,country_code FROM dqm.ors_base_device_info
<!--            WHERE active_statu = TRUE AND crm_register = 1 AND ( exce_flag != 1 OR exce_flag IS NULL ) AND serial_num NOT LIKE'%_H'  -->
        ) t1
        LEFT JOIN ( SELECT asset_id,report_param_codes,abnormal_param_codes FROM dqm.ors_device_param_report_log where report_date=#{bizDate}::DATE) t2 ON t1.asset_id = t2.asset_id
        LEFT JOIN ( SELECT asset_id FROM dqm.ors_device_check_config GROUP BY asset_id ) t3 ON t1.asset_id = t3.asset_id
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = t1.model_id
        group by
        omd.division_name,omd.product_group_name;
    </insert>

    <insert id="job_abnormal_analysis">
        DELETE FROM dqm.orc_mid_abnormal_analysis WHERE stat_date = #{bizDate}::DATE ;
        DELETE FROM dqm.orc_mid_abnormal_analysis WHERE stat_date = (#{bizDate}::DATE - INTERVAL '7 day')::DATE;
        INSERT INTO "dqm"."orc_mid_abnormal_analysis" ("device_name", "division", "product_group", "region", "device_code", "property_name", "abnormal_name", "abnormal_data", "abnormal_time", "stat_date")
        SELECT
            T2.device_code as device_name,
            T3.product_group_name as product_group,
            T3.division_name as division,
            '' as region,
            T2.asset_id as device_code,
            T1.property_name,
            T1.abnormal_name,
            T1.abnormal_data,
            T1.abnormal_time
            ,T1.stat_date
        FROM
            dqm.ors_device_data_abnormal_detail_day T1
        INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name
        INNER JOIN dqm.ors_model_division T3 ON T3.model_id = T2.model_id
        WHERE
            T1.stat_date = #{bizDate}::DATE
        ORDER BY
            T2.asset_id,
            T1.stat_date DESC;
    </insert>

    <insert id="job_abnormal_detail">
        DELETE FROM dqm.orc_mid_abnormal_detail WHERE stat_date = #{bizDate}::DATE ;
        DELETE FROM dqm.orc_mid_abnormal_detail WHERE stat_date = (#{bizDate}::DATE - INTERVAL '7 day')::DATE;

        INSERT INTO "dqm"."orc_mid_abnormal_detail" ("stat_date","product_group","division","region","device_code",
                                                     "device_name","model_id","model_name","hw_version","fw_version",
                "device_status","device_location","engine_worktime","working_time",
                "total_fuel_consumption","pumping_volume","driving_mileage","travel_speed",
                "fuel_level","engine_speed","water_temperature","total_electric_consumption",
                "soc_stateofcharge","total_idle_fuel_consumption","total_idle_time",
                "gear","total_time_left_moving","total_time_right_moving","oil_pressure",
                "pump_total_absorbed_torque","pump_motor_rotate_speed","charging_status",
                "charge_time_remain","single_charge_capacity","day_power_consumption",
                "action_code","total_time_rotation","total_no_action_power_consumption","idle_time_idle_fuel","work_time_fuel",
                 "idel_fuel_fuel","idel_time_work_time","mileage_speed","mileage_location","engine_time_fuel"
        )
        SELECT
        #{bizDate}::DATE as stat_date,
        T3.product_group_name as product_group,
        T3.division_name as division,
        '' as region,
        bdi.asset_id AS device_code, -- 物联盒ID
        bdi.device_code AS device_name, -- 设备编号
        bdi.model_id AS model_id,
        bdi.model_name AS model_name,
        bdi.hw_version AS hw_version,
        bdi.fw_version AS fw_version,
        (
        case when t1.device_status_9009 > 0 then '属性值长期未上报('||t1.device_status_9009||')' else '' end
        ||
        case when t1.device_status_9008 > 0 then '属性值从未上报('||t1.device_status_9008||')' else '' end
        ) device_status,

        (case when t1.device_location_9009 > 0 then '属性值长期未上报('||t1.device_location_9009||')' else '' end)
        ||
        (case when t1.device_location_9008 > 0 then '属性值从未上报('||t1.device_location_9008||')' else '' end)
        ||
        (case when t1.device_location_9001 > 0 then '属性值异常('||t1.device_location_9001||')' else '' end)
        ||
        (case when t1.device_location_9002 > 0 then '属性值超限('||t1.device_location_9002||')' else '' end)
        ||
        (case when t1.device_location_9007 > 0 then
        case when t1.deviceLocationCnt > 0 and (cast(t1.device_location_9007 as DECIMAL)/cast(t1.deviceLocationCnt as DECIMAL) &gt;= 0.004)
        then ''
        else '位置漂移('||t1.device_location_9007||')'
        end
        else '' end)
        device_location,

        (
        case when t1.engine_worktime_9009 > 0 then '属性值长期未上报('||t1.engine_worktime_9009||')' else '' end
        ||
        case when t1.engine_worktime_9008 > 0 then '属性值从未上报('||t1.engine_worktime_9008||')' else '' end
        ||
        case when t1.engine_worktime_9001 > 0 then '属性值异常('||t1.engine_worktime_9001||')' else '' end
        ||
        case when t1.engine_worktime_9004 > 0 then '属性值逆增长('||t1.engine_worktime_9004||')' else '' end
        ||
        case when t1.engine_worktime_9003 > 0 then '属性值日变化量异常('||t1.engine_worktime_9003||')' else '' end
        ||
        case when t1.engine_worktime_9005 > 0 then '属性值跳变('||t1.engine_worktime_9005||')' else '' end
        ) engine_worktime,

        (
        case when t1.working_time_9009 > 0 then '属性值长期未上报('||t1.working_time_9009||')' else '' end
        ||
        case when t1.working_time_9008 > 0 then '属性值从未上报('||t1.working_time_9008||')' else '' end
        ||
        case when t1.working_time_9001 > 0 then '属性值异常('||t1.working_time_9001||')' else '' end
        ||
        case when t1.working_time_9004 > 0 then '属性值逆增长('||t1.working_time_9004||')' else '' end
        ||
        case when t1.working_time_9003 > 0 then '属性值日变化量异常('||t1.working_time_9003||')' else '' end
        ||
        case when t1.working_time_9005 > 0 then '属性值跳变('||t1.working_time_9005||')' else '' end
        ) working_time,

        (
        case when t1.total_fuel_consumption_9009 > 0 then '属性值长期未上报('||t1.total_fuel_consumption_9009||')' else '' end
        ||
        case when t1.total_fuel_consumption_9008 > 0 then '属性值从未上报('||t1.total_fuel_consumption_9008||')' else '' end
        ||
        case when t1.total_fuel_consumption_9001 > 0 then '属性值异常('||t1.total_fuel_consumption_9001||')' else '' end
        ||
        case when t1.total_fuel_consumption_9004 > 0 then '属性值逆增长('||t1.total_fuel_consumption_9004||')' else '' end
        ||
        case when t1.total_fuel_consumption_9005 > 0 then '属性值跳变('||t1.total_fuel_consumption_9005||')' else '' end
        ) total_fuel_consumption,

        (
        case when t1.pumping_volume_9009 > 0 then '属性值长期未上报('||t1.pumping_volume_9009||')' else '' end
        ||
        case when t1.pumping_volume_9008 > 0 then '属性值从未上报('||t1.pumping_volume_9008||')' else '' end
        ||
        case when t1.pumping_volume_9001 > 0 then '属性值异常('||t1.pumping_volume_9001||')' else '' end
        ||
        case when t1.pumping_volume_9004 > 0 then '属性值逆增长('||t1.pumping_volume_9004||')' else '' end
        ) pumping_volume,

        (
        case when t1.driving_mileage_9009 > 0 then '属性值长期未上报('||t1.driving_mileage_9009||')' else '' end
        ||
        case when t1.driving_mileage_9008 > 0 then '属性值从未上报('||t1.driving_mileage_9008||')' else '' end
        ||
        case when t1.driving_mileage_9001 > 0 then '属性值异常('||t1.driving_mileage_9001||')' else '' end
        ||
        case when t1.driving_mileage_9004 > 0 then '属性值逆增长('||t1.driving_mileage_9004||')' else '' end
        ||
        case when t1.driving_mileage_9003 > 0 then '属性值日变化量异常('||t1.driving_mileage_9003||')' else '' end
        ||
        case when t1.driving_mileage_9005 > 0 then '属性值跳变('||t1.driving_mileage_9005||')' else '' end
        ) driving_mileage,

        (
        case when t1.travel_speed_9009 > 0 then '属性值长期未上报('||t1.travel_speed_9009||')' else '' end
        ||
        case when t1.travel_speed_9008 > 0 then '属性值从未上报('||t1.travel_speed_9008||')' else '' end
        ||
        case when t1.travel_speed_9001 > 0 then '属性值异常('||t1.travel_speed_9001||')' else '' end
        ||
        case when t1.travel_speed_9006 > 0 then '属性长期定值('||t1.travel_speed_9006||')' else '' end
        ) travel_speed,

        (
        case when t1.fuel_level_9009 > 0 then '属性值长期未上报('||t1.fuel_level_9009||')' else '' end
        ||
        case when t1.fuel_level_9008 > 0 then '属性值从未上报('||t1.fuel_level_9008||')' else '' end
        ||
        case when t1.fuel_level_9001 > 0 then '属性值异常('||t1.fuel_level_9001||')' else '' end
        ||
        case when t1.fuel_level_9006 > 0 then '属性长期定值('||t1.fuel_level_9006||')' else '' end
        ) fuel_level,

        (
        case when t1.engine_speed_9009 > 0 then '属性值长期未上报('||t1.engine_speed_9009||')' else '' end
        ||
        case when t1.engine_speed_9008 > 0 then '属性值从未上报('||t1.engine_speed_9008||')' else '' end
        ||
        case when t1.engine_speed_9001 > 0 then '属性值异常('||t1.engine_speed_9001||')' else '' end
        ||
        case when t1.engine_speed_9006 > 0 then '属性长期定值('||t1.engine_speed_9006||')' else '' end
        ) engine_speed,

        (
        case when t1.water_temperature_9009 > 0 then '属性值长期未上报('||t1.water_temperature_9009||')' else '' end
        ||
        case when t1.water_temperature_9008 > 0 then '属性值从未上报('||t1.water_temperature_9008||')' else '' end
        ||
        case when t1.water_temperature_9001 > 0 then '属性值异常('||t1.water_temperature_9001||')' else '' end
        ||
        case when t1.water_temperature_9006 > 0 then '属性长期定值('||t1.water_temperature_9006||')' else '' end
        ) water_temperature,

        (
        case when t1.total_electric_consumption_9009 > 0 then '属性值长期未上报('||t1.total_electric_consumption_9009||')' else '' end
        ||
        case when t1.total_electric_consumption_9008 > 0 then '属性值从未上报('||t1.total_electric_consumption_9008||')' else '' end
        ||
        case when t1.total_electric_consumption_9001 > 0 then '属性值异常('||t1.total_electric_consumption_9001||')' else '' end
        ||
        case when t1.total_electric_consumption_9004 > 0 then '属性值逆增长('||t1.total_electric_consumption_9004||')' else '' end
        ||
        case when t1.total_electric_consumption_9003 > 0 then '属性值日变化量异常('||t1.total_electric_consumption_9003||')' else '' end
        ||
        case when t1.total_electric_consumption_9005 > 0 then '属性值跳变('||t1.total_electric_consumption_9005||')' else '' end
        ) total_electric_consumption,

        (
        case when t1.soc_stateofcharge_9009 > 0 then '属性值长期未上报('||t1.soc_stateofcharge_9009||')' else '' end
        ||
        case when t1.soc_stateofcharge_9008 > 0 then '属性值从未上报('||t1.soc_stateofcharge_9008||')' else '' end
        ||
        case when t1.soc_stateofcharge_9001 > 0 then '属性值异常('||t1.soc_stateofcharge_9001||')' else '' end
        ) soc_stateofcharge,

        (
        case when t1.total_idle_fuel_consumption_9009 > 0 then '属性值长期未上报('||t1.total_idle_fuel_consumption_9009||')' else '' end
        ||
        case when t1.total_idle_fuel_consumption_9008 > 0 then '属性值从未上报('||t1.total_idle_fuel_consumption_9008||')' else '' end
        ||
        case when t1.total_idle_fuel_consumption_9001 > 0 then '属性值异常('||t1.total_idle_fuel_consumption_9001||')' else '' end
        ||
        case when t1.total_idle_fuel_consumption_9004 > 0 then '属性值逆增长('||t1.total_idle_fuel_consumption_9004||')' else '' end
        ||
        case when t1.total_idle_fuel_consumption_9005 > 0 then '属性值跳变('||t1.total_idle_fuel_consumption_9005||')' else '' end
        ) total_idle_fuel_consumption,

        (
        case when t1.total_idle_time_9009 > 0 then '属性值长期未上报('||t1.total_idle_time_9009||')' else '' end
        ||
        case when t1.total_idle_time_9008 > 0 then '属性值从未上报('||t1.total_idle_time_9008||')' else '' end
        ||
        case when t1.total_idle_time_9001 > 0 then '属性值异常('||t1.total_idle_time_9001||')' else '' end
        ||
        case when t1.total_idle_time_9004 > 0 then '属性值逆增长('||t1.total_idle_time_9004||')' else '' end
        ||
        case when t1.total_idle_time_9005 > 0 then '属性值跳变('||t1.total_idle_time_9005||')' else '' end
        ) total_idle_time,

        (
        case when t1.gear_9009 > 0 then '属性值长期未上报('||t1.gear_9009||')' else '' end
        ||
        case when t1.gear_9008 > 0 then '属性值从未上报('||t1.gear_9008||')' else '' end
        ||
        case when t1.gear_9001 > 0 then '属性值异常('||t1.gear_9001||')' else '' end
        ) gear,

        (
        case when t1.total_time_left_moving_9009 > 0 then '属性值长期未上报('||t1.total_time_left_moving_9009||')' else '' end
        ||
        case when t1.total_time_left_moving_9008 > 0 then '属性值从未上报('||t1.total_time_left_moving_9008||')' else '' end
        ||
        case when t1.total_time_left_moving_9001 > 0 then '属性值异常('||t1.total_time_left_moving_9001||')' else '' end
        ||
        case when t1.total_time_left_moving_9004 > 0 then '属性值逆增长('||t1.total_time_left_moving_9004||')' else '' end
        ||
        case when t1.total_time_left_moving_9005 > 0 then '属性值跳变('||t1.total_time_left_moving_9005||')' else '' end
        ) total_time_left_moving,

        (
        case when t1.total_time_right_moving_9009 > 0 then '属性值长期未上报('||t1.total_time_right_moving_9009||')' else '' end
        ||
        case when t1.total_time_right_moving_9008 > 0 then '属性值从未上报('||t1.total_time_right_moving_9008||')' else '' end
        ||
        case when t1.total_time_right_moving_9001 > 0 then '属性值异常('||t1.total_time_right_moving_9001||')' else '' end
        ||
        case when t1.total_time_right_moving_9004 > 0 then '属性值逆增长('||t1.total_time_right_moving_9004||')' else '' end
        ||
        case when t1.total_time_right_moving_9005 > 0 then '属性值跳变('||t1.total_time_right_moving_9005||')' else '' end
        ) total_time_right_moving,

        (
        case when t1.oil_pressure_9009 > 0 then '属性值长期未上报('||t1.oil_pressure_9009||')' else '' end
        ||
        case when t1.oil_pressure_9008 > 0 then '属性值从未上报('||t1.oil_pressure_9008||')' else '' end
        ||
        case when t1.oil_pressure_9001 > 0 then '属性值异常('||t1.oil_pressure_9001||')' else '' end
        ) oil_pressure,

        (
        case when t1.pump_total_absorbed_torque_9009 > 0 then '属性值长期未上报('||t1.pump_total_absorbed_torque_9009||')' else '' end
        ||
        case when t1.pump_total_absorbed_torque_9008 > 0 then '属性值从未上报('||t1.pump_total_absorbed_torque_9008||')' else '' end
        ||
        case when t1.pump_total_absorbed_torque_9001 > 0 then '属性值异常('||t1.pump_total_absorbed_torque_9001||')' else '' end
        ) pump_total_absorbed_torque,

        (
        case when t1.pump_motor_rotate_speed_9009 > 0 then '属性值长期未上报('||t1.pump_motor_rotate_speed_9009||')' else '' end
        ||
        case when t1.pump_motor_rotate_speed_9008 > 0 then '属性值从未上报('||t1.pump_motor_rotate_speed_9008||')' else '' end
        ||
        case when t1.pump_motor_rotate_speed_9001 > 0 then '属性值异常('||t1.pump_motor_rotate_speed_9001||')' else '' end
        ) pump_motor_rotate_speed,

        (
        case when t1.charging_status_9009 > 0 then '属性值长期未上报('||t1.charging_status_9009||')' else '' end
        ||
        case when t1.charging_status_9008 > 0 then '属性值从未上报('||t1.charging_status_9008||')' else '' end
        ||
        case when t1.charging_status_9001 > 0 then '属性值异常('||t1.charging_status_9001||')' else '' end
        ) charging_status,

        (
        case when t1.charge_time_remain_9009 > 0 then '属性值长期未上报('||t1.charge_time_remain_9009||')' else '' end
        ||
        case when t1.charge_time_remain_9008 > 0 then '属性值从未上报('||t1.charge_time_remain_9008||')' else '' end
        ||
        case when t1.charge_time_remain_9001 > 0 then '属性值异常('||t1.charge_time_remain_9001||')' else '' end
        ) charge_time_remain,

        (
        case when t1.single_charge_capacity_9009 > 0 then '属性值长期未上报('||t1.single_charge_capacity_9009||')' else '' end
        ||
        case when t1.single_charge_capacity_9008 > 0 then '属性值从未上报('||t1.single_charge_capacity_9008||')' else '' end
        ||
        case when t1.single_charge_capacity_9001 > 0 then '属性值异常('||t1.single_charge_capacity_9001||')' else '' end
        ) single_charge_capacity,

        (
        case when t1.day_power_consumption_9009 > 0 then '属性值长期未上报('||t1.day_power_consumption_9009||')' else '' end
        ||
        case when t1.day_power_consumption_9008 > 0 then '属性值从未上报('||t1.day_power_consumption_9008||')' else '' end
        ||
        case when t1.day_power_consumption_9001 > 0 then '属性值异常('||t1.day_power_consumption_9001||')' else '' end
        ) day_power_consumption,

        (
        case when t1.action_code_9009 > 0 then '属性值长期未上报('||t1.action_code_9009||')' else '' end
        ||
        case when t1.action_code_9008 > 0 then '属性值从未上报('||t1.action_code_9008||')' else '' end
        ||
        case when t1.action_code_9001 > 0 then '属性值异常('||t1.action_code_9001||')' else '' end
        ) action_code,

        (
        case when t1.total_time_rotation_9009 > 0 then '属性值长期未上报('||t1.total_time_rotation_9009||')' else '' end
        ||
        case when t1.total_time_rotation_9008 > 0 then '属性值从未上报('||t1.total_time_rotation_9008||')' else '' end
        ||
        case when t1.total_time_rotation_9001 > 0 then '属性值异常('||t1.total_time_rotation_9001||')' else '' end
        ||
        case when t1.total_time_rotation_9004 > 0 then '属性值逆增长('||t1.total_time_rotation_9004||')' else '' end
        ||
        case when t1.total_time_rotation_9005 > 0 then '属性值跳变('||t1.total_time_rotation_9005||')' else '' end
        ) total_time_rotation,

        (
        case when t1.total_no_action_power_consumption_9009 > 0 then '属性值长期未上报('||t1.total_no_action_power_consumption_9009||')' else '' end
        ||
        case when t1.total_no_action_power_consumption_9008 > 0 then '属性值从未上报('||t1.total_no_action_power_consumption_9008||')' else '' end
        ||
        case when t1.total_no_action_power_consumption_9001 > 0 then '属性值异常('||t1.total_no_action_power_consumption_9001||')' else '' end
        ||
        case when t1.total_no_action_power_consumption_9004 > 0 then '属性值逆增长('||t1.total_no_action_power_consumption_9004||')' else '' end
        ) total_no_action_power_consumption,

        (
        case when t1.idle_time_idle_fuel_9101 > 0 then '怠速时长变化，怠速油耗未变化('||t1.idle_time_idle_fuel_9101||')' else '' end
        ||
        case when t1.idle_time_idle_fuel_9102 > 0 then '怠速油耗变化，怠速时长未变化('||t1.idle_time_idle_fuel_9102||')' else '' end
        ||
        case when t1.idle_time_idle_fuel_9100 > 0 then '怠速时长怠速油耗关联工况变化不合理('||t1.idle_time_idle_fuel_9100||')' else '' end
        ) idle_time_idle_fuel,

        (
        case when t1.work_time_fuel_9103 > 0 then '工作时间变化，总油耗未变化('||t1.work_time_fuel_9103||')' else '' end
        ||
        case when t1.work_time_fuel_9104 > 0 then '总油耗变化，工作时长未变化('||t1.work_time_fuel_9104||')' else '' end
        ||
        case when t1.work_time_fuel_9100 > 0 then '工作时间总油耗关联工况变化不合理('||t1.work_time_fuel_9100||')' else '' end
        ) work_time_fuel,

        (
        case when t1.idel_fuel_fuel_9105 > 0 then '当日怠速油耗增长不合理('||t1.idel_fuel_fuel_9105||')' else '' end
        ||
        case when t1.idel_fuel_fuel_9100 > 0 then '怠速油耗总油耗关联关联工况变化不合理('||t1.idel_fuel_fuel_9100||')' else '' end
        ) idel_fuel_fuel,
        (
        case when t1.idel_time_work_time_9106 > 0 then '当日怠速时长增长不合理('||t1.idel_time_work_time_9106||')' else '' end
        ||
        case when t1.idel_time_work_time_9100 > 0 then '怠速时长工作时长关联工况变化不合理('||t1.idel_time_work_time_9100||')' else '' end
        ) idel_time_work_time,
        (
        case when t1.mileage_speed_9107 > 0 then '当日里程变化，无车速('||t1.mileage_speed_9107||')' else '' end
        ||
        case when t1.mileage_speed_9108 > 0 then '当日有持续车速，里程未变化('||t1.mileage_speed_9108||')' else '' end
        ||
        case when t1.mileage_speed_9100 > 0 then '行驶里程行驶速度关联工况变化不合理('||t1.mileage_speed_9100||')' else '' end
        ) mileage_speed,
        (
        case when t1.mileage_location_9109 > 0 then '设备里程变化，定位不变('||t1.mileage_location_9109||')' else '' end
        ||
        case when t1.mileage_location_9100 > 0 then '行驶里程设备位置关联工况变化不合理('||t1.mileage_location_9100||')' else '' end
        ) mileage_location,
        (
        case when t1.engine_time_fuel_9100 > 0 then '发动机工作时长总油耗关联工况变化不合理('||t1.engine_time_fuel_9100||')' else '' end
        ) engine_time_fuel


        FROM
        (
        SELECT
        s.device_name AS deviceName,
        SUM ( s.device_status_9008 ) AS device_status_9008,
        SUM ( s.device_status_9009 ) AS device_status_9009,
        SUM ( s.device_location_9008 ) AS device_location_9008,
        SUM ( s.device_location_9001 ) AS device_location_9001,
        SUM ( s.device_location_9002 ) AS device_location_9002,
        SUM ( s.device_location_9007 ) AS device_location_9007,
        SUM ( s.device_location_9009 ) AS device_location_9009,
        SUM ( s.engine_worktime_9008 ) AS engine_worktime_9008,
        SUM ( s.engine_worktime_9001 ) AS engine_worktime_9001,
        SUM ( s.engine_worktime_9004 ) AS engine_worktime_9004,
        SUM ( s.engine_worktime_9009 ) AS engine_worktime_9009,
        SUM ( s.engine_worktime_9003 ) AS engine_worktime_9003,
        SUM ( s.working_time_9008 ) AS working_time_9008,
        SUM ( s.working_time_9001 ) AS working_time_9001,
        SUM ( s.working_time_9004 ) AS working_time_9004,
        SUM ( s.working_time_9009 ) AS working_time_9009,
        SUM ( s.working_time_9003 ) AS working_time_9003,
        SUM ( s.total_fuel_consumption_9008 ) AS total_fuel_consumption_9008,
        SUM ( s.total_fuel_consumption_9001 ) AS total_fuel_consumption_9001,
        SUM ( s.total_fuel_consumption_9004 ) AS total_fuel_consumption_9004,
        SUM ( s.total_fuel_consumption_9009 ) AS total_fuel_consumption_9009,
        SUM ( s.total_fuel_consumption_9003 ) AS total_fuel_consumption_9003,
        SUM ( s.pumping_volume_9008 ) AS pumping_volume_9008,
        SUM ( s.pumping_volume_9001 ) AS pumping_volume_9001,
        SUM ( s.pumping_volume_9004 ) AS pumping_volume_9004,
        SUM ( s.pumping_volume_9009 ) AS pumping_volume_9009,
        SUM ( s.driving_mileage_9008 ) AS driving_mileage_9008,
        SUM ( s.driving_mileage_9001 ) AS driving_mileage_9001,
        SUM ( s.driving_mileage_9004 ) AS driving_mileage_9004,
        SUM ( s.driving_mileage_9009 ) AS driving_mileage_9009,
        SUM ( s.driving_mileage_9003 ) AS driving_mileage_9003,
        SUM ( s.travel_speed_9008 ) AS travel_speed_9008,
        SUM ( s.travel_speed_9001 ) AS travel_speed_9001,
        SUM ( s.travel_speed_9009 ) AS travel_speed_9009,
        SUM ( s.fuel_level_9001 ) AS fuel_level_9001,
        SUM ( s.fuel_level_9008 ) AS fuel_level_9008,
        SUM ( s.fuel_level_9009 ) AS fuel_level_9009,
        SUM ( s.engine_speed_9001 ) AS engine_speed_9001,
        SUM ( s.engine_speed_9008 ) AS engine_speed_9008,
        SUM ( s.engine_speed_9009 ) AS engine_speed_9009,
        SUM ( s.water_temperature_9008 ) AS water_temperature_9008,
        SUM ( s.water_temperature_9001 ) AS water_temperature_9001,
        SUM ( s.water_temperature_9009 ) AS water_temperature_9009,
        SUM ( s.total_electric_consumption_9008 ) AS total_electric_consumption_9008,
        SUM ( s.total_electric_consumption_9001 ) AS total_electric_consumption_9001,
        SUM ( s.total_electric_consumption_9004 ) AS total_electric_consumption_9004,
        SUM ( s.total_electric_consumption_9009 ) AS total_electric_consumption_9009,
        SUM ( s.total_electric_consumption_9003 ) AS total_electric_consumption_9003,
        SUM ( s.soc_stateofcharge_9008 ) AS soc_stateofcharge_9008,
        SUM ( s.soc_stateofcharge_9001 ) AS soc_stateofcharge_9001,
        SUM ( s.soc_stateofcharge_9009 ) AS soc_stateofcharge_9009,
        SUM ( s.total_idle_fuel_consumption_9001 ) AS total_idle_fuel_consumption_9001,
        SUM ( s.total_idle_fuel_consumption_9004 ) AS total_idle_fuel_consumption_9004,
        SUM ( s.total_idle_fuel_consumption_9008 ) AS total_idle_fuel_consumption_9008,
        SUM ( s.total_idle_fuel_consumption_9009 ) AS total_idle_fuel_consumption_9009,
        SUM ( s.total_idle_fuel_consumption_9005 ) AS total_idle_fuel_consumption_9005,
        SUM ( s.total_idle_time_9001 ) AS total_idle_time_9001,
        SUM ( s.total_idle_time_9004 ) AS total_idle_time_9004,
        SUM ( s.total_idle_time_9008 ) AS total_idle_time_9008,
        SUM ( s.total_idle_time_9009 ) AS total_idle_time_9009,
        SUM ( s.total_idle_time_9005 ) AS total_idle_time_9005,
        SUM ( s.gear_9008 ) AS gear_9008,
        SUM ( s.gear_9009 ) AS gear_9009,
        SUM ( s.gear_9001 ) AS gear_9001,
        SUM ( s.total_time_left_moving_9001 ) AS total_time_left_moving_9001,
        SUM ( s.total_time_left_moving_9004 ) AS total_time_left_moving_9004,
        SUM ( s.total_time_left_moving_9008 ) AS total_time_left_moving_9008,
        SUM ( s.total_time_left_moving_9009 ) AS total_time_left_moving_9009,
        SUM ( s.total_time_left_moving_9005 ) AS total_time_left_moving_9005,
        SUM ( s.total_time_right_moving_9001 ) AS total_time_right_moving_9001,
        SUM ( s.total_time_right_moving_9004 ) AS total_time_right_moving_9004,
        SUM ( s.total_time_right_moving_9008 ) AS total_time_right_moving_9008,
        SUM ( s.total_time_right_moving_9009 ) AS total_time_right_moving_9009,
        SUM ( s.total_time_right_moving_9005 ) AS total_time_right_moving_9005,
        SUM ( s.oil_pressure_9001 ) AS oil_pressure_9001,
        SUM ( s.oil_pressure_9008 ) AS oil_pressure_9008,
        SUM ( s.oil_pressure_9009 ) AS oil_pressure_9009,
        SUM ( s.pump_total_absorbed_torque_9001 ) AS pump_total_absorbed_torque_9001,
        SUM ( s.pump_total_absorbed_torque_9008 ) AS pump_total_absorbed_torque_9008,
        SUM ( s.pump_total_absorbed_torque_9009 ) AS pump_total_absorbed_torque_9009,
        SUM ( s.pump_motor_rotate_speed_9001 ) AS pump_motor_rotate_speed_9001,
        SUM ( s.pump_motor_rotate_speed_9008 ) AS pump_motor_rotate_speed_9008,
        SUM ( s.pump_motor_rotate_speed_9009 ) AS pump_motor_rotate_speed_9009,
        SUM ( s.charging_status_9008 ) AS charging_status_9008,
        SUM ( s.charging_status_9009 ) AS charging_status_9009,
        SUM ( s.charging_status_9001 ) AS charging_status_9001,
        SUM ( s.charge_time_remain_9001 ) AS charge_time_remain_9001,
        SUM ( s.charge_time_remain_9008 ) AS charge_time_remain_9008,
        SUM ( s.charge_time_remain_9009 ) AS charge_time_remain_9009,
        SUM ( s.single_charge_capacity_9001 ) AS single_charge_capacity_9001,
        SUM ( s.single_charge_capacity_9008 ) AS single_charge_capacity_9008,
        SUM ( s.single_charge_capacity_9009 ) AS single_charge_capacity_9009,
        SUM ( s.day_power_consumption_9001 ) AS day_power_consumption_9001,
        SUM ( s.day_power_consumption_9008 ) AS day_power_consumption_9008,
        SUM ( s.day_power_consumption_9009 ) AS day_power_consumption_9009,

        SUM ( s.action_code_9001 ) AS action_code_9001,
        SUM ( s.action_code_9008 ) AS action_code_9008,
        SUM ( s.action_code_9009 ) AS action_code_9009,

        SUM ( s.total_time_rotation_9001 ) AS total_time_rotation_9001,
        SUM ( s.total_time_rotation_9004 ) AS total_time_rotation_9004,
        SUM ( s.total_time_rotation_9008 ) AS total_time_rotation_9008,
        SUM ( s.total_time_rotation_9009 ) AS total_time_rotation_9009,
        SUM ( s.total_time_rotation_9005 ) AS total_time_rotation_9005,

        SUM ( s.total_no_action_power_consumption_9001 ) AS total_no_action_power_consumption_9001,
        SUM ( s.total_no_action_power_consumption_9004 ) AS total_no_action_power_consumption_9004,
        SUM ( s.total_no_action_power_consumption_9008 ) AS total_no_action_power_consumption_9008,
        SUM ( s.total_no_action_power_consumption_9009 ) AS total_no_action_power_consumption_9009,

        SUM ( s.driving_mileage_9005 ) AS driving_mileage_9005,
        SUM ( s.total_fuel_consumption_9005 ) AS total_fuel_consumption_9005,
        SUM ( s.total_electric_consumption_9005 ) AS total_electric_consumption_9005,
        SUM ( s.working_time_9005 ) AS working_time_9005,
        SUM ( s.engine_worktime_9005 ) AS engine_worktime_9005,
        SUM ( s.travel_speed_9006 ) AS travel_speed_9006,
        SUM ( s.engine_speed_9006 ) AS engine_speed_9006,
        SUM ( s.water_temperature_9006 ) AS water_temperature_9006,
        SUM ( s.fuel_level_9006 ) AS fuel_level_9006,

        SUM ( s.idle_time_idle_fuel_9101 ) AS idle_time_idle_fuel_9101,
        SUM ( s.idle_time_idle_fuel_9102 ) AS idle_time_idle_fuel_9102,
        SUM ( s.idle_time_idle_fuel_9100 ) AS idle_time_idle_fuel_9100,
        SUM ( s.work_time_fuel_9103 ) AS work_time_fuel_9103,
        SUM ( s.work_time_fuel_9104 ) AS work_time_fuel_9104,
        SUM ( s.work_time_fuel_9100 ) AS work_time_fuel_9100,

        SUM ( s.idel_fuel_fuel_9105 ) AS idel_fuel_fuel_9105,
        SUM ( s.idel_fuel_fuel_9100 ) AS idel_fuel_fuel_9100,
        SUM ( s.idel_time_work_time_9106 ) AS idel_time_work_time_9106,
        SUM ( s.idel_time_work_time_9100 ) AS idel_time_work_time_9100,
        SUM ( s.mileage_speed_9107 ) AS mileage_speed_9107,
        SUM ( s.mileage_speed_9108 ) AS mileage_speed_9108,
        SUM ( s.mileage_speed_9100 ) AS mileage_speed_9100,
        SUM ( s.mileage_location_9109 ) AS mileage_location_9109,
        SUM ( s.mileage_location_9100 ) AS mileage_location_9100,
        SUM ( s.engine_time_fuel_9100 ) AS engine_time_fuel_9100,

        SUM ( CASE WHEN s.device_location_cnt IS NOT NULL THEN s.device_location_cnt ELSE 0 END ) AS deviceLocationCnt
        FROM
        dqm.ors_device_data_abnormal_stat_day s
        WHERE
        s.stat_date = #{bizDate} :: DATE
        GROUP BY
        s.device_name
        ) t1
        INNER JOIN dqm.ors_base_device_info bdi ON t1.deviceName = bdi.asset_id
        AND bdi.device_code IS NOT NULL
        INNER JOIN dqm.ors_model_division T3 ON T3.model_id = bdi.model_id;
        -- INNER JOIN dqm.ors_country_region T4 ON T4.country_code = bdi.country_code
<!--            AND ( bdi.exce_flag != 1 OR bdi.exce_flag IS NULL )-->
<!--            AND bdi.serial_num NOT LIKE'%_H'-->

    </insert>

    <select id="analysis" resultType="map">
        SELECT
            t1.division,
<!--            t1.division_code,-->
            t1.product_group,
<!--            t1.product_group_code,-->
            t1.region,
<!--            t1.region_code,-->
            sum( case when t1.asset_id is null then 0 else 1 end) total_cnt,
            sum( case when t2.report_param_codes is null then 0 else 1 end) up_cnt,
            sum( case when t2.abnormal_param_codes is null then 0 else 1 end) abnormal_cnt,
            sum( case when t3.asset_id is null then 0 else 1 end) no_check_cnt
           ,#{bizDate}::DATE as stat_date
        FROM
            ( SELECT asset_id,division,division_code,product_group,product_group_code, region,region_code FROM dqm.ors_base_device_info
        WHERE active_statu = TRUE AND crm_register = 1 AND ( exce_flag != 1 OR exce_flag IS NULL ) AND model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')  ) t1
                LEFT JOIN ( SELECT asset_id,report_param_codes,abnormal_param_codes FROM dqm.ors_device_param_report_log where report_date=#{bizDate}::DATE) t2 ON t1.asset_id = t2.asset_id
                LEFT JOIN ( SELECT asset_id FROM dqm.ors_device_check_config GROUP BY asset_id ) t3 ON t1.asset_id = t3.asset_id
        group by t1.division,t1.division_code, t1.product_group,t1.product_group_code, t1.region,t1.region_code;
    </select>

    <select id="abnormal_analysis" resultType="map">
        SELECT DISTINCT ON
            ( T1.device_name ) T1.device_name AS asset_id,
                               T2.division,
                               T2.division_code,
                               T2.product_group,
                               T2.product_group_code,
                               t2.region,
                               t2.region_code,
                               T2.device_code,
                               T2.device_name,
                               T1.property,
                               T1.property_name,
                               T1.abnormal_name,
                               T1.abnormal_code,
                               T1.param_code,
                               T1.abnormal_data,
                               T1.abnormal_time
                              ,T1.stat_date
        FROM
            dqm.ors_device_data_abnormal_detail_day T1
                INNER JOIN dqm.ors_base_device_info T2 ON T2.asset_id = T1.device_name
        WHERE
            T1.stat_date = #{bizDate}::DATE
          AND T2.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        ORDER BY
            T1.device_name,
            T1.stat_date DESC
        ;
    </select>

    <select id="abnormal_detail" resultType="map">
        SELECT
            #{bizDate}::DATE as stat_date,
<!--            t1.endStatDate,-->
<!--            t1.startStatDate,-->
            bdi.product_group_code,
            bdi.product_group,
            bdi.division,
            bdi.division_code,
            bdi.region,
            bdi.region_code,
            bdi.thing_id AS thingId,
            bdi.asset_id AS deviceCode, -- 物联盒ID
            bdi.device_code AS deviceName, -- 设备编号
            bdi.model_id AS ModelId,
            bdi.model_name AS modelName,
            bdi.hw_version AS hwVersion,
            bdi.auth_token AS authToken,
            bdi.fw_version AS fwVersion,
            case when t1.device_status_9008 > 0 then '属性值从未上报('||t1.device_status_9008||')' else '' end device_status,

            (case when t1.device_location_9008 > 0 then '属性值从未上报('||t1.device_location_9008||')' else '' end)
                ||
            (case when t1.device_location_9001 > 0 then '属性值异常('||t1.device_location_9001||')' else '' end)
                ||
            (case when t1.device_location_9002 > 0 then '属性值超限('||t1.device_location_9002||')' else '' end)
                ||
            (case when t1.device_location_9007 > 0 then

                      case when t1.deviceLocationCnt > 0 and (cast(t1.device_location_9007 as DECIMAL)/cast(t1.deviceLocationCnt as DECIMAL) &lt;= 0.01)
        then ''
        else '位置漂移('||t1.device_location_9007||')'
        end
        else '' end)
        device_location,

        (case when t1.engine_worktime_9008 > 0 then '属性值从未上报('||t1.engine_worktime_9008||')' else '' end)
        ||
        (case when t1.engine_worktime_9001 > 0 then '属性值异常('||t1.engine_worktime_9001||')' else '' end)
        ||
        (case when t1.engine_worktime_9004 > 0 then '属性值逆增长('||t1.engine_worktime_9004||')' else '' end)
        ||
        (case when t1.engine_worktime_9005 > 0 then '属性值跳变('||t1.engine_worktime_9005||')' else '' end)
        engine_worktime,

        (case when t1.working_time_9008 > 0 then '属性值从未上报('||t1.working_time_9008||')' else '' end)
        ||
        (case when t1.working_time_9001 > 0 then '属性值异常('||t1.working_time_9001||')' else '' end)
        ||
        (case when t1.working_time_9004 > 0 then '属性值逆增长('||t1.working_time_9004||')' else '' end)
        ||
        (case when t1.working_time_9005 > 0 then '属性值跳变('||t1.working_time_9005||')' else '' end)
        working_time,

        (case when t1.total_fuel_consumption_9008 > 0 then '属性值从未上报('||t1.total_fuel_consumption_9008||')' else '' end)
        ||
        (case when t1.total_fuel_consumption_9001 > 0 then '属性值异常('||t1.total_fuel_consumption_9001||')' else '' end)
        ||
        (case when t1.total_fuel_consumption_9004 > 0 then '属性值逆增长('||t1.total_fuel_consumption_9004||')' else '' end)
        ||
        (case when t1.total_fuel_consumption_9005 > 0 then '属性值跳变('||t1.total_fuel_consumption_9005||')' else '' end)
        total_fuel_consumption,

        (case when t1.pumping_volume_9008 > 0 then '属性值从未上报('||t1.pumping_volume_9008||')' else '' end)
        ||
        (case when t1.pumping_volume_9001 > 0 then '属性值异常('||t1.pumping_volume_9001||')' else '' end)
        ||
        (case when t1.pumping_volume_9004 > 0 then '属性值逆增长('||t1.pumping_volume_9004||')' else '' end)
        pumping_volume,

        (case when t1.driving_mileage_9008 > 0 then '属性值从未上报('||t1.driving_mileage_9008||')' else '' end)
        ||
        (case when t1.driving_mileage_9001 > 0 then '属性值异常('||t1.driving_mileage_9001||')' else '' end)
        ||
        (case when t1.driving_mileage_9004 > 0 then '属性值逆增长('||t1.driving_mileage_9004||')' else '' end)
        driving_mileage
        FROM
        (
        SELECT
            s.device_name AS deviceName,
            SUM ( s.device_status_9008 ) AS device_status_9008,
            SUM ( s.device_location_9008 ) AS device_location_9008,
            SUM ( s.device_location_9001 ) AS device_location_9001,
            SUM ( s.device_location_9002 ) AS device_location_9002,
            SUM ( s.device_location_9007 ) AS device_location_9007,
            SUM ( s.engine_worktime_9008 ) AS engine_worktime_9008,
            SUM ( s.engine_worktime_9001 ) AS engine_worktime_9001,
            SUM ( s.engine_worktime_9004 ) AS engine_worktime_9004,
            SUM ( s.working_time_9008 ) AS working_time_9008,
            SUM ( s.working_time_9001 ) AS working_time_9001,
            SUM ( s.working_time_9004 ) AS working_time_9004,
            SUM ( s.total_fuel_consumption_9008 ) AS total_fuel_consumption_9008,
            SUM ( s.total_fuel_consumption_9001 ) AS total_fuel_consumption_9001,
            SUM ( s.total_fuel_consumption_9004 ) AS total_fuel_consumption_9004,
            SUM ( s.pumping_volume_9008 ) AS pumping_volume_9008,
            SUM ( s.pumping_volume_9001 ) AS pumping_volume_9001,
            SUM ( s.pumping_volume_9004 ) AS pumping_volume_9004,
            SUM ( s.driving_mileage_9008 ) AS driving_mileage_9008,
            SUM ( s.driving_mileage_9001 ) AS driving_mileage_9001,
            SUM ( s.driving_mileage_9004 ) AS driving_mileage_9004,
<!--            MAX ( s.stat_date ) AS endStatDate,-->
<!--            MIN ( s.stat_date ) AS startStatDate,-->
            SUM ( CASE WHEN s.device_location_cnt IS NOT NULL THEN s.device_location_cnt ELSE 0 END ) AS deviceLocationCnt
        FROM
            dqm.ors_device_data_abnormal_stat_day s
        WHERE
            s.stat_date = #{bizDate}::DATE
        GROUP BY
            s.device_name
        ) t1
        INNER JOIN dqm.ors_base_device_info bdi ON t1.deviceName = bdi.asset_id
            AND bdi.device_code IS NOT NULL
<!--            AND ( bdi.exce_flag != 1 OR bdi.exce_flag IS NULL )-->
            AND bdi.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        ;
<!--        ORDER BY t1.endStatDate DESC ;-->
    </select>

    <select id="countDataQuality_new2" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
        <if test="divisiones != null and divisiones.size > 0">
            T1.division,
            T1.division_code,
        </if>
        <if test="productCodes != null and productCodes.size > 0">
            T1.product_group,
            T1.product_group_code,
        </if>
        COUNT ( DISTINCT T1.asset_id ) AS total,  -- 设备总数
        COUNT ( DISTINCT CASE WHEN T1.crm_register = 1 THEN T1.asset_id END ) AS regist_num,  -- crm注册总数
        COUNT ( DISTINCT CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END ) AS un_regist_num,  -- crm未注册总数
        COUNT ( DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) AS active_num  -- 激活总数
        FROM
            dqm.ors_base_device_info T1
        LEFT JOIN dqm.ors_model_properties_config T2 ON T1.model_id = T2.model_id
        LEFT JOIN dqm.ors_device_check_config T3 ON T1.asset_id = T3.asset_id AND T3.create_time &lt;= #{endTime}::TIMESTAMP
        LEFT JOIN (
            SELECT
                param_codes,
                null_param_codes,
                abnormal_param_codes,
                asset_id
            FROM
                dqm.ors_device_param_report_log
                <where>
                    <if test="year != null and year != ''">
                        AND TO_CHAR(report_date, 'YYYY') = #{year}
                    </if>
                    <if test="startTime != null and startTime != ''">
                        AND report_date &gt;= #{startTime}::DATE
                    </if>
                    <if test="endTime != null and endTime != ''">
                        AND report_date &lt;= #{endTime}::DATE
                    </if>
                </where>
        ) T4 ON T1.asset_id = T4.asset_id
        WHERE
        T1.division_code IS NOT NULL
        AND T1.product_group_code IS NOT NULL
        AND T1.region_code IS NOT NULL
        <if test="divisiones != null and divisiones.size > 0">
            AND T1.division_code IN
            <foreach collection="divisiones" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCodes != null and productCodes.size > 0">
            AND T1.product_group_code IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="(divisiones != null and divisiones.size > 0) or (productCodes != null and productCodes.size > 0) ">
            GROUP BY
            <if test="divisiones != null and divisiones.size > 0">
                T1.division,
                T1.division_code
            </if>
            <if test="productCodes != null and productCodes.size > 0">
                T1.product_group,
                T1.product_group_code
            </if>
        </if>
    </select>

    <select id="countDataQuality_new" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
            <if test="divisiones != null and divisiones.size > 0">
                division,
                division_code,
            </if>
            <if test="productCodes != null and productCodes.size > 0">
                product_group,
                product_group_code,
            </if>
            COUNT ( DISTINCT asset_id ) AS total,  -- 设备总数
            COUNT ( DISTINCT CASE WHEN crm_register = 1 THEN asset_id END ) AS regist_num,  -- crm注册总数
            COUNT ( DISTINCT CASE WHEN crm_register = 0 OR crm_register IS NULL THEN asset_id END ) AS un_regist_num,  -- crm未注册总数
            COUNT ( DISTINCT CASE WHEN active_statu = TRUE THEN asset_id END ) AS active_num,  -- 激活总数
            stat_date as report_date,
            COUNT ( DISTINCT CASE WHEN param_codes IS NOT NULL
                THEN asset_id END) AS abnormal_total_num, -- 异常总数
            COUNT ( DISTINCT CASE WHEN null_param_codes IS NOT NULL
                THEN asset_id END) AS null_abnormal_total_num, -- 从未上报数据9008异常总数
            COUNT ( DISTINCT CASE WHEN abnormal_param_codes IS NOT NULL
                THEN asset_id END) AS param_abnormal_total_num, -- 非9008得异常总数
            COUNT ( DISTINCT CASE WHEN report_param_codes IS NOT NULL
                THEN asset_id END) AS total_report_num, -- 上报工况的设备总数
            COUNT ( DISTINCT CASE WHEN null_param_codes IS NULL
                THEN asset_id END) AS total_un_report_num  -- 未上报工况的设备总数
        FROM
            dqm.ors_device_param_report_day
        WHERE
            1=1
            <if test="year != null and year != ''">
                AND TO_CHAR(stat_date, 'YYYY') = #{year}
            </if>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
            AND exclude_state =0 -- 有剔除的设备不参与计算(包含属性剔除和整机剔除的)
            <choose>
                <when test="divisiones != null and divisiones.size > 0">
                    AND division_code IN
                    <foreach collection="divisiones" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND division_code IS NOT NULL
                </otherwise>
            </choose>
            <choose>
                <when test="productCodes != null and productCodes.size > 0">
                    AND product_group_code IN
                    <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND product_group_code IS NOT NULL
                </otherwise>
            </choose>
            AND region_code IS NOT NULL
        GROUP BY
            stat_date
            <if test="divisiones != null and divisiones.size > 0">
                ,division,division_code
            </if>
            <if test="productCodes != null and productCodes.size > 0">
                ,product_group,product_group_code
            </if>
    </select>

    <select id="countDataQuality" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">

        SELECT
            <if test="divisiones != null and divisiones.size > 0">
                T1.division,
                T1.division_code,
            </if>
            <if test="productCodes != null and productCodes.size > 0">
                T1.product_group,
                T1.product_group_code,
            </if>

            T1.total,
            T1.regist_num,
            T1.un_regist_num,
            T1.active_num,
            T3.report_date,
            T3.abnormal_total_num,
            T3.total_report_num,
            T3.total_un_report_num,
            T3.null_abnormal_total_num,
            T3.param_abnormal_total_num
        FROM
        (
            SELECT
                <if test="divisiones != null and divisiones.size > 0">
                    T1.division,
                    T1.division_code,
                </if>
                <if test="productCodes != null and productCodes.size > 0">
                    T1.product_group,
                    T1.product_group_code,
                </if>
                COUNT ( DISTINCT T1.asset_id ) AS total,  -- 设备总数
                COUNT ( DISTINCT CASE WHEN T1.crm_register = 1 THEN T1.asset_id END ) AS regist_num,  -- crm注册总数
                COUNT ( DISTINCT CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END ) AS un_regist_num,  -- crm未注册总数
                COUNT ( DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) AS active_num  -- 激活总数
            FROM
                dqm.ors_base_device_info T1
            WHERE
                T1.division_code IS NOT NULL
                AND T1.product_group_code IS NOT NULL
                AND T1.region_code IS NOT NULL
                <choose>
                    <when test="null != serialNum and '' != serialNum">
                        AND T1.model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                    </when>
                    <otherwise>
                        AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                    </otherwise>
                </choose>
                <if test="divisiones != null and divisiones.size > 0">
                    AND T1.division_code IN
                    <foreach collection="divisiones" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="productCodes != null and productCodes.size > 0">
                    AND T1.product_group_code IN
                    <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            <if test="(divisiones != null and divisiones.size > 0) or (productCodes != null and productCodes.size > 0) ">
                GROUP BY
                <if test="divisiones != null and divisiones.size > 0">
                    T1.division,
                    T1.division_code
                </if>
                <if test="productCodes != null and productCodes.size > 0">
                    T1.product_group,
                    T1.product_group_code
                </if>
            </if>
        ) T1
        INNER JOIN (
            SELECT
                <if test="divisiones != null and divisiones.size > 0">
                    T4.division_code,
                </if>
                <if test="productCodes != null and productCodes.size > 0">
                    T4.product_group_code,
                </if>
                COUNT ( DISTINCT CASE
                    WHEN T4.asset_id IS NOT NULL
                            AND T4.param_codes IS NOT NULL
                            AND (T5.check_param_code IS NULL OR T5.check_param_code NOT Like CONCAT('%',8601,'%'))
                        THEN T4.asset_id END
                ) AS abnormal_total_num,
                COUNT ( DISTINCT CASE
                    WHEN T4.asset_id IS NOT NULL
                            AND T4.null_param_codes IS NOT NULL
                            AND (T5.check_param_code IS NULL OR T5.check_param_code NOT Like CONCAT('%',8601,'%'))
                        THEN T4.asset_id END
                ) AS null_abnormal_total_num,
                COUNT ( DISTINCT CASE
                    WHEN T4.asset_id IS NOT NULL
                            AND T4.abnormal_param_codes IS NOT NULL
                            AND (T5.check_param_code IS NULL OR T5.check_param_code NOT Like CONCAT('%',8601,'%'))
                        THEN T4.asset_id END
                ) AS param_abnormal_total_num,
                COUNT ( DISTINCT CASE
                    WHEN T4.asset_id IS NOT NULL AND (T5.check_param_code IS NULL OR T5.check_param_code NOT Like CONCAT('%',8601,'%'))
                        THEN T4.asset_id END
                ) AS total_report_num,
                COUNT ( DISTINCT CASE
                    WHEN T4.asset_id IS NULL AND (T5.check_param_code IS NULL OR T5.check_param_code NOT Like CONCAT('%',8601,'%'))
                        THEN T4.asset_id END
                ) AS total_un_report_num,
                report_date
            FROM
                (select * from dqm.ors_device_param_report_log
                <where>
                    <if test="year != null and year != ''">
                        AND TO_CHAR(report_date, 'YYYY') = #{year}
                    </if>
                    <if test="startTime != null and startTime != ''">
                        AND report_date &gt;= #{startTime}::DATE
                    </if>
                    <if test="endTime != null and endTime != ''">
                        AND report_date &lt;= #{endTime}::DATE
                    </if>
                    <if test="divisiones != null and divisiones.size > 0">
                        AND division_code IN
                        <foreach collection="divisiones" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="productCodes != null and productCodes.size > 0">
                        AND product_group_code IN
                        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <choose>
                        <when test="null != serialNum and '' != serialNum">
                            AND model_id in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                        </when>
                        <otherwise>
                            AND model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                        </otherwise>
                    </choose>
                </where>
            ) T4
<!--                LEFT JOIN dqm.ors_device_check_config T5 ON T5.asset_id = T4.asset_id-->
<!--                        <if test="endTime != null and endTime != ''">-->
<!--                            AND T5.create_time &lt;= #{endTime}::TIMESTAMP-->
<!--                        </if>-->
                LEFT JOIN
                (select
                    asset_id,
                    string_agg (cast(param_code as varchar), ',' ) as check_param_code
                from
                    dqm.ors_device_check_config
                    <where>
                        <if test="endTime != null and endTime != ''">
                            create_time &lt;= #{endTime}::TIMESTAMP
                        </if>
                    </where>
                group by
                    asset_id
                ) T5 ON T5.asset_id = T4.asset_id
<!--            WHERE-->
<!--                T4.product_group_code IS NOT NULL and T4.division_code IS NOT NULL-->
            GROUP BY
                T4.report_date
                <if test="divisiones != null and divisiones.size > 0">
                    ,T4.division_code
                </if>
                <if test="productCodes != null and productCodes.size > 0">
                    ,T4.product_group_code
                </if>
        ) T3 ON
            <choose>
                <when test="divisiones != null and divisiones.size > 0">
                    T3.division_code = T1.division_code
                </when>
                <when test="productCodes != null and productCodes.size > 0">
                    T3.product_group_code = T1.product_group_code
                </when>
                <otherwise>
                    1=1
                </otherwise>
            </choose>
    </select>
</mapper>
