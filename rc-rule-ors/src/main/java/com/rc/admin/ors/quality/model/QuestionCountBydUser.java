package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/14 11:14
 * @describe
 */
@Getter
@Setter
public class QuestionCountBydUser {

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "待处理总数")
    private int waitHandleNum;

    @ApiModelProperty(value = "延期数量")
    private int delay7daysNum;

    @ApiModelProperty(value = "昨日待处理数量")
    private int yesterdayWaitNum;

    @ApiModelProperty(value = "今日待处理数量")
    private int todayWaitNum;

    @ApiModelProperty(value = "已处理数量")
    private int handledNum;

    @ApiModelProperty(value = "今日待处理数量较昨日差值")
    private int diffYesterdayAndToday;

    @ApiModelProperty(value = "截止昨天延期数量")
    private int yesterdayDelayNum;

    @ApiModelProperty(value = "今日较昨日延期数量差值")
    private int diffDelayNum;

    @ApiModelProperty(value = "总处理数量")
    private int handleTotal;

    @ApiModelProperty(value = "昨日处理数量")
    private int yesterdayHandleNum;

    @ApiModelProperty(value = "今日处理数量")
    private int todayHandleNum;

    @ApiModelProperty(value = "今日较昨日处理数量")
    private int handledDiffYeserday;

    @ApiModelProperty(value = "待验证数量")
    private int waitVerifyNum;

    @ApiModelProperty(value = "关闭数量")
    private int closedNum;

    @ApiModelProperty(value = "运营处理中")
    private int operateNum;

    @ApiModelProperty(value = "昨日待验证数量")
    private int yesterdayWaitVerifyNum;

    @ApiModelProperty(value = "今日待验证数量")
    private int todayWaitVerifyNum;

    @ApiModelProperty(value = "待验证较昨日差值")
    private int diffWaitVerifyNumYesterday;

    @ApiModelProperty(value = "昨日关闭数量")
    private int yesterdayCloseNum;

    @ApiModelProperty(value = "今日关闭数量")
    private int todayCloseNum;

    @ApiModelProperty(value = "已关闭较昨日差值")
    private int diffCloseNumYesterday;

    @ApiModelProperty(value = "昨日运营处理中数量")
    private int yesterdayOperateNum;

    @ApiModelProperty(value = "今日运营处理中数量")
    private int todayOperateNum;

    @ApiModelProperty(value = "运营处理中较昨日差值")
    private int diffOperateNumYesterday;

    private String userAccount;

    private String userName;

    private String createTime;

    @ApiModelProperty(value = "当天运营处理已结束数量")
    private int todayOperateFinishedNum;

    @ApiModelProperty(value = "当天验证已结束数量")
    private int todayWaitVerifyFinishedNum;

    public int getDiffYesterdayAndToday() {
        return todayWaitNum - yesterdayWaitNum;
    }

    public int getDiffDelayNum() {
        return delay7daysNum - yesterdayDelayNum;
    }

    public int getHandledDiffYeserday() {
        return todayHandleNum - yesterdayHandleNum;
    }

    public int getDiffWaitVerifyNumYesterday() {
        return todayWaitVerifyNum - yesterdayWaitVerifyNum;
    }

    public int getDiffCloseNumYesterday() {
        return todayCloseNum - yesterdayCloseNum;
    }

    public int getDiffOperateNumYesterday() {
        return todayOperateNum - yesterdayOperateNum;
    }
}
