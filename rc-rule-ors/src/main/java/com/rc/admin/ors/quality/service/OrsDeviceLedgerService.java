package com.rc.admin.ors.quality.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.excel.DeviceLedgerExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerNotActiveExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerOfflineExcel;
import com.rc.admin.ors.quality.model.DeviceLedgerReq;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
public interface OrsDeviceLedgerService {

    /**
     * 查询台账分页
     * @param page  分页对象
     * @param req   查询条件
     * @return  分页数据
     */
    Page<DeviceLedgerResp>  page(Page<DeviceLedgerResp> page, DeviceLedgerReq req);


    Long deviceOfflineCount();

    /**
     * 台账详情
     * @param id    产品id
     * @return  设备基本信息和扩展信息
     */
    DeviceLedgerResp detail(String id);


    /**
     * 根据离线时长返回对应的字符串显示
     * @param offlineTime
     * @return
     */
    String getOfflineTimeString(Date offlineTime);

    /**
     * 导出离线设备到指定路径
     * @return
     */
    ByteArrayOutputStream exportOfflineDevicesToPath(String newDivisionCode);

    /**
     * 导出未激活设备到指定路径
     * @return
     */
    ByteArrayOutputStream exportNotActiveDevicesToPath(String newDivisionCode);

    public DeviceLedgerExcel buildDeviceLedgerExcelVO(DeviceLedgerResp x, AtomicInteger ai);

    public DeviceLedgerOfflineExcel buildDeviceLedgerOfflineExcelVO(DeviceLedgerResp x, AtomicInteger ai);

    public DeviceLedgerNotActiveExcel buildDeviceLedgerNotActiveExcelVO(DeviceLedgerResp x, AtomicInteger ai);


    public String getHXModelStr();

}
