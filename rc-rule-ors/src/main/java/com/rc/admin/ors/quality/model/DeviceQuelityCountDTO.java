package com.rc.admin.ors.quality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/21 18:51
 * @describe
 */
@Getter
@Setter
public class DeviceQuelityCountDTO {

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("日期")
    private String day;

    private List<cData> datas;


    @Getter
    @Setter
    public static class cData {
        @ApiModelProperty("事业部")
        private String division;

        @ApiModelProperty("事业部编码")
        private String deviceCode;

        @ApiModelProperty("总数")
        private int total;

        @ApiModelProperty("正常数量")
        private int normalTotal;

        @ApiModelProperty("异常数量")
        private int abnormalTotal;

        @ApiModelProperty("比例")
        private double scale;
    }
}
