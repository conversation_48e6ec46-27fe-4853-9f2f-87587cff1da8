package com.rc.admin.ors.quality.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 设备剔除检查配置(OrsDeviceCheckConfig)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-23 18:27:53
 */
@SuppressWarnings("serial")
@Getter
@Setter
public class OrsDeviceCheckConfigImportExcel {

    @Excel(name = "*关键字", width = 15)
    private String deviceCode;

    @Excel(name = "*剔除类型(整机剔除直接填写整机)", width = 25)
    private String propertyName;

    @Excel(name = "*剔除原因", width = 20)
    private String excludeResean;

}

