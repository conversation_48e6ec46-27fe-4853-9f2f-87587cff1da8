<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper">


    <select id="deviceOfflineCount" resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total
        FROM
            dqm.ors_base_device_info AS obdi
                LEFT JOIN ( SELECT asset_id, string_agg ( exclude_type, ', ' ) AS exclude_type FROM dqm.ors_device_check_config GROUP BY asset_id ) odcc ON odcc.asset_id = obdi.asset_id
                LEFT JOIN ( SELECT device_name, MAX ( param_value_latest_time ) AS param_value_latest_time FROM dqm.ors_core_param_stat_latest GROUP BY device_name ) ocps ON ocps.device_name = obdi.asset_id
                INNER JOIN dqm.ors_model_division omd ON omd.model_id = obdi.model_id
        WHERE
            obdi.device_status = 2
          AND date_part( 'day', CURRENT_TIMESTAMP - ocps.param_value_latest_time ) >= 60;
    </select>

    <select id="ledgerPage" resultType="com.rc.admin.ors.quality.model.DeviceLedgerResp">
        SELECT
            omd.division_code as newDivisionCode,
            omd.product_group_code as newProductGroupCode,
            omd.division_name as newDivisionName,
            omd.product_group_name as newProductGroupName,
            obdi.rc_asset_id,
            obdi.division,
            obdi.product_group,
            obdi.device_code AS device_no,
            obdi.asset_id,
            obdi.model_id,
            obdi.thing_id as uuid,
            obdi.device_name as name,
            obdi.model_name,
            obdi.created,
            date_part('day', CURRENT_TIMESTAMP - obdi.created)+1 AS not_activat_days,
            obdi.region,
            obdi.country,
            obdi.country_code,
            obdi.factory_date,
            obdi.install_type,
            obdi.asset_id AS id,
            obdi.crm_register,
            CASE WHEN odcc.exclude_type='WHOLE' THEN '0'
                WHEN odcc.exclude_type != 'WHOLE' AND odcc.exclude_type IS NOT NULL THEN '1'
                ELSE NULL
            END AS is_eliminate,
            (case when obdi.device_status = 2 then date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time)+1 else null end) AS offline_days,
            (case when obdi.device_status = 2 then ocps.param_value_latest_time else null end) AS offline_time,
            obdi.exce_flag,
            obdi.exce_desc,
            obdi.hw_version,
            obdi.device_status,
            obdi.data_center_id,
            obdi.fw_version
        FROM
            dqm.ors_base_device_info as obdi
            LEFT JOIN (
                SELECT asset_id, string_agg(exclude_type, ', ') AS exclude_type FROM dqm.ors_device_check_config GROUP BY asset_id
            ) odcc ON odcc.asset_id = obdi.asset_id
            LEFT JOIN (
                SELECT device_name, MAX(param_value_latest_time) as param_value_latest_time FROM dqm.ors_core_param_stat_latest GROUP BY device_name
            ) ocps ON ocps.device_name = obdi.asset_id
            INNER JOIN  dqm.ors_model_division omd ON omd.model_id = obdi.model_id
            <if test="req.newDivisionCode != null and req.newDivisionCode != ''">
                AND omd.division_code IN
                <foreach item="item" index="index" collection="req.newDivisionCode.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.newProductGroupCode != null and req.newProductGroupCode != ''">
                AND omd.product_group_code IN
                <foreach item="item" index="index" collection="req.newProductGroupCode.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


        <where>
            <if test="req.dataCenterId != null">
                AND obdi.data_center_id = #{req.dataCenterId}
            </if>
            <if test="req.deviceStatus != null">
                AND obdi.device_status = #{req.deviceStatus}
            </if>
            <if test="req.division != null and req.division != ''">
                AND obdi.division_code IN
                <foreach item="item" index="index" collection="req.division.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.modelId != null and req.modelId != ''">
                AND obdi.model_id IN
                <foreach item="item" index="index" collection="req.modelId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.region != null and req.region != ''">
                AND obdi.region_code IN
                <foreach item="item" index="index" collection="req.region.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.country != null and req.country != ''">
                AND obdi.country_code IN
                <foreach item="item" index="index" collection="req.country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.productGroup != null and req.productGroup != ''">
                AND obdi.product_group_code IN
                <foreach item="item" index="index" collection="req.productGroup.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.beginDate != null">
                AND ocps.param_value_latest_time >= #{req.beginDate}
            </if>
            <if test="req.endDate != null">
                AND ocps.param_value_latest_time <![CDATA[<=]]> #{req.endDate}
            </if>
            <if test="req.factoryDuration != null and req.factoryDuration=='5'.toString()">
                AND obdi.factory_date is null
            </if>
            <if test="req.assetId != null and req.assetId != ''">
                AND (
                    upper(obdi.asset_id) LIKE CONCAT('%', upper(#{req.assetId}), '%')
                        OR
                    obdi.asset_id IN
                    <foreach item="item" index="index" collection="req.assetId.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.rcAssetId != null and req.rcAssetId != ''">
                AND (
                upper(obdi.rc_asset_id) LIKE CONCAT('%', upper(#{req.rcAssetId}), '%')
                OR
                obdi.rc_asset_id IN
                <foreach item="item" index="index" collection="req.rcAssetId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.name != null and req.name != ''">
                AND (
                    upper(obdi.device_name) LIKE CONCAT('%', upper(#{req.name}), '%')
                    OR
                    obdi.device_name IN
                    <foreach item="item" index="index" collection="req.name.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.modelName != null and req.modelName != ''">
                AND (
                    upper(obdi.model_name) = upper(#{req.modelName})
                    OR
                    obdi.model_name IN
                    <foreach item="item" index="index" collection="req.modelName.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.createdBegin != null">
                AND obdi.created &gt;= #{req.createdBegin}
            </if>
            <if test="req.createdEnd != null">
                AND obdi.created &lt;= #{req.createdEnd}
            </if>
            <if test="req.deviceNo != null and req.deviceNo != ''">
                AND (
                    upper(obdi.device_code) like CONCAT('%', upper(#{req.deviceNo}), '%')
                        OR
                    obdi.device_code IN
                    <foreach item="item" index="index" collection="req.deviceNo.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.crmRegister != null ">
                AND obdi.crm_register = #{req.crmRegister}
            </if>
            <if test="req.searchDeviceNo != null and req.searchDeviceNo != ''">
                AND (upper(obdi.device_code) LIKE CONCAT('%', upper(#{req.searchDeviceNo}), '%') OR upper(obdi.thing_id) LIKE CONCAT('%', upper(#{req.searchDeviceNo}), '%') OR upper(obdi.asset_id) LIKE CONCAT('%', upper(#{req.searchDeviceNo}), '%')) AND obdi.device_code IS NOT NULL
            </if>
            <if test="req.isEliminate != null and req.isEliminate != ''">
                <if test="req.isEliminate == '0'.toString()">
                    AND odcc.exclude_type = 'WHOLE'
                </if>
                <if test="req.isEliminate != '0'.toString()">
                    AND odcc.exclude_type !='WHOLE' AND odcc.exclude_type IS NOT NULL
                </if>
            </if>
            <if test="req.offlineTimeCode != null and req.offlineTimeCode != ''">
                AND obdi.device_status = 2
                <trim prefix="AND (" suffix=")" prefixOverrides="OR">
                    <if test="req.offlineTimeCode.contains('1'.toString())">
                        OR date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt; 1
                    </if>
                    <if test="req.offlineTimeCode.contains('2'.toString())">
                        OR (date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt;= 7 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt;= 1)
                    </if>
                    <if test="req.offlineTimeCode.contains('3'.toString())">
                        OR (date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt;= 15 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt; 7)
                    </if>
                    <if test="req.offlineTimeCode.contains('4'.toString())">
                        OR (date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt;= 30 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt; 15)
                    </if>
                    <if test="req.offlineTimeCode.contains('5'.toString())">
                        OR (date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt;= 60 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt; 30)
                    </if>
                    <if test="req.offlineTimeCode.contains('6'.toString())">
                        OR (date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &lt;= 180 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt; 60)
                    </if>
                    <if test="req.offlineTimeCode.contains('7'.toString())">
                        OR date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt; 180
                    </if>
                </trim>
            </if>
            <if test="req.installType != null">
                AND obdi.install_type = #{req.installType}
            </if>
            <if test="req.agentName != null and req.agentName != ''">
                AND obdi.agent like concat('%', #{req.agentName},'%')
            </if>
            <if test="req.userName != null and req.userName != ''">
                AND obdi.customer_name like concat('%', #{req.userName},'%')
            </if>
            <if test="req.deviceCodes != null and req.deviceCodes.size > 0">
                AND obdi.device_code IN
                <foreach collection="req.deviceCodes" open="(" close=")" item="deviceCode" separator=",">
                    #{deviceCode}
                </foreach>
            </if>
            <if test="req.exceFlag != null and req.exceFlag != ''">
                AND obdi.exce_flag = CAST( #{req.exceFlag} AS INT2)
            </if>

            <!--设备离线时间超过60天定义为长期离线设备-->
            <if test="req.queryModel != null and req.queryModel != '' and req.queryModel == 'offline_list'.toString()">
                AND obdi.device_status = 2 AND date_part('day', CURRENT_TIMESTAMP - ocps.param_value_latest_time) &gt;= 60
            </if>
            <!--设备从注册时间当天算起，超过60天仍处于未激活状态定义为长期未激活设备-->
            <if test="req.queryModel != null and req.queryModel != '' and req.queryModel == 'not_activat_list'.toString()">
                AND obdi.device_status = 1 AND date_part('day', CURRENT_TIMESTAMP - obdi.created) &gt;= 60
            </if>
        </where>
        ORDER BY ${req.sortColumn} ${req.sortBy}
    </select>

    <select id="findLedger" resultType="com.rc.admin.ors.quality.model.DeviceLedgerResp">
        SELECT
            omd.division_name as newDivisionName,
            omd.product_group_name as newProductGroupName,
            obdi.rc_asset_id,
            obdi.asset_id AS asset_id,
            obdi.thing_id as uuid,
            obdi.device_name as name,
            obdi.model_name,
            obdi.model_id,
            obdi.created,
            obdi.factory_date,
            obdi.hw_version,
            obdi.fw_version,
            obdi.auth_token,
            obdi.data_source,
            obdi.data_center_id,
            obdi.division,
            obdi.device_code AS device_no,
            obdi.region,
            obdi.country,
            obdi.product_group,
            obdi.agent,
            obdi.service,
            obdi.delivery_date,
            obdi.open_date,
            obdi.crm_register,
            CASE WHEN odcc.exclude_type='WHOLE' THEN '0'
                WHEN odcc.exclude_type != 'WHOLE' AND odcc.exclude_type IS NOT NULL THEN '1'
                ELSE NULL
            END AS is_eliminate,
            obdi.exce_flag,
            obdi.exce_desc,
            obdi.tbox_id
        FROM
            dqm.ors_base_device_info as obdi
            LEFT JOIN (
                SELECT asset_id, string_agg(exclude_type, ', ') AS exclude_type FROM dqm.ors_device_check_config GROUP BY asset_id
            ) odcc ON odcc.asset_id = obdi.asset_id
            INNER JOIN  dqm.ors_model_division omd ON omd.model_id = obdi.model_id
        <where>
            <if test="id != null and id != ''">
                obdi.asset_id = #{id}
            </if>
            <if test="deviceNo != null and deviceNo != ''">
                obdi.device_code = #{deviceNo}
            </if>
        </where>
    </select>

    <select id="countryList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            country_code AS value,
            country AS name
        FROM
            dqm.ors_base_device_info
        WHERE
            country_code IS NOT NULL
          AND country IS NOT NULL
        GROUP BY
            country_code,
            country
    </select>

    <select id="divisionList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            division_code AS value,
            division AS name
        FROM
            dqm.ors_base_device_info
        WHERE
            division_code IS NOT NULL
          AND division IS NOT NULL
        GROUP BY
            division_code,
            division
    </select>

    <select id="regionList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            region_code AS value,
            region AS name
        FROM
            dqm.ors_base_device_info
        WHERE
            region_code IS NOT NULL
          AND region IS NOT NULL
        GROUP BY
            region_code,
            region
    </select>

    <select id="newRegionList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            region_code AS value,
            region AS name
        FROM
            dqm.ors_country_region_sync
        WHERE
            region IS NOT NULL
            AND region_code IS NOT NULL
        GROUP BY region_code, region
    </select>

    <select id="productGroupList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            product_group_code AS value,
            product_group AS name
        FROM
            dqm.ors_base_device_info
        WHERE
            product_group_code IS NOT NULL
          AND product_group IS NOT NULL
        GROUP BY
            product_group_code,
            product_group
    </select>

    <select id="newCountryList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        select DISTINCT
            country_code AS value,
            country AS name
            FROM
            dqm.ors_base_device_info
        WHERE
            country_code IS NOT NULL
          AND country IS NOT NULL
    </select>

    <select id="newCountryDeviceList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            cnty_code   AS value,
            cnty_desc  AS name
        FROM
            dqm.ot_device_all
        where
            cnty_code IS NOT NULL
          AND cnty_desc IS NOT NULL
    </select>

    <select id="newCountryRegionList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            country_region_code AS value,
            country_region AS name
        FROM
            dqm.ors_country_region
        WHERE
            country_region_code IS NOT NULL
          AND country_region IS NOT NULL
        GROUP BY
            country_region_code,
            country_region
    </select>


    <select id="newDivisionDeviceList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            case when  bu_inner_desc is null then '1' else bu_inner_desc end   AS value,
            case when bu_inner_desc is null then '无事业部' else  bu_inner_desc end   AS name
        FROM
            dqm.ot_device_all
            order by value desc
    </select>


    <select id="getUserList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT
            T.username AS name,
            T.username  AS value
        FROM
            sys_user T
    </select>


    <select id="newProductGroupDeviceList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            d365_spart AS value,
            d365_spart_desc AS name
        FROM
            dqm.ot_device_all
        where d365_spart is not null
            and d365_spart_desc is not null
            <if test="syb != null and syb != ''">
                <choose>
                    <when test="sybCheck != null and sybCheck != '' and sybCheck == '1'.toString()">

                    </when>
                    <otherwise>
                        and bu_inner_desc
                        IN
                        <foreach item="item" index="index" collection="syb.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        union all
        select '1' as value, '无产品组' as name
        order by value desc
    </select>



    <select id="newDivisionList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            division_code AS value,
            division_name AS name
        FROM
            dqm.ors_model_division
        WHERE
            division_code IS NOT NULL
          AND division_name IS NOT NULL
        GROUP BY
            division_code,
            division_name
    </select>

    <select id="newProductGroupList" resultType="com.rc.admin.ors.quality.model.OrsBasicData">
        SELECT DISTINCT
            product_group_code AS value,
            product_group_name AS name
        FROM
            dqm.ors_model_division
        WHERE
            product_group_code IS NOT NULL
          AND product_group_name IS NOT NULL
        <if test="divisionCode != null and divisionCode != ''">
            and division_code
            IN
            <foreach item="item" index="index" collection="divisionCode.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
            product_group_code,
            product_group_name
    </select>

    <select id="assetIdList" resultType="string">
        SELECT DISTINCT
            obdi.asset_id AS asset_id
        FROM
            dqm.ors_base_device_info as obdi
            where obdi.asset_id is not null
            <if test="assetId != null and assetId != ''">
                AND (
                    upper(obdi.asset_id) like CONCAT('%', upper(#{assetId}), '%')
                    OR
                    obdi.asset_id IN
                    <foreach item="item" index="index" collection="assetId.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
        group by obdi.asset_id
    </select>


    <select id="nameList" resultType="string">
        SELECT DISTINCT
            obdi.device_name as name
        FROM
            dqm.ors_base_device_info as obdi
        where obdi.device_name is not null
        <if test="thingName != null and thingName != ''">
            AND upper(obdi.device_name) LIKE CONCAT('%', upper(#{thingName}), '%')
        </if>
        group by obdi.device_name
    </select>


    <select id="modelNameListNew" resultType="com.rc.admin.ors.quality.model.ModelInfo">
        SELECT DISTINCT
           ( case when oimi.description is not null then oimi.description else oimi.name  end ) model_name,
            obdi.model_id
        FROM
        dqm.ors_base_device_info as obdi
        left join dqm.ors_iot_model_info oimi
        on obdi.model_id = oimi.model_id
        <where>
            <if test="(divisionCode != null and divisionCode != '')  or (productGroupCode != null and productGroupCode != '')">
                and obdi.model_id in
                (
                select model_id from dqm.ors_model_division
                <where>
                    <if test="divisionCode != null and divisionCode != ''">
                        and division_code
                        IN
                        <foreach item="item" index="index" collection="divisionCode.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="productGroupCode != null and productGroupCode != ''">
                        and product_group_code
                        IN
                        <foreach item="item" index="index" collection="productGroupCode.split(',')"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
                )
            </if>
        </where>
    </select>


    <select id="modelNameList" resultType="com.rc.admin.ors.quality.model.ModelInfo">
        SELECT DISTINCT
            obdi.model_name,
            obdi.model_id
        FROM
            dqm.ors_base_device_info as obdi
        <where>
            <if test="(divisionCode != null and divisionCode != '')  or (productGroupCode != null and productGroupCode != '')">
            and obdi.model_id in
            (
            select model_id from dqm.ors_model_division
            <where>
                <if test="divisionCode != null and divisionCode != ''">
                    and division_code
                    IN
                    <foreach item="item" index="index" collection="divisionCode.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="productGroupCode != null and productGroupCode != ''">
                    and product_group_code
                    IN
                    <foreach item="item" index="index" collection="productGroupCode.split(',')"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            )
            </if>
            <if test="key != null and key != ''">
                AND (obdi.model_name like concat('%',#{key},'%') or obdi.model_id like concat('%',#{key},'%') )
            </if>
        </where>
    </select>

    <select id="deviceNoList" resultType="com.rc.admin.ors.quality.model.OrsDeviceListResp">
        SELECT
            obdi.device_code AS device_no,
            obdi.model_id,
            obdi.asset_id as asset_id,
            obdi.model_name,
            obdi.thing_id as uuid
        FROM
        dqm.ors_base_device_info as obdi
            <where>
                <if test="deviceNo != null and deviceNo != ''">
                    AND obdi.asset_id like #{deviceNo} || '%'
                </if>
            </where>
        <if test="size != null">
            limit #{size}
        </if>
    </select>

    <insert id="transferData">
        -- 迁移model表数据
        delete from dqm.ors_iot_model_info;
        insert into dqm.ors_iot_model_info (
            category,
            dept_scope,
            dept_scope_full_name,
            description,
            full_group_name,
            model_id,
            model_key,
            NAME,
            thing_type,
            offline_check_period,
            is_published,
            timestamp_vary_range_millis,
            timestamp_unit,
            created,
            created_by,
            creator_id,
            updated,
            updated_by,
            updator_id,
            enable_self_reg,
            model_secret,
            enable_set_gateway,
            VERSION,
            is_abstract,
            has_active_version,
            tenant_id,
            publish_status,
            enable_diagnosis,
            cloud_data_visualization_name,
            send_input_switch,
            model_status,
            etl_time
        )
        select category,
               dept_scope,
               dept_scope_full_name,
               description,
               full_group_name,
               model_id,
               model_key,
               NAME,
               thing_type,
               offline_check_period,
               is_published,
               timestamp_vary_range_millis,
               timestamp_unit,
               created,
               created_by,
               creator_id,
               updated,
               updated_by,
               updator_id,
               enable_self_reg,
               model_secret,
               enable_set_gateway,
               VERSION,
               is_abstract,
               has_active_version,
               tenant_id,
               publish_status,
               enable_diagnosis,
               cloud_data_visualization_name,
               send_input_switch,
               model_status,
               current_date
        from dqm.ors_iot_model_info_sync;

        -- 迁移model_property
        delete from dqm.ors_iot_model_property;
        insert into dqm.ors_iot_model_property (
            NAME,
            display_name,
            property_type,
            persist_strategy,
            period,
            privilege,
            fixed,
            created_at,
            expression_type,
            expression,
            from_property,
            window_size_mills,
            window_step_mills,
            window_allowed_lateness_mills,
            priority,
            execute_after_window,
            trigger_strategy,
            use_online_mock,
            invoke_mode,
            skip_output,
            disabled,
            model_id,
            etl_time
        )
        select NAME,
               display_name,
               property_type,
               persist_strategy,
               period,
               privilege,
               fixed,
               created_at,
               expression_type,
               expression,
               from_property,
               window_size_mills,
               window_step_mills,
               window_allowed_lateness_mills,
               priority,
               execute_after_window,
               trigger_strategy,
               use_online_mock,
               invoke_mode,
               skip_output,
               disabled,
               model_id,
               current_date
        from dqm.ors_iot_model_property_sync;

        -- 迁移设备信息
        delete from dqm.ors_iot_device_info;
        insert into dqm.ors_iot_device_info (
            asset_id,
            model_id,
            thing_id,
            class_id,
            protocol,
            phase,
            NAME,
            description,
            user_name,
            auth_token,
            created,
            created_by,
            updated,
            updated_by,
            connection_type,
            manufacturer,
            model,
            fw_version,
            hw_version,
            sim_imsi,
            phone_number,
            data_source,
            install_type,
            asset_id_nrc,
            etl_time,
            oline_statu,
            active_statu,
            first_data_time,
            device_status,
            data_center_id,
            country,
            country_code
        )
        select asset_id,
               model_id,
               thing_id,
               class_id,
               protocol,
               phase,
               NAME,
               description,
               user_name,
               auth_token,
               created,
               created_by,
               updated,
               updated_by,
               connection_type,
               manufacturer,
               model,
               fw_version,
               hw_version,
               sim_imsi,
               phone_number,
               data_source,
               install_type,
               asset_id_nrc,
               current_date,
               oline_statu,
               active_statu,
               first_data_time,
               device_status,
               data_center_id,
               country,
               country_code
        from dqm.ors_iot_device_info_sync;
    </insert>

    <update id="upNoDivision">
        update dqm.ors_base_device_info set division='无事业部',division_code='0000000001' where division is null and division_code is null;
        update dqm.ors_base_device_info set product_group='无产品组',product_group_code='0000000001' where product_group is null and product_group_code is null;
        -- update dqm.ors_base_device_info set region_code='0000000001', region = '无大区' where region_code = '' or region_code is null;
        -- update dqm.ors_base_device_info set country_code='0000000001', country = '无国家' where country_code = '' or country_code is null;
    </update>


    <update id="createPartitionTable">
        CREATE TABLE IF NOT EXISTS dqm.ors_device_start_record_${tableName}
            PARTITION OF dqm.ors_device_start_record
            FOR VALUES FROM ('${startYear}') TO ('${endYear}');
    </update>

    <insert id="mergeDeviceInfo">
        --asset_id取最后一个_的数据,并且对RC进行去重
        -- update dqm.ors_iot_device_info set asset_id_nrc = replace(split_part(asset_id,'_',-1),'RC','');
--         update dqm.ors_iot_device_info set asset_id_nrc = split_part(asset_id,'_',-1);
        --清空表
        TRUNCATE TABLE dqm.ors_base_device_info;
        --重置uuid_key的序列值为1
        ALTER SEQUENCE dqm.ors_base_device_info_uuid_seq RESTART WITH 1;
        --迁移合并生成台账数据
        WITH temp AS (
            SELECT
                T1.rc_asset_id,
                T1.asset_id,
                T1.asset_id_nrc,
                T1.serial_num,
                T1.tbox_id,
                T1.ml_model_id,
                T1.thing_id,
                mi.model_id,
                mi.name,
                T1.serial_num,
                T1.name,
                T1.install_type,
                T1.created,
                1 AS crm_register,
                current_date,
                T1.hw_version,
                T1.fw_version,
                T1.auth_token,
                T1.data_source,
                T1.oline_statu,
                T1.active_statu,
                0,
                T1.ex,
                T1.first_data_time,
                device_status,
                data_center_id,
                T1.country,
                T1.country_code
            FROM
                (
                    SELECT
                        dir.rc_asset_id,
                        dbi.asset_id,
                        dbi.asset_id_nrc,
                        dbi.asset_id as serial_num,
                        dir.tbox_id,
                        dir.ml_model_id,
                        dbi.thing_id,
                        dbi.asset_id AS device_code,
                        dbi.NAME,
                        dbi.install_type,
                        dbi.created,
                        CURRENT_DATE,
                        dbi.hw_version,
                        dbi.fw_version,
                        dbi.auth_token,
                        dbi.data_source,
                        dbi.oline_statu,
                        dbi.active_statu,
                        dbi.model_id,
                        dbi.first_data_time,
                        CASE
                             WHEN COUNT ( * ) OVER ( PARTITION BY dbi.asset_id ) > 1 THEN '设备编码重复'
                             ELSE null
                            END AS ex,
                        device_status,
                        data_center_id,
                        dbi.country,
                        dbi.country_code
                    FROM dqm.ors_iot_device_info AS dbi
                             LEFT JOIN dqm.ors_ml_device_info_relation AS dir ON  dbi.thing_id = dir.thing_instance_id
                             AND dir.thing_model_id = dbi.model_id
                ) AS T1
                    LEFT JOIN dqm.ors_iot_model_info mi on mi.model_id = T1.model_id
        )
        insert into dqm.ors_base_device_info (
            rc_asset_id,
            asset_id,
            asset_id_nrc,
            serial_num,
            tbox_id,
            ml_model_id,
            thing_id,
            model_id,
            model_name,
            device_code,
            device_name,
            install_type,
            created,
            crm_register,
            etl_time,
            hw_version,
            fw_version,
            auth_token,
            data_source,
            oline_statu,
            active_statu,
            exce_flag,
            exce_desc,
            first_data_time,
               device_status,
               data_center_id,
                country,
                country_code
        ) SELECT * FROM temp;
        --  标记异常数据
        UPDATE dqm.ors_base_device_info SET exce_flag = 1 WHERE exce_desc IS  NOT NULL;

        -- 处理已激活但是没有激活日期的数据，默认将创建时间作为激活时间
        UPDATE dqm.ors_base_device_info SET first_data_time=created WHERE active_statu=TRUE AND  first_data_time IS NULL;
    </insert>


    <select id="getPropertyModel" resultType="com.rc.admin.ors.quality.model.PropertyModel">
        select
        model_id,param_code,
        sum(case when reportType = 0 then 1 else 0 END) property_param_abnormal_total_num,
        sum(case when reportType = 2 then 1 else 0 END) property_null_abnormal_total_num,
        case when sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when reportType = 0 then 0 when reportType = 1 then 100 END)::numeric/sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)::numeric,2)
        ELSE 0  END
        as check_param_abnormal_rate,
        case when sum(case when (reportType = 1 or reportType = 2) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when reportType = 2 then 0 when reportType = 1 then 100  END)::numeric/sum(case when (reportType = 1 or reportType = 2) then 1 else 0 END)::numeric,2)
        ELSE 0  END
        as check_null_abnormal_rate
        FROM(
        select bdi.model_id,aa.device_name,aa.param_code,aa.stat_date,
        case when reportType = 1 AND T7.abnormal_code is not null then 0 else  reportType end as reportType
        from
        (
        select  device_name,param_code,stat_date,1 as reportType from  sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>

        union ALL
        select device_name,param_code,stat_date,2 as reportType from dqm.ors_device_data_abnormal_detail_day where abnormal_code in('9008','9009')
        <if test="startTime != null and startTime != ''">
            AND stat_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date &lt;= #{endTime}::DATE
        </if>
        )aa
        left join ors_base_device_info bdi on bdi.asset_id = aa.device_name
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = aa.device_name
        and T2.param_code = aa.param_code
        <if test="endTime != null and endTime != ''">
            AND T2.create_time  &lt;= #{endTime}::TIMESTAMP
        </if>
        left JOIN dqm.ors_device_data_abnormal_detail_day T7 on aa.device_name = T7.device_name
        and aa.param_code = T7.param_code  and aa.stat_date = T7.stat_date and   T7.abnormal_code not  in('9008','9009')
        WHERE
        T2.param_code is null
        and
        aa.device_name NOT IN ( SELECT asset_id FROM dqm.ors_device_check_config T2 WHERE exclude_type = 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND T2.create_time  &lt;= #{endTime}::TIMESTAMP
        </if>
        )
        )bb
        group by model_id,param_code
    </select>




    <select id="getDeviceModelByDay" resultType="com.rc.admin.ors.quality.model.DeviceModel">
        SELECT
        bdi.model_id,
        round( COALESCE ( AVG (1- bb.param_abnormal_rate ), 0 ), 4 ) AS param_abnormal_rate,
        round( COALESCE ( AVG (1- bb.null_abnormal_rate ), 0 ), 4 ) AS null_abnormal_rate
        FROM
        dqm.ors_device_rate_day bb
        LEFT JOIN dqm.ors_base_device_info bdi ON bdi.asset_id = bb.asset_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location
        FROM dqm.ors_device_location
            WHERE device_location = '国内'
            <if test="startTime != null and startTime != ''">
                AND stat_date::DATE &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date::DATE &lt;= #{endTime}::DATE
            </if>
        ) T5 ON bb.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="startTime != null and startTime != ''">
                AND bb.stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND bb.stat_date &lt;= #{endTime}::DATE
            </if>
            <if test="country != null and country != ''">
                AND bdi.country_code IN
                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            bdi.model_id
    </select>



    <select id="getDeviceModel" resultType="com.rc.admin.ors.quality.model.DeviceModel">
        select bdi.model_id,round(COALESCE(avg(bb.accuracy),0),2) as param_abnormal_rate,round(COALESCE(avg(bb.complete),0),2) as null_abnormal_rate
        from
        (
        select device_name,stat_date,
        case when sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when reportType = 0 then 0 when reportType = 1 then 100 END)::numeric/sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)::numeric,2)
        ELSE null  END
        as accuracy,

        case when sum(case when (reportType = 0 or reportType = 1 or reportType = 2) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when (reportType = 1 OR reportType=0)  then 100  else 0  END)::numeric/sum(case when (reportType = 0 or reportType = 1 or reportType = 2) then 1 else 0 END)::numeric,2)
        ELSE null  END
        as complete

        FROM
        (
        select aa.device_name,aa.param_code,aa.stat_date,
        case when reportType = 1 AND T7.abnormal_code is not null then 0 else  reportType end as reportType
        from
        (
        select  device_name,param_code,stat_date,1 as reportType from  sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>

        union ALL
        select device_name,param_code,stat_date,2 as reportType from dqm.ors_device_data_abnormal_detail_day where abnormal_code in('9008','9009')
        <if test="startTime != null and startTime != ''">
            AND stat_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date &lt;= #{endTime}::DATE
        </if>
        )aa
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = aa.device_name  and T2.param_code = aa.param_code
        <if test="endTime != null and endTime != ''">
            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        left JOIN dqm.ors_device_data_abnormal_detail_day T7 on aa.device_name = T7.device_name
        and aa.param_code = T7.param_code  and aa.stat_date = T7.stat_date and   T7.abnormal_code not  in('9008','9009')
        WHERE
        T2.param_code is null
        and
        aa.device_name NOT IN ( SELECT asset_id FROM dqm.ors_device_check_config T2 WHERE exclude_type = 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        )
        )T8
        group by device_name,stat_date
        )bb
        left join ors_base_device_info bdi on bdi.asset_id = bb.device_name
        group by  bdi.model_id


    </select>


    <select id="countDeviceByConditionNew" resultType="com.rc.admin.ors.quality.model.DeviceCountModelNew">
        SELECT
        omd.division_name AS division,
        omd.division_code,
        T1.model_id,
        MAX ( imi.NAME ) AS model_name,
        COUNT ( T1.asset_id ) AS total,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
        COUNT ( CASE WHEN T1.crm_register = 1 THEN T1.asset_id END ) AS regist_num,
        COUNT ( CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END ) AS un_regist_num,
        COUNT ( CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) AS active_num,
        </if>
        COALESCE(SUM(T3.total),0)  AS total_report_num,
        COALESCE(SUM(T8.total),0)  AS param_abnormal_total_num,
        COALESCE(SUM(T9.total),0)  AS null_abnormal_total_num,
        COALESCE(SUM(T2.total),0)  AS whole_exclud_num
        FROM
        dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="modelIds != null and modelIds != ''">
            AND omd.model_id IN
            <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        LEFT JOIN dqm.ors_iot_model_info imi ON imi.model_id = T1.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location FROM dqm.ors_device_location WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id
        LEFT JOIN (
        SELECT DISTINCT asset_id, 1 AS total
        FROM
        dqm.ors_device_check_config
        WHERE
        exclude_type = 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        ) T2 ON T2.asset_id = T1.asset_id

        LEFT JOIN (
        SELECT device_name, 1 AS total
        FROM
        sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY device_name
        ) T3 ON T3.device_name = T1.asset_id
        LEFT JOIN (
        SELECT device_name,1 AS total
        FROM
        dqm.ors_device_data_abnormal_detail_day
        WHERE
        abnormal_code NOT IN ( '9008', '9009' )
        <if test="startTime != null and startTime != ''">
            AND stat_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date &lt;= #{endTime}::DATE
        </if>
        GROUP BY device_name
        ) T8 ON T8.device_name = T1.asset_id
        LEFT JOIN (
        SELECT device_name, 1 AS total
        FROM dqm.ors_device_data_abnormal_detail_day
        WHERE
        abnormal_code IN ( '9008', '9009' )
        <if test="startTime != null and startTime != ''">
            AND stat_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date &lt;= #{endTime}::DATE
        </if>
        GROUP BY device_name
        ) T9 ON T9.device_name = T1.asset_id
        WHERE
        T5.device_location IS NULL
        <if test="agentName != null and agentName != ''">
            AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
        </if>
        <if test="installType != null and installType != '0'.toString()">
            AND T1.install_type = #{installType}::INT4
        </if>
        <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
            AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
        </if>
        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
            AND (
            omd.division_code IN
            <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="country != null and country != ''">
            AND T1.country_code IN
            <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        omd.division_name,
        omd.division_code,
        T1.model_id
    </select>


    <select id="countDeviceByConditionOld" resultType="com.rc.admin.ors.quality.model.DeviceCountModelNew">
        SELECT
        omd.division_name  as division,
        omd.division_code,
        T1.model_id,
        max(imi.name) AS model_name,
        COUNT ( DISTINCT T1.asset_id ) AS total,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
        COUNT ( DISTINCT CASE WHEN T1.crm_register = 1 THEN T1.asset_id END ) as regist_num ,
        COUNT ( DISTINCT CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END )  as un_regist_num,
        COUNT ( DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) as active_num,
        </if>
        COUNT ( DISTINCT CASE WHEN T3.device_name IS NOT NULL THEN T1.asset_id END  )  as total_report_num,
        COUNT ( DISTINCT CASE WHEN T8.device_name IS NOT NULL THEN T1.asset_id END  )  as param_abnormal_total_num,
        COUNT ( DISTINCT CASE WHEN T9.device_name IS NOT NULL THEN T1.asset_id END  )  as null_abnormal_total_num,
        COUNT ( DISTINCT CASE WHEN T2.asset_id IS NOT NULL  THEN T1.asset_id END)    as whole_exclud_num
        FROM
        dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="modelIds != null and modelIds != ''">
            AND omd.model_id IN
            <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left JOIN dqm.ors_iot_model_info imi on imi.model_id = T1.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location FROM dqm.ors_device_location WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = T1.asset_id
        AND T2.exclude_type = 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        left join (select device_name from sany_data_service.sanyds_core_param_stat_latest_day
        <where>
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        group by device_name)T3
        ON T3.device_name = T1.asset_id
        left join  (select device_name from dqm.ors_device_data_abnormal_detail_day
        <where>
            and abnormal_code not  in('9008','9009')
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY device_name)T8
        on T8.device_name = T1.asset_id
        left join  (select device_name from dqm.ors_device_data_abnormal_detail_day
        <where>
            and abnormal_code  in('9008','9009')
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
        </where>
        GROUP BY device_name)T9
        on T9.device_name = T1.asset_id
        <where>
            and (T5.device_location is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                AND (
                omd.division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="country != null and country != ''">
                AND T1.country_code IN
                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        omd.division_name,
        omd.division_code,
        T1.model_id
    </select>





    <select id="countDeviceByParamCodeNew" resultType="com.rc.admin.ors.quality.model.DeviceCountModelNew">
        select *,
        (case when report!=0 then ROUND((report-property_param_abnormal_total_num)::numeric/report::numeric,4) else 0
        end) as check_param_abnormal_rate,
        (case when report+property_null_abnormal_total_num!=0 then
        ROUND(report::numeric/(report+property_null_abnormal_total_num)::numeric,4) else 0 end) as
        check_null_abnormal_rate
        from (
        SELECT
        max(T1.division) as division,
        max(T1.division_code) as division_code,
        T1.model_id,
        T4.property,
        max(T4.property_name) as property_name,
        max(sd.dict_type) as dict_type,
        max(ct.name) as dict_type_name,
        COUNT (
        DISTINCT
        CASE
        WHEN T3.asset_id IS NOT NULL
        AND T2.asset_id is null
        AND T3.null_param_codes IS NOT NULL
        AND T3.null_param_codes LIKE CONCAT ( '%', T4.param_code :: TEXT, '%' ) THEN
        T1.asset_id
        END
        ) AS property_null_abnormal_total_num,
        COUNT (
        DISTINCT
        CASE
        WHEN T3.asset_id IS NOT NULL
        AND T2.asset_id is null
        AND T3.abnormal_param_codes IS NOT NULL
        AND T3.abnormal_param_codes LIKE CONCAT ( '%', T4.param_code :: TEXT, '%' ) THEN
        T1.asset_id
        END
        ) AS property_param_abnormal_total_num,
        COUNT ( DISTINCT CASE WHEN T2.param_code is not null THEN T1.asset_id END ) AS property_exclud_num,
        COUNT(
        DISTINCT
        case when T3.asset_id IS NOT NULL
        AND T2.asset_id is null
        AND T3.report_param_codes LIKE CONCAT ( '%', T4.param_code :: TEXT, '%' ) THEN
        T1.asset_id  END) as report
        FROM
        (
        SELECT
        T1.asset_id,
        omd.division_name AS division,
        omd.division_code,
        omd.product_group_name AS product_group,
        omd.product_group_code,
        T1.crm_register,
        T1.active_statu,
        T1.model_id
        FROM
        dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="modelIds != null and modelIds != ''">
            AND omd.model_id IN
            <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left JOIN dqm.ors_iot_model_info imi on imi.model_id = T1.model_id
        LEFT JOIN ( SELECT DISTINCT asset_id, device_location FROM dqm.ors_device_location WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387',
                'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="endTime != null and endTime != ''">
                <if test="storeCategory != null and storeCategory == '2'.toString()">
                    AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                </if>
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                AND (
                omd.division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="country != null and country != ''">
                AND T1.country_code IN
                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) T1
        INNER JOIN dqm.ors_model_properties_config T4 ON T4.model_id = T1.model_id
        <if test="inspection != null and inspection != ''">
            AND T4.param_code IN
            <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                #{item}::INT4
            </foreach>
        </if>
        <if test="dictTypeName != null and dictTypeName != ''">
            AND T4.param_code IN
            (
            select dict_id from sany_data_service.sanyds_dict  where dict_type in
            <foreach item="item" index="index" collection="dictTypeName.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        left join sany_data_service.sanyds_dict sd on sd.dict_id = T4.param_code
        left join sys_dict ct on ct.code = sd.dict_type and ct.dict_type = 'check_type'
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = T1.asset_id
        <if test="endTime != null and endTime != ''">
            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        and T2.param_code = T4.param_code and T2.exclude_type != 'WHOLE'

        LEFT JOIN (
        SELECT
        T3.param_codes,
        T3.null_param_codes,
        T3.abnormal_param_codes,
        T3.report_param_codes,
        T3.asset_id
        FROM
        dqm.ors_device_param_report_log T3
        WHERE
        T3.asset_id NOT IN (
        SELECT asset_id FROM dqm.ors_device_check_config T2
        <where>
            exclude_type = 'WHOLE'
            <if test="endTime != null and endTime != ''">
                AND T2.create_time &lt;= #{endTime}::TIMESTAMP
            </if>
        </where>
        )
        <if test="startTime != null and startTime != ''">
            AND T3.report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND T3.report_date &lt;= #{endTime}::DATE
        </if>
        ) T3 ON T3.asset_id = T1.asset_id
        GROUP BY
        T1.model_id,
        T4.property
        )aa
    </select>

    <select id="countDeviceByConditionDivision" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
        aa.division,
        aa.division_code,
        aa.model_id,
        MAX (aa.model_name ) AS model_name,
        count(1) AS total,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            COUNT ( CASE WHEN aa.crm_register = 1 THEN aa.asset_id END ) AS regist_num,
            COUNT ( CASE WHEN aa.crm_register = 0 OR aa.crm_register IS NULL THEN aa.asset_id END ) AS un_regist_num,
            COUNT ( CASE WHEN aa.active_statu = TRUE THEN aa.asset_id END ) AS active_num,
        </if>
        COALESCE (sum ( aa.whole_exclud ),0) AS whole_exclud_num,
        COALESCE (sum ( aa.total_property_exclud ),0) AS total_property_exclud_num,
        COALESCE (sum (aa.abnormal_codes),0) AS abnormal_total_num,
        COALESCE (sum (aa.null_param_codes ),0) AS null_abnormal_total_num,
        COALESCE (sum (aa.abnormal_param_codes ),0) AS param_abnormal_total_num,
        COALESCE (sum (CASE WHEN aa.deviceName IS NOT NULL THEN 1 else 0 END ),0) AS total_report_num,
        COALESCE (sum (CASE WHEN aa.deviceName IS NULL THEN 1 else 0 END ),0) AS total_un_report_num
        FROM
        (select
        T1.asset_id,
        T3.asset_id as deviceName,
        case when T3.division IS NOT NULL THEN T3.division ELSE T1.division END AS division,
        case when T3.division_code IS NOT NULL THEN T3.division_code ELSE T1.division_code END AS division_code,
        T1.model_id,
        T1.model_name,
        T1.crm_register,
        T1.active_statu,
        T4.whole_exclud,
        T5.total_property_exclud,
        abnormal_codes,
        null_param_codes,
        abnormal_param_codes
        from
        ( SELECT
        T1.asset_id,
        <if test="sybbh == '1'.toString()">
            omd.division_name AS division,
            omd.division_code,
        </if>
        omd.product_group_name AS product_group,
        omd.product_group_code,
        T1.crm_register,
        T1.active_statu,
        T1.model_id,
        T1.model_name
        FROM dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join (
        select distinct asset_id, device_location from dqm.ors_device_location
        where device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 on T1.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386',
                'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>

            <if test="modelIds != null and modelIds != ''">
                AND omd.model_id IN
                <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="country != null and country != ''">
                AND T1.country_code IN
                <foreach item="item" index="index" collection="country.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) T1
        LEFT JOIN (
        select
        1 AS param_codes,
        case when (max(T3.null_param_codes) is not null or max(T3.abnormal_param_codes) is not null) then 1 else 0 end
        as abnormal_codes,
        case when max(T3.null_param_codes) is not null then 1 else 0 end as null_param_codes,
        case when max(T3.abnormal_param_codes) is not null then 1 else 0 end as abnormal_param_codes,
        T3.asset_id,
        max (od.name) as division,
        max(T3.division_code) as division_code
        FROM
        dqm.ors_device_param_report_log T3
        left join (SELECT division_code as value,division_name as name FROM dqm.ors_model_division group by
        division_code,division_name) od on T3.division_code = od.value
        WHERE
        T3.asset_id NOT IN (
        SELECT asset_id FROM dqm.ors_device_check_config T2
        <where>
            exclude_type = 'WHOLE'
            <if test="endTime != null and endTime != ''">
                AND T2.create_time &lt;= #{endTime}::TIMESTAMP
            </if>
        </where>
        )
        <if test="startTime != null and startTime != ''">
            AND T3.report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND T3.report_date &lt;= #{endTime}::DATE
        </if>
        group by T3.asset_id
        ) T3 ON T3.asset_id = T1.asset_id
        left join
        (select asset_id,1 as whole_exclud from dqm.ors_device_check_config
        where exclude_type = 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        group by asset_id)T4 ON T4.asset_id = T1.asset_id
        left join
        (select asset_id,1 as total_property_exclud from dqm.ors_device_check_config
        where exclude_type != 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        group by asset_id)T5 ON T5.asset_id = T1.asset_id
        )aa
         <where>
             <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                 AND (
                 aa.division_code IN
                 <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
                 )
             </if>
         </where>
        GROUP BY
        aa.division,
        aa.division_code,
        aa.model_id
    </select>



    <select id="countDeviceByConditionChange" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
        T1.division,
        T1.division_code,
        T1.model_id,
        MAX (T1.model_name ) AS model_name,
        count(1) AS total,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            COUNT ( CASE WHEN T1.crm_register = 1 THEN T1.asset_id END ) AS regist_num,
            COUNT ( CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END ) AS un_regist_num,
            COUNT ( CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) AS active_num,
        </if>
        COALESCE (sum ( T4.whole_exclud ),0) AS whole_exclud_num,
        COALESCE (sum ( T5.total_property_exclud ),0) AS total_property_exclud_num,
        COALESCE (sum (abnormal_codes),0) AS abnormal_total_num,
        COALESCE (sum (null_param_codes ),0) AS null_abnormal_total_num,
        COALESCE (sum ( abnormal_param_codes ),0) AS param_abnormal_total_num,
        COALESCE (sum (CASE WHEN T3.asset_id IS NOT NULL THEN 1 else 0 END ),0) AS total_report_num,
        COALESCE (sum (CASE WHEN T3.asset_id IS NULL THEN 1 else 0 END ),0) AS total_un_report_num
        FROM
        ( SELECT
        T1.asset_id,
        <if test="sybbh == '1'.toString()">
            omd.division_name AS division,
            omd.division_code,
        </if>
        omd.product_group_name AS product_group,
        omd.product_group_code,
        T1.crm_register,
        T1.active_statu,
        T1.model_id,
        T1.model_name
        FROM dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join (
        select distinct asset_id, device_location from dqm.ors_device_location
        where device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 on T1.asset_id = T5.asset_id
        <where>
            and (T5.device_location is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                AND (
                omd.division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="modelIds != null and modelIds != ''">
                AND omd.model_id IN
                <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="country != null and country != ''">
                AND T1.country_code IN
                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) T1
        LEFT JOIN (
        select
        1 AS param_codes,
        case when (max(T3.null_param_codes) is not null or max(T3.abnormal_param_codes) is not null) then 1 else 0 end as abnormal_codes,
        case when max(T3.null_param_codes) is not null then 1 else 0 end as null_param_codes,
        case when max(T3.abnormal_param_codes) is not null then 1 else 0 end as abnormal_param_codes,
        T3.asset_id,
        max (od.name) as division,
        max(T3.division_code) as division_code
        FROM
        dqm.ors_device_param_report_log T3
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name) od on T3.division_code = od.value
        WHERE
        T3.asset_id NOT IN (
        SELECT asset_id FROM dqm.ors_device_check_config T2
        <where>
            exclude_type = 'WHOLE'
            <if test="endTime != null and endTime != ''">
                AND T2.create_time &lt;= #{endTime}::TIMESTAMP
            </if>
        </where>
        )
        <if test="startTime != null and startTime != ''">
            AND T3.report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND T3.report_date &lt;= #{endTime}::DATE
        </if>
        group by T3.asset_id
        ) T3 ON T3.asset_id = T1.asset_id
        left join
        (select asset_id,1 as whole_exclud from  dqm.ors_device_check_config
            where  exclude_type = 'WHOLE'
                <if test="endTime != null and endTime != ''">
                    AND create_time &lt;= #{endTime}::TIMESTAMP
                </if>
            group by asset_id)T4 ON T4.asset_id = T1.asset_id
        left join
        (select asset_id,1 as total_property_exclud from  dqm.ors_device_check_config
            where exclude_type != 'WHOLE'
                <if test="endTime != null and endTime != ''">
                    AND create_time &lt;= #{endTime}::TIMESTAMP
                </if>
            group by asset_id)T5 ON T5.asset_id = T1.asset_id
        GROUP BY
        T1.division,
        T1.division_code,
        T1.model_id
    </select>


    <select id="countDeviceByCondition" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
            T1.division,
            T1.division_code,
            T1.model_id,
            max(T1.model_name) AS model_name,
            COUNT(DISTINCT T1.asset_id) AS total,
            <if test="storeCategory != null and storeCategory == '1'.toString()">
                COUNT(DISTINCT CASE WHEN T1.crm_register = 1 THEN T1.asset_id END) AS regist_num,
                COUNT(DISTINCT CASE WHEN T1.crm_register = 0 OR T1.crm_register IS NULL THEN T1.asset_id END) AS un_regist_num,
                COUNT(DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END ) AS active_num,
            </if>
            COUNT(DISTINCT CASE WHEN T2.exclude_type = 'WHOLE' THEN T1.asset_id END ) AS whole_exclud_num,
            COUNT(DISTINCT CASE WHEN T2.exclude_type != 'WHOLE' THEN T1.asset_id END ) AS total_property_exclud_num,
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                         AND T3.param_codes IS NOT NULL
                    THEN T1.asset_id END
            ) AS abnormal_total_num,
            COUNT ( DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                        AND T3.null_param_codes IS NOT NULL
                    THEN T3.asset_id END
            ) AS null_abnormal_total_num,
            COUNT ( DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                        AND T3.abnormal_param_codes IS NOT NULL
                    THEN T3.asset_id END
            ) AS param_abnormal_total_num,
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                    THEN T1.asset_id END
            ) AS total_report_num,
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NULL
                    THEN T1.asset_id END
            ) AS total_un_report_num
        FROM
            <choose>
                <when test="storeCategory != null and storeCategory == '2'.toString()">

                    ( SELECT
                        T1.device_name AS asset_id,
                        <if test="sybbh == '1'.toString()">
                            T1.division,
                            T1.division_code,
                        </if>
                        <if test="sybbh == '2'.toString()">
                            T1.region AS division,
                            T1.region_code AS division_code,
                        </if>
                        T1.product_group,
                        T1.product_group_code

                    FROM dqm.ors_monthly_shipment_equipment T1
                    <where>
                        T1.division_code IS NOT NULL AND T1.product_group_code IS NOT NULL
                        <if test="sybbh == '2'.toString()">
                            AND T1.region_code IS NOT NULL
                        </if>

                        <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                            AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                        </if>
                        <if test="endTime != null and endTime != ''">
                            AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                        </if>
                        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                            AND (
                                T1.division_code IN
                                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            OR
                                T1.region_code IN
                                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            )
                        </if>
                        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                            AND T1.product_group_code IN
                            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </where>
                    ) T1
                </when>
                <otherwise>
                    ( SELECT
                        T1.asset_id,
                        <if test="sybbh == '1'.toString()">
                            omd.division_name AS division,
                            omd.division_code,
                        </if>
                        omd.product_group_name AS product_group,
                        omd.product_group_code,
                        T1.crm_register,
                        T1.active_statu,
                        T1.model_id,
                        T1.model_name
                      FROM dqm.ors_base_device_info T1
                        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
                        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                            AND omd.product_group_code IN
                            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        left join (
                            select distinct asset_id, device_location from dqm.ors_device_location
                            where device_location = '国内'
                            <if test="startTime != null and startTime != ''">
                                AND stat_date::DATE &gt;= #{startTime}::DATE
                            </if>
                            <if test="endTime != null and endTime != ''">
                                AND stat_date::DATE &lt;= #{endTime}::DATE
                            </if>
                        ) T5 on T1.asset_id = T5.asset_id
                        <where>
                            and (T5.device_location is null)
                            <if test="agentName != null and agentName != ''">
                                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
                            </if>
                            <if test="installType != null and installType != '0'.toString()">
                                AND T1.install_type = #{installType}::INT4
                            </if>
                            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                            </if>
<!--                            <if test="endTime != null and endTime != ''">-->
<!--                                AND T1.first_data_time &lt;= #{endTime}::DATE-->
<!--                            </if>-->
                            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                                AND (
                                omd.division_code IN
                                    <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                )
                            </if>
                            <if test="modelIds != null and modelIds != ''">
                                AND omd.model_id IN
                                <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="country != null and country != ''">
                                AND T1.country_code IN
                                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </where>
                    ) T1
                </otherwise>
            </choose>
            LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = T1.asset_id
                    <if test="endTime != null and endTime != ''">
                        AND T2.create_time &lt;= #{endTime}::TIMESTAMP
                    </if>
            LEFT JOIN (
                SELECT
                    T3.param_codes,
                    T3.null_param_codes,
                    T3.abnormal_param_codes,
                    T3.asset_id
                FROM
                    dqm.ors_device_param_report_log T3
                WHERE
                    T3.asset_id NOT IN (
                        SELECT asset_id FROM dqm.ors_device_check_config T2
                        <where>
                            exclude_type = 'WHOLE'
                            <if test="endTime != null and endTime != ''">
                                AND T2.create_time &lt;= #{endTime}::TIMESTAMP
                            </if>
                        </where>
                    )
                    <if test="startTime != null and startTime != ''">
                        AND T3.report_date &gt;= #{startTime}::DATE
                    </if>
                    <if test="endTime != null and endTime != ''">
                        AND T3.report_date &lt;= #{endTime}::DATE
                    </if>
            ) T3 ON T3.asset_id = T1.asset_id
        GROUP BY
        T1.division,
        T1.division_code,
        T1.model_id
    </select>

    <select id="countDeviceByParamCodeChange" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        -- 1. 预处理基础设备信息，应用所有过滤条件
        WITH base_devices AS (
        SELECT
        T1.asset_id,
        <if test="sybbh == '1'.toString()">
            omd.division_name AS division,
            omd.division_code,
        </if>
        T1.model_id,
        T1.model_name
        FROM dqm.ors_base_device_info T1
        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
            AND omd.product_group_code IN
            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        LEFT JOIN (
        SELECT DISTINCT asset_id
        FROM dqm.ors_device_location
        WHERE device_location = '国内'
        <if test="startTime != null and startTime != ''">
            AND stat_date::DATE &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND stat_date::DATE &lt;= #{endTime}::DATE
        </if>
        ) T5 ON T1.asset_id = T5.asset_id
        <where>
            and (T5.asset_id is null)
            <if test="agentName != null and agentName != ''">
                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND T1.install_type = #{installType}::INT4
            </if>
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>
            <if test="endTime != null and endTime != ''">
                <!--                                <if test="storeCategory != null and storeCategory == '1'.toString()">-->
                <!--                                    AND T1.first_data_time &lt;= #{endTime}::DATE-->
                <!--                                </if>-->
                <if test="storeCategory != null and storeCategory == '2'.toString()">
                    AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                </if>
            </if>
            <if test="modelIds != null and modelIds != ''">
                AND T1.model_id IN
                <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="country != null and country != ''">
                AND T1.country_code IN
                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ),

        -- 2. 预处理属性配置，应用所有过滤条件
        property_configs AS (
        SELECT
        mpc.model_id,
        mpc.param_code,
        mpc.property_name,
        sd.dict_type,
        ct.name AS dict_type_name
        FROM dqm.ors_model_properties_config mpc
        LEFT JOIN sany_data_service.sanyds_dict sd ON sd.dict_id = mpc.param_code
        LEFT JOIN sys_dict ct ON ct.code = sd.dict_type AND ct.dict_type = 'check_type'
        <where>
            <if test="inspection != null and inspection != ''">
                mpc.param_code IN
                <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::INT4
                </foreach>
            </if>
            <if test="dictTypeName != null and dictTypeName != ''">
                AND mpc.param_code IN (
                SELECT dict_id FROM sany_data_service.sanyds_dict
                WHERE dict_type IN
                <foreach item="item" index="index" collection="dictTypeName.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        ),
        -- 4. 预处理属性剔除配置
        property_exclusions AS (
        SELECT
        asset_id,
        param_code
        FROM dqm.ors_device_check_config
        WHERE exclude_type != 'WHOLE'
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        ),
        -- 5. 预处理报告日志，对每个设备只保留一条记录
        param_codes_expanded AS (
        SELECT
        asset_id,
        division_code,
        unnest(string_to_array(param_codes, ',')) AS param_code,
        'param' AS type
        FROM dqm.ors_device_param_report_log
        WHERE param_codes IS NOT NULL
        <if test="startTime != null and startTime != ''">
            AND report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND report_date &lt;= #{endTime}::DATE
        </if>
        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
            AND (
            division_code IN
            <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        UNION

        SELECT
        asset_id,
        division_code,
        unnest(string_to_array(null_param_codes, ',')) AS param_code,
        'null' AS type
        FROM dqm.ors_device_param_report_log
        WHERE null_param_codes IS NOT NULL
        <if test="startTime != null and startTime != ''">
            AND report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND report_date &lt;= #{endTime}::DATE
        </if>
        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
            AND (
            division_code IN
            <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        UNION

        SELECT
        asset_id,
        division_code,
        unnest(string_to_array(abnormal_param_codes, ',')) AS param_code,
        'abnormal' AS type
        FROM dqm.ors_device_param_report_log
        WHERE abnormal_param_codes IS NOT NULL
        <if test="startTime != null and startTime != ''">
            AND report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND report_date &lt;= #{endTime}::DATE
        </if>
        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
            AND (
            division_code IN
            <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        UNION

        SELECT
        asset_id,
        division_code,
        unnest(string_to_array(report_param_codes, ',')) AS param_code,
        'report' AS type
        FROM dqm.ors_device_param_report_log
        WHERE report_param_codes IS NOT NULL
        <if test="startTime != null and startTime != ''">
            AND report_date &gt;= #{startTime}::DATE
        </if>
        <if test="endTime != null and endTime != ''">
            AND report_date &lt;= #{endTime}::DATE
        </if>
        <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
            AND (
            division_code IN
            <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ),

        report_logs_aggregated AS (
        SELECT
        asset_id,
        max(division_code) as division_code,
        max(od.name) as division,
        array_agg(DISTINCT param_code) FILTER (WHERE type = 'param') AS param_codes_array,
        array_agg(DISTINCT param_code) FILTER (WHERE type = 'null') AS null_param_codes_array,
        array_agg(DISTINCT param_code) FILTER (WHERE type = 'abnormal') AS abnormal_param_codes_array,
        array_agg(DISTINCT param_code) FILTER (WHERE type = 'report') AS report_param_codes_array
        FROM param_codes_expanded
        left join (SELECT  division_code as value,division_name as name FROM dqm.ors_model_division group by   division_code,division_name)od on division_code = od.value
        GROUP BY asset_id
        ),
        -- 6. 设备-属性组合表，避免重复计算
        device_property_pairs AS (
        SELECT
        bd.asset_id,
        case when rl.division is not null then rl.division else bd.division end as division,
        case when rl.division_code is not null then rl.division_code else bd.division_code end as division_code,
        bd.model_id,
        bd.model_name,
        pc.param_code,
        pc.property_name,
        pc.dict_type,
        pc.dict_type_name,
        -- 预计算各种状态，避免在主查询中重复计算
        CASE WHEN pe.asset_id IS NOT NULL THEN 1 ELSE 0 END AS is_excluded,
        CASE WHEN rl.asset_id IS NOT NULL AND
        pc.param_code::text = ANY(rl.param_codes_array) THEN 1 ELSE 0 END AS has_abnormal,
        CASE WHEN rl.asset_id IS NOT NULL AND
        pc.param_code::text = ANY(rl.null_param_codes_array) THEN 1 ELSE 0 END AS has_null_abnormal,
        CASE WHEN rl.asset_id IS NOT NULL AND
        pc.param_code::text = ANY(rl.abnormal_param_codes_array) THEN 1 ELSE 0 END AS has_param_abnormal,
        CASE WHEN rl.asset_id IS NOT NULL AND
        pc.param_code::text = ANY(rl.report_param_codes_array) THEN 1 ELSE 0 END AS has_report
        FROM base_devices bd
        INNER JOIN property_configs pc ON pc.model_id = bd.model_id
        LEFT JOIN property_exclusions pe ON pe.asset_id = bd.asset_id AND pe.param_code = pc.param_code
        LEFT JOIN report_logs_aggregated rl ON rl.asset_id = bd.asset_id
        )

        -- 主查询：简单聚合预计算结果
        SELECT
        division,
        division_code,
        model_id,
        MAX(model_name) AS model_name,
        property_name,
        MAX(dict_type) AS dict_type,
        MAX(dict_type_name) AS dict_type_name,
        SUM(has_abnormal) AS property_abnormal_num,
        SUM(has_null_abnormal) AS property_null_abnormal_total_num,
        SUM(has_param_abnormal) AS property_param_abnormal_total_num,
        SUM(has_report) AS property_report_num,
        SUM(is_excluded) AS property_exclud_num
        FROM device_property_pairs
        <where>
            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                AND (
                division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        GROUP BY
        division,
        division_code,
        model_id,
        property_name order by division, model_id, property_name;
    </select>

    <select id="countDeviceByParamCode" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        with report_log as (
            SELECT
            T3.param_codes,
            T3.null_param_codes,
            T3.abnormal_param_codes,
            T3.report_param_codes,
            T3.asset_id
            FROM
            dqm.ors_device_param_report_log T3
            WHERE 1 = 1
            <if test="startTime != null and startTime != ''">
                AND T3.report_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND T3.report_date &lt;= #{endTime}::DATE
            </if>
        )

        SELECT
            T1.division,
            T1.division_code,
            T1.model_id,
            max(T1.model_name) AS model_name,
            <if test="storeCategory != null and storeCategory == '1'.toString()">
                T4.property_name,
                max(sd.dict_type) as dict_type ,
                max(ct.name) as dict_type_name,
            </if>
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                         AND T3.param_codes IS NOT NULL
                        <if test="storeCategory != null and storeCategory == '1'.toString()">
                            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
                        </if>
                    THEN T1.asset_id END
            ) AS property_abnormal_num,
            COUNT ( DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                    AND T3.null_param_codes IS NOT NULL
                    <if test="storeCategory != null and storeCategory == '1'.toString()">
                        AND T3.null_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
                    </if>
                    THEN T3.asset_id END
            ) AS property_null_abnormal_total_num,
            COUNT ( DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                    AND T3.abnormal_param_codes IS NOT NULL
                    <if test="storeCategory != null and storeCategory == '1'.toString()">
                        AND T3.abnormal_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
                    </if>
                    THEN T3.asset_id END
            ) AS property_param_abnormal_total_num,
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NOT NULL
                        <if test="storeCategory != null and storeCategory == '1'.toString()">
                            AND T3.report_param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
                        </if>
                    THEN T1.asset_id END
            ) AS property_report_num,
            COUNT(DISTINCT CASE
                WHEN T3.asset_id IS NULL
                        <if test="storeCategory != null and storeCategory == '1'.toString()">
                            AND T3.report_param_codes not LIKE CONCAT('%', T4.param_code::TEXT, '%')
                        </if>
                    THEN T1.asset_id END
            ) AS property_un_report_num
            <if test="storeCategory != null and storeCategory == '1'.toString()">
                ,COUNT(DISTINCT CASE WHEN T2.exclude_type != 'WHOLE' AND T2.param_code = T4.param_code THEN T1.asset_id END ) AS property_exclud_num,
                COUNT(DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END) AS property_active_num
            </if>
        FROM
            <choose>
                <when test="storeCategory != null and storeCategory == '2'.toString()">
                    ( SELECT
                        <if test="sybbh == '1'.toString()">
                            T1.division,
                            T1.division_code,
                        </if>
                        <if test="sybbh == '2'.toString()">
                            T1.region AS division,
                            T1.region_code AS division_code,
                        </if>
                        T1.product_group,
                        T1.product_group_code

                        FROM dqm.ors_monthly_shipment_equipment T1
                        <where>
                            T1.division_code IS NOT NULL AND T1.product_group_code IS NOT NULL
                            <if test="sybbh == '2'.toString()">
                                AND T1.region_code IS NOT NULL
                            </if>

                            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                            </if>
                            <if test="endTime != null and endTime != ''">
                                AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                            </if>
                            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                                AND (
                                T1.division_code IN
                                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                OR
                                T1.region_code IN
                                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                )
                            </if>
                            <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                                AND T1.product_group_code IN
                                <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </where>
                    ) T1
                </when>
                <otherwise>
                    (
                        SELECT
                            T1.asset_id,
                            <if test="sybbh == '1'.toString()">
                                omd.division_name AS division,
                                omd.division_code,
                            </if>
                            omd.product_group_name AS product_group,
                            omd.product_group_code,
                            T1.crm_register,
                            T1.active_statu,
                            T1.model_id,
                            T1.model_name
                        FROM dqm.ors_base_device_info T1
                        INNER JOIN dqm.ors_model_division omd ON omd.model_id = T1.model_id
                        <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                            AND omd.product_group_code IN
                            <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        left join (
                            select distinct asset_id, device_location from dqm.ors_device_location
                            where device_location = '国内'
                            <if test="startTime != null and startTime != ''">
                                AND stat_date::DATE &gt;= #{startTime}::DATE
                            </if>
                            <if test="endTime != null and endTime != ''">
                                AND stat_date::DATE &lt;= #{endTime}::DATE
                            </if>
                        ) T5 on T1.asset_id = T5.asset_id
                        <where>
                            and (T5.device_location is null)
                            <if test="agentName != null and agentName != ''">
                                AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
                            </if>
                            <if test="installType != null and installType != '0'.toString()">
                                AND T1.install_type = #{installType}::INT4
                            </if>
                            <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                                AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                            </if>
                            <if test="endTime != null and endTime != ''">
<!--                                <if test="storeCategory != null and storeCategory == '1'.toString()">-->
<!--                                    AND T1.first_data_time &lt;= #{endTime}::DATE-->
<!--                                </if>-->
                                <if test="storeCategory != null and storeCategory == '2'.toString()">
                                    AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                                </if>
                            </if>
                            <if test="modelIds != null and modelIds != ''">
                                AND omd.model_id IN
                                <foreach collection="modelIds.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                                AND (
                                omd.division_code IN
                                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                )
                            </if>
                            <if test="country != null and country != ''">
                                AND T1.country_code IN
                                <foreach item="item" index="index" collection="country.split(',')"  open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </where>
                    ) T1
                </otherwise>
            </choose>
            <if test="storeCategory != null and storeCategory == '1'.toString()">
                INNER JOIN dqm.ors_model_properties_config T4 ON T4.model_id = T1.model_id
                    <if test="inspection != null and inspection != ''">
                        AND T4.param_code IN
                        <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                            #{item}::INT4
                        </foreach>
                    </if>
                    <if test="dictTypeName != null and dictTypeName != ''">
                        AND T4.param_code IN
                       (
                            select dict_id from sany_data_service.sanyds_dict  where dict_type in
                            <foreach item="item" index="index" collection="dictTypeName.split(',')"  open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        )
                    </if>
                left join sany_data_service.sanyds_dict sd on sd.dict_id = T4.param_code
                left join sys_dict ct on ct.code = sd.dict_type and ct.dict_type = 'check_type'
            </if>
            LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = T1.asset_id
                        <if test="endTime != null and endTime != ''">
                            AND T2.create_time &lt;= #{endTime}::TIMESTAMP
                        </if>
            LEFT JOIN report_log as T3 ON T3.asset_id = T1.asset_id

        GROUP BY
        T1.division,
        T1.division_code,
        T1.model_id
            <if test="storeCategory != null and storeCategory == '1'.toString()">
                ,T4.property_name
            </if>

    </select>

    <select id="countDeviceByParamCode2" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
        T1.division,
        T1.division_code,
        T1.product_group,
        T1.product_group_code,
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            T4.property_name,
        </if>
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_abnormal_num,
        COUNT ( DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.null_param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T3.asset_id END
        ) AS property_null_abnormal_total_num,
        COUNT ( DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        AND T3.abnormal_param_codes IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T3.asset_id END
        ) AS property_param_abnormal_total_num,
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NOT NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_report_num,
        COUNT(DISTINCT CASE
        WHEN T3.asset_id IS NULL
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            AND T3.param_codes LIKE CONCAT('%', T4.param_code::TEXT, '%')
        </if>
        THEN T1.asset_id END
        ) AS property_un_report_num
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            ,COUNT(DISTINCT CASE WHEN T2.exclude_type != 'WHOLE' AND T2.param_code = T4.param_code THEN T1.asset_id END ) AS property_exclud_num,
            COUNT(DISTINCT CASE WHEN T1.active_statu = TRUE THEN T1.asset_id END) AS property_active_num
        </if>
        FROM
        <choose>
            <when test="storeCategory != null and storeCategory == '2'.toString()">
                ( SELECT
                T1.device_name AS asset_id,
                <if test="sybbh == '1'.toString()">
                    T1.division,
                    T1.division_code,
                </if>
                <if test="sybbh == '2'.toString()">
                    T1.region AS division,
                    T1.region_code AS division_code,
                </if>
                T1.product_group,
                T1.product_group_code

                FROM dqm.ors_monthly_shipment_equipment T1
                <where>
                    T1.division_code IS NOT NULL AND T1.product_group_code IS NOT NULL
                    <if test="sybbh == '2'.toString()">
                        AND T1.region_code IS NOT NULL
                    </if>

                    <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                        AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                    </if>
                    <if test="endTime != null and endTime != ''">
                        AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                    </if>
                    <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                        AND (
                        T1.division_code IN
                        <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        T1.region_code IN
                        <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                        AND T1.product_group_code IN
                        <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
                ) T1
            </when>
            <otherwise>
                (
                SELECT
                T1.asset_id,
                <if test="sybbh == '1'.toString()">
                    T1.division,
                    T1.division_code,
                </if>
                <if test="sybbh == '2'.toString()">
                    T1.region AS division,
                    T1.region_code AS division_code,
                </if>
                T1.product_group,
                T1.product_group_code,
                T1.crm_register,
                T1.active_statu,
                T1.model_id
                FROM dqm.ors_base_device_info T1
                <where>
                    T1.division IS NOT NULL AND T1.product_group IS NOT NULL
                    <if test="sybbh == '2'.toString()">
                        AND T1.region IS NOT NULL
                    </if>
                    <if test="country != null and country.size>0">
                        AND T1.country_code in
                        <foreach collection="country" separator="," open="(" close=")" item="item">
                            #{item}
                        </foreach>
                    </if>
                    <if test="agentName != null and agentName != ''">
                        AND T1.agent LIKE CONCAT('%', #{agentName}, '%')
                    </if>
                    <if test="installType != null and installType != '0'.toString()">
                        AND T1.install_type = #{installType}::INT4
                    </if>
                    <if test="hasHuaXin != null and hasHuaXin == '0'.toString() and storeCategory != null and storeCategory == '1'.toString()">
                        AND T1.model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
                    </if>
                    <if test="endTime != null and endTime != ''">
                        <!--                                <if test="storeCategory != null and storeCategory == '1'.toString()">-->
                        <!--                                    AND T1.first_data_time &lt;= #{endTime}::DATE-->
                        <!--                                </if>-->
                        <if test="storeCategory != null and storeCategory == '2'.toString()">
                            AND T1.import_time= TO_CHAR(#{startTime}::DATE , 'YYYY-MM')
                        </if>
                    </if>
                    <if test="divisionRegiondesc != null and divisionRegiondesc != ''">
                        AND (
                        T1.division_code IN
                        <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        T1.region_code IN
                        <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                        AND T1.product_group_code IN
                        <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
                ) T1
            </otherwise>
        </choose>
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            LEFT JOIN dqm.ors_model_properties_config T4 ON T4.model_id = T1.model_id
            <if test="inspection != null and inspection != ''">
                AND T4.param_code IN
                <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::INT4
                </foreach>
            </if>
        </if>
        LEFT JOIN
        (select
            asset_id,
            string_agg (cast(param_code as varchar), ',' ) as check_param_code
        from
            dqm.ors_device_check_config
        <if test="endTime != null and endTime != ''">
            where create_time &lt;= #{endTime}::TIMESTAMP
        </if>
        group by
            asset_id
        ) T2 ON T1.asset_id = T2.asset_id

        LEFT JOIN (
        SELECT
            T3.param_codes,
            T3.null_param_codes,
            T3.abnormal_param_codes,
            T3.asset_id
        FROM
            dqm.ors_device_param_report_log T3
        <where>
            <if test="startTime != null and startTime != ''">
                AND T3.report_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND T3.report_date &lt;= #{endTime}::DATE
            </if>
        </where>
        ) T3 ON T1.asset_id = T3.asset_id

        GROUP BY
        T1.division,
        T1.division_code,
        T1.product_group,
        T1.product_group_code
        <if test="storeCategory != null and storeCategory == '1'.toString()">
            ,T4.property_name
        </if>

    </select>

    <select id="countDeviceByParamCode_new" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
        SELECT
            <if test="sybbh == '1'.toString()">
                division,
                division_code,
            </if>
            <if test="sybbh == '2'.toString()">
                region AS division,
                region_code AS division_code,
            </if>
            product_group,
            product_group_code,
            param_code,
            param_name as property_name,
            stat_date as report_date,
            COUNT ( DISTINCT CASE WHEN param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS property_abnormal_num, -- 异常总数
            COUNT ( DISTINCT CASE WHEN null_param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS property_null_abnormal_total_num, -- 从未上报数据9008异常总数
            COUNT ( DISTINCT CASE WHEN abnormal_param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS property_param_abnormal_total_num, -- 非9008得异常总数
            COUNT ( DISTINCT CASE WHEN report_param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS property_report_num, -- 上报工况的设备总数
            COUNT ( DISTINCT CASE WHEN report_param_codes NOT LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS property_un_report_num,  -- 未上报工况的设备总数
            COUNT(DISTINCT CASE WHEN exclude_state != 0 THEN asset_id END ) AS property_exclud_num, -- 属性剔除设备数量，包含属性剔除和整机剔除
            COUNT(DISTINCT CASE WHEN active_statu = TRUE AND exclude_state=0 THEN asset_id END) AS property_active_num -- 激活总数
        FROM
            dqm.ors_device_param_report_day
        WHERE
            1=1
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
<!--            AND exclude_state !=2 &#45;&#45; 有整机剔除的设备不参与计算-->
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString()">
                AND model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>

            <if test="inspection != null and inspection != ''">
                AND param_code IN
                <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::INT4
                </foreach>
            </if>
            <if test="country != null and country.size>0">
                AND country_code in
                <foreach collection="country" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="agentName != null and agentName != ''">
                AND agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND install_type = #{installType}::INT4
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != '' and sybbh == '1'.toString() ">
                AND
                division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != '' and sybbh == '2'.toString() ">
                AND
                region_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                AND product_group_code IN
                <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND region_code IS NOT NULL
        GROUP BY
            stat_date
            <if test="sybbh == '1'.toString()">
                ,division
                ,division_code
            </if>
            <if test="sybbh == '2'.toString()">
                ,region
                ,region_code
            </if>
            ,product_group
            ,product_group_code
            ,param_code
            ,param_name
    </select>

    <select id="countDeviceByCondition_new" resultType="com.rc.admin.ors.quality.model.DeviceCountModel">
       SELECT
            <if test="sybbh == '1'.toString()">
                division,
                division_code,
            </if>
            <if test="sybbh == '2'.toString()">
                region AS division,
                region_code AS division_code,
            </if>
            product_group,
            product_group_code,
            COUNT ( DISTINCT asset_id ) AS total,  -- 设备总数
            COUNT ( DISTINCT CASE WHEN crm_register = 1 THEN asset_id END ) AS regist_num,  -- crm注册总数
            COUNT ( DISTINCT CASE WHEN crm_register = 0 OR crm_register IS NULL THEN asset_id END ) AS un_regist_num,  -- crm未注册总数
            COUNT ( DISTINCT CASE WHEN active_statu = TRUE THEN asset_id END ) AS active_num,  -- 激活总数
            COUNT(DISTINCT CASE WHEN exclude_state = 2 THEN asset_id END ) AS whole_exclud_num, -- 整机剔除设备数量
            COUNT ( DISTINCT CASE WHEN param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS abnormal_total_num, -- 异常总数
            COUNT ( DISTINCT CASE WHEN null_param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS null_abnormal_total_num, -- 从未上报数据9008异常总数
            COUNT ( DISTINCT CASE WHEN abnormal_param_codes LIKE CONCAT('%', param_code::TEXT, '%') AND exclude_state=0
            THEN asset_id END) AS param_abnormal_total_num, -- 非9008得异常总数
            COUNT ( DISTINCT CASE WHEN report_param_codes IS NOT NULL AND exclude_state=0
            THEN asset_id END) AS total_report_num, -- 上报工况的设备总数
            COUNT ( DISTINCT CASE WHEN report_param_codes IS NULL AND exclude_state=0
            THEN asset_id END) AS total_un_report_num  -- 未上报工况的设备总数
        FROM
            dqm.ors_device_param_report_day
        WHERE
            1=1
            <if test="startTime != null and startTime != ''">
                AND stat_date &gt;= #{startTime}::DATE
            </if>
            <if test="endTime != null and endTime != ''">
                AND stat_date &lt;= #{endTime}::DATE
            </if>
<!--            AND exclude_state !=2 &#45;&#45; 有整机剔除的设备不参与计算-->
            <if test="hasHuaXin != null and hasHuaXin == '0'.toString()">
                AND model_id NOT in ('cm1tsjzqFz2mI','BUFLT_5006_Model_5389', 'BUFLT_5006_Model_5386', 'BUFLT_5006_Model_5387', 'BUFLT_5006_Model_5388', 'BUFLT_5006_Model_5385')
            </if>

            <if test="inspection != null and inspection != ''">
                AND param_code IN
                <foreach collection="inspection.split(',')" item="item" open="(" separator="," close=")">
                    #{item}::INT4
                </foreach>
            </if>
            <if test="country != null and country.size>0">
                AND country_code in
                <foreach collection="country" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="agentName != null and agentName != ''">
                AND agent LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="installType != null and installType != '0'.toString()">
                AND install_type = #{installType}::INT4
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != '' and sybbh == '1'.toString() ">
                AND
                division_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="divisionRegiondesc != null and divisionRegiondesc != '' and sybbh == '2'.toString() ">
                AND
                region_code IN
                <foreach collection="divisionRegiondesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zehdSpartdesc != null and zehdSpartdesc != ''">
                AND product_group_code IN
                <foreach collection="zehdSpartdesc.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND region_code IS NOT NULL
        GROUP BY
            stat_date
            <if test="sybbh == '1'.toString()">
                ,division
                ,division_code
            </if>
            <if test="sybbh == '2'.toString()">
                ,region
                ,region_code
            </if>
            ,product_group
            ,product_group_code

    </select>
    <select id="rcAssetIdList" resultType="java.lang.String">
        SELECT DISTINCT
        obdi.rc_asset_id AS rc_asset_id
        FROM
        dqm.ors_base_device_info as obdi
        where obdi.rc_asset_id is not null
        <if test="rcAssetId != null and rcAssetId != ''">
            AND (
            upper(obdi.rc_asset_id) like CONCAT('%', upper(#{rcAssetId}), '%')
            OR
            obdi.rc_asset_id IN
            <foreach item="item" index="index" collection="rcAssetId.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        group by obdi.rc_asset_id
    </select>


    <insert id="insertDeviceRateDay">
        INSERT INTO ors_device_rate_day (asset_id, stat_date, param_abnormal_rate, null_abnormal_rate)
        select device_name,stat_date,
        case when sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when reportType = 0 then 0 when reportType = 1 then 1 END)::numeric/(sum(case when (reportType = 1 or reportType = 0) then 1 else 0 END)::numeric),4)
        ELSE null  END
        as param_abnormal_rate,
        case when sum(case when (reportType = 0 or reportType = 1 or reportType = 2) then 1 else 0 END)!=0
        THEN
        ROUND(sum(case when (reportType = 1 OR reportType=0)  then 1  else 0  END)::numeric/sum(case when (reportType = 0 or reportType = 1 or reportType = 2) then 1 else 0 END)::numeric,4)
        ELSE null  END
        as null_abnormal_rate

        FROM
        (
        select aa.device_name,aa.param_code,aa.stat_date,
        case when reportType = 1 AND T7.abnormal_code is not null then 0 else  reportType end as reportType
        from
        (
        select  device_name,param_code,stat_date,1 as reportType from  sany_data_service.sanyds_core_param_stat_latest_day
        WHERE stat_date = #{bizDate}::DATE
        union ALL
        select device_name,param_code,stat_date,2 as reportType from dqm.ors_device_data_abnormal_detail_day where abnormal_code in('9008','9009')
        AND stat_date = #{bizDate}::DATE
        )aa
        LEFT JOIN dqm.ors_device_check_config T2 ON T2.asset_id = aa.device_name  and T2.param_code = aa.param_code
        and T2.create_time::DATE  &lt;=  #{bizDate}::DATE
        left JOIN dqm.ors_device_data_abnormal_detail_day T7 on aa.device_name = T7.device_name
        and aa.param_code = T7.param_code  and aa.stat_date = T7.stat_date and   T7.abnormal_code not  in('9008','9009')
        WHERE
        T2.param_code is null
        and
        aa.device_name NOT IN ( SELECT asset_id FROM dqm.ors_device_check_config T2 WHERE exclude_type = 'WHOLE' AND T2.create_time::DATE  &lt;=   #{bizDate}::DATE )
        )T8
        group by device_name,stat_date
        ON CONFLICT (asset_id, stat_date)
        DO UPDATE SET
        param_abnormal_rate = EXCLUDED.param_abnormal_rate,
        null_abnormal_rate = EXCLUDED.null_abnormal_rate;
    </insert>


    <select id="getDeviceInfoByCheck" resultType="com.rc.admin.ors.quality.entity.OrsDeviceInfo">
        SELECT
            di.rc_asset_id,
            di.asset_id,
            di.asset_id_nrc,
            di.serial_num,
            di.tbox_id,
            di.ml_model_id,
            di.thing_id,
            di.model_id,
            di.model_name,
            di.device_code,
            di.device_name,
            di.install_type,
            di.created,
            di.crm_register,
            di.etl_time,
            di.hw_version,
            di.fw_version,
            di.auth_token,
            di.data_source,
            di.oline_statu,
            di.active_statu,
            di.exce_flag,
            di.exce_desc,
            di.first_data_time,
            di.device_status,
            di.data_center_id,
            di.country,
            di.country_code
        FROM
            dqm.ors_base_device_info di
        <where>
            <choose>
                <when test="modelId != null and modelId != ''">
                    and di.model_id = #{modelId}
                    <if test="assetId != null and assetId != ''">
                        and di.asset_id = #{assetId}
                    </if>
                </when>
                <otherwise>
                    <if test="checkDate != null and checkDate != ''">
                        and  di.asset_id in
                        (
                        select distinct asset_id from dqm.ors_device_check_config_history where  delete_time :: DATE = #{checkDate}:: DATE
                        union all
                        select distinct asset_id from dqm.ors_device_check_config where  (create_time :: DATE = #{checkDate}:: DATE
                        or update_time::DATE = #{checkDate}:: DATE )
                        )
                    </if>
                    <if test="assetId != null and assetId != ''">
                        and di.asset_id = #{assetId}
                    </if>
                </otherwise>
            </choose>
        </where>
    </select>


    <select id="getHXModelStr" resultType="java.lang.String">
        SELECT string_agg(DISTINCT model_id::text, ',') AS modelIds
        FROM dqm.ors_double_rate_config
        WHERE double_rate_sign='4'
    </select>
</mapper>
