package com.rc.admin.ors.quality.utils;

import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class BusinessConst {

  public static final Map<Integer, String> dataCenterMap = new HashMap<Integer, String>() {{
    put(0, "亚洲主站");
    put(1, "欧洲法兰克福站点");
    put(2, "亚洲新加坡站点");
  }};

  public static final Map<Integer, String> deviceStatusMap = new HashMap<Integer, String>() {{
    put(0, "停用");
    put(1, "未激活");
    put(2, "已激活");
  }};
}
