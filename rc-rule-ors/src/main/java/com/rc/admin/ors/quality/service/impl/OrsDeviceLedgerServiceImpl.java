package com.rc.admin.ors.quality.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.excel.DeviceLedgerExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerNotActiveExcel;
import com.rc.admin.ors.quality.excel.DeviceLedgerOfflineExcel;
import com.rc.admin.ors.quality.model.DeviceLedgerReq;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.ors.quality.service.OrsDeviceLedgerService;
import com.rc.admin.ors.quality.utils.BusinessConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Service
@Slf4j
public class OrsDeviceLedgerServiceImpl implements OrsDeviceLedgerService {

    @Autowired
    private OrsDeviceCheckConfigService orsDeviceCheckConfigService;

    @Autowired
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Override
    public Page<DeviceLedgerResp> page(Page<DeviceLedgerResp> page, DeviceLedgerReq req) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getNoRegisterDeviceList())) {
            req.setDeviceCodes(Arrays.asList(req.getNoRegisterDeviceList().split(",")));
        }
        return orsDeviceInfoMapper.ledgerPage(page, req);
    }

    @Override
    public Long deviceOfflineCount() {
        long startTime = System.currentTimeMillis();
        Long count = Long.valueOf(orsDeviceInfoMapper.deviceOfflineCount());
        long endTime = System.currentTimeMillis();
        log.info("查询离线设备数量耗时：{} ms", endTime - startTime);
        return count;
    }

    @Override
    public DeviceLedgerResp detail(String id) {
        return orsDeviceInfoMapper.findLedger(id, null);
    }

    @Override
    public String getOfflineTimeString(Date offlineTime) {
        if (offlineTime == null) {
            return "";
        }
        Date currentDate = new Date();
        long timeDifference = currentDate.getTime() - offlineTime.getTime();

        long daysDifference = timeDifference / (24 * 60 * 60 * 1000);
        // 根据时间差返回不同的字符串
        if (daysDifference < 1) {
            return "小于1天";
        } else if (daysDifference <= 7) {
            return "1-7天";
        } else if (daysDifference <= 15) {
            return "8-15天";
        } else if (daysDifference <= 30) {
            return "16-30天";
        } else if (daysDifference <= 60) {
            return "1个月到2个月内";
        }else if (daysDifference <= 180) {
            return "2个月到半年内";
        } else {
            return "超过半年";
        }
    }

    @Override
    public ByteArrayOutputStream exportOfflineDevicesToPath(String newDivisionCode) {

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            DeviceLedgerReq req = new DeviceLedgerReq();
            req.setNewDivisionCode(newDivisionCode);
            req.setQueryModel("offline_list");
            AtomicInteger ai = new AtomicInteger(1);
            log.info("导出离线设备到指定路径开始");
            long startTime = System.currentTimeMillis();
            // 提前查询总记录数
            Page<DeviceLedgerResp> countPage = this.page(new Page<>(1, 1), req);
            int totalCount = (int) countPage.getTotal();

            String fileName;

            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream, DeviceLedgerOfflineExcel.class).build();

            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备台账明细" + (sheetIndex + 1))
                            .head(DeviceLedgerOfflineExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceLedgerResp> page = this.page(new Page<>(currentPage, writeDataRows), req);
                            List<DeviceLedgerOfflineExcel> data = new ArrayList<>();
                            page.getRecords().forEach(x->{
                                DeviceLedgerOfflineExcel excel = buildDeviceLedgerOfflineExcelVO(x, ai);
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
            log.info("导出离线设备到指定路径结束");
            log.info("导出离线设备到指定路径耗时：{}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("导出离线设备到指定路径异常,{}",e);
            return null;
        }

        return outputStream;

    }

    @Override
    public ByteArrayOutputStream exportNotActiveDevicesToPath(String newDivisionCode) {

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            DeviceLedgerReq req = new DeviceLedgerReq();
            req.setNewDivisionCode(newDivisionCode);
            req.setQueryModel("not_activat_list");
            AtomicInteger ai = new AtomicInteger(1);
            log.info("导出长期未激活设备到指定路径开始");
            long startTime = System.currentTimeMillis();
            // 提前查询总记录数
            Page<DeviceLedgerResp> countPage = this.page(new Page<>(1, 1), req);
            int totalCount = (int) countPage.getTotal();
            if (totalCount == 0) {
                //return Response.failError("无数据可导出");
                throw new EasyException("无数据可导出");
            }

            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream,DeviceLedgerNotActiveExcel.class).build();

            // 创建一个固定大小为 10 的线程池
            //ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备台账明细" + (sheetIndex + 1))
                            .head(DeviceLedgerNotActiveExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            Page<DeviceLedgerResp> page = this.page(new Page<>(currentPage, writeDataRows), req);
                            List<DeviceLedgerNotActiveExcel> data = new ArrayList<>();
                            page.getRecords().forEach(x->{
                                DeviceLedgerNotActiveExcel excel = this.buildDeviceLedgerNotActiveExcelVO(x, ai);
                                data.add(excel);
                            });
                            log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                            synchronized (excelWriter) {
                                excelWriter.write(data, writeSheet);
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
            log.info("长期未激活设备导出结束");
            log.info("长期未激活设备导出耗时：{}", System.currentTimeMillis() - startTime);
        } catch (EasyException e) {
            log.error("长期未激活设备导出异常,{}",e);
            return null;
        }

        return outputStream;
    }

    @Override
    public DeviceLedgerExcel buildDeviceLedgerExcelVO(DeviceLedgerResp x, AtomicInteger ai) {
        DeviceLedgerExcel excel = new DeviceLedgerExcel();
        BeanUtils.copyProperties(x, excel);
        excel.setDataCenter(BusinessConst.dataCenterMap.get(x.getDataCenterId()));
        excel.setDeviceStatus(BusinessConst.deviceStatusMap.get(x.getDeviceStatus()));
        //离线时长
        Date offlineTime = x.getOfflineTime();
        excel.setOfflineTime(this.getOfflineTimeString(offlineTime));

        excel.setSort(ai.getAndIncrement());
        if(ObjectUtil.isNotNull(x.getCreated())) {
            try {
                LocalDateTime localDateTime = x.getCreated().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                excel.setCtime(formatter.format(localDateTime));
            }catch (Exception e){
                log.error("sdf.format error");
            }
        }
        String s = x.getFactoryDuration();
        switch (s) {
            case "1":
                excel.setFactoryDuration("1年内");
                break;
            case "2":
                excel.setFactoryDuration("2-3年内");
                break;
            case "3":
                excel.setFactoryDuration("4-5年内");
                break;
            case "4":
                excel.setFactoryDuration("大于6年");
                break;
            default:
                excel.setFactoryDuration("其他");
                break;
        }
        excel.setProblemFollow("");
        excel.setCrmRegister(String.valueOf(x.getCrmRegister()));
        if (StringUtils.isBlank(excel.getCrmRegister()) || "null".equals(excel.getCrmRegister())) {
            excel.setCrmRegister("0");
        }
        if (StringUtils.isBlank(excel.getFwVersion()) || "null".equals(excel.getFwVersion())) {
            excel.setFwVersion("");
        }
        // 手动翻译 isEliminate 字段
        String excludeType = x.getIsEliminate();
        if (excludeType!=null && excludeType.equals("0")) {
            excel.setIsEliminate("整机");
        }
        if (excludeType!=null && excludeType.equals("1")) {
            excel.setIsEliminate("属性");
        }

        return excel;
    }

    @Override
    public DeviceLedgerOfflineExcel buildDeviceLedgerOfflineExcelVO(DeviceLedgerResp x, AtomicInteger ai) {
        DeviceLedgerOfflineExcel excel = new DeviceLedgerOfflineExcel();
        BeanUtils.copyProperties(x, excel);
        excel.setDataCenter(BusinessConst.dataCenterMap.get(x.getDataCenterId()));
        excel.setDeviceStatus(BusinessConst.deviceStatusMap.get(x.getDeviceStatus()));
        if(x.getOfflineDays()!=null){
            excel.setOfflineDays(x.getOfflineDays() + "天");
        }
        //离线时长
        if(ObjectUtil.isNotNull(x.getOfflineTime())) {
            try {
                excel.setOfflineTime(sdf.format(x.getOfflineTime()));
            }catch (Exception e){
                log.error("sdf.format error");
            }
        }
        Date offlineTime = x.getOfflineTime();
        excel.setOfflineTimeStr(this.getOfflineTimeString(offlineTime));

        excel.setSort(ai.getAndIncrement());
        if(ObjectUtil.isNotNull(x.getCreated())) {
            try {
                excel.setCtime(sdf.format(x.getCreated()));
            }catch (Exception e){
                log.error("sdf.format error");
            }
        }

        return excel;
    }

    @Override
    public DeviceLedgerNotActiveExcel buildDeviceLedgerNotActiveExcelVO(DeviceLedgerResp x, AtomicInteger ai) {
        DeviceLedgerNotActiveExcel excel = new DeviceLedgerNotActiveExcel();
        BeanUtils.copyProperties(x, excel);
        excel.setDataCenter(BusinessConst.dataCenterMap.get(x.getDataCenterId()));
        excel.setDeviceStatus(BusinessConst.deviceStatusMap.get(x.getDeviceStatus()));
        excel.setSort(ai.getAndIncrement());
        if(x.getNotActivatDays()!=null){
            excel.setNotActivatDays(x.getNotActivatDays() + "天");
        }
        if(ObjectUtil.isNotNull(x.getCreated())) {
            try {
                excel.setCtime(sdf.format(x.getCreated()));
            }catch (Exception e){
                log.error("sdf.format error");
            }
        }

        return excel;
    }

    @Override
    public String getHXModelStr() {
        return orsDeviceInfoMapper.getHXModelStr();
    }


}
