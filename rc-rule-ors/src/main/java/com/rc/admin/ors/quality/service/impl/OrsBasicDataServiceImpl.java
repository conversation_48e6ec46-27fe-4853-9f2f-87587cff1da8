package com.rc.admin.ors.quality.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.dao.DeviceParamReportLogMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.OrsBasicDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
@Slf4j
@Service
public class OrsBasicDataServiceImpl implements OrsBasicDataService {

    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource
    private DeviceParamReportLogMapper deviceParamReportLogMapper;

    @Resource
    private OrsModelPropertiesConfigMapper orsModelPropertiesConfigMapper;

    @Override
    public List<OrsBasicData> countryList() {
        return orsDeviceInfoMapper.countryList();
    }

    @Override
    public List<OrsBasicData> divisionList() {
        return orsDeviceInfoMapper.divisionList();
    }

    @Override
    public List<OrsBasicData> regionList() {
        return orsDeviceInfoMapper.regionList();
    }

    @Override
    public List<OrsBasicData> newRegionList() {
        return orsDeviceInfoMapper.newRegionList();
    }

    @Override
    public List<OrsBasicData> productGroupList() {
        return orsDeviceInfoMapper.productGroupList();
    }

    @Override
    public List<OrsBasicData> newCountryList() {
        return orsDeviceInfoMapper.newCountryList();
    }

    @Override
    public List<OrsBasicData> newCountryDeviceList() {
        return orsDeviceInfoMapper.newCountryDeviceList();
    }

    @Override
    public List<OrsBasicData> newDivisionDeviceList() {
        return orsDeviceInfoMapper.newDivisionDeviceList();
    }

    @Override
    public List<OrsBasicData> getUserList() {
        return orsDeviceInfoMapper.getUserList();
    }

    @Override
    public List<OrsBasicData> newCountryRegionList() {
        return orsDeviceInfoMapper.newCountryRegionList();
    }

    @Override
    public List<OrsBasicData> newDivisionList() {
        return orsDeviceInfoMapper.newDivisionList();
    }

    @Override
    public List<OrsBasicData> newProductGroupList(String divisionCode) {
        return orsDeviceInfoMapper.newProductGroupList(divisionCode);
    }

    @Override
    public List<OrsBasicData> newProductGroupDeviceList(String  syb) {

        String sybCheck = null ;
        if(StringUtils.isNotBlank(syb)) {
            boolean contains = Arrays.asList(syb.split(",")).contains("1");
            if (contains) {
                sybCheck = "1";
            }
        }
        return orsDeviceInfoMapper.newProductGroupDeviceList(syb,sybCheck);
    }


    @Override
    public Page<String> assetIdList(String assetId, Page<?> page) {
        return orsDeviceInfoMapper.assetIdList(page, assetId);
    }

    @Override
    public Page<String> nameList(String thingName, Page<?> page) {
        return orsDeviceInfoMapper.nameList(page, thingName);
    }

    @Override
    public List<String> modelNameList(String divisionCode,String productGroupCode,String key) {
        return orsDeviceInfoMapper.modelNameList(divisionCode,productGroupCode,key);
    }


    @Override
    public List<String> modelNameListNew(String divisionCode,String productGroupCode) {
        return orsDeviceInfoMapper.modelNameListNew(divisionCode,productGroupCode);
    }


    @Override
    public List<OrsDeviceListResp> deviceNoList(String deviceNo, Integer size) {
        return orsDeviceInfoMapper.deviceNoList(deviceNo, size);
    }

    @Override
    public Page<OrsModelPropertiesConfig> findModelPropertiesByModelId(String modelId, Integer current, Integer pageSize) {
        return orsModelPropertiesConfigMapper.selectPage(new Page<>(current, pageSize),
            Wrappers.lambdaQuery(OrsModelPropertiesConfig.class).eq(OrsModelPropertiesConfig::getModelId, modelId)
                    .eq(OrsModelPropertiesConfig::getDataType,"single_param"));
    }

    //integrity回传数据完整性统计
    @Override
    public List<DeviceQuelityCountDTO> countDeviceQuelityByAll(DeviceQuelityCountQuery query) {
        query.setDataType("ALL");
        query.setAbnormalName("9008");//属性值从未上报
        return countDataQuality(query);
    }

    //quality回传数据准确性统计
    @Override
    public List<DeviceQuelityCountDTO> countDeviceQuelityByQuelity(DeviceQuelityCountQuery query) {
        query.setDataType("QUALITY");
        query.setAbnormalName("9001");//属性值异常
        return countDataQuality(query);
    }

    @Override
    public List<DeviceCountModel> getDataQualityReport(DataQualityReportReq req) {

        // 获得全部数据
        //List<DeviceCountModel> condition = orsDeviceInfoMapper.countDeviceByCondition(req);
//        List<DeviceCountModel> condition = orsDeviceInfoMapper.countDeviceByConditionChange(req);
//
//        // 获得属性对应的数据
//        List<DeviceCountModel> conditionByParam = orsDeviceInfoMapper.countDeviceByParamCode(req);

        // 使用CompletableFuture并行执行两个查询
        CompletableFuture<List<DeviceCountModel>> conditionFuture = CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            List<DeviceCountModel> result = orsDeviceInfoMapper.countDeviceByConditionDivision(req);
            log.info("countDeviceByConditionChange查询耗时：{}ms", System.currentTimeMillis() - startTime);
            return result;
        });

        CompletableFuture<List<DeviceCountModel>> paramFuture = CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            List<DeviceCountModel> result = orsDeviceInfoMapper.countDeviceByParamCodeChange(req);
            log.info("countDeviceByParamCodeChange查询耗时：{}ms", System.currentTimeMillis() - startTime);
            return result;
        });
        // 等待两个任务都完成
        List<DeviceCountModel> condition;
        List<DeviceCountModel> conditionByParam;
        try {
            condition = conditionFuture.get();
            conditionByParam = paramFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("并行查询执行异常：", e);
            throw new RuntimeException("数据查询失败", e);
        }
        // 根据获得的属性数据组装前端响应数据
        conditionByParam.forEach(x->{
            DeviceCountModel model = condition.stream().filter(c -> x.getDivision().equals(c.getDivision()) && x.getModelId().equals(c.getModelId())).findFirst().orElse(null);
            if (null != model) {
                model.setQueryAbnormalType(req.getAbnormalName());
                x.setTotal(model.getTotal());
                x.setRegistNum(model.getRegistNum());
                x.setUnRegistNum(model.getUnRegistNum());
                x.setActiveNum(model.getActiveNum());
                x.setWholeExcludNum(model.getWholeExcludNum());
                x.setAbnormalTotalNum(model.getAbnormalTotalNum());
                x.setTotalReportNum(model.getTotalReportNum());
                x.setTotalUnReportNum(model.getTotalUnReportNum());
                x.setQueryAbnormalType(req.getAbnormalName());
                x.setNullAbnormalTotalNum(model.getNullAbnormalTotalNum());
                x.setParamAbnormalTotalNum(model.getParamAbnormalTotalNum());
                x.setParamAbnormalRate(model.getParamAbnormalRate());
                x.setNullAbnormalRate(model.getNullAbnormalRate());
                x.setCheckParamAbnormalRate(model.getCheckParamAbnormalRate());
                x.setCheckNullAbnormalRate(model.getCheckNullAbnormalRate());
                x.setModelName(model.getModelName());
            }
        });

        conditionByParam.forEach(item->{
            item.setProductGroup(item.getModelId()+"-"+item.getModelName());
            item.setProductGroupCode(item.getModelId());
            item.setModelName(item.getProductGroup());
        });
        return conditionByParam;
    }


    public List<DeviceCountModelNew> detailWithResult(DataQualityReportReq req,List<DeviceCountModelNew> condition,List<DeviceModel> deviceModelList,List<DeviceCountModelNew> conditionByParam){
        Map<String, DeviceModel> deviceModeMap = deviceModelList.stream().collect(Collectors.toMap(DeviceModel::getModelId, i -> i, (k1, k2) -> k1));

        Map<String, DeviceModel> finalDeviceModeMap = deviceModeMap;
        condition.forEach(item->{
            if(finalDeviceModeMap.containsKey(item.getModelId())){
                item.setParamAbnormalRate(finalDeviceModeMap.get(item.getModelId()).getParamAbnormalRate());
                item.setNullAbnormalRate(finalDeviceModeMap.get(item.getModelId()).getNullAbnormalRate());
            }
        });
        // 根据获得的属性数据组装前端响应数据
        conditionByParam.forEach(x->{
            DeviceCountModelNew model = condition.stream().filter(c -> x.getDivision().equals(c.getDivision()) && x.getModelId().equals(c.getModelId())).findFirst().orElse(null);
            if (null != model) {
                model.setQueryAbnormalType(req.getAbnormalName());
                x.setTotal(model.getTotal());
                x.setRegistNum(model.getRegistNum());
                x.setUnRegistNum(model.getUnRegistNum());
                x.setActiveNum(model.getActiveNum());
                x.setWholeExcludNum(model.getWholeExcludNum());
                x.setAbnormalTotalNum(model.getAbnormalTotalNum());
                x.setTotalReportNum(model.getTotalReportNum());
                x.setTotalUnReportNum(model.getTotalUnReportNum());
                x.setQueryAbnormalType(req.getAbnormalName());
                x.setNullAbnormalTotalNum(model.getNullAbnormalTotalNum());
                x.setParamAbnormalTotalNum(model.getParamAbnormalTotalNum());
                x.setParamAbnormalRate(model.getParamAbnormalRate());
                x.setNullAbnormalRate(model.getNullAbnormalRate());
                x.setModelName(model.getModelName());
            }
        });

        conditionByParam.forEach(item->{
            item.setProductGroup(item.getModelId()+"-"+item.getModelName());
            item.setProductGroupCode(item.getModelId());
            item.setModelName(item.getProductGroup());
        });
        return conditionByParam;
    }


    @Override
    public List<DeviceCountModelNew> getDataQualityReportNew(DataQualityReportReq req) {

        // 获得全部数据
        CompletableFuture<List<DeviceCountModelNew>> conditionFuture = CompletableFuture.supplyAsync(() ->
                {
                    long l = System.currentTimeMillis();
                    List<DeviceCountModelNew> deviceCountModelNews = orsDeviceInfoMapper.countDeviceByConditionNew(req);
                    log.info("deviceCountModelNews查询时间："+(System.currentTimeMillis()-l));
                    return deviceCountModelNews;
                }
                );
        //获取以设备为维度来计算的数据
        CompletableFuture<List<DeviceModel>> deviceModelFuture = CompletableFuture.supplyAsync(() ->
                {
                    long l = System.currentTimeMillis();
                    List<DeviceModel> deviceModelByDay = orsDeviceInfoMapper.getDeviceModelByDay(req);
                    log.info("deviceModelByDay查询时间："+(System.currentTimeMillis()-l));
                    return deviceModelByDay;
                }
                );
        // 获得属性对应的数据
        CompletableFuture<List<DeviceCountModelNew>> conditionByParamFuture = CompletableFuture.supplyAsync(() ->
                {
                    long l = System.currentTimeMillis();
                    List<DeviceCountModelNew> deviceCountModelNews = orsDeviceInfoMapper.countDeviceByParamCodeNew(req);
                    log.info("deviceCountParamNews查询时间："+(System.currentTimeMillis()-l));
                    return deviceCountModelNews;
                }
                );
        CompletableFuture<List<DeviceCountModelNew>> combinedFuture = CompletableFuture.allOf(conditionFuture, deviceModelFuture, conditionByParamFuture)
                .thenApply(v -> {
                    try {
                        long l = System.currentTimeMillis();
                        // 获取异步方法的结果
                        List<DeviceCountModelNew> conditionResult = conditionFuture.get();
                        List<DeviceModel> deviceModelResult = deviceModelFuture.get();
                        List<DeviceCountModelNew> conditionByParamResult = conditionByParamFuture.get();

                        List<DeviceCountModelNew> deviceCountModelNews = detailWithResult(req, conditionResult, deviceModelResult, conditionByParamResult);
                        log.info("combinedFuture数据组合用时间："+(System.currentTimeMillis()-l));
                        // 调用 processResults 方法，并返回结果
                        return deviceCountModelNews;
                    } catch (InterruptedException | ExecutionException e) {
                        log.info("运行时异常：{}",e.getMessage(),e);
                        throw new RuntimeException(e);
                    }
                });
        // 等待所有 CompletableFuture 完成，并获取最终结果
        List<DeviceCountModelNew> result = CollUtil.newArrayList();
        try {
            result = combinedFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            log.info("运行时异常：{}",e.getMessage(),e);
        }
        return result;
    }

    @Override
    @Async
    public void insertDeviceRateDay(Date bizDate) {
        try {
            orsDeviceInfoMapper.insertDeviceRateDay(bizDate);
        }catch (Exception e){
            log.info("设备单天准确性异常率以及完整性异常率计算失败：{}",e.getMessage(),e);
        }
    }

    @Override
    public Page<String> rcAssetIdList(String rcAssetId, Page<?> page) {
        return orsDeviceInfoMapper.rcAssetIdList(page, rcAssetId);
    }

    public static void main(String[] args) {
        YearMonth month = YearMonth.now().minusMonths(1);
        LocalDate previousMonthFirstDay = month.atDay(1);
        LocalDate previousMonthLastDay = month.atEndOfMonth();
        YearMonth yearMonth = month.minusMonths(1);
        LocalDate previousMonthStart = yearMonth.atDay(1);
        LocalDate previousMonthEnd = yearMonth.atEndOfMonth();

        System.out.println(Date.from(previousMonthFirstDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        System.out.println(Date.from(previousMonthLastDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        System.out.println(Date.from(previousMonthStart.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        System.out.println(Date.from(previousMonthEnd.atStartOfDay(ZoneId.systemDefault()).toInstant()));

    }

    private final SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyy-MM");
    private final SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");

    private final List<String> day31Monthes = Arrays.asList("01", "03", "05", "07", "08", "10", "12");


    /** 2024-04-29
     回传数据完整性统计  9008//属性值从未上报
     正常数量 = 上报数据的设备总数(包含上报了工况数据和上报异常数据的设备总和并按设备编号去重) - 属性值异常的设备数量(只包含9008属性值从未上报的设备)
     比率 = 正常数量 / 上报数据的设备总数

     回传数据准确性统计 9001//属性值异常
     正常数量 = 上报数据的设备总数(包含上报了工况数据和上报异常数据的设备总和并按设备编号去重) - 属性值异常的设备数量(包含非9008属性值从未上报的其他异常设备)
     比率 = 正常数量 / 上报数据的设备总数
     */
    private List<DeviceQuelityCountDTO> countDataQuality(DeviceQuelityCountQuery query) {
        List<DeviceQuelityCountDTO> data = new ArrayList<>();

        Map<String, Map<String, List<DeviceCountModel>>> collect3 = new HashMap<>();

        // 处理时间区间
        List<String> dates = new ArrayList<>();
        try {
            if (StringUtils.isNotBlank(query.getYear())) {
                List<DateTime> months = DateUtil.rangeToList(sdfDay.parse(query.getYear() + "-01-01"), new Date(), DateField.MONTH);
                for (DateTime month : months) {
                    dates.add(sdfMonth.format(month));
                }
            }
            if (StringUtils.isNotBlank(query.getMonth()) || StringUtils.isNotBlank(query.getStartTime())) {
                List<DateTime> months = new ArrayList<>();
                if (StringUtils.isNotBlank(query.getMonth())) {
                    String[] split = query.getMonth().split("-");
                    String endDay = query.getMonth() + "-30";
                    if (split[1].equals("02")) {
                        endDay = query.getMonth() + "-28";
                    }
                    if (day31Monthes.contains(split[1])) {
                        endDay = query.getMonth() + "-31";
                    }
                    String curMonth = sdfMonth.format(new Date());
                    if (query.getMonth().equals(curMonth)) {
                        endDay = sdfDay.format(new Date());
                    }
                    months = DateUtil.rangeToList(sdfDay.parse(query.getMonth() + "-01"), sdfDay.parse(endDay), DateField.DAY_OF_MONTH);
                }
                if (StringUtils.isNotBlank(query.getStartTime())) {
                    months = DateUtil.rangeToList(sdfDay.parse(query.getStartTime()), sdfDay.parse(query.getEndTime()), DateField.DAY_OF_MONTH);
                }
                for (DateTime month : months) {
                    dates.add(sdfDay.format(month));
                }
                query.setStartTime(dates.get(0));
                query.setEndTime(dates.get(dates.size()-1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<DeviceCountModel> models = null;
        if(query.getNewMethod().intValue() == 0)
        {//旧方法
            models = deviceParamReportLogMapper.countDataQuality(query);
        }
        else
        {//新方法
            models = deviceParamReportLogMapper.countDataQuality_new(query);
        }

        // 判断是按事业部维度
        List<OrsBaseDeviceInfo> finalInfos = new ArrayList<>();
        if (!query.getDivisiones().isEmpty() && query.getProductCodes().isEmpty()) {
            Map<String, List<DeviceCountModel>> map = models.stream().collect(Collectors.groupingBy(DeviceCountModel::getDivisionCode));
            map.forEach((k, v)->{
                OrsBaseDeviceInfo info  = new OrsBaseDeviceInfo();
                info.setDivision(v.get(0).getDivision());
                info.setDivisionCode(k);
                finalInfos.add(info);
            });
            collect3 = models.stream().collect(Collectors.groupingBy(DeviceCountModel::getReportDate, Collectors.groupingBy(DeviceCountModel::getDivision)));
        }
        // 判断是按产品组维度
        if (!query.getProductCodes().isEmpty()) {
            Map<String, List<DeviceCountModel>> map = models.stream().collect(Collectors.groupingBy(DeviceCountModel::getProductGroupCode));
            map.forEach((k, v)->{
                OrsBaseDeviceInfo info  = new OrsBaseDeviceInfo();
                info.setDivision(v.get(0).getProductGroup());
                info.setDivisionCode(k);
                finalInfos.add(info);
            });
            collect3 = models.stream().collect(Collectors.groupingBy(DeviceCountModel::getReportDate, Collectors.groupingBy(DeviceCountModel::getProductGroup)));
        }
        // 如果没有事业部或者产品组  则以时间维度统计
        Map<String, List<DeviceCountModel>> collect = new HashMap<>();
        if (query.getDivisiones().isEmpty() && query.getProductCodes().isEmpty()) {
            collect = models.stream().collect(Collectors.groupingBy(DeviceCountModel::getReportDate));
        }

        // 组装返回数据结构
        for (String date : dates) {
            // 找出月份下对应的设备与设备台账信息匹配, 并按事业部分组
            DeviceQuelityCountDTO dto = new DeviceQuelityCountDTO();
            dto.setMonth(date);
            dto.setDay(date);


            List<DeviceQuelityCountDTO.cData> clist = new ArrayList<>();
            //事业部或者产品组不为空
            if (!query.getDivisiones().isEmpty() || !query.getProductCodes().isEmpty()) {
                for (OrsBaseDeviceInfo group : finalInfos) {
                    DeviceQuelityCountDTO.cData c = new DeviceQuelityCountDTO.cData();
                    c.setDivision(group.getDivision());//事业部
                    c.setNormalTotal(0);//正常数量
                    c.setTotal(0);//总数

                    Map<String, List<DeviceCountModel>> listMap = collect3.get(date);
                    if (null != listMap) {
                        List<DeviceCountModel> logs = listMap.get(group.getDivision());
                        if (null != logs) {
                            // (总异常设备数-设备剔除数)÷(激活台数-设备剔除数)
                            DeviceCountModel model = logs.get(0);
                            model.setQueryAbnormalType(query.getAbnormalName());//异常类型:9001属性值异常//9008是属性值从未上报
                            c.setTotal(model.getTotalReportNum());//该时间段内上报数据的设备总数
                            c.setAbnormalTotal(model.getParamAbnormalTotalNum());//该时间段属性值异常的设备数量(除了9008异常的)
                            if ("9008".equals(query.getAbnormalName())) {//9008是属性值从未上报
                                c.setAbnormalTotal(model.getNullAbnormalTotalNum());//上传null值的异常设备数量
                            }
                            c.setNormalTotal(model.getTotalReportNum() - c.getAbnormalTotal());//正常数量=上报数据的设备总数-属性值异常的设备数量
                            if(c.getTotal()!=0)
                                c.setScale((double) c.getNormalTotal() / c.getTotal());//比率=正常数量/上报数据的设备总数
                        }
                    }
                    clist.add(c);
                }
            }

            //事业部和产品组为空的
            if (query.getDivisiones().isEmpty() && query.getProductCodes().isEmpty()) {
                List<DeviceCountModel> list = collect.get(date);
                if (null != list && !list.isEmpty()) {
                    DeviceQuelityCountDTO.cData c = new DeviceQuelityCountDTO.cData();
                    c.setDivision(date);//事业部
                    c.setNormalTotal(0);//正常数量
                    c.setTotal(0);//总数
                    // (总异常设备数-设备剔除数)÷(激活台数-设备剔除数)
                    DeviceCountModel model = list.get(0);
                    model.setQueryAbnormalType(query.getAbnormalName());
                    c.setTotal(model.getTotalReportNum());
                    c.setAbnormalTotal(model.getParamAbnormalTotalNum());
                    if ("9008".equals(query.getAbnormalName())) {
                        c.setAbnormalTotal(model.getNullAbnormalTotalNum());
                    }
                    c.setNormalTotal(model.getTotalReportNum() - c.getAbnormalTotal());
                    if(c.getTotal()!=0)
                        c.setScale((double) c.getNormalTotal() / c.getTotal());

                    clist.add(c);
                }
            }
            dto.setDatas(clist);
            data.add(dto);
        }
        return data.stream().sorted(Comparator.comparing(DeviceQuelityCountDTO::getMonth)).collect(Collectors.toList());
    }
}
