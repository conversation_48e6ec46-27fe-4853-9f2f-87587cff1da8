package com.rc.admin.ors.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsReq;
import com.rc.admin.ors.quality.model.DqmDeviceDataExceptionsResp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

/**
 * 设备数据异常明细(OrsDeviceDataAbnormalDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-27 14:51:51
 */
public interface OrsDeviceDataAbnormalDetailMapper extends BaseMapper<OrsDeviceDataAbnormalDetail> {

    /**
     * 同步异常数据
     */
    void syncAbnormalData(@Param("bizDate") Date bizDate);

    /**
     * 清除7天之前的数据
     */
    void deleteSanyDataService(@Param("bizDate") Date bizDate);

    @Select("select count(1) cnt from dqm.ors_device_data_abnormal_detail WHERE stat_date = #{bizDate}::DATE")
    Integer getAbnormalDetailCnt(@Param("bizDate") Date bizDate);
    //-- 更新位置异常数据，是否小于位置工况总数的1%,小于1%的数据abnormal_effective设置为0
    void updataAbnormalEffective(@Param("bizDate") Date bizDate);



    //(9004, 9001)
    void updataAbnormalEffectiveOne(@Param("bizDate") Date bizDate);
    //(9007, 9001, 9002)
    void updataAbnormalEffectiveTwo(@Param("bizDate") Date bizDate);
    //9001
    void updataAbnormalEffectiveThree(@Param("bizDate") Date bizDate);




    Page<DqmDeviceDataExceptionsResp> findAbnormalDetail(Page<?> page, @Param("req") DqmDeviceDataExceptionsReq req);
}

