package com.rc.admin.ors.quality.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PropertyModel {

    @ApiModelProperty("模型id")
    private String modelId;

    @ApiModelProperty("属性Code")
    private String property;

    @ApiModelProperty("准确性异常的数量")
    private int  propertyParamAbnormalTotalNum;

    @ApiModelProperty("完整性异常的数量")
    private int propertyNullAbnormalTotalNum;

    @ApiModelProperty("检查项准确性异常率")
    private double checkParamAbnormalRate;

    @ApiModelProperty("检查项完整性异常率")
    private double  checkNullAbnormalRate;

}
