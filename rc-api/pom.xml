<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.rc.admin</groupId>
        <artifactId>rc-admin</artifactId>
        <version>0.1.0</version>
    </parent>

    <artifactId>rc-api</artifactId>
    <packaging>jar</packaging>

    <description>RC Api</description>

    <dependencies>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-sys</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-sany</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-scheduler</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-pst</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-rule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-activiti</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-rule-ors</artifactId>
        </dependency>
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.17.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <finalName>${project.build.finalName}</finalName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.8.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <!--阿里云主仓库，代理了maven central和jcenter仓库-->
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>
