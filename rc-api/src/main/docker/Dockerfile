FROM registry.rootcloud.com/devops/openjdk:8-jre

<PERSON><PERSON>V SPRING_OUTPUT_ANSI_ENABLED=NEVER \
    JHIPSTER_SLEEP=0 \
    JAVA_OPTS="-server -Xms4096m -Xmx4096m -Xmn256m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -XX:InitialBootClassLoaderMetaspaceSize=64m -XX:SurvivorRatio=1 -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSInitiatingOccupancyOnly -XX:-OmitStackTraceInFastThrow -XX:CMSInitiatingOccupancyFraction=90 -XX:+ScavengeBeforeFullGC -XX:+CMSScavengeBeforeRemark -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/java-heapdump/"

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

CMD echo "The application will start in ${JHIPSTER_SLEEP}s..." && \
    sleep ${JHIPSTER_SLEEP} && \
    java ${JAVA_OPTS}  -Djava.security.egd=file:/dev/./urandom -jar /app.jar --spring.profiles.active=prod -Duser.timezone=Asia/Shanghai

EXPOSE 80

ADD *.jar /app.jar