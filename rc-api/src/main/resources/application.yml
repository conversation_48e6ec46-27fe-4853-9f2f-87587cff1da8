project:
  # 版本号
  version: 0.5.0
  # 项目访问url
  url: http://127.0.0.1
  # 前端访问url
  front-end-url: http://localhost:3100
  # 文件上传路径(不要写以~开头的路径会导致无法访问)
  file-upload-path: /home/<USER>/dmq/dmq-api
springdoc:
  api-docs:
    #是否开启文档功能
    enabled: true
  #包扫描路径
  packagesToScan: com.rc.admin
server:
  # 端口号
  port: 8085
  servlet:
    context-path: /dmq
    session:
      timeout: 120s
  tomcat:
    connection-timeout: 120s
logging:
  file:
    path: logs
spring:
  elasticsearch:
    rest:
      # 连接超时时间
      connection-timeout: 1s
      username:
      password:
      # 读取超时时间
      read-timeout: 30s
      # es rest 接口地址，多个用逗号隔开
      uris: ${ES_HOST:127.0.0.1}:${ES_PORT:9200}
  # Redis
  redis:
    # 数据库索引（默认为0）
    database: 9
    # 服务器地址 *************
    host: **********
    # 服务器连接端口 30544
    port: 6379
    # 服务器连接密码（默认为空） TSR453idese*e
    password: ecm123456
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间 单位: 秒
    timeout: 10
    # 默认的数据过期时间 30 分钟 单位: 秒
    expire: 1800
  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:mysql://${DB_HOST:**********}:${DB_PORT:3306}/${DB_NAME:rc_iot_tool}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMulQueries=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullDatabaseMeansCurrent=true&useInformationSchema=true
          username: ${DB_USERNAME:root}
          password: ${DB_PASSWORD:sanyECM_3188}
      primary: 'master'
      # druid连接池监控
      druid:
        connection-properties:
          # 慢SQL记录
          druid.stat.slowSqlMillis: 2000
        filters: stat
        # 初始化时建立物理连接的个数
        initial-size: 5
        # 最小连接池数量
        min-idle: 5
        # 最大连接池数量
        max-active: 100
        # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。
        max-pool-prepared-statement-per-connection-size: 50
        # 获取连接时最大等待时间，单位毫秒
        max-wait: 60000
        # 连接保持空闲而不被驱逐的最小时间
        min-evictable-idle-time-millis: 300000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        time-between-eviction-runs-millis: 60000
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        use-global-data-source-stat: true
        validation-query: SELECT 1
    druid:
      # druid连接池监控
      stat-view-servlet:
        enabled: true
        allow: ""
        login-username: admin
        login-password: 123
      filter:
        stat:
          merge-sql: false
  #启动模式
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    async:
      request-timeout: 300s
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
# 打印sql语句,调试用
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
aj:
  captcha:
    # 滑动验证，底图路径，不配置将使用默认图片
    jigsaw: classpath:images/verification
async:
  executor:
    thread:
      # 配置核心线程数
      core-pool-size: 5
      # 配置最大线程数
      max-pool-size: 5
      # 配置队列大小
      queue-capacity: 999
      # 配置线程池中的线程的名称前缀
      name-prefix: async-
      # 配置线程最大空闲时间 s
      keep-alive-seconds: 30


#飞书小程序
feishu:
  #appId: cli_a403a5a575b89076
  appId: cli_a73d60549238d077
  #  appId: cli_a4f30a8ec2f8d077
  #  appSecret: 3hwWQwsL24wOnUtn7iaRohwClVH2wokB
  #appSecret: jgFAPj45SLGZj0Dasr37Rdc5ERKnHxKs8
  appSecret: qUdE5o36UrmDFpDm7qDywhekVLIdLYiX
  #  appSecret: XygtgL8nG5uYRPrO4vtJOgrCJtfdy2z4
  httpToken: https://open.work.sany.com.cn/open-apis/auth/v3/tenant_access_token/internal
  httpMessage: https://open.work.sany.com.cn/open-apis/message/v4/batch_send/
  httpOpenId: https://open.work.sany.com.cn/open-apis/contact/v3/users/batch_get_id
  receiveId: oc_4366227265cdeab0f4fc808d88c85896

#集成平台配置
esb:
  casService: http://tcs-test.sany.com.cn
  authKey: e5174a80-ae0e-424b-9690-8d66cecfb046
  esbHost: http://ipaas-esb-test.sany.com.cn

knife4j:
  enable: true

mail:
  username: <EMAIL>
  password: 29neQnB#
  host: smtp.sany.com.cn
  port: 25
