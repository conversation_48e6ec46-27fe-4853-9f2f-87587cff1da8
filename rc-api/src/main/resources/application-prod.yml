project:
  # 项目访问url
  url: http://127.0.0.1
  # 前端访问url
  front-end-url: http://localhost:3100
  # 文件上传路径(不要写以~开头的路径会导致无法访问)
  file-upload-path: /home/<USER>/dmq/dmq-api
springdoc:
  api-docs:
    #是否开启文档功能
    enabled: true
  #包扫描路径
  packagesToScan: com.rc.admin
server:
  # 端口号
  port: ${SERVER_PORT:8085}
  servlet:
    context-path: /dmq
logging:
  file:
    path: logs
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:*************:12002}
    listener:
      missing-topics-fatal: false
      type: batch
      concurrency: 4
    consumer:
      auto-commit-interval: 1000ms
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 300
      properties:
        session-timeout: 120000ms
        request-timeout: 180000ms
        group-id: rc-rule
  elasticsearch:
    rest:
      # 连接超时时间
      connection-timeout: 1s
      username:
      password:
      # 读取超时时间
      read-timeout: 30s
      # es rest 接口地址，多个用逗号隔开
      uris: ${ES_HOST:127.0.0.1}:${ES_PORT:9200}
  # Redis
  redis:
    # 数据库索引（默认为0）
    database: 9
    # 服务器地址
    host: ${REDIS_HOST:************}
    # 服务器连接端口
    port: ${REDIS_PORT:6379}
    # 服务器连接密码（默认为空）
    password: ${REDIS_PWD:rc2023dqmredisprd}
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间 单位: 秒
    timeout: 10
    # 默认的数据过期时间 30 分钟 单位: 秒
    expire: 1800
  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:5432}/${DB_NAME:postgres_newc}?useUnicode=true&characterEncoding=utf8&&currentSchema=dqm&timezone=Asia/Shanghai&rewriteBatchedStatements=true
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:rc2023dqmprd}
          driverClassName: org.postgresql.Driver
        sany_data_service:
          url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:5432}/${DB_NAME:postgres_newc}?useUnicode=true&characterEncoding=utf8&&currentSchema=sany_data_service&timezone=Asia/Shanghai
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:rc2023dqmprd}
          driverClassName: org.postgresql.Driver
#        sany_server:
#          url: jdbc:mysql://${SANY_DB_HOST:***********}:${SANY_DB_PORT:3306}/${SANY_DB_NAME:sany_service}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMulQueries=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullDatabaseMeansCurrent=true&useInformationSchema=true
#          username: ${SANY_DB_USERNAME:aws_ro}
#          password: ${SANY_DB_PASSWORD:K8_aJa2ErSLIZiN}
#          driverClassName: com.mysql.cj.jdbc.Driver
aj:
  captcha:
    # 滑动验证，底图路径，不配置将使用默认图片
    jigsaw: classpath:images/verification
async:
  executor:
    thread:
      # 配置核心线程数
      core-pool-size: 5
      # 配置最大线程数
      max-pool-size: 5
      # 配置队列大小
      queue-capacity: 999
      # 配置线程池中的线程的名称前缀
      name-prefix: async-
      # 配置线程最大空闲时间 s
      keep-alive-seconds: 30
#飞书小程序
feishu:
  #appId: cli_a403a5a575b89076
  appId: cli_a73d60549238d077
  #  appId: cli_a4f30a8ec2f8d077
  #  appSecret: 3hwWQwsL24wOnUtn7iaRohwClVH2wokB
  #appSecret: jgFAPj45SLGZj0Dasr37Rdc5ERKnHxKs8
  appSecret: qUdE5o36UrmDFpDm7qDywhekVLIdLYiX
  #  appSecret: XygtgL8nG5uYRPrO4vtJOgrCJtfdy2z4
  httpToken: https://open.work.sany.com.cn/open-apis/auth/v3/tenant_access_token/internal
  httpMessage: https://open.work.sany.com.cn/open-apis/message/v4/batch_send/
  httpOpenId: https://open.work.sany.com.cn/open-apis/contact/v3/users/batch_get_id
  receiveId: oc_4366227265cdeab0f4fc808d88c85896
#集成平台配置
esb:
  casService: http://tcs-test.sany.com.cn
  authKey: e5174a80-ae0e-424b-9690-8d66cecfb046
  esbHost: http://ipaas-esb-test.sany.com.cn

ors:
  rootCloud:
    clientId: 20250214b4e9e6c1faf930b9
    clientSecret: 3868c834bf52a274ee975187038831fb
    grantType: client_credentials
    organizationId: 6653ec92d31141004170a5f3
    authKey: 9b423204-733b-4715-8d9e-cd79010d0c50
    esbUrl: http://ipaas-esb.sany.com.cn/data/data-center-api/api/service/post-data/V1.101/85ab01c7642043f795003ead032b5e6b
mail:
  username: <EMAIL>
  password: Kaifa202508!
  host: smtp.sany.com.cn
  port: 25