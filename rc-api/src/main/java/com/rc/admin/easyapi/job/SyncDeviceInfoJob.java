package com.rc.admin.easyapi.job;

import com.rc.admin.easyapi.dao.IotDeviceInfoMapper;
import com.rc.admin.easyapi.service.DeviceProfileService;
import com.rc.admin.easyapi.service.WorkingConditionService;
import com.rc.admin.easyapi.service.dqmService.OrsBigdataEquipmentBaseInfoAllService;
import com.rc.admin.easyapi.service.dqmService.OrsDeviceLocationService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 同步设备信息
 * <AUTHOR>
 * @date 2023-07-28
 */
@Component
@Log4j2
public class SyncDeviceInfoJob {

    @Resource
    IotDeviceInfoMapper infoMapper;
    @Resource
    WorkingConditionService workingConditionService;

    @Resource
    DeviceProfileService deviceProfileService;

    //@Resource
    //OrsBigdataEquipmentBaseInfoAllService infoAllService;

    @Resource
    OrsDeviceLocationService orsDeviceLocationService;


    public void syncDeviceJob(){
        List<String> regionValues = infoMapper.getRegionValues();
        for (String regionValue : regionValues) {
            try {
                workingConditionService.GetFileExportDevice(regionValue);
            } catch (Exception e) {
              log.error("同步设备数采规则信息异常:"+regionValue);
              e.printStackTrace();
            }
        }
    }
    public void syncRootCloudDeviceJob(){
            try {
                workingConditionService.syncRootCloudDeviceInfo();
            } catch (Exception e) {
              log.error("根云设备信息设值异常");
            }
    }

    public void syncDeviceProfileJob() {
        try {
            deviceProfileService.syncDeviceProfiles();
        } catch (Exception e) {
            log.error("同步设备台账失败：", e);
        }
    }

    /**
     * 每日增量同步设备信息
     */
    // public void synBigdataInfoAllJob() {
    //     log.info("同步CRM设备信息job开始。。。。");
    //     try {
    //         infoAllService.deviceIncrementalDataSynchronization();
    //     } catch (Exception e) {
    //         log.error("同步设备台账失败：", e);
    //     }
    //     log.info("同步CRM设备信息job结束。。。。");
    // }

    /**
     * 每日更新设备定位信息
     */
    public void updateDeviceLocation() {
        log.info("更新设备定位信息开始。。。。");
        try {
            orsDeviceLocationService.updateDeviceLocation(null);
        } catch (Exception e) {
            log.error("更新设备定位信息失败：", e);
        }
        log.info("更新设备定位信息结束。。。。");
    }

}
