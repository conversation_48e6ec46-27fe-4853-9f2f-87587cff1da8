package com.rc.admin.easyapi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.auth.common.constant.SysRoleConst;
import com.rc.admin.auth.dao.SysDeptMapper;
import com.rc.admin.auth.dao.SysUserMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.dao.SubscriptionMapper;
import com.rc.admin.easyapi.entity.Subscription;
import com.rc.admin.easyapi.model.req.SubscriptionReq;
import com.rc.admin.easyapi.model.resp.SubscriptionResp;
import com.rc.admin.sys.common.constant.WhetherConst;
import com.rc.admin.util.ShiroUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SubscriptionService extends ServiceImpl<SubscriptionMapper, Subscription> {

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    public Page<SubscriptionResp> getSubscription(SubscriptionReq subscription) {
        /*Page<Subscription> page = this.page(new Page<>(subscription.getCurrent(), subscription.getPageSize()), new QueryWrapper<Subscription>().like(StringUtils.isNotBlank(subscription.getAccount()), "account", subscription.getAccount()).eq(StringUtils.isNotBlank(subscription.getDeptId()), "dept_id", subscription.getDeptId()).orderByDesc(Arrays.asList("account","updated_at")));
        Page<SubscriptionResp> subscriptionRespPage = convertToSubscriptionRespPage(page);
        List<SysUser> username = sysUserMapper.selectList(new QueryWrapper<SysUser>().in(StringUtils.isNotBlank(subscription.getAccount()),"username", subscription.getAccount()));
        List<SysDept> sysDepts = sysDeptMapper.selectList(new QueryWrapper<SysDept>().eq("status", "1").isNotNull("simple_name"));
        Map<String, String> collect = sysDepts.stream().collect(Collectors.toMap(SysDept::getId, SysDept::getSimpleName));
        Map<String, SysUser> userMap = username.stream()
                .collect(Collectors.toMap(SysUser::getUsername, user -> user));
        subscriptionRespPage.getRecords().forEach(item -> {
            SysUser sysUser = userMap.get(item.getAccount());
            if (sysUser != null) {
                item.setEmail(sysUser.getEmail());
                item.setNickname(sysUser.getNickname());
                item.setPhone(sysUser.getPhoneNumber());
            }
          item.setSimpleName(collect.get(item.getDeptId()));
        });
        // 对结果进行排序
        subscriptionRespPage.setRecords(subscriptionRespPage.getRecords().stream()
                .sorted(Comparator.comparing((SubscriptionResp item) -> item.getAccount())
                        .thenComparing((SubscriptionResp item) -> item.getUpdatedAt(), Comparator.reverseOrder()))
                .collect(Collectors.toList()));*/

        // 非系统管理员，仅显示非系统数据
        if (!ShiroUtil.havRole(SysRoleConst.SYS_ADMIN)) {
            subscription.setSys(WhetherConst.NO);
        }
        return this.baseMapper.getSubscriptionPage(new Page<>(subscription.getCurrent(), subscription.getPageSize()),subscription);
    }
    public Page<SubscriptionResp> convertToSubscriptionRespPage(Page<Subscription> subscriptionPage) {
        List<SubscriptionResp> subscriptionRespList = subscriptionPage.getRecords().stream()
                .map(this::convertToSubscriptionResp)
                .collect(Collectors.toList());
        Page<SubscriptionResp> subscriptionRespPage = new Page<SubscriptionResp>();
        subscriptionRespPage.setCurrent(subscriptionPage.getCurrent());
        subscriptionRespPage.setPageSize(subscriptionPage.getPageSize());
        subscriptionRespPage.setTotal(subscriptionPage.getTotal());
        subscriptionRespPage.setRecords(subscriptionRespList);
        subscriptionRespPage.setPages(subscriptionPage.getPages());
        subscriptionRespPage.setOtherSize(subscriptionPage.getOtherSize());
        return subscriptionRespPage;
    }

    private SubscriptionResp convertToSubscriptionResp(Subscription subscription) {
        // 假设 SubscriptionResp 有一个构造函数或 setter 方法来设置 Subscription 的属性
        SubscriptionResp subscriptionResp = new SubscriptionResp();
        BeanUtils.copyProperties(subscription,subscriptionResp);
        return subscriptionResp;
    }

    public List<String> getDivisionList() {
        return baseMapper.getDivisionList();
    }
    public List<String> getDivisionCodeList(List<String> list) {
        return baseMapper.getDivisionCodeList(list);
    }
}