<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.easyapi.dao.RcIotPointVerificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.easyapi.entity.RcIotPointVerification">
        <id column="id" property="id" />
        <result column="second_process" property="secondProcess" />
        <result column="type" property="type" />
        <result column="description" property="description" />
        <result column="dest_address_name" property="destAddressName" />
        <result column="remark" property="remark" />
        <result column="expression" property="expression" />
        <result column="point_address" property="pointAddress" />
        <result column="data_type" property="dataType" />
        <result column="mode" property="mode" />
        <result column="acquisition_frequency" property="acquisitionFrequency" />
        <result column="save_policy" property="savePolicy" />
        <result column="data_registration" property="dataRegistration" />
        <result column="protocol_name" property="protocolName" />
        <result column="event_mode" property="eventMode" />
        <result column="express" property="express" />
        <result column="create_time" property="createTime" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.easyapi.entity.RcIotPointVerification">
        select t.* from rc_iot_point_verification t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getById" resultType="com.rc.admin.easyapi.entity.RcIotPointVerification">
        select t.* from rc_iot_point_verification t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.easyapi.entity.RcIotPointVerification">
        select t.* from rc_iot_point_verification t
        left join sys_dict sd_data_type on sd_data_type.code = t.data_type and sd_data_type.dict_type = 'dataType'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="queryData" resultType="com.rc.admin.easyapi.entity.RcIotPointVerification">
        select t.* from rc_iot_point_verification t  limit 1
    </select>
    <select id="getVerificationSecondProcess" resultType="java.lang.String">
        select DISTINCT  second_process from rc_iot_point_verification
    </select>
    <select id="getDeviceSecondProcess" resultType="java.lang.String">
        select DISTINCT  second_process from rc_iot_device_info where second_process!='' and second_process is not null    </select>

    <select id="selectOtherPoints" resultType="com.rc.admin.easyapi.model.resp.PointResp">
        select dest_address_name as name, description from rc_iot_collection_point where device_code = #{deviceCode}
        union all
        select dest_address_name as name, description from rc_iot_customize_point where device_code = #{deviceCode}
    </select>
    <select id="getBySecondProcess" resultType="java.lang.String">
        select DISTINCT  dest_address_name from rc_iot_point_verification where second_process = #{secondProcess}   </select>
</mapper>
