package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.easyapi.service.IotModelAbnormalBitInfoService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@ResponseResult
@RequestMapping("/api/rc/iot/model/abnormal/bit")

public class IotModelAbnormalBitInfoController {

    @Resource
    private IotModelAbnormalBitInfoService iotModelAbnormalBitInfoService;

    /**
     * 获取异常位信息
     * @param modelId 模型ID
     * @return 异常位信息
     */
    @RequestMapping("/getAbnormalBitInfo")
    public Response getAbnormalBitInfo(String modelId) {
        return Response.success(iotModelAbnormalBitInfoService.getModelAbnormalBitInfo(modelId));
    }

    /**
     * 获取bit位配置信息
     */
    @RequestMapping("/getBitConfigInfo")
    public Response getBitConfigInfo() {
        return Response.success(iotModelAbnormalBitInfoService.getModelAbnormalBitConfig());
    }
}
