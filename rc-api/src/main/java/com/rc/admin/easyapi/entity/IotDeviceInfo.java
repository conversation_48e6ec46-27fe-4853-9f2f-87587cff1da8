package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(value = "rc_iot_device_info")
@ApiModel(value = "rc_iot_device_info对象", description = "设备数据表")
public class IotDeviceInfo {

    @TableField(value = "device_code")
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;
    @TableField(value = "name")
    @ApiModelProperty(value = "设备名称")
    private String name;
    @TableField(value = "child_company_name")
    @ApiModelProperty(value = "子公司名称")
    private String childCompanyName;
    @TableField(value = "factory")
    @ApiModelProperty(value = "工厂")
    private String factory;
    @TableField(value = "work_center")
    @ApiModelProperty(value = "工作中心")
    private String workCenter;
    @TableField(value = "work_group")
    @ApiModelProperty(value = "班组")
    private String workGroup;
    @TableField(value = "protocol_type")
    @ApiModelProperty(value = "设备协议名称")
    private String protocolType;
    @TableField(value = "paraItem_value")
    @ApiModelProperty(value = "通讯参数")
    private String paraItemValue;
    @TableField(value = "station_number")
    @ApiModelProperty(value = "站号")
    private String stationNumber;
    @TableField(value = "scan_interval_time")
    @ApiModelProperty(value = "扫描周期")
    private String scanIntervalTime;
    @TableField(value = "con_timeout")
    @ApiModelProperty(value = "链接超时")
    private String conTimeout;
    @TableField(value = "recon_delay")
    @ApiModelProperty(value = "重连延时")
    private String reconDelay;
    @TableField(value = "customer_param")
    @ApiModelProperty(value = "自定义参数")
    private String customerParam;
    @TableField(value = "descript")
    @ApiModelProperty(value = "描述")
    private String descript;
    @TableField(value = "collection_type")
    @ApiModelProperty(value = "高频采集")
    private String collectionType;
    @TableField(value = "source")
    @ApiModelProperty(value = "来源")
    private String source;
    @TableField(value = "first_process")
    @ApiModelProperty(value = "一级工艺编码")
    private String firstProcess;
    @TableField(value = "first_process_name")
    @ApiModelProperty(value = "一级工艺名称")
    private String firstProcessName;
    @TableField(value = "second_process")
    @ApiModelProperty(value = "二级工艺编码")
    private String secondProcess;
    @TableField(value = "second_process_name")
    @ApiModelProperty(value = "二级工艺名称")
    private String secondProcessName;
    @TableField(value = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;
}
