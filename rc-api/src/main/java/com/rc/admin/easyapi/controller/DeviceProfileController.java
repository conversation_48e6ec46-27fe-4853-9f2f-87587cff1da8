package com.rc.admin.easyapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.common.core.base.BaseController;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.easyapi.job.SyncAbnormalData;
import com.rc.admin.easyapi.service.DeviceProfileService;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.ors.quality.dao.OrcTaskFileMapper;
import com.rc.admin.ors.quality.entity.OrcTaskFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@RestController
@ResponseResult
@RequestMapping("/device-profile")
@Api(value = "设备档案相关接口")
public class DeviceProfileController extends BaseController {

    private final DeviceProfileService deviceProfileService;

    @Autowired
    public DeviceProfileController(DeviceProfileService deviceProfileService) {
        this.deviceProfileService = deviceProfileService;
    }

    @GetMapping("/task")
    public void triggerTask() {
        deviceProfileService.syncDeviceProfiles();
    }

    @GetMapping("/pageData")
    public Page<DeviceProfile> select(Page<DeviceProfile> page, DeviceProfile deviceProfile){
        return deviceProfileService.selectPage(page, deviceProfile);
    }

    @GetMapping("/export")
    public String exportData(DeviceProfile deviceProfile){
        return deviceProfileService.exportData(deviceProfile);
    }

    @Resource
    SyncAbnormalData syncAbnormalData;

    @Resource
    private OrcTaskFileMapper orcTaskFileMapper;

    @ApiOperation("执行后台任务")
    @GetMapping("/export/local")
    public String exportLocalData(){
        new Thread(new Runnable() {
            @Override
            public void run() {
                syncAbnormalData.execOrcTaskFile();
            }
        }).start();
        return "已提交后台执行任务";
    }

    @ApiOperation("查询任务")
    @PostMapping("/export/select")
    public List<OrcTaskFile> exportSelectData(OrcTaskFile orcTaskFile){
        return orcTaskFileMapper.findByAll(orcTaskFile);
    }
    @ApiOperation("删除任务")
    @GetMapping("/export/delete")
    public String exportDeleteData(Integer task_id){
        int taskId = orcTaskFileMapper.delete(new QueryWrapper<OrcTaskFile>().eq("task_id", task_id));
        if(taskId>0)return  "task_id:"+task_id+" 删除成功";

        return "task_id:"+task_id+" 删除失败";
    }

    @ApiOperation("导出数据")
    @GetMapping("/export/down")
    public void selectAll(Integer task_id, HttpServletResponse response) {
        OrcTaskFile orcTaskFile = orcTaskFileMapper.selectOne(new QueryWrapper<OrcTaskFile>().eq("task_id", task_id));
        if(orcTaskFile !=null)
        {
            Object resultFile = orcTaskFile.getResultFile();
            if(resultFile == null )return;
            byte[] bytes = (byte[])resultFile;
            try {
                response.setCharacterEncoding("UTF-8");
                response.setHeader("content-Type", "application/vnd.ms-excel");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("export.xlsx", "UTF-8"));
                response.getOutputStream().write(bytes);
            } catch (IOException var4) {
                throw new RuntimeException(var4);
            }
        }
    }
}
