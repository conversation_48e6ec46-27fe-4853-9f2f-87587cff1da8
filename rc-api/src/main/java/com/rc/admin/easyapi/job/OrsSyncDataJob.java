package com.rc.admin.easyapi.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.easyapi.service.WorkingConditionService;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsCoreParamStatLatestMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.service.OrsDeviceInfoRelationService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *     ors同步设备数据定时任务
 * </p>
 * <AUTHOR>
 * @since 2023/10/23
 */
@Slf4j
@Component
public class OrsSyncDataJob {

    @Autowired
    private OrsSyncDeviceService orsSyncDeviceService;

    @Resource
    private OrsCoreParamStatLatestMapper orsCoreParamStatLatestMapper;

    @Resource
    private OrsDeviceInfoRelationService orsDeviceInfoRelationService;

    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource
    private WorkingConditionService workingConditionService;

    /**
     * 每天1点同步数据
     */
//    @Scheduled(fixedDelay = 500000000L)
    public void syncDeviceInfo() {
        log.info("同步根云设备信息job开始。。。。");
        try {
            // 同步IoT数据
            orsSyncDeviceService.syncData();

            // 同步machineLink数据
            orsDeviceInfoRelationService.syncHistoryDevice();

            // 合成数据
            orsDeviceInfoMapper.mergeDeviceInfo();

            //将事业部为空的数据更新为无事业部
            orsDeviceInfoMapper.upNoDivision();

//            // 最后处理一次数据
            List<OrsBaseDeviceInfo> infos = orsBaseDeviceInfoMapper.selectList(
                    new QueryWrapper<OrsBaseDeviceInfo>()
                            .lambda()
                            .like(OrsBaseDeviceInfo::getExceDesc, "设备编码重复")
            );
            infos = infos.stream().filter(info -> info.getSerialNum() != null).collect(Collectors.toList());
            Map<String, List<OrsBaseDeviceInfo>> collect = infos.stream().collect(Collectors.groupingBy(OrsBaseDeviceInfo::getSerialNum));
            collect.forEach((k, v)->{
                OrsBaseDeviceInfo info = v.get(0);
                orsBaseDeviceInfoMapper.delete(
                        new QueryWrapper<OrsBaseDeviceInfo>().lambda().eq(OrsBaseDeviceInfo::getSerialNum, k)
                );
                orsBaseDeviceInfoMapper.insert(info);
            });
            //设备同步完成后进行剔除标签工况上传
            //workingConditionService.uploadWorkingConditionDataToPlatform();
        } catch (Exception e) {
            log.error("根云设备同步异常", e);
        }
        log.info("同步根云设备信息job结束。。。。");
    }

    /**
     * 同步核心工况最新数据，每天3点执行
     */
//    @Scheduled(cron = "0 30 3 * * ?")
    public void syncSanyCoreData(){
        orsCoreParamStatLatestMapper.syncSanyData(null);
    }


    /**
     * 同步设备-国家（三一集成平台）
     */
    public void syncDeviceCountryData(){
        orsSyncDeviceService.resultMap();
    }


    /**
     * 国区同步
     */
    public void syncCountryRegionData(){
        orsSyncDeviceService.syncNationalRegion();
    }



    /**
     * 历史删除剔除数据推送剔除信息
     */
    public void syncCheckConfigHistory(){
        workingConditionService.allUploadWorkingConditionDataToPlatform(null,null,null,null);
    }
}
