package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.easyapi.model.req.PointResultListReq;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.easyapi.service.IotDeviceInfoService;
import com.rc.admin.easyapi.util.WorkbookUtil;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.util.office.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/api/v1/point")
@Api(value = "iot点位相关接口")
public class IotCollectionPointController {
    @Resource
    private IotDeviceInfoService rulesService;
    @Autowired
    public HttpServletResponse httpServletResponse;

    @ApiOperation(value = "iot设备点位分页查询")
    @PostMapping("/page")
    public Response getPointList(@RequestBody PointResultListReq req) {
        Page<PointResultListResp> pageUtil = rulesService.getPointList(req);
        return Response.success(pageUtil);
    }
    @ApiOperation(value = "iot设备点位列表导出")
    @PostMapping("/pointExport")
    public Response pageHistoryData( @RequestBody PointResultListReq req) {
//      rulesService.pagePointDataExport(req);
        try {
            req.setCurrent(0);
            req.setPageSize(500000);
            Page<PointResultListResp> pointList = rulesService.getPointList(req);
            if (CollectionUtils.isEmpty(pointList.getRecords())){
                return Response.failError("500","查无数据");
            }
            String downloadId = ExcelUtil.writeAndGetDownloadId(null, "iot设备点位信息", pointList.getRecords(), PointResultListResp.class);
            return Response.success(downloadId);
            //WorkbookUtil.pagePointDataExport(httpServletResponse,rulesService,req);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "iot点位的查询子公司列表")
    @PostMapping("/childCompanyNameList")
    public Response getChildCompanyNameList() {
        try {
            return Response.success(rulesService.getChildCompanyNameList());
        } catch (Exception e) {
            return Response.failError("查询列表失败");
        }
    }
}
