package com.rc.admin.easyapi.model.resp;

import lombok.Data;

import java.util.Objects;

@Data
public class PointResp {

    /**
     * 点位名称
     */
    private String name;

    /**
     * 点位描述
     */
    private String description;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PointResp pointResp = (PointResp) o;
        return name.equals(pointResp.name) &&
                description.equals(pointResp.description);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, description);
    }
}
