package com.rc.admin.easyapi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.easyapi.entity.IotModelAbnormalBitInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface IotModelAbnormalBitInfoMapper extends BaseMapper<IotModelAbnormalBitInfo> {

    @Select("SELECT * FROM orc_iot_abnormal_bit_config")
    List<Map<String,String>> getModelAbnormalBitConfig();
}
