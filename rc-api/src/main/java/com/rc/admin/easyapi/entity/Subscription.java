package com.rc.admin.easyapi.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("orc_subscriptions")
@ApiModel(description = "订阅信息")
public class Subscription {
    @TableId(value = "account", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "账户id", required = true)
    private String account;

    @TableField("is_enabled")
    @ApiModelProperty(value = "是否启用：0为关闭，1为启用", required = true)
    private int isEnabled;
    @TableField("dept_id")
    @ApiModelProperty(value = "组织编码", required = true)
    private String deptId;

    @TableField("notification_channels")
    @ApiModelProperty(value = "通知渠道：多个渠道，结构：\"邮件,飞书\"", required = true)
    private String notificationChannels;

    @TableField("subscribed_departments")
    @ApiModelProperty(value = "订阅事业部：事业部可多个，结构：\"重机,重起,重装\"", required = true)
    private String subscribedDepartments;

    @TableField("subscription_period")
    @ApiModelProperty(value = "订阅周期：年，月，日", required = true)
    private String subscriptionPeriod;

    @TableField("created_at")
    @ApiModelProperty(value = "创建时间", required = true)
    private Timestamp createdAt;

    @TableField("updated_at")
    @ApiModelProperty(value = "更新时间", required = true)
    private Timestamp updatedAt;


    @TableField(exist=false)
    @ApiModelProperty(value = "是否是管理员")
    private String sys;
}