package com.rc.admin.easyapi.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.RcIotPointVerification;
import com.rc.admin.easyapi.model.resp.PointResp;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 数据核验标准模板表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface RcIotPointVerificationService {
    /**
     * 列表
     *
     * @param rcIotPointVerification 查询条件
     * @param page   分页
     * @return Page<RcIotPointVerification>
     */
    Page<RcIotPointVerification> select(RcIotPointVerification rcIotPointVerification, Page<RcIotPointVerification> page);

    /**
     * 详情
     *
     * @param id id
     * @return RcIotPointVerification
     */
    RcIotPointVerification get(String id);

    /**
     * 新增
     * @return RcIotPointVerification
     */
    RcIotPointVerification add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param rcIotPointVerification 表单内容
     * @return RcIotPointVerification
     */
    RcIotPointVerification saveData(RcIotPointVerification rcIotPointVerification);

    /**
     * 导出数据
     *
     * @param rcIotPointVerification 查询条件
     */
    void exportData(HttpServletResponse httpServletResponse,RcIotPointVerification rcIotPointVerification);

    /**
     * 导入数据
     *
     * @param file 文件
     */
    String importData(MultipartFile file);

    /**
     * 获取点位列表
     * @param secondProcess 二级工艺编码
     * @param deviceCode 设备编码
     * @return 点位列表
     */
    Set<PointResp> selectPoints(String secondProcess, String deviceCode);
}
