package com.rc.admin.easyapi.service;

import com.rc.admin.auth.model.entity.RealWorkConditionResp;
import com.rc.admin.easyapi.model.req.QueryWorkingConditionReq;

import java.util.Map;

/**
 * <p>
 *     工况服务类.
 * </p>
 * <AUTHOR>
 * @since 2023-07-24
 */
public interface WorkingConditionService {

    /**
     * 根云,根连实时工况查询
     * @param req 查询入参
     * @return  工况信息
     */
    Map<String,Object> getRealTimeWorkingCondition(QueryWorkingConditionReq req);

    /**
     * 根云历史工况查询
     * @param req 入参信息
     * @return 工况信息
     */
    Map getHistoryWorkingCondition(QueryWorkingConditionReq req);

    /**
     * 根连实时工况查询
     * @param deviceCode 设备编号
     * @return 工况信息
     */
    RealWorkConditionResp getRealTimeWorkingCondition(String deviceCode);

    /**
     * 获取根云token
     * @return token信息
     */
    String getLoginToken();

    /**
     * 同步设备数采规则信息
     * @param url url地址
     */
    void GetFileExportDevice(String url);

    /**
     * 根云同步设备信息物模型相关信息
     */
    void syncRootCloudDeviceInfo();


    /**
     * 上传工况值到平台
     */
    void uploadWorkingConditionDataToPlatform();


    /**
     *  标志 如果标志为空 则是昨天的历史数据
     *  否则就是全量设备数据
     * @param sign
     */
    void allUploadWorkingConditionDataToPlatform(String sign,String  date,String assetId,String modelId);
}
