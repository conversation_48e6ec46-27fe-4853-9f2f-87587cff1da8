package com.rc.admin.easyapi.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 设备数据核验结果表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface RcIotEquipmentVerificationResultsService {
    /**
     * 列表
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     * @param page   分页
     * @return Page<RcIotEquipmentVerificationResults>
     */
    Page<RcIotEquipmentVerificationResults> select(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults, Page<RcIotEquipmentVerificationResults> page);

    /**
     * 详情
     *
     * @param id id
     * @return RcIotEquipmentVerificationResults
     */
    RcIotEquipmentVerificationResults get(String id);

    /**
     * 新增
     * @return RcIotEquipmentVerificationResults
     */
    RcIotEquipmentVerificationResults add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param rcIotEquipmentVerificationResults 表单内容
     * @return RcIotEquipmentVerificationResults
     */
    RcIotEquipmentVerificationResults saveData(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults);

    /**
     * 导出数据
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     */
    void exportData(HttpServletResponse httpServletResponse, RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults);

    /**
     * 点位核验
     */
    String pointVerification(List<String> secondProcesslist,List<String> field);
    /**
     * 获取点位列表
     */
    List<Map<String,String>> getSecondProcess();

}
