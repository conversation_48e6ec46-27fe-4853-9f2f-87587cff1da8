package com.rc.admin.easyapi.controller;

import com.rc.admin.auth.model.entity.RealWorkConditionResp;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.easyapi.model.req.IotDeviceInfoReq;
import com.rc.admin.easyapi.model.req.QueryWorkingConditionReq;
import com.rc.admin.easyapi.model.resp.IotDeviceInfoResp;
import com.rc.admin.easyapi.service.IotDataQualityInspectionHistoryService;
import com.rc.admin.easyapi.service.IotDeviceInfoService;
import com.rc.admin.easyapi.service.WorkingConditionService;
import com.rc.admin.easyapi.util.WorkbookUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/iot-data-collection")
@Api(value = "iot数据采集规则相关接口")
public class IotDeviceInfoController {


    @Resource
    private IotDeviceInfoService rulesService;

    @Resource
    private WorkingConditionService wcs;

    @Autowired
    public HttpServletResponse httpServletResponse;
    @Autowired
    public IotDataQualityInspectionHistoryService inspectionHistoryService;

    @ApiOperation(value = "iot数据采集规则分页查询")
    @PostMapping("/page")
    public Response getItpOrderPages(@RequestBody IotDeviceInfoReq req) {
        Page<IotDeviceInfoResp> pageUtil = rulesService.selectPages(req);
        return Response.success(pageUtil);
    }

    @ApiOperation(value = "同步数据")
    @PostMapping("/syncDeviceInfo")
    public Response getItpOrderPages(@RequestBody String url) {
        wcs.GetFileExportDevice(url);
        return Response.success();
    }

    @ApiOperation(value = "iot数据采集规则导出")
    @PostMapping("/deviceInfoExport")
    public Response deviceInfoExport(@RequestBody IotDeviceInfoReq req) {
        try {
             WorkbookUtil.IotDeviceInfoExport(httpServletResponse,rulesService,req);
        } catch (Exception e) {
            return Response.failError("导出失败");
        }
        return Response.success();
    }
    @ApiOperation(value = "根云历史工况查询")
    @PostMapping("/getHistoryWorkingCondition")
    public Response getHistoryWorkingCondition(@RequestBody QueryWorkingConditionReq req) {
        try {
            return Response.success(wcs.getHistoryWorkingCondition(req));
        } catch (Exception e) {
            return Response.failError("查询失败");
        }
    }

    @ApiOperation(value = "根云,根连实时工况查询")
    @PostMapping("/getRealTimeWorkingCondition")
    public Response getRealTimeWorkingCondition(@RequestBody QueryWorkingConditionReq req) {
        try {
            return Response.success(wcs.getRealTimeWorkingCondition(req));
        } catch (Exception e) {
            return Response.failError("查询失败");
        }
    }
    @ApiOperation(value = "根连实时工况查询")
    @GetMapping("/getRootRealTimeWorkingCondition/{deviceCode}")
    public Response getRootRealTimeWorkingCondition( @PathVariable("deviceCode")String deviceCode) {
        try {
            long interfaceTime = System.currentTimeMillis();
            RealWorkConditionResp realTimeWorkingCondition = wcs.getRealTimeWorkingCondition(deviceCode);
            interfaceTime = System.currentTimeMillis() - interfaceTime;
            return Response.success(realTimeWorkingCondition);
        } catch (Exception e) {
            return Response.failError("查询失败");
        }
    }

    @ApiOperation(value = "获取设备列表")
    @GetMapping("/getDeviceCodeList/{deviceCode}")
    public Response getDeviceCodeList( @PathVariable("deviceCode")String deviceCode) {
        try {
            List<Map<String, String>> deviceCodeList = rulesService.getDeviceCodeList(deviceCode);
            return Response.success(deviceCodeList);
        } catch (Exception e) {
            return Response.failError("查询失败");
        }
    }


    @ApiOperation(value = "获取实时工况历史检查列表")
    @GetMapping("/selectHistoryData")
    public Response selectHistoryData( @RequestParam("deviceCode")String deviceCode,@RequestParam("current")int current,@RequestParam("pageSize")int pageSize) {
        try {
            return Response.success(inspectionHistoryService.selectHistoryData(deviceCode,current,pageSize));
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failError("查询失败");
        }
    }

    @ApiOperation(value = "获取实时工况历史检查列表导出")
    @GetMapping("/historyDataExport")
    public Response pageHistoryData( @RequestParam("startTime")String startTime,@RequestParam("endTime")String endTime) {
        try {
            WorkbookUtil.pageHistoryDataExport(httpServletResponse,inspectionHistoryService,startTime,endTime);
        } catch (Exception e) {
            return Response.failError("导出失败");
        }
        return Response.success();
    }
}
