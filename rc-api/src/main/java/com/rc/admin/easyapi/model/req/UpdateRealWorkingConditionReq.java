package com.rc.admin.easyapi.model.req;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class UpdateRealWorkingConditionReq {
    private String modelId;
    private List<SubUpdate> subUpdates;
    private String updateType;
    private String orsDataCenterId;

    @Data
    public static class SubUpdate{
        private List<Map<String,String>> staticProperties;
        private String thingId;
    }

}
