package com.rc.admin.easyapi.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.easyapi.service.dqmService.OrsBigdataEquipmentBaseInfoAllService;
import com.rc.admin.easyapi.service.dqmService.OrsMonthlyShipmentEquipmentService;
import com.rc.admin.easyapi.util.StringUtil;
import com.rc.admin.easyapi.util.WorkbookUtil;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.DeviceDataRuleMapper;
import com.rc.admin.ors.quality.dao.OrsBigdataEquipmentBaseInfoAllMapper;
import com.rc.admin.ors.quality.excel.DetailDeviceExceptInfoDetailExcel;
import com.rc.admin.ors.quality.excel.DeviceExceptInfoCountExcel;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.OrsBasicDataService;
import com.rc.admin.ors.quality.utils.BusinessConst;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
@ResponseResult
@RequestMapping("/api/v1/report")
@Api(value = "设备报表相关接口")
public class DqmDeviceRelatedController {
    @Resource
    private OrsBigdataEquipmentBaseInfoAllService infoAllService;
    @Resource
    private OrsMonthlyShipmentEquipmentService equipmentService;
    @Resource
    private OrsBigdataEquipmentBaseInfoAllMapper mapper;

    @Resource
    private OrsBasicDataService orsBasicDataService;

    @Resource
    private DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;

    @Resource
    private DeviceDataRuleMapper deviceDataRuleMapper;

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    @ApiOperation(value = "设备数据异常明细")
    @PostMapping("/deviceDataExceptionInfo")
    public Response deviceDataExceptions(@RequestBody DqmDeviceDataExceptionsReq req) {
        try {
//            Page<DqmDeviceDataExceptionsResp> abnormalDetail = infoAllService.getDeviceDataExceptionsInfo(req);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataExceptionsResp> abnormalDetail
                    = deviceDataAbnormalDetailDayMapper.findAbnormalDetail(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(req.getCurrent(), req.getPageSize()), req);
            return Response.success(abnormalDetail);
        } catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }


    @ApiOperation(value = "设备数据异常明细导出")
    @PostMapping("/deviceDataExceptionInfoExport")
    public void deviceDataExceptionsExport(@RequestBody DqmDeviceDataExceptionsReq dataExceptionsReq,HttpServletResponse response) {
        // try {
        //     Workbook sheets = WorkbookUtil.deviceDataExceptionInfoExport(dataExceptionsReq, deviceDataAbnormalDetailDayMapper);
        //     EasyPoiUtils.downLoadExcel("数据质量月度报表.xlsx", response, sheets);
        //     response.getOutputStream().close();
        //     return Response.success();
        // } catch (Exception e) {
        //     throw new RuntimeException(e);
        // }
        //期间所有 检查时间范围如果超过3天限制导出
        /*if ("all".equals(dataExceptionsReq.getDataScope())
                && StringUtils.isNotBlank(dataExceptionsReq.getStartTime())
                && StringUtils.isNotBlank(dataExceptionsReq.getEndTime())
                && DateUtil.betweenDay(DateUtil.parseDate(dataExceptionsReq.getStartTime()),
                DateUtil.parseDate(dataExceptionsReq.getEndTime()), false) + 1 > 3
                && StringUtils.isBlank(dataExceptionsReq.getDeviceCode())) {
            throw new EasyException("数据范围为期间所有，如果超过3天范围的请指定设备编号进行设备数据异常明细导出");
        }*/

        // if(StringUtils.isBlank(dataExceptionsReq.getDeviceCode())){
        //     throw new EasyException("只支持指定设备编号、最多500台、设备数据异常明细导出");
        // }

        log.info("设备数据异常明细导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataExceptionsResp> countPage =
                deviceDataAbnormalDetailDayMapper.findAbnormalDetail(
                        new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 1),
                        dataExceptionsReq
                );
        int totalCount = (int) countPage.getTotal();
        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }

        // 新加得规则：仅限制数据量＞5w时不支持导出
        if(totalCount > 50000){
            throw new EasyException("列表数据量过大已经超过5w，请通过筛选减少列表数据进行导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("设备数据异常明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 10 的线程池
            // ExecutorService executorService = Executors.newFixedThreadPool(3);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "设备数据异常明细" + (sheetIndex + 1))
                            .head(DqmDeviceDataExceptionsResp.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        long writeIndexTime = System.currentTimeMillis();
                        // 计算全局分页参数
                        int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;
                        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataExceptionsResp> page
                                = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(currentPage, writeDataRows);
                        List<DqmDeviceDataExceptionsResp> data = deviceDataAbnormalDetailDayMapper
                                .findAbnormalDetail(page, dataExceptionsReq)
                                .getRecords();
                        log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                        synchronized (excelWriter) {
                            excelWriter.write(data, writeSheet);
                        }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     log.info("关闭线程池");
                //     executorService.shutdown();
                // }
            }
            log.info("设备数据异常明细导出结束");
            log.info("设备数据异常明细导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出设备数据异常明细失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }

    }

    @ApiOperation(value = "设备历史数据异常明细")
    @PostMapping("/historyDeviceDataExceptionInfo")
    public Response historyDeviceDataExceptionInfo(@RequestBody DqmHistoryDeviceDataExceptionsReq dataExceptionsReq) {
        try {
            Page<DqmHistoryDeviceDataExceptionResp> dataExceptionsInfo = infoAllService.getHistoryDeviceDataExceptionsInfo1(dataExceptionsReq);
            return Response.success(dataExceptionsInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failError(e.getMessage());
        }
    }

    // @ApiOperation(value = "设备历史异常统计导出")
    // @PostMapping("/historyDeviceDataExceptionInfoExport")
    // public void historyDeviceDataExceptionInfoExport(@RequestBody DqmHistoryDeviceDataExceptionsReq dataExceptionsReq,
    //                                                  HttpServletResponse response) {
    //     log.info("设备历史异常统计导出开始");
    //     long startTime = System.currentTimeMillis();
    //     // 每个Sheet最大行数
    //     int sheetDataRows = 1000000;
    //     // 每次写入的最大行数
    //     int writeDataRows = 200000;
    //
    //     String[] fileNames = {"历史异常统计","设备历史异常明细"};
    //
    //     // ExecutorService executorService = Executors.newFixedThreadPool(2);
    //     try {
    //         //并行处理
    //         CompletableFuture<ByteArrayOutputStream> future1 = CompletableFuture.supplyAsync(() -> {
    //             long startTime1 = System.currentTimeMillis();
    //             ByteArrayOutputStream out = handleExceptionsDataExport(dataExceptionsReq, sheetDataRows, writeDataRows, fileNames[0]);
    //             log.info("handleExceptionsDataExport处理完成耗时：{}", System.currentTimeMillis() - startTime1);
    //             return out;
    //         },asyncExecutor);
    //
    //         CompletableFuture<ByteArrayOutputStream> future2 = CompletableFuture.supplyAsync(() -> {
    //             long startTime2 = System.currentTimeMillis();
    //             ByteArrayOutputStream out = handleExceptionsDetailDataExport(dataExceptionsReq, sheetDataRows, writeDataRows, fileNames[1]);
    //             log.info("handleExceptionsDetailDataExport处理完成耗时：{}", System.currentTimeMillis() - startTime2);
    //             return out;
    //         },asyncExecutor);
    //
    //
    //         // 等待所有任务完成
    //         CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1,  future2);
    //
    //         // 获取结果
    //         try {
    //             allFutures.get();  // 阻塞直到所有任务完成
    //             ByteArrayOutputStream outputStream1 = future1.get();
    //             ByteArrayOutputStream outputStream2 = future2.get();
    //
    //             //导出
    //             log.info("开始压缩Excel到zip中......");
    //             long downLoadBeginTime = System.currentTimeMillis();
    //             EasyPoiUtils.downloadByteArrayOutputStreamAsZip(response,"设备历史数据异常数据",fileNames,outputStream1,outputStream2);
    //             log.info("压缩Excel耗时：{}",System.currentTimeMillis() - downLoadBeginTime);
    //         } catch (Exception e) {
    //             log.error("设备历史异常统计导出失败：{}", e.getMessage());
    //             throw new EasyException("导出失败，请联系管理员");
    //         }
    //
    //         log.info("设备历史异常统计导出结束");
    //         log.info("设备历史异常统计导出总耗时：{}", System.currentTimeMillis() - startTime);
    //     } finally {
    //         // if(executorService!=null){
    //         //     executorService.shutdown();
    //         // }
    //     }
    // }

    @ApiOperation(value = "设备历史异常统计导出")
    @PostMapping("/historyDeviceDataExceptionInfoExport")
    public void historyDeviceDataExceptionInfoExport(@RequestBody DqmHistoryDeviceDataExceptionsReq dataExceptionsReq,
                                                     HttpServletResponse response) {

        AtomicInteger ai = new AtomicInteger(1);

        // 不支持多行导出
        /*if (StringUtils.isNotBlank(dataExceptionsReq.getRow()) && !"1".equals(dataExceptionsReq.getRow())) {
            throw new EasyException("由于数据量过大，不支持多行场景导出");
        }*/

        log.info("设备历史异常统计导出开始");
        long startTime = System.currentTimeMillis();
        // 提前查询总记录数
        dataExceptionsReq.setPageSize(1);
        dataExceptionsReq.setCurrent(1);
        Page<DqmHistoryDeviceDataExceptionResp> exceptions = infoAllService.getHistoryDeviceDataExceptionsInfo1(dataExceptionsReq);
        int totalCount = (int) exceptions.getTotal();
        log.info("历史异常统计总记录数：{}", totalCount);

        if (totalCount == 0) {
            //return Response.failError("无数据可导出");
            throw new EasyException("无数据可导出");
        }

        // 新加得规则：仅限制数据量＞5w时不支持导出
        if(totalCount > 50000){
            throw new EasyException("列表数据量过大已经超过5w，请通过筛选减少列表数据进行导出");
        }

        // 设置响应头，必须在获取OutputStream之前
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("历史异常统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        List<String> dynamicKeyList = getDynamicKeyList(dataExceptionsReq);

        try (OutputStream outputStream = response.getOutputStream()) {
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 50000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个固定大小为 6 的线程池
            //ExecutorService executorService = Executors.newFixedThreadPool(2);
            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "历史异常统计" + (sheetIndex + 1))
                            .head(DeviceExceptInfoCountExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .includeColumnFieldNames(dynamicKeyList)
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            dataExceptionsReq.setPageSize(writeDataRows);
                            dataExceptionsReq.setCurrent(currentPage);
                            Page<DqmHistoryDeviceDataExceptionResp> historyDeviceDataExceptionsInfo = infoAllService.getHistoryDeviceDataExceptionsInfo1(dataExceptionsReq);
                            List<DqmHistoryDeviceDataExceptionResp> data =
                                    historyDeviceDataExceptionsInfo.getRecords();
                            List<DeviceExceptInfoCountExcel> countExcels = new ArrayList<>();
                            if(CollectionUtil.isNotEmpty(data)) {
                                // 构建导出数据
                                buildDeviceExceptInfoCountExcelData(data, countExcels, ai);

                                log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                                synchronized (excelWriter) {
                                    excelWriter.write(countExcels, writeSheet);
                                }
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
                // if(executorService!=null){
                //     executorService.shutdown();
                // }
            }
            log.info("历史异常统计导出结束");
            log.info("历史异常统计导出耗时：{}", System.currentTimeMillis() - startTime);
            //return Response.success();
        } catch (Exception e) {
            log.error("导出历史异常统计失败", e);
            //return Response.failError("导出失败，请联系管理员");
            throw new EasyException("导出失败，请联系管理员");
        }
    }




    private List<String> getDynamicKeyList(DqmHistoryDeviceDataExceptionsReq dataExceptionsReq){
        List<String> dynamicKeyList = CollUtil.newArrayList();
        dynamicKeyList.add("sybbh");
        dynamicKeyList.add("country");
        dynamicKeyList.add("modelId");
        dynamicKeyList.add("dataCenterId");
        dynamicKeyList.add("deviceName");
        dynamicKeyList.add("deviceCode");
        dynamicKeyList.add("startStatDate");
        dynamicKeyList.add("endStatDate");
        dynamicKeyList.add("deviceLocation");
        dynamicKeyList.add("handle");
        dynamicKeyList.add("fwVersion");
        dynamicKeyList.add("hwVersion");
        dynamicKeyList.add("authToken");
        dynamicKeyList.add("modelName");
        dynamicKeyList.add("zehdSpartdesc");
        mapper.getModelList(dataExceptionsReq).stream()
                .filter(Objects::nonNull)
                .map(model -> "abnormal" + model)
                .forEach(dynamicKeyList::add);

        // 工作时间异常
        if(dynamicKeyList.contains("abnormal8102")){
            dynamicKeyList.add("workingTimeExceptCount");
        }
        // 发动机工作时间
        if(dynamicKeyList.contains("abnormal8105")){
            dynamicKeyList.add("engineWorktimeExceptCount");
        }
        // 总油耗
        if(dynamicKeyList.contains("abnormal8201")){
            dynamicKeyList.add("totalFuelConsumptionExceptCount");
        }
        // 方量
        if(dynamicKeyList.contains("abnormal8401")){
            dynamicKeyList.add("pumpingVolumeExceptCount");
        }
        // 行驶里程
        if(dynamicKeyList.contains("abnormal8403")){
            dynamicKeyList.add("drivingMileageExceptCount");
        }
        // 设备位置
        if(dynamicKeyList.contains("abnormal8501")){
            dynamicKeyList.add("deviceLocationExceptCount");
        }
        // 设备状态
        if(dynamicKeyList.contains("abnormal8503")){
            dynamicKeyList.add("deviceStateExceptCount");
        }
        // 行驶速度
        if(dynamicKeyList.contains("abnormal8510")){
            dynamicKeyList.add("travelSpeedExceptCount");
        }
        // 油位
        if(dynamicKeyList.contains("abnormal8506")){
            dynamicKeyList.add("fuelLevelExceptCount");
        }
        // 发动机转速
        if(dynamicKeyList.contains("abnormal8507")){
            dynamicKeyList.add("engineSpeedExceptCount");
        }
        // 发动机水温
        if(dynamicKeyList.contains("abnormal8508")){
            dynamicKeyList.add("engineTemperatureExceptCount");
        }
        // 当前电量
        if(dynamicKeyList.contains("abnormal8509")){
            dynamicKeyList.add("stateOfChargeExceptCount");
        }
        // 总电耗
        if(dynamicKeyList.contains("abnormal8511")){
            dynamicKeyList.add("totalElectricExceptCount");
        }
        //怠速油耗
        if(dynamicKeyList.contains("abnormal8205")){
            dynamicKeyList.add("totalIdleFuelConsumptionExceptCount");
        }
        //怠速时长
        if(dynamicKeyList.contains("abnormal8106")){
            dynamicKeyList.add("totalIdleTimeExceptCount");
        }
        //档位
        if(dynamicKeyList.contains("abnormal8602")){
            dynamicKeyList.add("gearExceptCount");
        }
        //左行走工时
        if(dynamicKeyList.contains("abnormal8108")){
            dynamicKeyList.add("totalTimeLeftMovingExceptCount");
        }
        if(dynamicKeyList.contains("abnormal8107")){
            dynamicKeyList.add("totalTimeRightMovingExceptCount");
        }
        //机油压力
        if(dynamicKeyList.contains("abnormal8603")){
            dynamicKeyList.add("oilPressureExceptCount");
        }
        //泵吸收功率
        if(dynamicKeyList.contains("abnormal8604")){
            dynamicKeyList.add("pumpTotalAbsorbedTorqueExceptCount");
        }
        //电机转速
        if(dynamicKeyList.contains("abnormal8605")){
            dynamicKeyList.add("pumpMotorRotateSpeedCount");
        }
        if(dynamicKeyList.contains("abnormal8606")){
            dynamicKeyList.add("chargingStatusCount");
        }
        if(dynamicKeyList.contains("abnormal8607")){
            dynamicKeyList.add("chargeTimeRemainCount");
        }
        if(dynamicKeyList.contains("abnormal8608")){
            dynamicKeyList.add("singleChargeCapacityCount");
        }
        if(dynamicKeyList.contains("abnormal8609")){
            dynamicKeyList.add("dayPowerConsumptionCount");
        }
        if(dynamicKeyList.contains("abnormal8610")){
            dynamicKeyList.add("actionCode");
        }
        if(dynamicKeyList.contains("abnormal8611")){
            dynamicKeyList.add("totalTimeRotation");
        }
        if(dynamicKeyList.contains("abnormal8305")){
            dynamicKeyList.add("totalNoActionPowerConsumption");
        }

        if(dynamicKeyList.contains("abnormal8701")){
            dynamicKeyList.add("idleTimeIdleFuel");
        }
        if(dynamicKeyList.contains("abnormal8702")){
            dynamicKeyList.add("workTimeFuel");
        }
        if(dynamicKeyList.contains("abnormal8703")){
            dynamicKeyList.add("idelFuelFuel");
        }
        if(dynamicKeyList.contains("abnormal8704")){
            dynamicKeyList.add("idelTimeWorkTime");
        }
        if(dynamicKeyList.contains("abnormal8705")){
            dynamicKeyList.add("mileageSpeed");
        }
        if(dynamicKeyList.contains("abnormal8706")){
            dynamicKeyList.add("mileageLocation");
        }
        if(dynamicKeyList.contains("abnormal8707")){
            dynamicKeyList.add("engineTimeFuel");
        }

        return dynamicKeyList;
    }

    private void buildDeviceExceptInfoCountExcelData(List<DqmHistoryDeviceDataExceptionResp> excels,
                                                     List<DeviceExceptInfoCountExcel> countExcels,
                                                     AtomicInteger ai) {
        long startTime = System.currentTimeMillis();
        DeviceExceptInfoCountExcel countExcel;
        for (DqmHistoryDeviceDataExceptionResp record : excels) {
            countExcel = new DeviceExceptInfoCountExcel();
            countExcel.setSort(ai.getAndIncrement());
            countExcel.setDivision(convertNullData(record.getSybbh()));
            countExcel.setProductGroup(convertNullData(record.getZehdSpartdesc()));
            countExcel.setDataCenter(convertNullData(BusinessConst.dataCenterMap.get(record.getDataCenterId())));
            countExcel.setDeviceName(convertNullData(record.getDeviceName()));
            countExcel.setDeviceCode(convertNullData(record.getDeviceCode()));
            countExcel.setStartTime(convertNullData(record.getStartStatDate()));
            countExcel.setEndTime(convertNullData(record.getEndStatDate()));
            countExcel.setStartStatDate(convertNullData(record.getStartStatDate()));
            countExcel.setEndStatDate(convertNullData(record.getEndStatDate()));
            countExcel.setDeviceLocation(convertNullData(record.getDeviceLocation()));
            countExcel.setFwVersion(convertNullData(record.getFwVersion()));
            countExcel.setHwVersion(convertNullData(record.getHwVersion()));
            countExcel.setAuthToken(convertNullData(record.getAuthToken()));
            countExcel.setModelId(convertNullData(record.getModelId()));
            countExcel.setModelName(convertNullData(record.getModelName()));
            countExcel.setCountry(convertNullData(record.getCountry()));

            // 找到对应设备异常数据
            //List<DqmDeviceDataExceptionsResp> exc = resps.stream().filter(x -> StringUtils.isNotBlank(x.getDeviceCode()) &&  x.getDeviceCode().equals(record.getDeviceName())).collect(Collectors.toList());

            // 工作时间异常
            if (!record.getAbnormal8102().isEmpty()) {
                String collect = record.getAbnormal8102().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setWorkingTimeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8102()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8102".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8102");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 发动机工作时间
            if (!record.getAbnormal8105().isEmpty()) {
                String collect = record.getAbnormal8105().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineWorktimeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8105()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8105".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8105");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 总油耗
            if (!record.getAbnormal8201().isEmpty()) {
                String collect = record.getAbnormal8201().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalFuelConsumptionExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8201()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8201".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8201");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 方量
            if (!record.getAbnormal8401().isEmpty()) {
                String collect = record.getAbnormal8401().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setPumpingVolumeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8401()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8401".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8401");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 行驶里程
            if (!record.getAbnormal8403().isEmpty()) {
                String collect = record.getAbnormal8403().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDrivingMileageExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8403()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8403".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8403");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 设备位置
            if (!record.getAbnormal8501().isEmpty()) {
                String collect = record.getAbnormal8501().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDeviceLocationExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8501()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8501".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8501");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 设备状态
            if (!record.getAbnormal8503().isEmpty()) {
                String collect = record.getAbnormal8503().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDeviceStateExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8503()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8503".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8503");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 行驶速度
            if (!record.getAbnormal8510().isEmpty()) {
                String collect = record.getAbnormal8510().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTravelSpeedExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8510()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8510".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8510");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 油位
            if (!record.getAbnormal8506().isEmpty()) {
                String collect = record.getAbnormal8506().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setFuelLevelExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8506()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8506".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8506");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 发动机转速
            if (!record.getAbnormal8507().isEmpty()) {
                String collect = record.getAbnormal8507().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineSpeedExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8507()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8507".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8507");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 发动机水温
            if (!record.getAbnormal8508().isEmpty()) {
                String collect = record.getAbnormal8508().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineTemperatureExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8508()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8508".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8508");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 当前电量
            if (!record.getAbnormal8509().isEmpty()) {
                String collect = record.getAbnormal8509().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setStateOfChargeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8509()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8509".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8509");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 总电耗
            if (!record.getAbnormal8511().isEmpty()) {
                String collect = record.getAbnormal8511().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8511()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8511".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8511");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
//新增属性
            //怠速油耗
            if (!record.getAbnormal8205().isEmpty()) {
                String collect = record.getAbnormal8205().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalIdleFuelConsumptionExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8205()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8205".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8205");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //怠速时长
            if (!record.getAbnormal8106().isEmpty()) {
                String collect = record.getAbnormal8106().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalIdleTimeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8106()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8106".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8106");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //档位
            if (!record.getAbnormal8602().isEmpty()) {
                String collect = record.getAbnormal8602().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setGearExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8602()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8602".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8602");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //左行走工时
            if (!record.getAbnormal8108().isEmpty()) {
                String collect = record.getAbnormal8108().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalTimeLeftMovingExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8108()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8108".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8108");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //右行走工时
            if (!record.getAbnormal8107().isEmpty()) {
                String collect = record.getAbnormal8107().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalTimeRightMovingExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8107()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8107".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8107");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //机油压力
            if (!record.getAbnormal8603().isEmpty()) {
                String collect = record.getAbnormal8603().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setOilPressureExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8603()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8603".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8603");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //泵吸收功率
            if (!record.getAbnormal8604().isEmpty()) {
                String collect = record.getAbnormal8604().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setPumpTotalAbsorbedTorqueExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8604()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8604".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8604");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
//2024-12-30
            //电机转速
            if (!record.getAbnormal8605().isEmpty()) {
                String collect = record.getAbnormal8605().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setPumpMotorRotateSpeedCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8605()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8605".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8605");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //充电状态
            if (!record.getAbnormal8606().isEmpty()) {
                String collect = record.getAbnormal8606().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setChargingStatusCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8606()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8606".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8606");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //充电剩余时间
            if (!record.getAbnormal8607().isEmpty()) {
                String collect = record.getAbnormal8607().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setChargeTimeRemainCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8607()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8607".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8607");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //单次充电电量
            if (!record.getAbnormal8608().isEmpty()) {
                String collect = record.getAbnormal8608().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setSingleChargeCapacityCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8608()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8608".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8608");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //当日电耗
            if (!record.getAbnormal8609().isEmpty()) {
                String collect = record.getAbnormal8609().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDayPowerConsumptionCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8609()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8609");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            //动作编码
            if (!record.getAbnormal8610().isEmpty()) {
                String collect = record.getAbnormal8610().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setActionCode(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8610()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8610");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            //回转时间
            if (!record.getAbnormal8611().isEmpty()) {
                String collect = record.getAbnormal8611().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalTimeRotation(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8611()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8611");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            //怠速电耗
            if (!record.getAbnormal8305().isEmpty()) {
                String collect = record.getAbnormal8305().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalNoActionPowerConsumption(collect);
            }

            //怠速时长&怠速油耗
            if (!record.getAbnormal8701().isEmpty()) {
                String collect = record.getAbnormal8701().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setIdleTimeIdleFuel(collect);
            }

            //工作时间&总油耗
            if (!record.getAbnormal8702().isEmpty()) {
                String collect = record.getAbnormal8702().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setWorkTimeFuel(collect);
            }

            //怠速油耗&总油耗
            if (!record.getAbnormal8703().isEmpty()) {
                String collect = record.getAbnormal8703().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setIdelFuelFuel(collect);
            }

            //怠速时长&工作时长
            if (!record.getAbnormal8704().isEmpty()) {
                String collect = record.getAbnormal8704().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setIdelTimeWorkTime(collect);
            }

            //行驶里程&行驶速度
            if (!record.getAbnormal8705().isEmpty()) {
                String collect = record.getAbnormal8705().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setMileageSpeed(collect);
            }

            //行驶里程&设备位置
            if (!record.getAbnormal8706().isEmpty()) {
                String collect = record.getAbnormal8706().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setMileageLocation(collect);
            }

            //发动机工作时长&总油耗
            if (!record.getAbnormal8707().isEmpty()) {
                String collect = record.getAbnormal8707().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineTimeFuel(collect);
            }

            countExcels.add(countExcel);
        }

        log.info("处理完成耗时：{}", System.currentTimeMillis() - startTime);
    }


    @ApiOperation(value = "异常项查询")
    @GetMapping("/queryAbnormal")
    public Response queryAbnormal(String dictType,String paramCode) {
        try {
            return Response.success(infoAllService.queryAbnormal(dictType,paramCode));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @ApiOperation(value = "检查项查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "divisionCode", value = "事业部code"),
            @ApiImplicitParam(name = "modelId", value = "模型id"),
            @ApiImplicitParam(name = "dictType", value = "检查类型")
    })
    @GetMapping("/queryParamCode")
    public Response queryParamCode(String modelId,String divisionCode,String dictType) {
        try {
            return Response.success(infoAllService.queryParamCode(modelId,divisionCode,dictType));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "剔除类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "divisionCode", value = "事业部code"),
            @ApiImplicitParam(name = "modelId", value = "模型id")
    })
    @GetMapping("/queryExcludeParamCode")
    public Response queryExcludeParamCode(String modelId,String divisionCode) {
        try {
            List<Map<String,String>> list = infoAllService.queryParamCode(modelId,divisionCode,"single_param");
            Map<String,String> map = new HashMap<>();
            map.put("dict_id","8601");
            map.put("dict_desc","整机");
            list.add(map);
            return Response.success(list);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "数据质量月度报表")
    @PostMapping("/getDataQualityReport")
    public Response getDataQualityReport(@RequestBody DataQualityReportReq dataExceptionsReq) {
        try {
            dataExceptionsReq.setStoreCategory("1");
            dataExceptionsReq.setMark("mark");
            if(StringUtils.isNotBlank(dataExceptionsReq.getSwitchSign()) && "1".equals(dataExceptionsReq.getSwitchSign())){
                List<DeviceCountModel> dataExceptionsInfo
                        = orsBasicDataService.getDataQualityReport(dataExceptionsReq);
                if (Objects.isNull(dataExceptionsInfo)) {
                    return Response.failError("查无数据");
                }
                return Response.success(dataExceptionsInfo);
            }else{
                List<DeviceCountModelNew> dataExceptionsInfoNew
                        = orsBasicDataService.getDataQualityReportNew(dataExceptionsReq);
                if (Objects.isNull(dataExceptionsInfoNew)) {
                    return Response.failError("查无数据");
                }
                return Response.success(dataExceptionsInfoNew);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "数据质量月度报表新的")
    @PostMapping("/getDataQualityReportNew")
    public Response getDataQualityReportNew(@RequestBody DataQualityReportReq dataExceptionsReq) {
        try {
            dataExceptionsReq.setStoreCategory("1");
            dataExceptionsReq.setMark("mark");
            List<DeviceCountModelNew> dataExceptionsInfo
                    = orsBasicDataService.getDataQualityReportNew(dataExceptionsReq);
            if (Objects.isNull(dataExceptionsInfo)) {
                return Response.failError("查无数据");
            }
            return Response.success(dataExceptionsInfo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "数据质量月度报表导出")
    @PostMapping("/getDataQualityReportExport")
    public Response getDataQualityReportExport(@RequestBody DataQualityReportReq dataExceptionsReq, HttpServletResponse response) throws IOException {
        try {
            List<DeviceCountModelNew> dataExceptionsInfo = CollUtil.newArrayList();
            dataExceptionsReq.setStoreCategory("1");
            dataExceptionsReq.setMark("mark");
            if(StringUtils.isNotBlank(dataExceptionsReq.getSwitchSign()) && "1".equals(dataExceptionsReq.getSwitchSign())){
                List<DeviceCountModel> dataQualityReport = orsBasicDataService.getDataQualityReport(dataExceptionsReq);
                for (DeviceCountModel item : dataQualityReport) {
                    DeviceCountModelNew deviceCountModelNew = new DeviceCountModelNew();
                    BeanUtils.copyProperties(item, deviceCountModelNew);
                    dataExceptionsInfo.add(deviceCountModelNew);
                }
            }else{
                dataExceptionsInfo
                        = orsBasicDataService.getDataQualityReportNew(dataExceptionsReq);
            }
            Workbook workBook = WorkbookUtil.exportSaleDayReportNew(dataExceptionsInfo,dataExceptionsReq.getSybbh());
            if (workBook != null) {
                EasyPoiUtils.downLoadExcel("数据质量月度报表.xlsx", response, workBook);
                response.getOutputStream().close();
                return Response.success();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Response.failError("查无数据。");
    }

    @ApiOperation(value = "海外月度发货设备管理导入")
    @PostMapping("/importDevice")
    public Response importDevice(@RequestParam("file") MultipartFile file,@RequestParam("time") String time, HttpServletResponse response) throws Exception {
        try {
            OrsMonthlyShipmentEquipmentReq orsMonthlyShipmentEquipmentReq = new OrsMonthlyShipmentEquipmentReq();
            orsMonthlyShipmentEquipmentReq.setFile(file);
            orsMonthlyShipmentEquipmentReq.setTime(time);
            List<OrsMonthlyShipmentEquipmentResp> orsMonthlyShipmentEquipmentResps = equipmentService.importEquipment(orsMonthlyShipmentEquipmentReq);
            if (!CollectionUtils.isEmpty(orsMonthlyShipmentEquipmentResps)) {

                    ExportParams exportParams = new ExportParams("设备月度导入错误信息", "设备月度导入错误信息");
                    exportParams.setCreateHeadRows(true);
                    Workbook workbook = ExcelExportUtil.exportExcel(exportParams, OrsMonthlyShipmentEquipmentResp.class, orsMonthlyShipmentEquipmentResps);
                    if (workbook != null) {
                        EasyPoiUtils.downLoadExcel("设备月度导入错误信息.xlsx", response, workbook);
                        response.getOutputStream().close();
                    }
//                if(orsMonthlyShipmentEquipmentResps.get(0).getDeviceName().equals("文件格式异常")){
//                    return Response.failError("导入失败，请查看自动下载的导入失败明细文件");
//                }
                    return Response.failError("导入失败，文件格式错误");
//                }
            }
            return Response.success("导入成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Response.failError("导入失败，文件格式错误");
    }
    @ApiOperation(value = "海外月度发货设备管理查询")
    @PostMapping("/getDeviceInfo")
    public Response getDeviceInfo(@RequestBody OrsMonthlyShipmentEquipmentReq req) {
        try {
            Page<OrsMonthlyShipmentEquipmentResp> deviceInfo = equipmentService.getDeviceInfo(req);
            return Response.success(deviceInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failError(e.getMessage());
        }
    }

    @ApiOperation(value = "海外月度发货设备管理查询筛选条件")
    @GetMapping("/getDeviceParamList")
    public Response getDeviceParamList(@RequestParam("time")String time,
                                       @RequestParam("paramType")String paramType,
                                       @RequestParam(value = "paramValue", required = false)String paramValue) {
        try {
            List<String> list = equipmentService.getDeviceParamList(time,paramType, paramValue);
            return Response.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failError(e.getMessage());
        }
    }

    @ApiOperation(value = "海外月度发货设备管理删除本期")
    @PostMapping("/deleteDeviceInfo")
    public Response deleteDeviceInfo(@RequestBody OrsMonthlyShipmentEquipmentReq req) {
        try {
             equipmentService.deleteDeviceInfo(req);
            return Response.success();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failError(e.getMessage());
        }

    }

    @ApiOperation(value = "海外月度发货设备导入模板下载")
    @GetMapping("/templateDownload")
    public Response templateDownload(HttpServletResponse response) throws IOException {
        try {
                Workbook workBook = WorkbookUtil.exportImportDevice(null);
                EasyPoiUtils.downLoadExcel("月度发货设备导入模板.xlsx", response, workBook);
            response.getOutputStream().close();
            return Response.success("下载成功");
        } catch (Exception e){
        e.printStackTrace();
    }
        return Response.failError("下载异常");

    }


    @ApiOperation(value = "异常明细表抽象为天表接口")
    @GetMapping("/syncAbnormalDetails2Day")
    public Response syncAbnormalDetails2Day(String date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(StringUtil.isEmpty(date)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, -1);
            date = dateFormat.format(calendar.getTime());
        }
        Date parseDate = null;
        try {
            parseDate = dateFormat.parse(date);
        } catch (ParseException e) {
            Response.failError("date格式有误，参考格式：yyyy-MM-dd");
        }
        List<Map<String, String>> list = mapper.queryMergeDetails(parseDate);
        if(list != null && list.size() > 0){
            int size = 200;
            int page = list.size()%size==0?list.size()/size:list.size()/size+1;
            for (int i = 0; i < page; i++) {
                List<Map<String, String>> mapList = list.stream().skip(i * size).limit(size).collect(Collectors.toList());
                mapper.syncDetailsToDay(mapList);
            }
            infoAllService.statDeviceHistoryException(parseDate);
        }

        return Response.success();
    }

    @ApiOperation(value = "设备异常统计接口")
    @GetMapping("/statDeviceExceptionDay")
    public Response statDeviceExceptionDay(String date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(StringUtil.isEmpty(date)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, -1);
            date = dateFormat.format(calendar.getTime());
        }
        Date parseDate = null;
        try {
            parseDate = dateFormat.parse(date);
        } catch (ParseException e) {
            Response.failError("date格式有误，参考格式：yyyy-MM-dd");
        }
        infoAllService.statDeviceHistoryException(parseDate);

        return Response.success();
    }

//    @ApiOperation(value = "数据质量月度未注册列表")
//    @PostMapping("/getDataQualityReport")
//    public Response getDataQualityReport(@RequestBody DataQualityReportReq dataExceptionsReq) {
//        try {
//            dataExceptionsReq.setMark("mark");
//            Page<DataQualityReportResp> dataExceptionsInfo = infoAllService.getDataQualityReport(dataExceptionsReq);
//            if (Objects.isNull(dataExceptionsInfo)) {
//                return Response.failError("查无数据");
//            }
//            return Response.success(dataExceptionsInfo);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @ApiOperation(value = "数据质量月度异常设备清单")
//    @PostMapping("/getDataQualityReport")
//    public Response getDataQualityReport(@RequestBody DataQualityReportReq dataExceptionsReq) {
//        try {
//            dataExceptionsReq.setMark("mark");
//            Page<DataQualityReportResp> dataExceptionsInfo = infoAllService.getDataQualityReport(dataExceptionsReq);
//            if (Objects.isNull(dataExceptionsInfo)) {
//                return Response.failError("查无数据");
//            }
//            return Response.success(dataExceptionsInfo);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }


    private String convertNullData(String data) {
        if (StringUtils.isBlank(data)) {
            return "";
        }
        return data;
    }

    public static void main(String[] args) {

        String path = "C:\\Users\\<USER>\\Downloads\\设备历史异常统计_1710474091133.xlsx"; // 替换为你的文件路径
        String content ="";
        try {
            content = new String(Files.readAllBytes(Paths.get(path)));
            System.out.println(content);
        } catch (IOException e) {
            e.printStackTrace();
        }

        JSONArray jsonArray = JSONObject.parseArray(content);
        JSONArray data1 = jsonArray.getJSONObject(0).getJSONArray("data");
        List<DeviceExceptInfoCountExcel> deviceExceptInfoCountExcels = data1.toJavaList(DeviceExceptInfoCountExcel.class);

        JSONArray data2 = jsonArray.getJSONObject(0).getJSONArray("data");
        List<DetailDeviceExceptInfoDetailExcel> detailDeviceExceptInfoDetailExcels = data2.toJavaList(DetailDeviceExceptInfoDetailExcel.class);

        List<Map<String, Object>> exportParamList = Lists.newArrayList();

        Map<String, Object> sheetMap = Maps.newHashMap();
        sheetMap.put("title",new ExportParams("异常统计", "历史异常统计"));
        sheetMap.put("entity",DeviceExceptInfoCountExcel.class);
        sheetMap.put("data",deviceExceptInfoCountExcels);
        exportParamList.add(sheetMap);

        sheetMap= Maps.newHashMap();
        sheetMap.put("title",new ExportParams("异常明细", "设备历史异常明细"));
        sheetMap.put("entity",DetailDeviceExceptInfoDetailExcel.class);
        sheetMap.put("data",detailDeviceExceptInfoDetailExcels);
        exportParamList.add(sheetMap);
//        EasyPoiUtils.exportExcel(exportParamList,"设备历史数据异常数据.xlsx",response);
//        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clz, dataList);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParamList, ExcelType.XSSF);
    }

    @ApiOperation(value = "设备数据交叉检查规则查询 ")
    @PostMapping("/deviceDataRuleExceptionInfo")
    public Response deviceDataRuleExceptions(@RequestBody DqmDeviceDataRuleExceptionsReq req) {
        try {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataRuleExceptionsResp> abnormal
                    = deviceDataRuleMapper.findAbnormal(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(req.getCurrent(), req.getPageSize()), req) ;
            return Response.success(abnormal);
        } catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }


    @ApiOperation(value = "设备数据交叉检查规则导出")
    @PostMapping("/deviceDataRuleExceptionInfoExport")
    public Response deviceDataRuleExceptionsExport(@RequestBody DqmDeviceDataRuleExceptionsReq dataExceptionsReq,HttpServletResponse response) {

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataRuleExceptionsResp>
                abnormal = deviceDataRuleMapper.findAbnormal(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 1), dataExceptionsReq);

        int totalCount = (int) abnormal.getTotal();
        if (totalCount == 0) {
            throw new EasyException("无数据可导出");
        }
        // 新加得规则：仅限制数据量＞5w时不支持导出
        if(totalCount > 50000){
            throw new EasyException("列表数据量过大已经超过5w，请通过筛选减少列表数据进行导出");
        }

        try {
            Workbook sheets = WorkbookUtil.deviceDataRuleExceptionInfoExport(dataExceptionsReq, deviceDataRuleMapper);
            EasyPoiUtils.downLoadExcel("规则异常数据统计.xlsx", response, sheets);
            response.getOutputStream().close();
            return Response.success();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "设备数据交叉检查规则明细查询 ")
    @PostMapping("/deviceDataRuleDetailExceptionInfo")
    public Response deviceDataRuleDetailExceptions(@RequestBody DqmDeviceDataRuleDetailExceptionsReq req) {
        try {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataRuleDetailExceptionsResp> abnormalDetail
                    = deviceDataRuleMapper.findAbnormalDetail(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(req.getCurrent(), req.getPageSize()), req);
            return Response.success(abnormalDetail);
        } catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }

    @ApiOperation(value = "设备数据交叉检查规则明细查询导出")
    @PostMapping("/deviceDataRuleDetailExceptionInfoExport")
    public Response deviceDataRuleDetailExceptionsExport(@RequestBody DqmDeviceDataRuleDetailExceptionsReq dataExceptionsReq,HttpServletResponse response) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DqmDeviceDataRuleDetailExceptionsResp> abnormalDetail
                = deviceDataRuleMapper.findAbnormalDetail(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1,1), dataExceptionsReq);

        int totalCount = (int) abnormalDetail.getTotal();
        if (totalCount == 0) {
            throw new EasyException("无数据可导出");
        }
        // 新加得规则：仅限制数据量＞5w时不支持导出
        if(totalCount > 50000){
            throw new EasyException("列表数据量过大已经超过5w，请通过筛选减少列表数据进行导出");
        }
        try {
            Workbook sheets = WorkbookUtil.deviceDataRuleDetailExceptionInfoExport(dataExceptionsReq, deviceDataRuleMapper);
            EasyPoiUtils.downLoadExcel("规则异常数据统计明细.xlsx", response, sheets);
            response.getOutputStream().close();
            return Response.success();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
