package com.rc.admin.easyapi.model.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class IotDeviceInfoResp  {
    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", width = 25, orderNum = "1")
    private String deviceCode;
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称", width = 25, orderNum = "0")
    private String name;
    @ApiModelProperty(value = "子公司名称")
//    @Excel(name = "子公司名称", width = 25, orderNum = "")
    private String childCompanyName;
    @ApiModelProperty(value = "工厂")
    @Excel(name = "工厂", width = 25, orderNum = "3")
    private String factory;
    @ApiModelProperty(value = "工作中心")
    @Excel(name = "工作中心", width = 25, orderNum = "4")
    private String workCenter;
    @ApiModelProperty(value = "班组")
    @Excel(name = "班组", width = 25, orderNum = "5")
    private String workGroup;
    @ApiModelProperty(value = "设备协议名称")
    @Excel(name = "设备协议名称", width = 25, orderNum = "6")
    private String protocolType;
    @ApiModelProperty(value = "通讯参数")
    @Excel(name = "通讯参数", width = 25, orderNum = "7")
    private String paraItemValue;
    @ApiModelProperty(value = "站号")
//    @Excel(name = "站号", width = 25, orderNum = "")
    private String stationNumber;
    @ApiModelProperty(value = "扫描周期")
//    @Excel(name = "扫描周期", width = 25, orderNum = "")
    private String scanIntervalTime;
    @ApiModelProperty(value = "链接超时")
//    @Excel(name = "链接超时", width = 25, orderNum = "")
    private String conTimeout;
    @ApiModelProperty(value = "重连延时")
//    @Excel(name = "重连延时", width = 25, orderNum = "")
    private String reconDelay;
    @ApiModelProperty(value = "自定义参数")
//    @Excel(name = "自定义参数", width = 25, orderNum = "")
    private String customerParam;
    @ApiModelProperty(value = "描述")
    @Excel(name = "描述", width = 25, orderNum = "12")
    private String description;
    @ApiModelProperty(value = "高频采集")
//    @Excel(name = "高频采集", width = 25, orderNum = "")
    private String collectionType;
    @ApiModelProperty(value = "点位名称")
    @Excel(name = "点位名称", width = 25, orderNum = "8")
    private String destAddressName;
    @ApiModelProperty(value = "触发方式")
    @Excel(name = "触发方式", width = 25, orderNum = "10")
    private String eventMode;
    @ApiModelProperty(value = "表达式")
    @Excel(name = "表达式", width = 25, orderNum = "11")
    private String eventCondition;
    @ApiModelProperty(value = "点位运算")
    @Excel(name = "点位运算", width = 25, orderNum = "9")
    private String express;
    @ApiModelProperty(value = "区域")
    @Excel(name = "区域", width = 25, orderNum = "2")
    private String region;
    @ApiModelProperty(value = "标红点位")
    private List<String> destAddressNameList;
}
