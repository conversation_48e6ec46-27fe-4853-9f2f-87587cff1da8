package com.rc.admin.easyapi.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IotCustomizePoint {
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    @ApiModelProperty(value = "点位名称")
    private String destAddressName;
    @ApiModelProperty(value = "值类型")
    private String type;
    @ApiModelProperty(value = "换算类型")
    private String conversionType;
    @ApiModelProperty(value = "表达式")
    private String expression;
    @ApiModelProperty(value = "采集数据最小值")
    private String gatherMin;
    @ApiModelProperty(value = "采集数据最大值")
    private String gatherMax;
    @ApiModelProperty(value = "转换数据最小值")
    private String shiftMin;
    @ApiModelProperty(value = "转换数据最大值")
    private String shiftMax;
    @ApiModelProperty(value = "精度类型")
    private String precisionType;
    @ApiModelProperty(value = "小数位数")
    private String decimalPlace;
    @ApiModelProperty(value = "指数")
    private String exponent;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "来源")
    private String source;
}
