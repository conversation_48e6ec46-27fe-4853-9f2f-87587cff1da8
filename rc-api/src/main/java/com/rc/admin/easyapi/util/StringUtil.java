package com.rc.admin.easyapi.util;

import com.rc.admin.common.core.constant.CommonConst;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

public class StringUtil {
    public static boolean isEmpty(@Nullable Object str) {
        return str == null || "".equals(str);
    }

    public static List<String> getSplitList(String str){
        List<String> splitList = new ArrayList<>();
        String[] split = str.split(CommonConst.SPLIT);

        for (String s : split) {
            splitList.add(s);
        }
        return splitList;
    }
}
