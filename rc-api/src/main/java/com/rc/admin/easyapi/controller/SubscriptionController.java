package com.rc.admin.easyapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.easyapi.entity.Subscription;
import com.rc.admin.easyapi.model.req.SubscriptionReq;
import com.rc.admin.easyapi.service.SubscriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@RestController
@RequestMapping("/api/rc/iot/subscriptions")
@Api(tags = "订阅信息管理")
public class SubscriptionController {
    @Resource
    private SubscriptionService subscriptionService;
    /**
     * 获取所有订阅信息
     * 该方法用于获取系统中所有的订阅信息，以列表的形式返回
     * 主要用于需要展示或处理所有订阅信息的场景
     *
     * @return 订阅信息列表，包含所有订阅详情
     */
    @GetMapping
    @ApiOperation("获取所有订阅信息")
    public Response getAllSubscriptions() {
        return Response.success(subscriptionService.list());
    }
    /**
     * 根据账户ID获取订阅信息
     * 此方法使用GET请求来获取指定账户ID的订阅信息它首先从路径中提取账户ID，
     * 然后调用订阅服务的getById方法来获取订阅对象如果找到订阅信息，则返回HTTP状态码200（OK），
     * 并附带订阅信息；如果未找到，则返回HTTP状态码404（Not Found）
     *
     * @param subscription 查询实例
     * @return ResponseEntity<Subscription> 返回包含订阅信息的响应实体，
     *         或在未找到时返回404 Not Found
     */
    @PostMapping("/getSubscriptionPageInfo")
    @ApiOperation("根据账户ID获取订阅信息")
    public Response getSubscription(@RequestBody SubscriptionReq subscription) {
        return Response.success(subscriptionService.getSubscription(subscription));
    }
    /**
     * 创建订阅信息
     * 该方法通过接收一个Subscription对象来创建新的订阅信息
     * 使用@PostMapping注解表明该方法处理POST请求，通常用于创建新资源
     *
     * @param subscription 订阅对象，包含需要创建订阅的信息
     * @return 返回创建的订阅对象
     */
    @PostMapping
    @ApiOperation("创建新的订阅信息")
    public Response createSubscription(@RequestBody Subscription subscription) {
        subscriptionService.save(subscription);
        return Response.success();
    }
    /**
     * 更新用户的订阅信息
     * 此方法通过账户标识接收更新请求，并根据提供的详细订阅信息来更新订阅
     *
     * @param account 账户标识，用于定位要更新的订阅
     * @param subscriptionDetails 包含更新信息的订阅对象，包括启用状态、通知渠道、订阅部门和订阅周期
     * @return 如果订阅存在并成功更新，则返回更新后的订阅对象；如果找不到订阅，则返回404未找到
     */
    @PutMapping("/{account}")
    @ApiOperation("更新订阅信息")
    public Response updateSubscription(@PathVariable String account, @RequestBody Subscription subscriptionDetails) {
        QueryWrapper<Subscription> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account", account);
        //queryWrapper.eq(StringUtils.isNotBlank(subscriptionDetails.getDeptId()),"dept_id",subscriptionDetails.getDeptId());
        Subscription subscription = subscriptionService.getOne(queryWrapper);
        Boolean sign = false;
        if(ObjectUtils.isEmpty(subscription)){
            subscription =  new Subscription();
            sign = true;
        }
        subscription.setAccount(account);
        subscription.setIsEnabled(subscriptionDetails.getIsEnabled());
        subscription.setNotificationChannels(subscriptionDetails.getNotificationChannels());
        String[] split = subscriptionDetails.getSubscribedDepartments().replace("事业部", "").split(",");
        Set<String> uniqueDepartments = new HashSet<>(Arrays.asList(split));
        String[] uniqueDepartmentsArray = uniqueDepartments.toArray(new String[0]);
        subscription.setSubscribedDepartments(String.join(",", uniqueDepartmentsArray));
        subscription.setSubscriptionPeriod(subscriptionDetails.getSubscriptionPeriod());

        if(sign){
            subscription.setCreatedAt(new Timestamp(System.currentTimeMillis()));
            subscriptionService.save(subscription);
        }else{
            subscription.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            subscriptionService.updateById(subscription);
        }
        return Response.success(subscription);
    }
    /**
     * 删除指定账户的订阅信息
     *
     * @param account 要删除订阅信息的账户标识
     * @return 如果删除成功，返回204 NO_CONTENT，表示成功但响应体为空；
     *         如果删除失败（例如账户不存在），返回404 NOT_FOUND，表示未找到指定的订阅信息
     */
    @DeleteMapping("/{account}")
    @ApiOperation("删除订阅信息")
    public Response deleteSubscription(@PathVariable String account) {
        if (subscriptionService.removeById(account)) {
            return Response.success();
        } else {
            return Response.success();
        }
    }
    /**
     * 获取部门列表
     * 该接口用于获取系统中的部门列表，以便进行数据订阅和管理
     * @return 包含部门列表的Response对象
     */
    @GetMapping("/getDivisionList")
    @ApiOperation("获取事业部")
    public Response getDivisionList() {
            return Response.success(subscriptionService.getDivisionList());
    }
}
