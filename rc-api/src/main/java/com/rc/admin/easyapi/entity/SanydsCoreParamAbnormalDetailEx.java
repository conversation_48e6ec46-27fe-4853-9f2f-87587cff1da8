package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 核心工况异常明细表
    */
@Data
@NoArgsConstructor
@TableName(value = "sanyds_core_param_abnormal_detail")
public class SanydsCoreParamAbnormalDetailEx {
    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 模型ID
     */
    @TableField(value = "model_id")
    private String modelId;

    /**
     * 设备编码
     */
    @TableField(value = "device_name")
    private String deviceName;

    /**
     * 统计日期
     */
    @TableField(value = "stat_date")
    private Date statDate;

    /**
     * 属性编码：4位数字，
    前2位表示属性大类，
    后两位表示属性小类。
属性大类定义：
    81，工时
    82，油耗
    83，电耗
    84，施工量
    85，其他自定义
    其他未定义
针对“工时”大类，属性小类定义：
    01，开机时间
    02，工作时间
    03，待机时间
    04，行驶时间
    其他未定义
针对“油耗”大类，属性小类定义：
    01，开机油耗
    02，工作油耗
    03，待机油耗
    04，行驶油耗
    其他未定义
针对“电耗”大类，属性小类定义：
    01，开机电耗
    02，工作电耗
    03，待机电耗
    04，行驶电耗
    其他未定义
针对“施工量”大类，属性小类定义：
    01，泵送方量（方）
    02，生产方量（方）
    03，行驶里程（km）
    04，吊箱数量（个）
    05，吊载次数（次）
    06，施工距离（m）
    其他未定义
【具体以sanyds_dict字典表定义为准】
     */
    @TableField(value = "param_code")
    private Integer paramCode;

    /**
     * 异常编码：4位数字，
    前2位表示异常大类，
    后两位表示异常小类。
异常大类定义：
    90，数据值异常；
    其他未定义
异常小类定义：
    01，属性值0值
    02，属性值超值域
    03，日增量超值域
    04，属性值反方向增长
    05，属性值骤升
    06，属性值骤降
    07，位置漂移
    其他未定义
【具体以sanyds_dict字典表定义为准】
     */
    @TableField(value = "abnormal_code")
    private Integer abnormalCode;

    /**
     * 异常发生时间
     */
    @TableField(value = "abnormal_time")
    private Date abnormalTime;

    /**
     * 异常明细数据
     */
    @TableField(value = "abnormal_detail")
    private String abnormalDetail;

    /**
     * 数据生成时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableId(type = IdType.AUTO)
    private String id;
}