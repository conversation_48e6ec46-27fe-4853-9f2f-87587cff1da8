package com.rc.admin.easyapi.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rc_iot_data_quality_inspection_history")
@ApiModel(value = "rc_iot_data_quality_inspection_history对象", description = "数据质量检查历史表")
public class IotDataQualityInspectionHistory {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(value = "device_code")
    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", width = 10, orderNum = "0")
    private String deviceCode;
    @TableField(value = "dest_address_name")
    @ApiModelProperty(value = "点位名称")
    @Excel(name = "点位名称", width = 10, orderNum = "1")
    private String destAddressName;
    @TableField(value = "description")
    @ApiModelProperty(value = "描述")
    @Excel(name = "描述", width = 10, orderNum = "2")
    private String description;
    @TableField(value = "gl_value")
    @ApiModelProperty(value = "根连值")
    @Excel(name = "根连值", width = 10, orderNum = "3")
    private String glValue;
    @TableField(value = "gy_value")
    @ApiModelProperty(value = "根云值")
    @Excel(name = "根云值", width = 10, orderNum = "4")
    private String gyValue;
    @TableField(value = "scan_interval")
    @ApiModelProperty(value = "采集频率")
    @Excel(name = "采集频率(毫秒)", width = 10, orderNum = "5")
    private String scanInterval;
    @TableField(value = "update_time")
    @ApiModelProperty(value = "根连值更新时间")
    @Excel(name = "更新时间", width = 10, orderNum = "6")
    private String updateTime;
    @TableField(value = "change_time")
    @ApiModelProperty(value = "值改变时间")
    @Excel(name = "值改变时间", width = 10, orderNum = "7")
    private String changeTime;
    @TableField(value = "gy_time_cloud")
    @ApiModelProperty(value = "根云入云时间")
    @Excel(name = "入云时间", width = 10, orderNum = "8")
    private String gyTimeCloud;
    @TableField(value = "gy_time_local")
    @ApiModelProperty(value = "根云本地时间")
    @Excel(name = "本地时间", width = 10, orderNum = "9")
    private String gyTimeLocal;
    @TableField(value = "gy_write_time")
    @ApiModelProperty(value = "根云写入时间")
    @Excel(name = "生成时间", width = 10, orderNum = "10")
    private String gyWriteTime;
    @TableField(value = "rule_subject")
    @ApiModelProperty(value = "检查规则主题")
    @Excel(name = "检查规则主题", width = 10, orderNum = "11")
    private String ruleSubject;
    @TableField(value = "rule_script")
    @ApiModelProperty(value = "检查规则表达式")
    @Excel(name = "检查规则表达式", width = 10, orderNum = "12")
    private String ruleScript;
    @TableField(value = "rule_remarks")
    @ApiModelProperty(value = "检查规则公式说明")
    @Excel(name = "检查规则公式说明", width = 10, orderNum = "13")
    private String ruleRemarks;
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间", width = 20, orderNum = "14", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
