package com.rc.admin.easyapi.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rc.admin.easyapi.constants.ServiceNameAndUrl;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 */
public class HttpRequest {
    private static final Logger log = LoggerFactory.getLogger(HttpRequest.class);

    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段

            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("sendGet:" + e.getMessage(), e);
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                log.error("sendGet:" + e2.getMessage(), e2);
            }
        }
        return result;
    }
    public static InputStream sendPostUtf8GetFileExportDevice(String host,String token) {
        InputStream inputStream = null;
        try {
            String urlNameString = "http://" +host+ServiceNameAndUrl.FILE_EXPORT_DEVICE.getUrl();
            log.info("请求地址：{}" + urlNameString);
            SSLConnectionSocketFactory scsf = new SSLConnectionSocketFactory(
                    SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(),
                    NoopHostnameVerifier.INSTANCE);
            CloseableHttpClient	client = HttpClients.custom().setSSLSocketFactory(scsf).build();
            HttpPost httpPost = new HttpPost(urlNameString);

            //添加请求头
            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            httpPost.addHeader("TOKEN", token);
            httpPost.addHeader("X-Powered-By", "Express");
            httpPost.addHeader("Host", host);
            httpPost.setEntity(new StringEntity(JSON.toJSONString(new ArrayList<>()),"utf-8"));

            HttpResponse response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            inputStream =  entity.getContent();
        } catch (Exception e) {
            log.error("sendGetUTF8:" + e.getMessage(), e);
        }
        return inputStream;
    }
    public static String doPost(String url,Map<String,String> header,Map<String,Object> params){
        String strResult = "";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        //添加超时时间
        RequestConfig config = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(10000)
                .build();
        httpPost.setConfig(config);
        //添加请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.addHeader("Authorization", "Basic MjAyMzA3MjQ1NTU0NTNjYmE0Y2YxYWJlOjFjMTdjYjZjOGQ2NTE5Y2IwOGJiZWZhMjRlOWRiNjY1");

        try {
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    //添加请求头
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            if (params != null && !params.isEmpty()) {
                httpPost.setEntity(new StringEntity(JSON.toJSONString(params),"utf-8"));
            }

            CloseableHttpResponse resp = null;
            try {
                resp = client.execute(httpPost);
                HttpEntity respEntity = resp.getEntity();
                strResult = EntityUtils.toString(respEntity, "UTF-8");
            } finally {
                if (resp != null) {
                    resp.close();
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return strResult;
    }
    public static String sendPost(String url, String param, String token) throws Exception {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Authorization", "Bearer "+token);
            conn.setRequestProperty("x-data-center-id",  "ningxiang" );
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setConnectTimeout(60000);
            conn.setReadTimeout(60000);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("sendPost:" + e.getMessage(), e);
            throw new Exception(e);
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("sendPost:" + ex.getMessage(), ex);
            }
        }
        return result;
    }
    public static String sendPostUtf8(String url, String param) throws Exception {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL("http://"+url+"?"+param);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setConnectTimeout(60000);
            conn.setReadTimeout(60000);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.write("");
//            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("error sendPostUTF8: " + e.getMessage(), e);
            throw new Exception(e);
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("error sendPostUTF8: " + ex.getMessage(), ex);
            }
        }
        return result;
    }

    public static String sendGetUtf8RealTime(String url,String param,String token,String region) {
        String result = "";
        BufferedReader  in = null;
        try {
            String urlNameString = url +"?"+ param;
            log.info("请求地址：{}" + urlNameString);
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("Authorization", "Bearer "+token);
            connection.setRequestProperty("x-data-center-id", StringUtils.isEmpty(region) ? "ningxiang" : region);
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                log.info(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("sendGetUTF8:" + e.getMessage(), e);
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                log.error("sendGetUTF8:" + e2.getMessage(), e2);
            }
        }
        return result;
    }

    public static String sendPostUtf8History(String url, String param,String token,String region) throws Exception {

        String strResult = "";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        //添加超时时间
        RequestConfig config = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(10000)
                .build();
        httpPost.setConfig(config);
        //添加请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.addHeader("Authorization", "Bearer "+token);
//            conn.setRequestProperty("x-data-center-id", StringUtils.isEmpty(region) ? "ningxiang" : region);
        httpPost.addHeader("X-RCD-Tenant-Id", StringUtils.isEmpty(region) ? "ningxiang" : region);
        try {
            if (param != null && !param.isEmpty()) {
                httpPost.setEntity(new StringEntity(param,"utf-8"));
            }

            CloseableHttpResponse resp = null;
            try {
                resp = client.execute(httpPost);
                HttpEntity respEntity = resp.getEntity();
                strResult = EntityUtils.toString(respEntity, "UTF-8");
            } finally {
                if (resp != null) {
                    resp.close();
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return strResult;
    }

    public static String sendPostUtf8Login(String param) throws Exception {
        String strResult = "";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(ServiceNameAndUrl.ROOT_CLOUD_LOGIN.getName()+ServiceNameAndUrl.ROOT_CLOUD_LOGIN.getUrl());
        //添加超时时间
        RequestConfig config = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(10000)
                .build();
        httpPost.setConfig(config);
        //添加请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.addHeader("Authorization", "Basic MjAyMzA3MjQ1NTU0NTNjYmE0Y2YxYWJlOjFjMTdjYjZjOGQ2NTE5Y2IwOGJiZWZhMjRlOWRiNjY1");

        try {
            if (param != null && !param.isEmpty()) {
                httpPost.setEntity(new StringEntity(param,"utf-8"));
            }

            CloseableHttpResponse resp = null;
            try {
                resp = client.execute(httpPost);
                HttpEntity respEntity = resp.getEntity();
                strResult = EntityUtils.toString(respEntity, "UTF-8");
            } finally {
                if (resp != null) {
                    resp.close();
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return strResult;
    }

    public static void main(String[] args) {
        String TOKEN = null;
        try {
//            Map<String,String> map = new HashMap<>();
//            map.put("client_id","20230724555453cba4cf1abe");
//            map.put("client_secret","1c17cb6c8d6519cb08bbefa24e9db665");
//            map.put("grant_type","client_credentials");
//            try {
//                String rsp = HttpRequest.sendPostUtf8Login(JSONObject.toJSONString(map));
//                if (rsp!=null) {
//                    Map map1 = JSONObject.parseObject(rsp, Map.class);
//                    if (map1.get("access_token") != null){
//                        TOKEN = (String) map1.get("access_token");
//                    }
//
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error("获取根云token异常"+e.getMessage());
//            }
//            Map<String,String> map1 = new HashMap<>();
//            map1.put("classId","DEVICE");
//            String url = ServiceNameAndUrl.ROOT_CLOUD_DEVICE.getUrl();
////            try {
//                String s = HttpRequest.sendPostUtf8History(url, JSONObject.toJSONString(map1), TOKEN,"ningxiang");
            String s = HttpRequest.sendGet("http://10.16.2.37"+ServiceNameAndUrl.DEVICE_ALL_INFO.getUrl(),  null);
            System.out.println(s);
//            System.out.println(sendPostGetRealTimeWorkingCondition("10.16.2.58" + ServiceNameAndUrl.ROOT_CONNECTION_REAL_TIME_WORKING_CONDITION.getUrl(),"code=",null,null));
//            StringBuilder sb = new StringBuilder();
//            List<String> list  = new ArrayList<>();
//            Date date = new Date();
//            list.add("1lNOoMGMNS8");
//            sb.append("thingIds=").append(JSONObject.toJSONString(list)).append("&");
//            sb.append("queryStrategy=").append("all").append("&");
////            sb.append("startTime=").append(DateUtil.timeFormat("2023-07-30 00:00:01")).append("&");
////            sb.append("endTime=").append(DateUtil.timeFormat("2023-07-31 00:00:01")).append("&");
////            sb.append("skip=").append("0").append("&");
////            sb.append("limit=").append("1000").append("&");
////            sb.append("sort=").append("DESC").append("&");
//
//            String url = ServiceNameAndUrl.REAL_TIME_WORKING_CONDITION.getName() + ServiceNameAndUrl.REAL_TIME_WORKING_CONDITION.getUrl().replace("{modelId}", "1lAoGy2vnGg");
//            Map map1 = JSONObject.parseObject(HttpRequest.sendGetUtf8RealTime(url, sb.toString(), TOKEN, null), Map.class);
//            System.out.println(map1);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public static String sendPostGetRealTimeWorkingCondition(String url, String param,String token,String region) throws Exception {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL("http://"+url+"?"+param);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("x-data-center-id", StringUtils.isEmpty(region) ? "ningxiang" : region);
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setConnectTimeout(60000);
            conn.setReadTimeout(60000);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.write("");
//            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("error sendPostUTF8: " + e.getMessage(), e);
            throw new Exception(e);
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("error sendPostUTF8: " + ex.getMessage(), ex);
            }
        }
        return result;
    }


    public static String sendPutUtf8History(String url, String param,String token) throws Exception {

        String strResult = "";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        //添加超时时间
        RequestConfig config = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(10000)
                .build();
        httpPut.setConfig(config);
        //添加请求头
        httpPut.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPut.addHeader("Authorization", "Bearer "+token);
//            conn.setRequestProperty("x-data-center-id", StringUtils.isEmpty(region) ? "ningxiang" : region);
//        httpPost.addHeader("X-RCD-Tenant-Id", StringUtils.isEmpty(region) ? "ningxiang" : region);
        try {
            if (param != null && !param.isEmpty()) {
                httpPut.setEntity(new StringEntity(param,"utf-8"));
            }

            CloseableHttpResponse resp = null;
            try {
                resp = client.execute(httpPut);
                HttpEntity respEntity = resp.getEntity();
                strResult = EntityUtils.toString(respEntity, "UTF-8");
            } finally {
                if (resp != null) {
                    resp.close();
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return strResult;
    }
}