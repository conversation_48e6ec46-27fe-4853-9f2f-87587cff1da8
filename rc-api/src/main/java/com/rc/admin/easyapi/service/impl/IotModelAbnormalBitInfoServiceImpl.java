package com.rc.admin.easyapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rc.admin.easyapi.dao.IotModelAbnormalBitInfoMapper;
import com.rc.admin.easyapi.entity.IotModelAbnormalBitInfo;
import com.rc.admin.easyapi.service.IotModelAbnormalBitInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class IotModelAbnormalBitInfoServiceImpl implements IotModelAbnormalBitInfoService {

    @Resource
    private IotModelAbnormalBitInfoMapper iotModelAbnormalBitInfoMapper;
    @Override
    public List<IotModelAbnormalBitInfo> getModelAbnormalBitInfo(String modelId) {
        return iotModelAbnormalBitInfoMapper.selectList(new QueryWrapper<IotModelAbnormalBitInfo>().eq(StringUtils.isNotBlank(modelId),"model_id", modelId));
    }

    @Override
    public List<Map<String, String>> getModelAbnormalBitConfig() {
        return iotModelAbnormalBitInfoMapper.getModelAbnormalBitConfig();
    }
}
