package com.rc.admin.easyapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rc.admin.auth.model.entity.RealTimeDeviceInfoResp;
import com.rc.admin.auth.model.entity.RealTimeDeviceWorkConditionResp;
import com.rc.admin.auth.model.entity.RealWorkConditionResp;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisUtil;
import com.rc.admin.easyapi.constants.ServiceNameAndUrl;
import com.rc.admin.easyapi.dao.IotDeviceInfoMapper;
import com.rc.admin.easyapi.dao.IotEliminateFlagConfigMapper;
import com.rc.admin.easyapi.dao.IotModelAbnormalBitInfoMapper;
import com.rc.admin.easyapi.dao.RcIotPointVerificationMapper;
import com.rc.admin.easyapi.entity.*;
import com.rc.admin.easyapi.model.req.QueryWorkingConditionReq;
import com.rc.admin.easyapi.model.req.UpdateRealWorkingConditionReq;
import com.rc.admin.easyapi.model.resp.DeviceInfoAllResp;
import com.rc.admin.easyapi.model.resp.PayloadDataResp;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.easyapi.service.WorkingConditionService;
import com.rc.admin.easyapi.util.*;
import com.rc.admin.exception.CustomException;
import com.rc.admin.ors.quality.dao.OrsDeviceCheckConfigMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceInfoMapper;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.OrsDeviceInfo;
import com.rc.admin.ors.quality.model.OrsRootCloudTokenResp;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.service.RuleAdaptService;
import com.rc.admin.sys.model.SysRedisVO;
import com.rc.admin.sys.service.SysRedisService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class WorkingConditionServiceImpl implements WorkingConditionService {


    @Value("${ors.rootCloud.clientId:20250214b4e9e6c1faf930b9}")
    private String clientId;

    @Value("${ors.rootCloud.clientSecret:3868c834bf52a274ee975187038831fb}")
    private String clientSecret;

    @Value("${ors.rootCloud.grantType:client_credentials}")
    private String grantType;

    @Value("${ors.rootCloud.urlPre:http://federation-openapi-gateway-zone-china.ngc.sanygroup.com}")
    private String urlPre;

    private static final String TOKEN_KEY = "ors:newc1:token";

//    private static String TOKEN ;
    @Resource
    private IotDeviceInfoMapper infoMapper;
    @Resource
    private SysRedisService serviceRedis;

    @Resource
    private RuleAdaptService ruleAdaptService;
    @Resource
    private RcIotPointVerificationMapper verificationMapper;
    @Resource
    private OrsDeviceCheckConfigMapper orsDeviceCheckConfigMapper;
    @Resource
    private OrsDeviceCheckConfigService orsDeviceCheckConfigService;
    @Resource
    private IotEliminateFlagConfigMapper iotEliminateFlagConfigMapper;
    @Resource
    private IotModelAbnormalBitInfoMapper iotModelAbnormalBitInfoMapper;
    @Resource
    private OrsDeviceInfoMapper orsDeviceInfoMapper;

    @Override
    public Map<String,Object> getRealTimeWorkingCondition(QueryWorkingConditionReq req) {
//        Object value = getLoginToken();
        Map<String,Object> mapResp = parameterVerification(req);
        if (mapResp != null) {return mapResp;}
        Object o = RedisUtil.get(ServiceEnum.DEVICE_INFO + ":" + req.getAssetIds().get(0));
        DeviceInfoAllResp deviceInfoAllResp = JSONObject.parseObject(o.toString(), DeviceInfoAllResp.class);
        IotDeviceInfo info = deviceInfoAllResp.getInfo();
        StringBuilder sb = new StringBuilder();
        List<String> bySecondProcess = verificationMapper.getBySecondProcess(info.getSecondProcess());
        if (CollectionUtils.isEmpty(bySecondProcess)) {
            deviceInfoAllResp.getIotCollectionPoints().forEach(i-> bySecondProcess.add(i.getDestAddressName()));
            deviceInfoAllResp.getIotCustomizePoints().forEach(i-> bySecondProcess.add(i.getDestAddressName()));
        }
        List<String> secondProcess = bySecondProcess.stream().distinct().collect(Collectors.toList());

        sb.append("thingIds=").append(JSONObject.toJSONString(req.getThingIds())).append("&");
        sb.append("queryStrategy=").append(StringUtils.isBlank(req.getQueryStrategy()) ? "all" : req.getQueryStrategy()).append("&");
        if (!CollectionUtils.isEmpty(req.getProperties())){sb.append("properties=").append(JSONObject.toJSONString(req.getProperties()));}
//        String url = ServiceNameAndUrl.REAL_TIME_WORKING_CONDITION.getName() + ServiceNameAndUrl.REAL_TIME_WORKING_CONDITION.getUrl().replace("{modelId}", req.getModelId());


//        resp = JSONObject.parseObject(HttpRequest.sendGetUtf8RealTime(url,sb.toString(), String.valueOf(value), req.getRegion()), RealTimeWorkingConditionResp.class);
        //获取根连实时工况
        long l = System.currentTimeMillis();
        List<Map<String,Object>> requestRootLink = requestRootLink(info.getDeviceId(), info.getSource());
        if (CollectionUtils.isEmpty(requestRootLink)){
            return  null;
        }
        System.out.println("根连工况获取"+(System.currentTimeMillis()-l)+"/毫秒");
        //获取根云实时工况
         l = System.currentTimeMillis();
        List<Map<String,Object>> rootCloudWorkDatas = selectRootCloudWorkDatas(req.getThingIds().get(0), req.getModelId(), secondProcess);
        List<Map<String,PayloadDataResp>> rootCloudList = new ArrayList<>();
        System.out.println("根云工况获取"+(System.currentTimeMillis()-l)+"/毫秒");
        l = System.currentTimeMillis();
        rootCloudWorkDatas.forEach(m->{
            Map<String,PayloadDataResp> data = new HashMap<>();
            String time = m.get("timestamp")+"";
            m.keySet().forEach(mm->{
                PayloadDataResp p = new PayloadDataResp();
                p.setValue(m.get(mm));
                p.setTimeCloud(time);
                p.setTimeLocal(time);
                p.setWriteTime(time);
                data.put(mm,p);
            });
            rootCloudList.add(data);
        });
        //数据封装
        RealTimeDeviceInfoResp realTimeDeviceInfoResp = new RealTimeDeviceInfoResp();
        List<RealTimeDeviceWorkConditionResp> respList = new ArrayList<>();

        realTimeDeviceInfoResp.setDeviceCode(info.getDeviceCode());
        realTimeDeviceInfoResp.setName(info.getName());
        Map<String, Object> stringObjectMap = requestRootLink.get(0);
        List<Map<String, PayloadDataResp>> collects = rootCloudList.stream().filter(m -> m.get("timestamp").getTimeCloud().equals(stringObjectMap.get("ts").toString())).collect(Collectors.toList());
        Map<String, PayloadDataResp> collectMap = new HashMap<>();
        if (collects.size()>0){
            collectMap = collects.get(0);
        }else{
            if (!CollectionUtils.isEmpty(rootCloudList)){
                List<Map<String, PayloadDataResp>> collectsMap = rootCloudList.stream().filter(m -> Long.parseLong(m.get("timestamp").getTimeCloud())<(long)stringObjectMap.get("ts")).collect(Collectors.toList());
                if (!collectsMap.isEmpty()) {
                    collectMap = collectsMap.get(0);
                }
            }
        }
        for (String s : stringObjectMap.keySet()) {
            if ("ts".equals(s)){
                continue;
            }
            RealTimeDeviceWorkConditionResp resp = new RealTimeDeviceWorkConditionResp();
            resp.setName(s);
            resp.setUpdateTime(Objects.nonNull(stringObjectMap.get("ts"))?DateUtil.formatDateTime(new Date(Long.parseLong(stringObjectMap.get("ts").toString()))) : "");
            resp.setChangeTime(Objects.nonNull(stringObjectMap.get("ts"))?DateUtil.formatDateTime(new Date(Long.parseLong(stringObjectMap.get("ts").toString()))) : "");
            resp.setValue(stringObjectMap.get(s).toString());
            PayloadDataResp payloadDataResp = collectMap.get(s);
            if(Objects.nonNull(payloadDataResp)){
                resp.setRootCloudTimeCloud(DateUtil.formatDateTime(new Date(Long.parseLong(payloadDataResp.getTimeCloud()))));
                resp.setRootCloudTimeLocal(DateUtil.formatDateTime(new Date(Long.parseLong(payloadDataResp.getTimeLocal()))));
                resp.setRootCloudWriteTime(DateUtil.formatDateTime(new Date(Long.parseLong(payloadDataResp.getWriteTime()))));
                resp.setRootCloudValue(payloadDataResp.getValue()!=null ? payloadDataResp.getValue().toString() : null);
            }
            respList.add(resp);
        }
        RealWorkConditionResp realWorkConditionResp = getRealTimeWorkingCondition(req.getAssetIds().get(0));
        if (realWorkConditionResp==null || !"Connect".equals(realWorkConditionResp.getDevice().getStatus())){
            return  null;
        }
        RealWorkConditionResp real = new RealWorkConditionResp();
        realTimeDeviceInfoResp.setStatus(realWorkConditionResp.getDevice().getStatus());
        real.setDevice(realTimeDeviceInfoResp);
        real.setItemList(respList);


        //补全返回结果信息
        List<PointResultListResp> pointInfo = infoMapper.getPointInfo(req.getAssetIds().get(0));
        List<PointResultListResp> zdyPoint = null;
        if (!CollectionUtils.isEmpty(pointInfo)) {
             zdyPoint = pointInfo.stream().filter(point -> "自定义点位".equals(point.getPointType())).collect(Collectors.toList());
            Map<String, List<PointResultListResp>> collect = pointInfo.stream().collect(Collectors.groupingBy(PointResultListResp::getDestAddressName));
            real.getItemList().parallelStream().forEach(rtd -> {
                List<PointResultListResp> pointResultListResps = collect.get(rtd.getName());
                if (!CollectionUtils.isEmpty(pointResultListResps)) {
                    PointResultListResp pointResultListResp = pointResultListResps.get(0);
                    rtd.setFrequencyAcquisition(pointResultListResp.getFrequencyAcquisition());
                    rtd.setUnits(pointResultListResp.getUnit());
                    rtd.setReadWritePower(pointResultListResp.getReadWritePower());
                    rtd.setType(pointResultListResp.getType());
                    rtd.setPointAddress(pointResultListResp.getPointAddress());
                    rtd.setPointType(pointResultListResp.getPointType());
                    if (StringUtils.isEmpty(rtd.getScanInterval())) {
                        rtd.setScanInterval(pointResultListResp.getAcquisitionFrequency());
                    }
                }
            });
        }

        //数据封装进行规则检查
        Map<String,Object> map = new HashMap<>();
        IotRootCloudDeviceInfo rootCloudDeviceInfo = infoMapper.getRootCloudDeviceInfo(real.getDevice().getDeviceCode());
        if (Objects.nonNull(rootCloudDeviceInfo)) {
            map.put("__deviceId__", rootCloudDeviceInfo.getThingId());
            map.put("__assetId__", rootCloudDeviceInfo.getAssetId());
            map.put("__deviceTypeId__", rootCloudDeviceInfo.getModelId());
            map.put("__timestamp__", real.getDevice().getUpdateTime());//更新时间
            real.getItemList().forEach(r -> map.put(r.getName(), r.getValue()));
            map.put("rawData",real);
        }
        mapResp = new HashMap<>();
        // 将封装后的map推送数据检测模块。
        if (!CollectionUtils.isEmpty(real.getItemList())) {
            map.put("__cloud_time__", real.getItemList().get(0).getRootCloudTimeCloud());//入云时间
        }
        map.put("rawData",real);

        try {
            ruleAdaptService.realTimeDataRuleAdapt(new cn.hutool.json.JSONObject(map));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //插入计算点位
        if (!CollectionUtils.isEmpty(zdyPoint)) {
            zdyPoint.forEach(p -> {
                RealTimeDeviceWorkConditionResp respNew = new RealTimeDeviceWorkConditionResp();
                respNew.setPointType("计算点位");
                respNew.setName(p.getDestAddressName());
                respNew.setExplain(p.getDescription());
                respNew.setType(p.getType());
                respNew.setUnit(p.getUnit());
                respNew.setExpress(p.getExpress());
                respNew.setEventMode(p.getEventMode());
                respNew.setEventCondition(p.getEventCondition());
                real.getItemList().add(respNew);
            });
        }

        List<RcIotPointVerification> pointTemplateList = infoMapper.getPointTemplateList(real.getDevice().getDeviceCode());
        if (!CollectionUtils.isEmpty(pointTemplateList) && pointTemplateList.get(0)!=null) {
            List<RealTimeDeviceWorkConditionResp> list = new ArrayList<>();
            Map<String, List<RcIotPointVerification>> collect = pointTemplateList.stream().collect(Collectors.groupingBy(RcIotPointVerification::getDestAddressName));
            Map<String, List<RealTimeDeviceWorkConditionResp>> collect1 = real.getItemList().stream().collect(Collectors.groupingBy(RealTimeDeviceWorkConditionResp::getName));
            collect.keySet().forEach(s->{
                if (CollectionUtils.isEmpty(collect1.get(s))){
                    RealTimeDeviceWorkConditionResp resp1 = new RealTimeDeviceWorkConditionResp();
                    List<RcIotPointVerification> rcIotPointVerifications = collect.get(s);
                    resp1.setName(rcIotPointVerifications.get(0).getDestAddressName());
                    resp1.setExplain(rcIotPointVerifications.get(0).getDescription());
                    list.add(resp1);
                }
            });
            real.getItemList().addAll(list);
            real.getItemList().parallelStream().forEach(it->{
                List<RcIotPointVerification> verifications = collect.get(it.getName());
                if(!CollectionUtils.isEmpty(verifications)){
                    it.setExplain(verifications.get(0).getDescription());
                    it.setValueType(verifications.get(0).getDataType());
                }
            });
        }

        Comparator<RealTimeDeviceWorkConditionResp> name = Comparator.comparing(RealTimeDeviceWorkConditionResp::getName);
        Comparator<RealTimeDeviceWorkConditionResp> pointType = Comparator.comparing(RealTimeDeviceWorkConditionResp::getPointType);
        real.getItemList().sort(name.thenComparing(pointType));
        mapResp.put("data",real);
        System.out.println("数据处理时间"+(System.currentTimeMillis()-l)+"/毫秒");
        return mapResp;
    }


    @Override
    public Map getHistoryWorkingCondition(QueryWorkingConditionReq req) {
        Object value = getLoginToken();
        StringBuilder sb = new StringBuilder();
        sb.append("thingIds=").append(JSONObject.toJSONString(req.getThingIds())).append("&");
        sb.append("queryStrategy=").append(StringUtils.isBlank(req.getQueryStrategy()) ? "all" : req.getQueryStrategy()).append("&");
        sb.append("startTime=").append(DateUtil.formatDate(req.getStartTime(),"yyyy-MM-ddTHH:mm:ss.SSSZ")).append("&");
        sb.append("endTime=").append(DateUtil.formatDate(req.getEndTime(),"yyyy-MM-ddTHH:mm:ss.SSSZ")).append("&");
        sb.append("skip=").append(req.getSkip()).append("&");
        sb.append("limit=").append("1000").append("&");
        sb.append("sort=").append("DESC").append("&");
        if (!CollectionUtils.isEmpty(req.getProperties())){sb.append("properties=").append(JSONObject.toJSONString(req.getProperties()));}
        String url = ServiceNameAndUrl.HISTORY_WORKING_CONDITION.getName() + ServiceNameAndUrl.HISTORY_WORKING_CONDITION.getUrl().replace("{modelId}", req.getModelId());
        Map map = JSONObject.parseObject(HttpRequest.sendGetUtf8RealTime(url, sb.toString(), String.valueOf(value), req.getRegion()), Map.class);
        if (Objects.nonNull(map.get("message")) && map.get("message").toString().contains("token")){
            value = getLoginToken();
        }else{
            return map;
        }
        return JSONObject.parseObject(HttpRequest.sendGetUtf8RealTime(url,sb.toString(), String.valueOf(value), req.getRegion()), Map.class);
    }

    @Override
    public RealWorkConditionResp getRealTimeWorkingCondition(String deviceCode) {
        Object value = getLoginToken();
        if (StringUtils.isEmpty(deviceCode)){ return null;}
        Map<String, String> regionValues = infoMapper.getRegionValuesInfo(deviceCode);
        StringBuilder sb = new StringBuilder();
        sb.append("code=").append(deviceCode);
        String url = regionValues.get("ip") + ServiceNameAndUrl.ROOT_CONNECTION_REAL_TIME_WORKING_CONDITION.getUrl();
        try {
            String s = HttpRequest.sendPostGetRealTimeWorkingCondition(url, sb.toString(), String.valueOf(value), regionValues.get("value"));
            if (s!=null) {
                Map map = JSONObject.parseObject(s, Map.class);
                if (StringUtil.isEmpty(map.get("code"))) {
                    Set strings = map.keySet();
                    if (!CollectionUtils.isEmpty(strings)){
                        for (Object str : strings) {
                            String s1 = JSONObject.toJSONString(map.get(str));
                            return JSONObject.parseObject(s1, RealWorkConditionResp.class);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取根连实时工况异常");
        }
        return null;
    }

    @Override
    public String getLoginToken() {
        Map<String,String> map = new HashMap<>();
        map.put("client_id","20230724555453cba4cf1abe");
        map.put("client_secret","1c17cb6c8d6519cb08bbefa24e9db665");
        map.put("grant_type","client_credentials");
        SysRedisVO sysRedisVOs = serviceRedis.get("dqm-gl-token");
        if (Objects.nonNull(sysRedisVOs)){
            if (!StringUtil.isEmpty(sysRedisVOs.getValue())){
                return String.valueOf(sysRedisVOs.getValue());
            }
        }

        try {
            String rsp = HttpRequest.sendPostUtf8Login(JSONObject.toJSONString(map));
            if (rsp!=null) {
                Map map1 = JSONObject.parseObject(rsp, Map.class);
                if (map1.get("access_token") != null){
                  String  token = (String) map1.get("access_token");
                    SysRedisVO sysRedisVO = new SysRedisVO();
                    sysRedisVO.setKey("dqm-gl-token");
                    sysRedisVO.setValue(token);
                    sysRedisVO.setExpire(1800L);
                    serviceRedis.save(sysRedisVO);
                    return token;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取根云token异常"+e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional
    public void GetFileExportDevice(String url) {
        Object value = getLoginToken();
        boolean flag = false;
        InputStream inputStream = HttpRequest.sendPostUtf8GetFileExportDevice(url, String.valueOf(value));
        try {
            List<InputStream> inputStreams = ZipUtils.readZipToInputStreamList(inputStream, ".xlsx");
             WorkbookUtil.fileExportDeviceSave(inputStreams,infoMapper,url);
        } catch (IOException e) {
            log.error("数采平台设备信息处理异常");
            flag = true;
        }
        if (flag){
            getLoginToken();
             inputStream = HttpRequest.sendPostUtf8GetFileExportDevice("", String.valueOf(value));
            try {
                List<InputStream> inputStreams = ZipUtils.readZipToInputStreamList(inputStream, ".xlsx");
                WorkbookUtil.fileExportDeviceSave(inputStreams,infoMapper,url);
            } catch (IOException e) {
                log.error("数采平台设备信息处理异常二次");
            }
        }
    }

    @Override
    @Transactional
    public void syncRootCloudDeviceInfo() {
        Object token = getLoginToken();
        List<String> Values = infoMapper.getValues();
        for (String value : Values) {
            for (int i = 0; i < 100; i++) {
                Map<String, Object> map = new HashMap<>();
                map.put("classId", "DEVICE");
                map.put("limit", 1000);
                map.put("skip", i*1000);
                String url = ServiceNameAndUrl.ROOT_CLOUD_DEVICE.getUrl();
                try {
                    String s = HttpRequest.sendPostUtf8History(url, JSONObject.toJSONString(map), String.valueOf(token), value);
                    if (s != null) {
                        Map maps = JSONObject.parseObject(s, Map.class);
                        if ((int) maps.get("status") == 200) {
                            List<Map> payload = JSONObject.parseArray(JSONObject.toJSONString(maps.get("payload")), Map.class);
                            if (CollectionUtils.isEmpty(payload)) {
                                return;
                            }
                            List<IotRootCloudDeviceInfo> list = new ArrayList<>();
                            for (Map ma : payload) {
                                try {
                                    IotRootCloudDeviceInfo iotRootCloudDeviceInfo = new IotRootCloudDeviceInfo();
                                    Map model = JSONObject.parseObject(JSONObject.toJSONString(ma.get("model")), Map.class);
                                    iotRootCloudDeviceInfo.setAssetId((String) ma.get("assetId"));
                                    iotRootCloudDeviceInfo.setModelId((String) model.get("modelId"));
                                    iotRootCloudDeviceInfo.setThingId((String) ma.get("thingId"));
                                    iotRootCloudDeviceInfo.setClassId((String) ma.get("classId"));
                                    iotRootCloudDeviceInfo.setProtocol((String) ma.get("protocol"));
                                    iotRootCloudDeviceInfo.setPhase((String) ma.get("phase"));
                                    list.add(iotRootCloudDeviceInfo);
                                } catch (Exception e) {
                                    log.error("根云设备信息设值异常");
                                }
                            }
                            infoMapper.rootCloudDeviceInfoSave(list);
                        }
                    }
                } catch (Exception e) {
                    log.error("根云设备同步异常");
                }
            }
        }
    }

    @Override
    public void allUploadWorkingConditionDataToPlatform(String sign,String date,String assetId,String modelIdStr){
        long startTime = System.currentTimeMillis();
        List<IotModelAbnormalBitInfo> abnormalBitInfoList = iotModelAbnormalBitInfoMapper.selectList(null);
        Map<String, List<IotModelAbnormalBitInfo>> abnormalBitInfoMap = abnormalBitInfoList.stream().collect(Collectors.groupingBy(IotModelAbnormalBitInfo::getModelId));

        // sign 标志 如果标志为空 则是昨天的历史数据
        // 否则就是全量数据
        String checkDate = "";
        if(StringUtils.isBlank(sign)){
            if(StringUtils.isNotBlank(date)){
                checkDate = date;
            }else {
                checkDate = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        }

        //获取所有设备信息
        //获取所要更新设备信息
        List<OrsDeviceInfo> deviceInfoList = orsDeviceInfoMapper.getDeviceInfoByCheck(checkDate,assetId,modelIdStr);
        log.info("推送标签设备的数量:{}",deviceInfoList.size());
        if(CollUtil.isEmpty(deviceInfoList)){
            return;
        }

        //获取所有剔除信息
        List<OrsDeviceCheckConfig> checkList = orsDeviceCheckConfigMapper.selectAll();
        //剔除根据uuid分组
        // uuid->即是 IOT物实例id
        Map<String, List<OrsDeviceCheckConfig>> collect = checkList.stream().collect(Collectors.groupingBy(OrsDeviceCheckConfig::getUuid));

        Map<Integer, String> dataCenterIdMap = new HashMap<>();
        dataCenterIdMap.put(1, "nc_262_de");
        dataCenterIdMap.put(2, "nc_525_sg");

        //设备数据按照站点分组
        Map<Integer, List<OrsDeviceInfo>> deviceInfoMap
                = deviceInfoList.stream().collect(Collectors.groupingBy(OrsDeviceInfo::getDataCenterId));

        for (Map.Entry<Integer, List<OrsDeviceInfo>> deviceInfoEntry : deviceInfoMap.entrySet()) {
            if (CollUtil.isEmpty(deviceInfoEntry.getValue())) {
                continue;
            }
            //站点
            Integer dataCenterId = deviceInfoEntry.getKey();
            // 根据模型进行分组
            Map<String, List<OrsDeviceInfo>> collectMap = deviceInfoEntry.getValue().stream().collect(Collectors.groupingBy(OrsDeviceInfo::getModelId));
            // 组装发送内容
            List<UpdateRealWorkingConditionReq> reqList = new ArrayList<>();
            for (Map.Entry<String, List<OrsDeviceInfo>> entry : collectMap.entrySet()) {
                // 模型id
                String modelId = entry.getKey();
                List<UpdateRealWorkingConditionReq.SubUpdate> updateList = new ArrayList<>();
                //这个-站点-模型-下的设备list
                for (OrsDeviceInfo orsDeviceInfo : entry.getValue()) {
                    // thingId
                    String thingId = orsDeviceInfo.getThingId();
                    UpdateRealWorkingConditionReq.SubUpdate update = new UpdateRealWorkingConditionReq.SubUpdate();
                    List<Map<String,String>> propertyList = new ArrayList<>();
                    Map<String,String> valueMap = new HashMap<>();
                    valueMap.put("propertyName","remove_flag");
                    List<String> attributesToQuery = CollUtil.newArrayList();
                    if(CollUtil.isNotEmpty(collect.get(thingId))){
                        attributesToQuery = collect.get(thingId).stream().filter(f -> f.getParamCode() != null).map(v->String.valueOf(v.getParamCode())).collect(Collectors.toList());
                    }
                    String s = generateDefaultFlagTwo(abnormalBitInfoMap.get(modelId), attributesToQuery);
                    valueMap.put("propertyValueJson","\""+ s +"\"");
                    propertyList.add(valueMap);
                    update.setThingId(thingId); // thingId
                    update.setStaticProperties(propertyList);
                    updateList.add(update);
                }
                // 一次性上传200个设备
                List<List<UpdateRealWorkingConditionReq.SubUpdate>> partition = Lists.partition(updateList, 200);
                for (List<UpdateRealWorkingConditionReq.SubUpdate> batchList : partition) {
                    UpdateRealWorkingConditionReq req = new UpdateRealWorkingConditionReq();
                    req.setModelId(modelId); // 模型ID
                    req.setUpdateType("DEVICE");
                    req.setSubUpdates(batchList);
                    req.setOrsDataCenterId(dataCenterIdMap.get(dataCenterId));
                    //按模型分组添加
                    reqList.add(req);
                }
            }
            if(CollUtil.isEmpty(reqList)){
                return;
            }
            log.info("uploadWorkingConditionDataToPlatformCheck json:{}",JSONObject.toJSONString(reqList));

            reqList.parallelStream().forEach(v->{
                // 上传工况
                uploadToPlatform(v,0);
            });
            log.info("execute uploadWorkingConditionDataToPlatformCheck byStation={} total timer:{}",dataCenterId,System.currentTimeMillis()-startTime);
        }
        log.info("execute uploadWorkingConditionDataToPlatformCheck send size:{} total timer:{}",deviceInfoList.size(),System.currentTimeMillis()-startTime);
    }

    @Override
    public void uploadWorkingConditionDataToPlatform() {
        long startTime = System.currentTimeMillis();
       // STEP-1：从库中获取设备的剔除标签配置
       // List<IotEliminateFlagConfig> configs = iotEliminateFlagConfigMapper.selectList(Wrappers.<IotEliminateFlagConfig>lambdaQuery().eq(IotEliminateFlagConfig::getFlagEnable,1));
       // Map<String, IotEliminateFlagConfig> configMap = configs.stream().collect(Collectors.toMap(IotEliminateFlagConfig::getModelId, item -> item));
        List<IotModelAbnormalBitInfo> abnormalBitInfoList = iotModelAbnormalBitInfoMapper.selectList(null);
        Map<String, List<IotModelAbnormalBitInfo>> abnormalBitInfoMap = abnormalBitInfoList.stream().collect(Collectors.groupingBy(IotModelAbnormalBitInfo::getModelId));

        // STEP-2：从库中获取设备的剔除信息并组装发送信息
        List<OrsDeviceCheckConfig> checkList = orsDeviceCheckConfigMapper.selectAll();
        if(CollUtil.isEmpty(checkList)){
            return;
        }
        Map<Integer, String> dataCenterIdMap = new HashMap<>();
        dataCenterIdMap.put(1, "nc_262_de");
        dataCenterIdMap.put(2, "nc_525_sg");

        Map<Integer, List<OrsDeviceCheckConfig>> dataCenterCheckMap = checkList.stream().collect(Collectors.groupingBy(OrsDeviceCheckConfig::getDataCenterId));
        for (Map.Entry<Integer, List<OrsDeviceCheckConfig>> dataCenterCheckEntry : dataCenterCheckMap.entrySet()) {
            if (CollUtil.isEmpty(dataCenterCheckEntry.getValue())) {
                continue;
            }
            Integer dataCenterId = dataCenterCheckEntry.getKey();

            // 根据模型进行分组
            Map<String, List<OrsDeviceCheckConfig>> collectMap = dataCenterCheckEntry.getValue().stream().collect(Collectors.groupingBy(OrsDeviceCheckConfig::getModelId));
            // 组装发送内容
            List<UpdateRealWorkingConditionReq> reqList = new ArrayList<>();

            for (Map.Entry<String, List<OrsDeviceCheckConfig>> entry : collectMap.entrySet()) {
                // modelId
                String modelId = entry.getKey();

                List<UpdateRealWorkingConditionReq.SubUpdate> updateList = new ArrayList<>();
                // uuid->即是 IOT物实例id
                Map<String, List<OrsDeviceCheckConfig>> collect = entry.getValue().stream().collect(Collectors.groupingBy(OrsDeviceCheckConfig::getUuid));

                for (Map.Entry<String, List<OrsDeviceCheckConfig>> item : collect.entrySet()) {
                    // thingId
                    String thingId = item.getKey();
                    UpdateRealWorkingConditionReq.SubUpdate update = new UpdateRealWorkingConditionReq.SubUpdate();

                    List<Map<String,String>> propertyList = new ArrayList<>();

                    Map<String,String> valueMap = new HashMap<>();
                    valueMap.put("propertyName","remove_flag");
                    List<String> attributesToQuery = item.getValue().stream().filter(f -> f.getParamCode() != null).map(v->String.valueOf(v.getParamCode())).collect(Collectors.toList());
                    //valueMap.put("propertyValueJson","\""+ this.generateDefaultFlag(configMap.get(modelId),attributesToQuery)+"\"");
                    String s = generateDefaultFlagTwo(abnormalBitInfoMap.get(modelId), attributesToQuery);
                    valueMap.put("propertyValueJson","\""+ s +"\"");
                    propertyList.add(valueMap);
                    //log.info("json:{}",s);
                    update.setThingId(thingId); // thingId
                    update.setStaticProperties(propertyList);
                    updateList.add(update);
                }
                // 一次性上传200个设备
                List<List<UpdateRealWorkingConditionReq.SubUpdate>> partition = Lists.partition(updateList, 200);
                for (List<UpdateRealWorkingConditionReq.SubUpdate> batchList : partition) {
                    UpdateRealWorkingConditionReq req = new UpdateRealWorkingConditionReq();
                    req.setModelId(modelId); // 模型ID
                    req.setUpdateType("DEVICE");
                    req.setSubUpdates(batchList);
                    req.setOrsDataCenterId(dataCenterIdMap.get(dataCenterId));

                    //按模型分组添加
                    reqList.add(req);
                }
            }

            if(CollUtil.isEmpty(reqList)){
                return;
            }
            log.info("uploadWorkingConditionDataToPlatform json:{}",JSONObject.toJSONString(reqList));


            reqList.parallelStream().forEach(v->{
                // 上传工况
                uploadToPlatform(v,0);
            });
            log.info("execute uploadWorkingConditionDataToPlatform send size:{} total timer:{}",checkList.size(),System.currentTimeMillis()-startTime);

        }
        log.info("execute uploadWorkingConditionDataToPlatform send size:{} total timer:{}",checkList.size(),System.currentTimeMillis()-startTime);
}

    public static String generateDefaultFlag(IotEliminateFlagConfig config, List<String> dbResult) {
        // 默认剔除标签:
        StringBuilder defaultFlagBuilder = new StringBuilder("1");
        if(config == null){
            // 如果没有配置的情况 表示所有属性都进行异常判断
             defaultFlagBuilder.append(StrUtil.repeat("1",32));
        }else if(dbResult.contains("8601")){
            // 如果剔除列表中为整机，所有都是位置都是9 不支持
            defaultFlagBuilder.append(StrUtil.repeat("9",config.getFlagConfig().split(",").length));
        }else {
            String[] attrArray = config.getFlagConfig().split(",");
            for (String attr : attrArray) {
                if (dbResult.contains(attr)) {
                    // 属性在剔除列表中 默认是9 表示不支持
                    defaultFlagBuilder.append('9');
                } else {
                    // 属性不在剔除列表中 表示是1 表示要进行属性异常判断
                    defaultFlagBuilder.append('1');
                }
            }
        }
        return defaultFlagBuilder.toString();
    }
    public static String generateDefaultFlagTwo(List<IotModelAbnormalBitInfo> config, List<String> dbResult) {

        // 默认剔除标签:
        StringBuilder defaultFlagBuilder = new StringBuilder("1");
        if(config == null){
            // 如果没有配置的情况 表示所有属性都进行异常判断
             defaultFlagBuilder.append(StrUtil.repeat("9",32));
        }else if(dbResult.contains("8601")){
            // 如果剔除列表中为整机，所有都是位置都是9 不支持
            defaultFlagBuilder.append(StrUtil.repeat("9",config.size()-1));
        }else {
            // 根据 bitIndex 对 config 进行排序
            config.sort(Comparator.comparingInt(IotModelAbnormalBitInfo::getBitIndex));
//            List<String> collect = config.stream().map(IotModelAbnormalBitInfo::getDictId).collect(Collectors.toList()).stream().map(String::valueOf).collect(Collectors.toList());
            for (IotModelAbnormalBitInfo iotModelAbnormalBitInfo : config) {
                if (iotModelAbnormalBitInfo.getDictId()!=null && iotModelAbnormalBitInfo.getDictId() >0 && dbResult.contains(iotModelAbnormalBitInfo.getDictId()+"")) {
                    // 属性在剔除列表中 默认是9 表示不支持
                    defaultFlagBuilder.append('9');
                }else if (iotModelAbnormalBitInfo.getBitDesc().equals("启机标签")) {
                    // 属性在剔除列表中 默认是9 表示不支持
                    //defaultFlagBuilder.append('1');
                }else if (iotModelAbnormalBitInfo.getDictId() == null || iotModelAbnormalBitInfo.getDictId() ==0) {
                    // 属性在剔除列表中 默认是9 表示不支持
                    defaultFlagBuilder.append('9');
                }else if (iotModelAbnormalBitInfo.getBitKey().equals("reserveValue") || iotModelAbnormalBitInfo.getBitKey().equals("silence_flag")) {
                    // 属性在剔除列表中 默认是9 表示不支持
                    defaultFlagBuilder.append('9');
                }else {
                    // 属性不在剔除列表中 表示是1 表示要进行属性异常判断
                    defaultFlagBuilder.append('1');
                }
            }
        }
        return defaultFlagBuilder.toString();
    }


    //获取平台token
    private String getRootCloudToken() {

        // 如果redis里面存在，直接从redis里面获取
        SysRedisVO redisToken = serviceRedis.get(TOKEN_KEY);
        if (Objects.nonNull(redisToken) && Objects.nonNull(redisToken.getValue())) {
            return String.valueOf(redisToken.getValue());
        }

        Map<String, String> param = Maps.newHashMapWithExpectedSize(3);
        param.put("client_id", clientId);
        param.put("client_secret", clientSecret);
        param.put("grant_type", grantType);

        try {
            cn.hutool.http.HttpRequest request = cn.hutool.http.HttpRequest.post(urlPre + "/account-manage/v2/auth/login");
            request.body(JSONObject.toJSONString(param));
            HttpResponse response = request.execute();
            if (StrUtil.isBlank(response.body())) {
                return null;
            }
            OrsRootCloudTokenResp resp = JSONObject.parseObject(response.body(), OrsRootCloudTokenResp.class);
            String token = resp.getAccessToken();
            // 获得token  存入Redis
            if (StrUtil.isNotBlank(token)) {
                SysRedisVO sysRedisVO = new SysRedisVO();
                sysRedisVO.setKey(TOKEN_KEY);
                sysRedisVO.setValue(token);
                sysRedisVO.setExpire(1800L);
                serviceRedis.save(sysRedisVO);
                return token;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取根云token异常" + e.getMessage());
        }
        return null;
    }

    // 上传到平台
    private void uploadToPlatform(UpdateRealWorkingConditionReq req,int retryCount) {
        // 最大重试次数
        final int MAX_RETRY_COUNT = 1;

        try {
            // 获取根云令牌
            String rootCloudToken = this.getRootCloudToken();
            Map<String, String> header = new HashMap<>();
            header.put("Authorization", "Bearer " + rootCloudToken);
            header.put("request-id", "DQM-" + UUID.randomUUID());
            header.put("X-Data-Center-Id", req.getOrsDataCenterId());

            // 请求URL
            String url = urlPre + "/thing-instance/v1/thing/thing-instances/bulk-staticProperties";

            // 创建PUT请求
            cn.hutool.http.HttpRequest request = HttpUtil.createRequest(Method.PUT, url);

            // 添加请求头
            request.header(Header.CONTENT_TYPE, "application/json");
            request.addHeaders(header);

            // 设置请求体
            request.body(JSONObject.toJSONString(req));

            // 执行请求
            String result = request.execute().body();
            log.info("uploadToPlatform result json: {}", result);

            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.get("payload") == null) {
                // 如果 payload 为 null，表示存在设备不存在或异常
                // 递归深度检查，避免死循环
                if (retryCount >= MAX_RETRY_COUNT) {
                    log.error("uploadToPlatform failed after maximum retries. Request: {}", req);
                    return;
                }

                // 遍历 subUpdates 并逐一下发
                req.getSubUpdates().forEach(subUpdate -> {
                    // 创建一个新的请求实例
                    UpdateRealWorkingConditionReq singleUpdateReq = new UpdateRealWorkingConditionReq();
                    singleUpdateReq.setModelId(req.getModelId());
                    singleUpdateReq.setUpdateType(req.getUpdateType());
                    singleUpdateReq.setSubUpdates(Collections.singletonList(subUpdate));

                    // 递归调用，增加重试计数
                    uploadToPlatform(singleUpdateReq, retryCount + 1);
                });
            }
        } catch (Exception e) {
            log.error("uploadToPlatform fail: {}", e.getMessage());
        }
    }


    @Nullable
    private static Map<String,Object> parameterVerification(QueryWorkingConditionReq req) {
        Map<String,Object> resp;
        if (StringUtils.isBlank(req.getModelId())){
            resp =  new HashMap<>();
            resp.put("code","500");
            resp.put("message","物模型不能为空");
            return resp;
        }
        if (CollectionUtils.isEmpty(req.getAssetIds()) && CollectionUtils.isEmpty(req.getThingIds())){
            resp =   new HashMap<>();
            resp.put("code","500");
            resp.put("message","物标识或物实例ID不能为空");
            return resp;
        }
        return null;
    }

    private List<Map<String,Object>> requestRootLink(String rootLinkDeviceId, String dataSourceIp ) {
        LocalDateTime startTime =LocalDateTime.now();
        // 查不合规开始时间前30s往后推一个小时的工况
        long timestamp = LocalDateTimeUtil.toEpochMilli(startTime);
        String url = "http://" + dataSourceIp + ":9882/api/db/Device/GetHistoricalConditions";
        Map<String,Object> requestBody = new HashMap<>();
        requestBody.put("deviceId", rootLinkDeviceId);
        requestBody.put("endTimestamp", timestamp);
        requestBody.put("pageIndex", 2);
        requestBody.put("pageSize", 10);
        String signKey = "fbc660a1d085438a807c4137ff1e3315";
        String origin = requestBody + "&" + timestamp + "&" + signKey;
        String sign = DigestUtils.md5DigestAsHex(origin.getBytes(StandardCharsets.UTF_8));
        HttpResponse httpResponse = cn.hutool.http.HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("rootlink_language", "zh-CN")
                .header("rootlink_sign", sign)
                .header("rootlink_timestamp", String.valueOf(timestamp))
                .header("TOKEN", getLoginToken())
                .body(JSONObject.toJSONString(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根连工况查询失败，IP=" + dataSourceIp);
        }

        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        if (Integer.parseInt(responseBody.get("code").toString()) == 500){
            return null;
        }
        Map data = JSONObject.parseObject(JSONObject.toJSONString(responseBody.get("data")), Map.class);
        List<Map> items = JSONObject.parseArray(JSONObject.toJSONString(data.get("items")), Map.class);
        List<Map<String,Object>> list = new ArrayList<>();
        items.forEach(m->{
            Map<String,Object> map = new HashMap<>();
            long ts = Long.parseLong(m.get("ts").toString());
            map.put("ts",ts);
            JSONObject addresses = (JSONObject) m.get("addresses");
            for (Map.Entry<String, Object> stringObjectEntry : addresses.entrySet()) {
                JSONObject value = (JSONObject)stringObjectEntry.getValue();
                    map.put(String.valueOf(stringObjectEntry.getKey()),value.get("v"));
            }

            list.add(map);
        });
        return list;
    }

    public List<Map<String,Object>> selectRootCloudWorkDatas(String thingId, String modelId,List<String> pointList) {
        LocalDateTime startTimeArg = LocalDateTime.now();
        LocalDateTime startTime = startTimeArg.minusMinutes(1);
        String url = String.format("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn/historian-manage/v1/historian/models/%s/things/%s", modelId, thingId);
        Map<String,Object> requestBody = new HashMap<>();
        requestBody.put("startTime", startTime.toString());
        requestBody.put("endTime", startTimeArg.toString());
//        requestBody.put("properties", "all");//JSONObject.toJSONString(pointList)
        requestBody.put("sort", "DESC");
        requestBody.put("limit", 20);
        requestBody.put("skip", 0);
        HttpResponse httpResponse = cn.hutool.http.HttpRequest.post(url)
                .header("Authorization", "Bearer " + getLoginToken())
                .body(JSONObject.toJSONString(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根云设备历史工况查询失败，deviceId：" + thingId);
        }
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        JSONArray payload = responseBody.getJSONArray("payload");

        List<Map<String,Object>> rootCloudWorkDatas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(payload)) {
            Map map = JSONObject.parseObject(JSONObject.toJSONString(payload.get(0)), Map.class);
            List<String> columns = JSONObject.parseArray(JSONObject.toJSONString(map.get("columns")),String.class);
            List rows = JSONObject.parseArray(JSONObject.toJSONString(map.get("rows")));
            for (Object datas : rows) {
                Map<String,Object> workData = new HashMap<>();
                List<String> data = JSONObject.parseArray(JSONObject.toJSONString(datas),String.class);
                long ts = Instant.parse(data.get(0)).toEpochMilli();
                workData.put("timestamp", ts);
                for (int j = 0; j < data.size(); j++) {
                    workData.put(columns.get(j), data.get(j));
                }
                rootCloudWorkDatas.add(workData);
            }

        }
        return rootCloudWorkDatas;
    }
}
