package com.rc.admin.easyapi.job;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rc.admin.easyapi.service.dqmService.OrsBigdataEquipmentBaseInfoAllService;
import com.rc.admin.ors.quality.dao.*;
import com.rc.admin.ors.quality.entity.DeviceDataAbnormalDetailDay;
import com.rc.admin.ors.quality.entity.OrcTaskFile;
import com.rc.admin.ors.quality.entity.OrsDeviceDataAbnormalDetail;
import com.rc.admin.ors.quality.model.DeviceLocationResp;
import com.rc.admin.ors.quality.model.UnReportDevice;
import com.rc.admin.ors.quality.service.OrsDeviceDataAbnormalDetailService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:05
 * @describe 同步异常数据
 */
@EnableScheduling
@Component
@Slf4j
public class SyncAbnormalData {

    @Resource
    private OrsDeviceDataAbnormalDetailMapper orsDeviceDataAbnormalDetailMapper;

    @Resource
    private OrsDeviceDataAbnormalDetailService orsDeviceDataAbnormalDetailService;

    @Resource
    private OrsSyncDeviceService orsSyncDeviceService;

    @Resource
    private DeviceParamReportLogMapper deviceParamReportLogMapper;

    @Resource
    private OrsBigdataEquipmentBaseInfoAllMapper orsBigdataEquipmentBaseInfoAllMapper;

    @Resource
    private OrsCoreParamStatLatestMapper orsCoreParamStatLatestMapper;

    @Resource
    private OrsBigdataEquipmentBaseInfoAllService infoAllService;

    @Resource
    private DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;

    @Resource
    private SanydsCoreParamAbnormalDetailMapper sanydsCoreParamAbnormalDetailMapper;

    private static final String UTC_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final String BJ_TIMEFORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String BJ_DATEFORMAT = "yyyy-MM-dd";

    private final SimpleDateFormat sdf = new SimpleDateFormat(BJ_TIMEFORMAT);


    private ExecutorService executors = Executors.newFixedThreadPool(5);

    private final int pageSize = 5000;

    @Value("${spring.profiles.active}")
    private String activeEnv;

    public void syncAbnormalData_new(Date bizDate){
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            orsDeviceDataAbnormalDetailMapper.deleteSanyDataService(bizDate);
        });

        orsDeviceDataAbnormalDetailMapper.syncAbnormalData(bizDate);
        Integer abnormalDetailCnt = orsDeviceDataAbnormalDetailMapper.getAbnormalDetailCnt(bizDate);
        Date currentTime = new Date();
        SimpleDateFormat bjTimeformat = new SimpleDateFormat(BJ_TIMEFORMAT);
        String current = bjTimeformat.format(currentTime);
        FlowJob.statis_log.add("dqm.ors_device_data_abnormal_detail="+abnormalDetailCnt + ", 当前时间" + current);
        log.info("=====================3、同步异常明细数据:{}条, 当前时间{}",abnormalDetailCnt, current);
        //orsDeviceDataAbnormalDetailMapper.updataAbnormalEffective(bizDate);
        try {
            orsDeviceDataAbnormalDetailMapper.updataAbnormalEffectiveOne(bizDate);
            orsDeviceDataAbnormalDetailMapper.updataAbnormalEffectiveTwo(bizDate);
            orsDeviceDataAbnormalDetailMapper.updataAbnormalEffectiveThree(bizDate);
        }catch (Exception e){
            log.info("更新effective 报错：",e.getMessage(),e);
        }
        log.info("=====================3.1、更新位置异常数据，是否小于位置工况总数的1%,小于1%的数据abnormal_effective设置为0");

    }


    /**
     * 同步异常数据，每天凌晨4点同步数据，底表每天凌晨3点同步，这里延后一个小时
     */
//    @Scheduled(fixedRate = 50000)
    public void syncAbnormalData(Date bizDate){
        log.info("开始同步异常数据，当前环境{}", activeEnv);
        if ("prod".equals(activeEnv)) {
//            Date now = new Date();
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(now);
//            calendar.add(Calendar.DAY_OF_YEAR, -1);
//            Date yesterday = calendar.getTime();
            Integer count = sanydsCoreParamAbnormalDetailMapper.countAbnormalDevice(bizDate);
            JSONObject json = new JSONObject();
            json.put("msgtype", "markdown");

            SimpleDateFormat df = new SimpleDateFormat(BJ_DATEFORMAT);
            String s = df.format(bizDate);

            JSONObject text = new JSONObject();
            String msg = "";
            if (null == count || count == 0) {
                msg = "<font color=\"red\">【警告】</font>\n"+s+"日没有任何工况异常上报。";
            }
            if (null != count && count > 0) {
                msg = "<font color=\"green\">【温馨提示】</font>\n"+s+"日共有"+ count +"台设备上报异常工况。";
                orsDeviceDataAbnormalDetailMapper.syncAbnormalData(bizDate);
            }
            text.put("mentioned_mobile_list", Collections.singletonList("@all"));
            text.put("content", msg);

            json.put("markdown", text);

            String cpwechatUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b0358386-a64c-44bb-9d61-df5c6e20399b";
            HttpUtil.post(cpwechatUrl, json.toJSONString());
        } else {
            orsDeviceDataAbnormalDetailMapper.syncAbnormalData(bizDate);
        }
        Integer abnormalDetailCnt = orsDeviceDataAbnormalDetailMapper.getAbnormalDetailCnt(bizDate);
        FlowJob.statis_log.add("dqm.ors_device_data_abnormal_detail count="+abnormalDetailCnt);
        log.info("3、同步异常明细数据:{}条",abnormalDetailCnt);
        orsDeviceDataAbnormalDetailMapper.updataAbnormalEffective(bizDate);

    }

    /*
        小时表(sany_data_service.sanyds_device_hour_work_cnt)的数据汇总到天表(sany_data_service.sanyds_device_work_cnt)
     */
    public void aggDeviceHourWorkCnt(Date bizDate) {
        orsBigdataEquipmentBaseInfoAllMapper.aggDeviceHourWorkCnt(bizDate);
    }
    public Integer getDeviceWorkCnt(Date bizDate) {
        return orsBigdataEquipmentBaseInfoAllMapper.getDeviceWorkCnt(bizDate);
    }

    /**
     * 根据物模型检查已有异常数据，并补偿未上报的数据,这里每天5.30执行，这里只补偿已上报设备的属性，且是从未上报过的属性，某属性只要又一次上报，后续不再补偿。
     * 此任务一定要确保在异常数据同步{@link com.rc.admin.easyapi.job.SyncAbnormalData#syncAbnormalData}之后再执行
     */
//    @Scheduled(fixedDelay = 50000)
    public void checkAbnormalData(Date bizDate) throws ParseException {

//        Date now = new Date();
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(now);
//        calendar.add(Calendar.DAY_OF_YEAR, -1);
//        Date yesterday = calendar.getTime();
        //2024-05-27
        // 根据已有的设备台账信息，在已上报的设备中，还有哪些属性是从未上报工况的设备及其属性
        // 但是这里有个问题可能dqm.ors_core_param_stat_latest不存在设备属性数据,但是在 dqm.ors_device_data_abnormal_detail里面有产生该属性的异常
        // 那么就会导致生成一个错误从未上报异常，处理方式在ors_device_data_abnormal_detail_day任务里面删除从未上报异常
        List<UnReportDevice> data = orsCoreParamStatLatestMapper.findUnreportUnbnormalData(bizDate);

        log.info("=====================4、属性从未上报{}条",data.size());
        OrsDeviceDataAbnormalDetail detail;
        List<OrsDeviceDataAbnormalDetail> detailes = new ArrayList<>();
        int a = 1;
        SimpleDateFormat df = new SimpleDateFormat(BJ_DATEFORMAT);
        String s = df.format(bizDate);

        //先清空未上报的设备属性进行补偿的数据
        orsCoreParamStatLatestMapper.removeByDate(s,9008);

        s = s + " 23:59:59";
        SimpleDateFormat timeFormat = new SimpleDateFormat(BJ_TIMEFORMAT);
        Date parse = timeFormat.parse(s);
        // 对未上报的设备属性进行补偿
        for (UnReportDevice device : data) {
            detail = new OrsDeviceDataAbnormalDetail();

            UUID uuid = UUID.randomUUID();
            detail.setDetailId(uuid.toString());

            detail.setUuid(device.getThingId());
            detail.setModelName(device.getModelName());
            detail.setModelId(device.getModelId());
            detail.setDeviceName(device.getAssetId());
            detail.setAbnormalTime(parse);
            detail.setAbnormalName("属性值从未上报");
            detail.setDivisionCode(device.getDivisionCode());
            detail.setDataCenterId(device.getDataCenterId());
            detail.setParamCode(device.getParamCode());
            detail.setStatDate(bizDate);
            detail.setAbnormalCode(9008);
//            detail.setTenantId(map.get(device.getModelId()));

            // 处理属性值，
            String desc = device.getDictDesc();
            String[] split = desc.split("[:（]");
            detail.setPropertyName(split[0]);
            detail.setProperty(split[split.length - 1]);

            detail.setLastPt("0");
            detail.setLastPv("0");
            detail.setCurPv("0");
            detail.setCurPt("0");
            detail.setPtInc("0");
            detail.setPvInc("0");
            detail.setCreateTime(new Date());
            detailes.add(detail);

            // 批量保存，每次保存pageSize条数据
            if (a == pageSize) {
                orsDeviceDataAbnormalDetailService.saveBatch(detailes);
                a = 1;
                detailes = new ArrayList<>();
            } else {
                a++;
            }
        }

        // 保存最后一部分数据
        if (!detailes.isEmpty()) {
            orsDeviceDataAbnormalDetailService.saveBatch(detailes);
        }
        try {
            handleLongTimeNoReport(bizDate);
        }catch (Exception e){
            log.info("属性长期未上报运行错误：{}",e.getMessage(),e);
        }
        // 异步执行
//        Executors.newCachedThreadPool().submit(()->{
        // 将明细表数据抽象成天表数据
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        String date = dateFormat.format(bizDate);
//        List<Map<String, String>> list = null;
//        Date parse1 = null;
//        try {
//            parse1 = dateFormat.parse(date);
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }


        //上报工况的设备
//        orsBigdataEquipmentBaseInfoAllMapper.ors_up_work_device(bizDate);

        // 根据处理后的异常底表抽象异常天表，即每设备每天只保留一条数据
        orsBigdataEquipmentBaseInfoAllMapper.ors_device_data_abnormal_detail_day(bizDate);
        Integer abnormalDetailDayCnt = orsBigdataEquipmentBaseInfoAllMapper.getAbnormalDetailDayCnt(bizDate);
        Date currentTime = new Date();
        SimpleDateFormat bjTimeformat = new SimpleDateFormat(BJ_TIMEFORMAT);
        String current = bjTimeformat.format(currentTime);
        FlowJob.statis_log.add("dqm.ors_device_data_abnormal_detail_day="+abnormalDetailDayCnt + ", 当前时间" + current);
        log.info("=====================5、abnormalDetailDayCnt {}条, 当前时间{}",abnormalDetailDayCnt, current);
        // 根据处理后的异常底表统计某属性的异常次数
        orsBigdataEquipmentBaseInfoAllMapper.ors_device_data_abnormal_stat_day(bizDate);
        Integer abnormalStatDayCnt = orsBigdataEquipmentBaseInfoAllMapper.getAbnormalStatDayCnt(bizDate);
        currentTime = new Date();
        current = bjTimeformat.format(currentTime);
        FlowJob.statis_log.add("dqm.ors_device_data_abnormal_stat_day="+abnormalStatDayCnt + ", 当前时间" + current);
        log.info("=====================6、abnormalStatDayCnt {}条, 当前时间{}",abnormalStatDayCnt, current);

//        log.info("5、开始transferAbnormalDataToDay============");
        // 根据处理后的异常底表抽象异常天表，即每设备每天只保留一条数据
        // 根据处理后的异常底表统计某属性的异常次数
//        orsBigdataEquipmentBaseInfoAllMapper.transferAbnormalDataToDay(parse1);
//        });
//        log.info("6、开始generateReportLog============");
        // 按天生成设备工况上报记录日志
        //generateReportLog();
    }


    /**
     * 处理属性长期未上报
     * @return
     */
    public List<OrsDeviceDataAbnormalDetail> handleLongTimeNoReport(Date bizDate) throws ParseException{
        List<OrsDeviceDataAbnormalDetail> details = CollUtil.newArrayList();
        List<UnReportDevice> data = orsCoreParamStatLatestMapper.findLongTimeNoReport(bizDate);
        log.info("=====================4、属性长期未上报{}条",data.size());

        Map<String, DeviceLocationResp> collectMap
                = orsCoreParamStatLatestMapper.getDeviceLocationResp(bizDate).stream().collect(Collectors.toMap(DeviceLocationResp::getDeviceName, i -> i, (k1, k2) -> k1));

        List<OrsDeviceDataAbnormalDetail> detailes = new ArrayList<>();
        int a = 1;
        SimpleDateFormat df = new SimpleDateFormat(BJ_DATEFORMAT);
        String s = df.format(bizDate);

        //先清空未上报的设备属性进行补偿的数据
        orsCoreParamStatLatestMapper.removeByDate(s,9009);

        s = s + " 23:59:59";
        SimpleDateFormat timeFormat = new SimpleDateFormat(BJ_TIMEFORMAT);
        Date parse = timeFormat.parse(s);
        // 对未上报的设备属性进行补偿
        for (UnReportDevice device : data) {
            OrsDeviceDataAbnormalDetail detail = new OrsDeviceDataAbnormalDetail();

            UUID uuid = UUID.randomUUID();
            detail.setDetailId(uuid.toString());

            detail.setUuid(device.getThingId());
            detail.setModelName(device.getModelName());
            detail.setModelId(device.getModelId());
            detail.setDeviceName(device.getAssetId());
            detail.setAbnormalTime(parse);
            detail.setDivisionCode(device.getDivisionCode());
            detail.setDataCenterId(device.getDataCenterId());
            detail.setAbnormalName("属性值长期未上报");
            detail.setParamCode(device.getParamCode());

            //异常数据 {最后上报时间：XX，属性值：XX，未上报持续天数：XX天}
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode json = mapper.createObjectNode();

            json.put("最后上报时间",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(device.getParamValueLatestTime()));
            //如果是设备位置
            if( 8501 == device.getParamCode()){
                if(collectMap.containsKey(device.getAssetId())){
                    json.put("属性值",collectMap.get(device.getAssetId()).getKeyValue());
                }
            }else{
                json.put("属性值",device.getParamValue());
            }
            //json.put("未上报持续天数", String.format("%d天", device.getDeviceNameCount()));

            String jsonStr = json.toString();
            detail.setAbnormalData(jsonStr);

            detail.setStatDate(bizDate);
            detail.setAbnormalCode(9009);

            // 处理属性值，
            String desc = device.getDictDesc();
            String[] split = desc.split("[:（]");
            detail.setPropertyName(split[0]);
            detail.setProperty(split[split.length - 1]);
            detail.setLastPt("0");
            detail.setLastPv("0");
            detail.setCurPv("0");
            detail.setCurPt("0");
            detail.setPtInc("0");
            detail.setPvInc("0");
            detail.setCreateTime(new Date());
            detailes.add(detail);
        }
        if(CollUtil.isNotEmpty(detailes)){
            orsDeviceDataAbnormalDetailService.saveBatch(detailes);
        }
        return details;
    }



    /**
     * 异常是护具抽象到天表，已废弃，功能已迁移到 {@link com.rc.admin.easyapi.job.SyncAbnormalData#checkAbnormalData}
     * @throws Exception
     */
//    @Scheduled(cron = "0 */5 * * * ?")
    public void syncAbnormalDetailsToDayInfo() throws Exception {
        // 将明细表数据抽象成天表数据
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -1);
        String date = dateFormat.format(calendar.getTime());
        List<Map<String, String>> list = orsBigdataEquipmentBaseInfoAllMapper.queryMergeDetails(dateFormat.parse(date));
        if(list != null && list.size() > 0){
            int size = 200;
            int page = list.size()%size==0?list.size()/size:list.size()/size+1;
            for (int i = 0; i < page; i++) {
                List<Map<String, String>> mapList = list.stream().skip(i * size).limit(size).collect(Collectors.toList());
                orsBigdataEquipmentBaseInfoAllMapper.syncDetailsToDay(mapList);
            }
            infoAllService.statDeviceHistoryException(dateFormat.parse(date));
        }
    }

    /**
     * 生成设备上报记录
     */
    public void generateReportLog(){
        deviceParamReportLogMapper.generateReportLog(null);
        // 查询当天有上报工况的设备信息
//        List<UnReportDevice> reportDevice = orsCoreParamStatLatestMapper.getTodayReportDevice();

        // 根据上一步有上报工况的设备，查询出对应的异常信息
//        if (!reportDevice.isEmpty()) {
//            List<String> devices = reportDevice.stream().map(UnReportDevice::getDeviceName).distinct().collect(Collectors.toList());
//            List<OrsDeviceDataAbnormalDetail> list = orsDeviceDataAbnormalDetailService.list(
//                    new QueryWrapper<OrsDeviceDataAbnormalDetail>()
//                            .lambda()
//                            .in(OrsDeviceDataAbnormalDetail::getDeviceName, devices)
//                            .apply("stat_date = CURRENT_DATE - INTERVAL '1 day'")
//            );
//
//            Map<String, List<UnReportDevice>> collect = reportDevice.stream().collect(Collectors.groupingBy(UnReportDevice::getDeviceName));
//            Map<String, List<OrsDeviceDataAbnormalDetail>> map = list.stream().collect(Collectors.groupingBy(OrsDeviceDataAbnormalDetail::getDeviceName));
//            Date cur = new Date();
//            collect.forEach((k, v)->{
//                DeviceParamReportLog log = new DeviceParamReportLog();
//                log.setAssetId(k);
//                log.setCreateTime(cur);
//
//                // 获得上报的工况属性
//                String properyNames = v.stream().map(UnReportDevice::getPropertyName).distinct().collect(Collectors.joining(","));
//                String paramCodes = v.stream().map(x -> String.valueOf(x.getParamCode())).distinct().collect(Collectors.joining(","));
//                log.setParamCodes(paramCodes);
//                log.setPropertyNames(properyNames);
//
//                List<OrsDeviceDataAbnormalDetail> abnormalDetails = map.get(k);
//                if (null != abnormalDetails && !abnormalDetails.isEmpty()) {
//                    // 获得异常属性，不包含未上报的
//                    Stream<OrsDeviceDataAbnormalDetail> stream = abnormalDetails.stream().filter(x -> x.getAbnormalCode() != 9008);
//                    String abnormalProperyNames = stream.map(OrsDeviceDataAbnormalDetail::getPropertyName).distinct().collect(Collectors.joining(","));
//                    String abnormalParamCodes = stream.map(x -> String.valueOf(x.getParamCode())).collect(Collectors.joining(","));
//                    log.setAbnormalProperties(abnormalProperyNames);
//                    log.setAbnormalParamCodes(abnormalParamCodes);
//
//                    // 获得未上报的属性
//                    stream = abnormalDetails.stream().filter(x -> x.getAbnormalCode() == 9008);
//                    abnormalProperyNames = stream.map(OrsDeviceDataAbnormalDetail::getPropertyName).distinct().collect(Collectors.joining(","));
//                    abnormalParamCodes = stream.map(x -> String.valueOf(x.getParamCode())).collect(Collectors.joining(","));
//                    log.setNullProperties(abnormalProperyNames);
//                    log.setNullParamCodes(abnormalParamCodes);
//                }
//                UnReportDevice c = v.get(0);
//                log.setReportDate(c.getParamValueLatestTime());
//                log.setDivision(c.getDivision());
//                log.setDivisionCode(c.getDivisionCode());
//                log.setProductGroup(c.getProductGroup());
//                log.setProductGroupCode(c.getProductGroupCode());
//                deviceParamReportLogService.save(log);
//            });
//        }
    }

    /**
     * 清理数据 一次性使用
     */
    public void clearData(){
        List<DeviceDataAbnormalDetailDay> detailDays = deviceDataAbnormalDetailDayMapper.clearData();
        Map<Date, List<DeviceDataAbnormalDetailDay>> map = detailDays.stream().collect(Collectors.groupingBy(DeviceDataAbnormalDetailDay::getStatDate));
        map.forEach((k, v)->{
            List<String> collect = v.stream().map(DeviceDataAbnormalDetailDay::getDeviceName).distinct().collect(Collectors.toList());
            deviceDataAbnormalDetailDayMapper.delete(
                    new QueryWrapper<DeviceDataAbnormalDetailDay>()
                            .lambda()
                            .eq(DeviceDataAbnormalDetailDay::getStatDate, k)
                            .in(DeviceDataAbnormalDetailDay::getDeviceName, collect)
            );
        });
    }
    @Resource
    NativeSql nativeSql;
    @Resource
    private OrcTaskFileMapper orcTaskFileMapper;
    public void execOrcTaskFile(){
        OrcTaskFile orcTaskFile = orcTaskFileMapper.selectOne(new QueryWrapper<OrcTaskFile>()
                .eq("task_status", 1));
        if(orcTaskFile == null || orcTaskFile.getTaskId() == null)
        {
            log.info("无sql任务执行");
            return ;
        }
        log.info("开始执行sql任务:{}",orcTaskFile.getTaskId());

        LambdaUpdateWrapper<OrcTaskFile> orcTaskFileLambdaUpdateWrapper =
                new UpdateWrapper<OrcTaskFile>().lambda();
        orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getStartTime,new Date());
        orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getTaskStatus,2);
        orcTaskFileLambdaUpdateWrapper.eq(OrcTaskFile::getTaskId,orcTaskFile.getTaskId());
        orcTaskFileMapper.update(null,orcTaskFileLambdaUpdateWrapper);

        if("xlsx".equals(orcTaskFile.getResultFileType()))
        {
            ByteArrayOutputStream outputStream = null;
            Workbook workbook = null;
            try
            {
//                SqlRunner runner = new SqlRunner(sqlSessionFactory.openSession().getConnection());
//                List<Object> result = runner.selectList(sql);

//                List<Map<String, Object>> dataList = SqlRunner.db().selectList(orcTaskFile.getExecSql());
                List<LinkedHashMap<String, Object>> list = nativeSql.nativeSql(orcTaskFile.getExecSql());
                log.info("执行sql任务:{},查询结果数量:{}",orcTaskFile.getTaskId(),list.size());
                byte[] bytes = null;
                if(list.size() > 0)
                {
                    // 创建Excel表头
                    List<ExcelExportEntity> entityList = new ArrayList<>();

                    LinkedHashMap<String, Object> stringObjectMap = list.get(0);
                    for (String key : stringObjectMap.keySet())
                    {
                        entityList.add(new ExcelExportEntity(key, key ));
                    }

                    workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "Sheet1"),entityList,list);
//                    saveWorkbookToFile(workbook,"D:\\aaa.xlsx");
                    outputStream = new ByteArrayOutputStream();
                    workbook.write(outputStream);
                    bytes = outputStream.toByteArray();
                }
                log.info("执行sql任务:{},excel生成成功",orcTaskFile.getTaskId());

                orcTaskFileLambdaUpdateWrapper =
                        new UpdateWrapper<OrcTaskFile>().lambda();
                orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getEndTime,new Date());
                orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getTaskStatus,0);
                if(bytes != null)
                {
                    orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getResultFile,bytes);
                    orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getResultSize,bytes.length);
                }
                orcTaskFileLambdaUpdateWrapper.eq(OrcTaskFile::getTaskId,orcTaskFile.getTaskId());
                orcTaskFileMapper.update(null,orcTaskFileLambdaUpdateWrapper);
                log.info("执行sql任务:{},更新任务状态成功",orcTaskFile.getTaskId());
            }
            catch (Exception e)
            {
                log.error("执行sql任务错误：",e);
                orcTaskFileLambdaUpdateWrapper =
                        new UpdateWrapper<OrcTaskFile>().lambda();
                orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getEndTime,new Date());
                orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getTaskStatus,3);
                orcTaskFileLambdaUpdateWrapper.set(OrcTaskFile::getExecError,getExceptionMessage(e));
                orcTaskFileLambdaUpdateWrapper.eq(OrcTaskFile::getTaskId,orcTaskFile.getTaskId());
                orcTaskFileMapper.update(null,orcTaskFileLambdaUpdateWrapper);
                try
                {
                    if(outputStream != null )
                    {
                        outputStream.close();
                    }
                    if(workbook != null)
                    {
                        workbook.close();
                    }
                } catch (Exception ex) {
                    log.error("执行sql任务错误：",ex);

                    ex.printStackTrace();
                    throw new RuntimeException(ex);

                }
                throw new RuntimeException(e);
            }
        }
    }

    public static void saveWorkbookToFile(Workbook workbook, String filePath) {
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public static String getExceptionMessage(Exception e) {
        StringWriter sw = null;
        PrintWriter pw = null;
        try {
            sw = new StringWriter();
            pw = new PrintWriter(sw);
            // 将出错的栈信息输出到printWriter中
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
        } finally {
            if (sw != null) {
                try {
                    sw.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (pw != null) {
                pw.close();
            }
        }
        return sw.toString();
    }

    public static void main(String[] args) {
        String s = "[this_pv:818.600000000, this_pt:1698309735065, last_pv:817.500000000, last_pt:1698309675065, pv_inc:-1.100000000, pt_inc:60000, flag:true]";
        s = s
                .replace("[", "[{\"")
                .replace("]", "\"}]")
                .replace(",", "\"},{\"")
                .replace(":", "\":\"")
                .replace(" ", "")
        ;
        JSONArray objects = JSONObject.parseArray(s);
        System.out.println(objects.size());
    }
}
