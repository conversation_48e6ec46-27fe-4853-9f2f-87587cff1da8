package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@TableName(value = "orc_iot_model_abnormal_bit_info")
@ApiModel(value = "orc_iot_model_abnormal_bit_info对象", description = "模型异常位键值明细表")
public class IotModelAbnormalBitInfo {
    @TableField(value = "model_id")
    @ApiModelProperty(value = "模型id")
    private String modelId;
    @TableField(value = "model_name")
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    @TableField(value = "model_desc")
    @ApiModelProperty(value = "模型描述")
    private String modelDesc;
    @TableField(value = "bit_key")
    @ApiModelProperty(value = "bit位键值")
    private String bitKey;
    @TableField(value = "bit_desc")
    @ApiModelProperty(value = "bit位键值描述")
    private String bitDesc;
    @TableField(value = "bit_index")
    @ApiModelProperty(value = "bit位")
    private Integer bitIndex;
    @TableField(value = "max_value")
    @ApiModelProperty(value = "最大值")
    private String maxValue;
    @TableField(value = "min_value")
    @ApiModelProperty(value = "最小值")
    private String minValue;
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "dict_id")
    @ApiModelProperty(value = "")
    private Integer dictId;

}
