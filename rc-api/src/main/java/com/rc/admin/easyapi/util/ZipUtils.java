package com.rc.admin.easyapi.util;

import cn.hutool.core.io.FileUtil;
import com.anji.captcha.util.StringUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class ZipUtils {
    /**
     * 解读zip文件
     *
     * @param inputStream    压缩文件流
     * @param suffixType 文件后缀（非空时只处理固定后缀的文件）
     * @return 处理结果
     * @throws IOException
     */
    public static List<InputStream> readZipToInputStreamList(InputStream inputStream, String suffixType) throws IOException {
        List<InputStream> list = new ArrayList<>();
        //判断文件是否存在
//        if (!zipFile.exists()) {
//          return null;
//        }
        //获取文件流
//        InputStream inputStream = FileUtil.getInputStream(zipFile);
        //转化文件流为压缩文件流
        ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("gbk"));
        ZipEntry zipEntry;
        while ((zipEntry = zipInputStream.getNextEntry()) != null) {
            //如果文件后缀条件不为空且后缀条件不符则跳过文件读取
            if (StringUtils.isNotBlank(suffixType) && !zipEntry.getName().endsWith(suffixType)) {
                continue;
            }

            //文件读取处理
            byte[] buffer = new byte[1024];
            int len;
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            while ((len = zipInputStream.read(buffer)) != -1) {
                byteStream.write(buffer, 0, len);
            }
            // 关闭流
            byteStream.close();

            //读取的文件转为所需的流添加到集合中
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteStream.toByteArray());
            list.add(byteArrayInputStream);
        }
        return list;
    }

//    /**
//     * 解读zip文件
//     *
//     * @param filePath   压缩文件路径
//     * @param suffixType 文件后缀（非空时只处理固定后缀的文件）
//     * @return 处理结果
//     * @throws IOException
//     */
//    public static List<InputStream> readZipToInputStreamList(String filePath, String suffixType) throws IOException {
//        File zipFile = new File(filePath);
//        return readZipToInputStreamList(zipFile, suffixType);
//    }
}
