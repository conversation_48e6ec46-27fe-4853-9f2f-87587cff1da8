package com.rc.admin.easyapi.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PointResultListReq {
    @ApiModelProperty(value = "数采平台")
    private  String region;
    @ApiModelProperty(value = "子公司名称")
    private  String childCompanyName;
    @ApiModelProperty(value = "设备编号/设备名称")
    private  String deviceCode;
    @ApiModelProperty(value = "点位名称")
    private  String destAddressName;
    @ApiModelProperty(value = "描述")
    private  String description;
    @ApiModelProperty(value = "页码")
    private  int current;
    @ApiModelProperty(value = "条数")
    private  int pageSize;
    @ApiModelProperty(value = "字段")
    private  String sortField;
    @ApiModelProperty(value = "排序类型")
    private  String sortOrder;
}
