package com.rc.admin.easyapi.service.impl;

import cn.hutool.core.lang.Validator;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.dao.IotDeviceInfoMapper;
import com.rc.admin.easyapi.model.req.IotDeviceInfoReq;
import com.rc.admin.easyapi.model.req.PointResultListReq;
import com.rc.admin.easyapi.model.resp.IotDeviceInfoResp;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.easyapi.service.IotDeviceInfoService;
import com.rc.admin.util.office.ExcelUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Log4j2
public class IotDeviceInfoServiceImpl implements IotDeviceInfoService {

    @Resource
    private IotDeviceInfoMapper rulesMapper;

    @Override
    public Page<IotDeviceInfoResp>  selectPages(IotDeviceInfoReq req) {
        Page<IotDeviceInfoResp> pageResp = new Page<>();
        if (req.getCurrent()<=0){
            req.setCurrent(1);
        }
        if (req.getPageSize()<=0){
            req.setPageSize(10);
        }
        if(Validator.isEmpty(req.getSortField())){
            req.setSortField("name");
            req.setSortOrder("asc");
        }
        pageResp.setCurrent(req.getCurrent());
        pageResp.setPageSize(req.getPageSize());
        int page = req.getCurrent();
        int size = req.getPageSize();
        req.setPageSize(size);
        req.setCurrent((page - 1) * size);
        int i = rulesMapper.selectPagesCount(req);
        if (i>0){
            List<IotDeviceInfoResp> rulesList = rulesMapper.selectPages(req);
            //获取自定义点位信息
            if ("1".equals(req.getSecondaryCalculation())) {
                List<Map<String, String>> destList = rulesMapper.getDestAddressNameList();
                Map<String, List<Map<String, String>>> device_code = destList.stream().collect(Collectors.groupingBy(stringStringMap -> stringStringMap.get("device_code")));
                if (!CollectionUtils.isEmpty(destList)) {
                    rulesList.parallelStream().forEach(iotDeviceInfoResp -> {
                        List<Map<String, String>> maps = device_code.get(iotDeviceInfoResp.getDeviceCode());
                        if (!CollectionUtils.isEmpty(maps)){
                            List<String> dest_address_name = maps.stream().map(stringStringMap -> stringStringMap.get("dest_address_name")).collect(Collectors.toList());
                        List<String> destAddressNameList = new ArrayList<>();
                        String express = iotDeviceInfoResp.getExpress();
                        if (StringUtils.isNotBlank(express)) {
                            dest_address_name.forEach(destAddressName -> {
                                if (express.contains(destAddressName)) {
                                    destAddressNameList.add(destAddressName);
                                }
                            });
                        }
                        iotDeviceInfoResp.setDestAddressNameList(destAddressNameList);
                    }
                    });
                }
            }
            pageResp.setSize(size);
            pageResp.setRecords(rulesList);
            pageResp.setTotal(i);
        }
        return pageResp;
    }

    @Override
    public List<Map<String,String>> getDeviceCodeList(String deviceCode) {
        return rulesMapper.getDeviceCodeList(deviceCode);
    }

    @Override
    public Page<PointResultListResp> getPointList(PointResultListReq dto) {
        Page<PointResultListResp> pageResp = new Page<>();
        if (dto.getCurrent()<=0){
            dto.setCurrent(1);
        }
        if (dto.getPageSize()<=0){
            dto.setPageSize(10);
        }
        if(Validator.isEmpty(dto.getSortField())){
            dto.setSortField("name");
            dto.setSortOrder("asc");
        }
        pageResp.setCurrent(dto.getCurrent());
        pageResp.setPageSize(dto.getPageSize());
        int page = dto.getCurrent();
        int size = dto.getPageSize();
        dto.setPageSize(size);
        dto.setCurrent((page - 1) * size);

        int pointListCount = rulesMapper.getPointListCount(dto);
        if (pointListCount>0){
            List<PointResultListResp> pointList = rulesMapper.getPointList(dto);
            fillUpDeviceInfo(pointList);
            pageResp.setRecords(pointList);

        }
        pageResp.setSize(size);
        pageResp.setTotal(pointListCount);

        return pageResp;
    }

    /**
     * 在返回的点位信息种填充对应设备的信息
     * @param pointList
     */
    private void fillUpDeviceInfo(List<PointResultListResp> pointList) {

        List<Map<String, String>> deviceList = this.getDeviceCodeList(null);
        if (CollectionUtils.isEmpty(deviceList)) {
            log.error("设备信息列表为空，请检查");
            return;
        }

        Map<String, Map<String, String>> deviceMap = new HashMap<>();
        deviceList.forEach(v -> deviceMap.put(v.get("deviceCode"), v));

        pointList.forEach(v -> v.setProtocolType(deviceMap.get(v.getDeviceCode()).get("protocolType")));
    }

    @Override
    public List<String> getChildCompanyNameList() {
        return rulesMapper.getChildCompanyNameList();
    }

    @Override
    public String pagePointDataExport(PointResultListReq req) {
        req.setCurrent(0);
        req.setPageSize(500000);
        List<PointResultListResp> pointList = rulesMapper.getPointList(req);
        return ExcelUtil.writeAndGetDownloadId("iot设备点位信息", "iot设备点位信息", pointList, PointResultListResp.class);
    }
}
