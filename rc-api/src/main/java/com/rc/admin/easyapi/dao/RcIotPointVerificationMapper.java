package com.rc.admin.easyapi.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.RcIotPointVerification;
import com.rc.admin.easyapi.model.resp.PointResp;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 数据核验标准模板表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface RcIotPointVerificationMapper extends BaseMapper<RcIotPointVerification> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<RcIotPointVerification>
     */
    List<RcIotPointVerification> select(Page<RcIotPointVerification> page, @Param("ew") QueryWrapper<RcIotPointVerification> queryWrapper);
    /**
     * 查询详细信息
     *
     * @param id id
     * @return RcIotPointVerification
     */
    RcIotPointVerification getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<RcIotPointVerification>
     */
    List<RcIotPointVerification> exportData(@Param("ew") QueryWrapper<RcIotPointVerification> queryWrapper);
    /**
     * 获取列表数据
     *
     * @return List<RcIotPointVerification>
     */
    List<RcIotPointVerification> queryData();
    /**
     * 获取点位列表
     *
     * @return List<Map<String,String>>
     */
    @Select("select DISTINCT ipv.second_process as secondProcess,idp.second_process_name as secondProcessName from  rc_iot_point_verification as ipv\n" +
            "left join rc_iot_device_profile as idp on ipv.second_process = idp.second_process ")
    List<Map<String,String>> getSecondProcess();

    /**
     * 插入点位模板
     */
    @Insert("<script> REPLACE INTO rc_iot_point_verification(" +
            "second_process,type,description,dest_address_name,remark,data_type,mode,acquisition_frequency,save_policy,data_registration,point_address,event_mode,expression,express,protocol_name,create_time,point_type) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.secondProcess},#{item.type},#{item.description},#{item.destAddressName},#{item.remark}, #{item.dataType}, #{item.mode}, #{item.acquisitionFrequency}, #{item.savePolicy}, #{item.dataRegistration}, #{item.pointAddress}, #{item.eventMode}, #{item.expression},#{item.express},#{item.protocolName},NOW(),#{item.pointType})" +
            "  </foreach> " +
            "</script>")
    void saveRcIotPointVerification(@Param("list") List<RcIotPointVerification> list);

    /**
     * 获取模板二级工艺编码
     */
    List<String> getVerificationSecondProcess();
    /**
     * 获取设备二级工艺编码
     */
    List<String> getDeviceSecondProcess();

    /**
     * 根据设备编号查询采集点位和自定义定位
     * @param deviceCode 设备编号
     * @return 点位集合
     */
    List<PointResp> selectOtherPoints(String deviceCode);

    /**
     * 获取点位
     */
    List<String> getBySecondProcess(@Param("secondProcess") String secondProcess);
}