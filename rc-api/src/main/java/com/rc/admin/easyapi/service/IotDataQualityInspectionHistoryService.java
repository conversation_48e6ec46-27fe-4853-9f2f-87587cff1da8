package com.rc.admin.easyapi.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.IotDataQualityInspectionHistory;

import java.util.List;

/**
 * <p>
 *     历史工况服务类.
 * </p>
 * <AUTHOR>
 * @since 2023-07-23
 */
public interface IotDataQualityInspectionHistoryService {

    /**
     * 获取实时工况历史检查列表
     * @param deviceCode 设备编号
     * @param pageNo 页码
     * @param pageSize 条数
     * @return 分页结果
     */
    Page<IotDataQualityInspectionHistory> selectHistoryData(String deviceCode, int pageNo, int pageSize);

    /**
     * 数据质量检查历史信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNo 页码
     * @param size 条数
     * @return
     */
    List<IotDataQualityInspectionHistory> pageHistoryData( String startTime, String endTime,int pageNo,int size);

    IotDataQualityInspectionHistory selectByCondition(IotDataQualityInspectionHistory iotDataQualityInspectionHistory);

}
