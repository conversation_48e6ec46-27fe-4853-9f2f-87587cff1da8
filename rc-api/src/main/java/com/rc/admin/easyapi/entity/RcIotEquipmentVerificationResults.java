package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 设备数据核验结果表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@TableName("rc_iot_equipment_verification_results")
public class RcIotEquipmentVerificationResults extends Model<RcIotEquipmentVerificationResults> {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 数采平台
     */
    @Excel(name = "数采平台", width = 15, orderNum = "0")
//    @NotBlank(message = "数采平台不能为空")
    private String region;
    /**
     * 二级工艺编码
     */
    @Excel(name = "二级工艺编码", width = 10, orderNum = "1")
//    @NotBlank(message = "二级工艺编码不能为空")
    private String secondProcess;
    /**
     * 协议名称
     */
    @Excel(name = "协议名称", width = 25, orderNum = "2")
    private String protocolName;
    /**
     * 设备名称
     */
    @Excel(name = "设备名称", width = 25, orderNum = "3")
//    @NotBlank(message = "设备名称不能为空")
    private String deviceName;
    /**
     * 设备编号
     */
    @Excel(name = "设备编号", width = 15, orderNum = "4")
//    @NotBlank(message = "设备编号不能为空")
    private String deviceCode;
    /**
     * 子公司
     */
    @Excel(name = "子公司", width = 25, orderNum = "5")
//    @NotBlank(message = "子公司不能为空")
    private String childCompanyName;
    /**
     * 点位类型
     */
    @Excel(name = "点位类型", width = 10, orderNum = "6")
//    @NotBlank(message = "点位类型不能为空")
    private String pointType;
    /**
     * 类型
     */
    @Excel(name = "类型", width = 10, orderNum = "7")
    private String type;
    /**
     * 核验汇总结果
     */
    @Excel(name = "类型", width = 10, orderNum = "8")
    private String verificationSumResult;
    /**
     * 模板-点位名称
     */
    @Excel(name = "模板-点位名称", width = 15, orderNum = "9")
    private String destAddressName;

    /**
     * 点位名称检查结果
     */
    @Excel(name = "点位名称检查结果", width = 15, orderNum = "10")
    private String destAddressNameResult;
    /**
     * 模板-描述
     */
    @Excel(name = "模板-描述", width = 25, orderNum = "11")
    private String description;
    /**
     * 描述检查结果
     */
    @Excel(name = "描述检查结果", width = 25, orderNum = "12")
    private String descriptionResult;
    /**
     * 模板-值类型
     */
    @Excel(name = "模板-值类型", width = 10, orderNum = "13")
    private String dataType;
    /**
     * 值类型检查结果
     */
    @Excel(name = "值类型检查结果", width = 10, orderNum = "14")
    private String dataTypeResult;
    /**
     * 模板-采集频率
     */
    @Excel(name = "模板-采集频率", width = 10, orderNum = "15")
    private String acquisitionFrequency;
    /**
     * 采集频率检查结果
     */
    @Excel(name = "采集频率检查结果", width = 10, orderNum = "16")
    private String acquisitionFrequencyResult;
    /**
     * 模板-点位地址
     */
    @Excel(name = "模板-点位地址", width = 12, orderNum = "17")
    private String pointAddress;
    /**
     * 点位地址
     */
    @Excel(name = "点位地址检查结果", width = 12, orderNum = "18")
    private String pointAddressResult;
    /**
     * 模板-触发方式
     */
    @Excel(name = "模板-触发方式", width = 25, orderNum = "19")
    private String eventMode;
    /**
     * 触发方式
     */
    @Excel(name = "触发方式检查结果", width = 25, orderNum = "20")
    private String eventModeResult;

    /**
     * 模板-点位值运算
     */
    @Excel(name = "模板-点位值运算", width = 25, orderNum = "21")
    private String express;
    /**
     * 点位值运算
     */
    @Excel(name = "点位值运算检查结果", width = 25, orderNum = "22")
    private String expressResult;
    /**
     * 逻辑表达式
     */
    @Excel(name = "逻辑表达式", width = 25, orderNum = "23")
    private String eventCondition;
    /**
     * 传输方式
     */
    @Excel(name = "传输方式", width = 5, orderNum = "24")
    private String mode;
    /**
     * 保存策略
     */
    @Excel(name = "保存策略", width = 5, orderNum = "25")
    private String savePolicy;
    /**
     * 数据等级
     */
    @Excel(name = "数据等级", width = 5, orderNum = "26")
    private String dataRegistration;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25, orderNum = "27")
    private String remark;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, orderNum = "28", exportFormat = "yyyy-MM-dd HH:mm:ss")
//    @NotNull(message = "创建时间不能为空")
    private Date createTime;

    // 非表字段
    /**
     * 创建时间 - 开始时间
     */
    @TableField(exist=false)
    private Date startCreateTime;
    /**
     * 创建时间 - 结束时间
     */
    @TableField(exist=false)
    private Date endCreateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public String getVerificationSumResult() {
        return verificationSumResult;
    }

    public void setVerificationSumResult(String verificationSumResult) {
        this.verificationSumResult = verificationSumResult;
    }

    public String getDestAddressName() {
        return destAddressName;
    }

    public void setDestAddressName(String destAddressName) {
        this.destAddressName = destAddressName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getAcquisitionFrequency() {
        return acquisitionFrequency;
    }

    public void setAcquisitionFrequency(String acquisitionFrequency) {
        this.acquisitionFrequency = acquisitionFrequency;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getPointAddressResult() {
        return pointAddressResult;
    }

    public void setPointAddressResult(String pointAddressResult) {
        this.pointAddressResult = pointAddressResult;
    }

    public String getEventMode() {
        return eventMode;
    }

    public void setEventMode(String eventMode) {
        this.eventMode = eventMode;
    }

    public String getEventModeResult() {
        return eventModeResult;
    }

    public void setEventModeResult(String eventModeResult) {
        this.eventModeResult = eventModeResult;
    }

    public String getExpress() {
        return express;
    }

    public void setExpress(String express) {
        this.express = express;
    }

    public String getExpressResult() {
        return expressResult;
    }

    public void setExpressResult(String expressResult) {
        this.expressResult = expressResult;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
    public String getSecondProcess() {
        return secondProcess;
    }

    public void setSecondProcess(String secondProcess) {
        this.secondProcess = secondProcess;
    }
    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
    public String getChildCompanyName() {
        return childCompanyName;
    }

    public void setChildCompanyName(String childCompanyName) {
        this.childCompanyName = childCompanyName;
    }
    public String getPointType() {
        return pointType;
    }

    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDestAddressNameResult() {
        return destAddressNameResult;
    }

    public void setDestAddressNameResult(String destAddressNameResult) {
        this.destAddressNameResult = destAddressNameResult;
    }
    public String getDescriptionResult() {
        return descriptionResult;
    }

    public void setDescriptionResult(String descriptionResult) {
        this.descriptionResult = descriptionResult;
    }
    public String getDataTypeResult() {
        return dataTypeResult;
    }

    public void setDataTypeResult(String dataTypeResult) {
        this.dataTypeResult = dataTypeResult;
    }

    public String getAcquisitionFrequencyResult() {
        return acquisitionFrequencyResult;
    }

    public void setAcquisitionFrequencyResult(String acquisitionFrequencyResult) {
        this.acquisitionFrequencyResult = acquisitionFrequencyResult;
    }
    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
    public String getSavePolicy() {
        return savePolicy;
    }

    public void setSavePolicy(String savePolicy) {
        this.savePolicy = savePolicy;
    }
    public String getDataRegistration() {
        return dataRegistration;
    }

    public void setDataRegistration(String dataRegistration) {
        this.dataRegistration = dataRegistration;
    }
    public String getEventCondition() {
        return eventCondition;
    }

    public void setEventCondition(String eventCondition) {
        this.eventCondition = eventCondition;
    }
    public String getPointAddress() {
        return pointAddress;
    }

    public void setPointAddress(String pointAddress) {
        this.pointAddress = pointAddress;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(Date startCreateTime) {
        this.startCreateTime = startCreateTime;
    }
    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }
}
