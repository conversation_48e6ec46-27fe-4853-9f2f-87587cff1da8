package com.rc.admin.easyapi.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.model.Rule;
import com.rc.admin.sys.dao.SysDictMapper;
import com.rc.admin.sys.model.SysDict;
import com.rc.admin.sys.service.SysDictService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrgAndProcessDictService {

    private static final String ORG_DICT_TYPE = "orgDict";
    private static final String PROCESS_DICT_TYPE = "processDict";
    private static final String ENABLED_STATUS = "1";

    private final SysDictMapper sysDictMapper;

    private final SysDictService sysDictService;

    @Autowired
    public OrgAndProcessDictService(SysDictService sysDictService, SysDictMapper sysDictMapper) {
        this.sysDictService = sysDictService;
        this.sysDictMapper = sysDictMapper;
    }

    public List<SysDict> selectOrgs() {
        return sysDictMapper.selectList(new QueryWrapper<SysDict>().lambda()
            .eq(SysDict::getDictType, ORG_DICT_TYPE)
            .isNull(SysDict::getParentCode)
            .eq(SysDict::getStatus, ENABLED_STATUS)
            .orderByAsc(SysDict::getId)
        );
    }

    public List<SysDict> selectCompanies(String orgCode) {
        return sysDictMapper.selectList(new QueryWrapper<SysDict>().lambda()
                .eq(SysDict::getDictType, ORG_DICT_TYPE)
                .eq(SysDict::getParentCode, orgCode)
                .eq(SysDict::getStatus, ENABLED_STATUS)
                .orderByAsc(SysDict::getId)
        );
    }

    public List<SysDict> selectFirstProcesses() {
        List<SysDict> firstProcesses = sysDictMapper.selectList(new QueryWrapper<SysDict>().lambda()
                .eq(SysDict::getDictType, PROCESS_DICT_TYPE)
                .isNull(SysDict::getParentCode)
                .eq(SysDict::getStatus, ENABLED_STATUS)
                .orderByAsc(SysDict::getCode)
        );
        SysDict sysDict = new SysDict();
        sysDict.setId("0");
        sysDict.setDictType(PROCESS_DICT_TYPE);
        sysDict.setCode(Rule.GENERAL_FIRST_PROCESS_CODE);
        sysDict.setName("通用");
        firstProcesses.add(0, sysDict);
        return firstProcesses;
    }

    public List<SysDict> selectSecondProcesses(String firstProcess) {
        return sysDictMapper.selectList(new QueryWrapper<SysDict>().lambda()
                .eq(SysDict::getDictType, PROCESS_DICT_TYPE)
                .eq(SysDict::getParentCode, firstProcess)
                .eq(SysDict::getStatus, ENABLED_STATUS)
                .orderByAsc(SysDict::getCode)
        );
    }

    public List<SysDict> selectSecondProcesses() {
        return sysDictMapper.selectList(new QueryWrapper<SysDict>().lambda()
                .eq(SysDict::getDictType, PROCESS_DICT_TYPE)
                .isNotNull(SysDict::getParentCode)
                .eq(SysDict::getStatus, ENABLED_STATUS)
                .orderByAsc(SysDict::getCode)
        );
    }

    @Async
    public void dealOrgAndProcessDict(List<DeviceProfile> deviceProfiles) {
        deviceProfiles.forEach(item -> {
            SysDict org = sysDictService.getDictByCode(ORG_DICT_TYPE, item.getOrgCode());
            if (org == null) {
                org = new SysDict();
                org.setDictType(ORG_DICT_TYPE);
                org.setCode(item.getOrgCode());
                org.setName(item.getOrgName());
                org.setStatus(ENABLED_STATUS);
                org.setOrderNo(1);
                sysDictService.saveData(org);
            }
            if (StringUtils.isNotBlank(item.getCompanyCode())) {
                SysDict company = sysDictService.getDictByCode(ORG_DICT_TYPE, item.getCompanyCode());
                if (company == null) {
                    company = new SysDict();
                    company.setDictType(ORG_DICT_TYPE);
                    company.setCode(item.getCompanyCode());
                    company.setName(item.getCompanyName());
                    company.setStatus(ENABLED_STATUS);
                    company.setOrderNo(1);
                    company.setParentCode(item.getOrgCode());
                    company.setParentName(item.getOrgName());
                    sysDictService.saveData(company);
                }
            }
            SysDict firstProcess = sysDictService.getDictByCode(PROCESS_DICT_TYPE, item.getFirstProcess());
            if (firstProcess == null) {
                firstProcess = new SysDict();
                firstProcess.setDictType(PROCESS_DICT_TYPE);
                firstProcess.setCode(item.getFirstProcess());
                firstProcess.setName(item.getFirstProcessName());
                firstProcess.setStatus(ENABLED_STATUS);
                firstProcess.setOrderNo(1);
                sysDictService.saveData(firstProcess);
            }
            if (StringUtils.isNotBlank(item.getSecondProcess())) {
                SysDict secondProcess = sysDictService.getDictByCode(PROCESS_DICT_TYPE, item.getSecondProcess());
                if (secondProcess == null) {
                    secondProcess = new SysDict();
                    secondProcess.setDictType(PROCESS_DICT_TYPE);
                    secondProcess.setCode(item.getSecondProcess());
                    secondProcess.setName(item.getSecondProcessName());
                    secondProcess.setStatus(ENABLED_STATUS);
                    secondProcess.setParentCode(item.getFirstProcess());
                    secondProcess.setParentName(item.getSecondProcessName());
                    secondProcess.setOrderNo(1);
                    sysDictService.saveData(secondProcess);
                }
            }
        });
    }

}
