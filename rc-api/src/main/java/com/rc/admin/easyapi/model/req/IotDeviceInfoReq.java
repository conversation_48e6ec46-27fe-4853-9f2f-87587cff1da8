package com.rc.admin.easyapi.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IotDeviceInfoReq  {

    @ApiModelProperty(value = "设备编号")
    private String deviceCode;
    @ApiModelProperty(value = "设备名称")
    private String name;
    @ApiModelProperty(value = "子公司名称")
    private String childCompanyName;
    @ApiModelProperty(value = "工厂")
    private String factory;
    @ApiModelProperty(value = "工作中心")
    private String workCenter;
    @ApiModelProperty(value = "班组")
    private String workGroup;
    @ApiModelProperty(value = "设备协议名称")
    private String protocolType;
    @ApiModelProperty(value = "通讯参数")
    private String paraItemValue;
    @ApiModelProperty(value = "站号")
    private String stationNumber;
    @ApiModelProperty(value = "扫描周期")
    private String scanIntervalTime;
    @ApiModelProperty(value = "链接超时")
    private String conTimeout;
    @ApiModelProperty(value = "重连延时")
    private String reconDelay;
    @ApiModelProperty(value = "自定义参数")
    private String customerParam;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "高频采集")
    private String collectionType;
    @ApiModelProperty(value = "点位名称")
    private String destAddressName;
    @ApiModelProperty(value = "触发方式")
    private String eventMode;
    @ApiModelProperty(value = "表达式")
    private String eventCondition;
    @ApiModelProperty(value = "点位运算")
    private String express;

    @ApiModelProperty(value = "页码")
    private int current;
    @ApiModelProperty(value = "条数")
    private int pageSize;
    @ApiModelProperty(value = "二次计算 1为 选中")
    private String secondaryCalculation;

    /**
     * 排序方式
     */
    private String sortOrder;

    /**
     * 排序字段
     */
    private String sortField;
}
