package com.rc.admin.easyapi.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.lang.Validator;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.easyapi.dao.RcIotPointVerificationMapper;
import com.rc.admin.easyapi.entity.IotCollectionPoint;
import com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults;
import com.rc.admin.easyapi.entity.RcIotPointVerification;
import com.rc.admin.easyapi.model.resp.PointResp;
import com.rc.admin.easyapi.service.RcIotPointVerificationService;
import com.rc.admin.easyapi.util.WorkbookUtil;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.util.ToolUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据核验标准模板表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class RcIotPointVerificationServiceImpl extends ServiceImpl<RcIotPointVerificationMapper, RcIotPointVerification> implements RcIotPointVerificationService, ImportService {


    private final RcIotPointVerificationMapper rcIotPointVerificationMapper;

    @Autowired
    public RcIotPointVerificationServiceImpl(RcIotPointVerificationMapper rcIotPointVerificationMapper) {
        this.rcIotPointVerificationMapper = rcIotPointVerificationMapper;
    }

    /**
     * 列表
     *
     * @param rcIotPointVerification 查询条件
     * @param page   分页
     * @return Page<RcIotPointVerification>
     */
    @Override
    public Page<RcIotPointVerification> select(RcIotPointVerification rcIotPointVerification, Page<RcIotPointVerification> page) {
        QueryWrapper<RcIotPointVerification> queryWrapper = getQueryWrapper(rcIotPointVerification);
        if (Validator.isEmpty(page.getSortField())){
            page.setSortField("secondProcess");
            page.setSortOrder("ascend");
        }else {
            page.setSortOrder("asc".equals(page.getSortOrder()) ? "ascend" : "descend");
        }
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    /**
     * 获取查询条件
     *
     * @param rcIotPointVerification 查询条件
     * @return QueryWrapper<RcIotPointVerification>
     */
    private QueryWrapper<RcIotPointVerification> getQueryWrapper(RcIotPointVerification rcIotPointVerification){
        QueryWrapper<RcIotPointVerification> queryWrapper = new QueryWrapper<>();
        if(rcIotPointVerification != null){
            // 查询条件
            // 二级工艺编码
            if (Validator.isNotEmpty(rcIotPointVerification.getSecondProcess())) {
                queryWrapper.like("t.second_process", rcIotPointVerification.getSecondProcess());
            }
            // 类型
            if (Validator.isNotEmpty(rcIotPointVerification.getType())) {
                queryWrapper.like("t.type", rcIotPointVerification.getType());
            }
            // 协议
            if (Validator.isNotEmpty(rcIotPointVerification.getProtocolName())) {
                queryWrapper.like("t.protocol_name", rcIotPointVerification.getProtocolName());
            }
            // 点位描述
            if (Validator.isNotEmpty(rcIotPointVerification.getDescription())) {
                queryWrapper.like("t.description", rcIotPointVerification.getDescription());
            }
            // 点位名称
            if (Validator.isNotEmpty(rcIotPointVerification.getDestAddressName())) {
                queryWrapper.like("t.dest_address_name", rcIotPointVerification.getDestAddressName());
            }
            // 备注
            if (Validator.isNotEmpty(rcIotPointVerification.getRemark())) {
                queryWrapper.like("t.remark", rcIotPointVerification.getRemark());
            }
            // 逻辑表达式
            if (Validator.isNotEmpty(rcIotPointVerification.getExpression())) {
                queryWrapper.like("t.expression", rcIotPointVerification.getExpression());
            }
            // 点位地址
            if (Validator.isNotEmpty(rcIotPointVerification.getPointAddress())) {
                queryWrapper.like("t.point_address", rcIotPointVerification.getPointAddress());
            }
            // 数据类型(值类型)
//            if (Validator.isNotEmpty(rcIotPointVerification.getDataType())) {
//                if (rcIotPointVerification.getDataType().contains(CommonConst.SPLIT)) {
//                    queryWrapper.in("t.data_type", rcIotPointVerification.getDataType().split(CommonConst.SPLIT));
//                } else {
//                    queryWrapper.eq("t.data_type", rcIotPointVerification.getDataType());
//                }
//            }
            // 传输方式
            if (Validator.isNotEmpty(rcIotPointVerification.getMode())) {
                queryWrapper.like("t.mode", rcIotPointVerification.getMode());
            }
            // 采集频率
            if (Validator.isNotEmpty(rcIotPointVerification.getAcquisitionFrequency())) {
                queryWrapper.like("t.acquisition_frequency", rcIotPointVerification.getAcquisitionFrequency());
            }
            // 保存策略
            if (Validator.isNotEmpty(rcIotPointVerification.getSavePolicy())) {
                queryWrapper.like("t.save_policy", rcIotPointVerification.getSavePolicy());
            }
            // 数据等级
            if (Validator.isNotEmpty(rcIotPointVerification.getDataRegistration())) {
                queryWrapper.like("t.data_registration", rcIotPointVerification.getDataRegistration());
            }
            // 创建时间 - 开始时间
            if (Validator.isNotEmpty(rcIotPointVerification.getStartCreateTime())) {
                queryWrapper.ge("t.create_time", rcIotPointVerification.getStartCreateTime());
            }
            // 创建时间 - 结束时间
            if (Validator.isNotEmpty(rcIotPointVerification.getEndCreateTime())) {
                queryWrapper.le("t.create_time", rcIotPointVerification.getEndCreateTime());
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return RcIotPointVerification
     */
    @Override
    public RcIotPointVerification get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return RcIotPointVerification
     */
    @Override
    public RcIotPointVerification add() {
        RcIotPointVerification rcIotPointVerification = new  RcIotPointVerification();
        // 设置默认值
        return rcIotPointVerification;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param rcIotPointVerification 表单内容
     * @return RcIotPointVerification
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RcIotPointVerification saveData(RcIotPointVerification rcIotPointVerification) {
        ToolUtil.checkParams(rcIotPointVerification);
        if (Validator.isEmpty(rcIotPointVerification.getId())) {
            // 新增,设置默认值
        }
        return (RcIotPointVerification) ToolUtil.checkResult(saveOrUpdate(rcIotPointVerification), rcIotPointVerification);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public void exportData(HttpServletResponse httpServletResponse,RcIotPointVerification rcIotPointVerification) {
//        List<RcIotPointVerification> list = baseMapper.queryData();
        List<RcIotPointVerification> list = new ArrayList<>();
        String fileName = "数据核验标准模板表";
        try {
            httpServletResponse.setContentType("application/octet-stream");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            httpServletResponse.setCharacterEncoding("utf-8");
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, fileName), RcIotPointVerification.class, list);
            workbook.createCellStyle().setFillBackgroundColor(IndexedColors.AQUA.getIndex());
            ServletOutputStream outputStream = httpServletResponse.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            throw new EasyException("文件写入失败[" + e.getMessage() + "]");
        }
    }

    @Override
    @Transactional
    public String importData(MultipartFile file) {
        //获取工艺编码列表
        List<String> secondProcess = baseMapper.getDeviceSecondProcess();
        try {
            List<String> errorList = new ArrayList<>();
            int errorTotal = 0;
            List<Sheet> sheets = ExcelUtil.getReader(file.getInputStream()).getSheets();
            for (int i = 0; i < sheets.size(); i++) {
                try {
                    List<RcIotPointVerification> list = new ArrayList<>();
                    ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), i);
                    Sheet rows = reader.getSheet();
                    String[] sheetNames = rows.getSheetName().replace(" ", "").split("&");
                    for (String sheetName : sheetNames) {
                        List<Map<String, Object>> read = reader.read(0, 1, Integer.MAX_VALUE);
                        if (CollectionUtils.isEmpty(read)){
                            return "导入的二级工艺"+sheetName+"模板不能为空!";
                        }
                        if (!secondProcess.contains(sheetName)){
                            return "导入的二级工艺"+sheetName+"不存在!";
                        }
                        int index = 1;
                        //baseMapper.delete(new QueryWrapper<RcIotPointVerification>().lambda().eq(Validator.isNotEmpty(sheetName), RcIotPointVerification::getSecondProcess, sheetName));
                        for (Map<String, Object> m : read) {
                            RcIotPointVerification rcIotPointVerification = new RcIotPointVerification();
                            rcIotPointVerification.setSecondProcess(sheetName == null ? "" : sheetName);
                            if (Validator.isEmpty(m.get("类型(必填)"))){
                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,类型不能为空");
                                errorTotal++;
                            }else{
                                rcIotPointVerification.setType(m.get("类型(必填)").toString());
                            }
                            if (Validator.isEmpty(m.get("点位描述(必填)"))){
                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,点位描述不能为空");
                                errorTotal++;
                            }else{
                                rcIotPointVerification.setDescription( m.get("点位描述(必填)").toString());
                            }
                            if (Validator.isEmpty(m.get("点位名称(必填)"))){
                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,点位名称不能为空");
                                errorTotal++;
                            }else{
                                rcIotPointVerification.setDestAddressName( m.get("点位名称(必填)").toString());
                            }
                            if (Validator.isEmpty(m.get("数据类型(必填)"))){
                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,数据类型(值类型)不能为空");
                                errorTotal++;
                            }else{
                                rcIotPointVerification.setDataType( m.get("数据类型(必填)").toString());
                            }
                            // 暂时改为非必填
//                            if (Validator.isEmpty(m.get("传输方式(必填)"))){
//                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,传输方式不能为空");
//                                errorTotal++;
//                            }
//                            else{
//                                rcIotPointVerification.setMode( m.get("传输方式(必填)").toString());
//                            }
                            if (Validator.isEmpty(m.get("点位类型(必填)"))){
                                errorList.add("二级工艺编码:"+sheetName+",第"+index+"行,点位类型不能为空");
                                errorTotal++;
                            }else{
                                rcIotPointVerification.setPointType( m.get("点位类型(必填)").toString());
                            }
                            rcIotPointVerification.setRemark(m.get("备注") == null ? "" : m.get("备注").toString());
                            rcIotPointVerification.setAcquisitionFrequency(m.get("采集频率(毫秒)") == null ? "" : m.get("采集频率(毫秒)").toString());
                            rcIotPointVerification.setSavePolicy(m.get("保存策略") == null ? "" : m.get("保存策略").toString());
                            rcIotPointVerification.setDataRegistration(m.get("数据等级") == null ? "" : m.get("数据等级").toString());
                            rcIotPointVerification.setPointAddress(m.get("点位地址") == null ? "" : m.get("点位地址").toString());
                            rcIotPointVerification.setEventMode(m.get("触发方式") == null ? "" : m.get("触发方式").toString());
                            rcIotPointVerification.setExpression(m.get("逻辑表达式") == null ? "" : m.get("逻辑表达式").toString());
                            rcIotPointVerification.setExpress(m.get("点位值运算") == null ? "" : m.get("点位值运算").toString());
                            rcIotPointVerification.setProtocolName(m.get("协议名称") == null ? "" : m.get("协议名称").toString());
                            list.add(rcIotPointVerification);
                            index ++;
                        }
                        if (errorTotal>0){
                            return JSONObject.toJSONString(errorList);
                        }
                        baseMapper.saveRcIotPointVerification(list);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("数据导入失败:"+e.getMessage());
                    return JSONObject.toJSONString(e.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public Set<PointResp> selectPoints(String secondProcess, String deviceCode) {
        List<PointResp> modelPoints = baseMapper.selectList(new QueryWrapper<RcIotPointVerification>().lambda()
                .eq(RcIotPointVerification::getSecondProcess, secondProcess)
                .orderByAsc(RcIotPointVerification::getDestAddressName)
        ).stream().map(item -> {
            PointResp pointResp = new PointResp();
            pointResp.setName(item.getDestAddressName());
            pointResp.setDescription(item.getDescription());
            return pointResp;
        }).distinct().collect(Collectors.toList());
        List<PointResp> otherPoints = rcIotPointVerificationMapper.selectOtherPoints(deviceCode);
        modelPoints.addAll(otherPoints);
        return new HashSet<>(modelPoints);
    }

}