package com.rc.admin.easyapi.model.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PointResultListResp {
    @ApiModelProperty(value = "数采平台")
    @Excel(name = "数采平台", width = 10, orderNum = "0")
    @TableField(value = "region")
    private  String region;
    @ApiModelProperty(value = "子公司名称")
    @Excel(name = "子公司名称", width = 12, orderNum = "1")
    @TableField(value = "child_company_name")
    private  String childCompanyName;
    @ApiModelProperty(value = "点位类型")
    @Excel(name = "点位类型", width = 12, orderNum = "2")
    @TableField(value = "point_type")
    private  String pointType;
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称", width = 12, orderNum = "3")
    @TableField(value = "name")
    private  String name;
    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", width = 12, orderNum = "4")
    @TableField(value = "device_code")
    private  String deviceCode;
    @ApiModelProperty(value = "点位名称")
    @Excel(name = "点位名称", width = 12, orderNum = "5")
    @TableField(value = "dest_address_name")
    private  String destAddressName;
    @ApiModelProperty(value = "描述")
    @Excel(name = "描述", width = 12, orderNum = "6")
    @TableField(value = "description")
    private  String description;
    @ApiModelProperty(value = "点位地址")
    @Excel(name = "点位地址", width = 12, orderNum = "7")
    @TableField(value = "point_address")
    private  String pointAddress;
    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型", width = 12, orderNum = "8")
    @TableField(value = "type")
    private  String type;
    @ApiModelProperty(value = "读写权限")
    @Excel(name = "读写权限", width = 12, orderNum = "9")
    @TableField(value = "read_write_power")
    private  String readWritePower;
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位", width = 12, orderNum = "10")
    @TableField(value = "unit")
    private  String unit;
    @ApiModelProperty(value = "寄存器类型")
    @Excel(name = "寄存器类型", width = 12, orderNum = "11")
    @TableField(value = "register_class")
    private  String registerClass;
    @ApiModelProperty(value = "起始地址")
    @Excel(name = "起始地址", width = 12, orderNum = "12")
    @TableField(value = "starting_address")
    private  String startingAddress;
    @ApiModelProperty(value = "偏移量")
    @Excel(name = "偏移量", width = 12, orderNum = "13")
    @TableField(value = "offset")
    private  String offset;
    @ApiModelProperty(value = "点位计算")
    @Excel(name = "点位计算", width = 12, orderNum = "14")
    @TableField(value = "express")
    private  String express;
    @ApiModelProperty(value = "触发方式")
    @Excel(name = "触发方式", width = 12, orderNum = "15")
    @TableField(value = "event_mode")
    private  String eventMode;
    @ApiModelProperty(value = "规则表达式")
    @Excel(name = "规则表达式", width = 12, orderNum = "16")
    @TableField(value = "event_condition")
    private  String eventCondition;
    @ApiModelProperty(value = "高频采集")
    @Excel(name = "高频采集", width = 12, orderNum = "17")
    @TableField(value = "frequency_acquisition")
    private  String frequencyAcquisition;
    @ApiModelProperty(value = "采集频率")
    @Excel(name = "采集频率", width = 12, orderNum = "18")
    @TableField(value = "acquisition_frequency")
    private  String acquisitionFrequency;
    private  String secondProcess;
    @ApiModelProperty(value = "协议名称")
    @Excel(name = "协议名称", width = 12, orderNum = "19")
    private  String protocolType;
}
