package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.base.BaseController;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.model.Rule;
import com.rc.admin.service.RuleService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@ResponseResult
@RequestMapping("/rule")
@Api(value = "规则配置相关接口")
public class RuleController extends BaseController {

    private final RuleService ruleService;

    @Autowired
    public RuleController(RuleService ruleService) {
        this.ruleService = ruleService;
    }

    @PostMapping
    public Rule addRule(@RequestBody @Valid Rule rule) {
        return ruleService.addRule(rule);
    }

    @GetMapping("/page")
    public Page<Rule> selectPage(Page<Rule> page, Rule rule){
        return ruleService.selectPage(page, rule);
    }

    @GetMapping("/export")
    public String exportData(Rule rule){
        return ruleService.exportData(rule);
    }

    @GetMapping("/{ruleId}")
    public Rule selectRule(@PathVariable("ruleId") Long ruleId) {
        return ruleService.selectRule(ruleId);
    }

    @PutMapping
    public Rule updateRule(@RequestBody @Valid Rule rule) {
        return ruleService.updateRule(rule);
    }

    @DeleteMapping("/{ruleId}")
    public void deleteRule(@PathVariable("ruleId") Long ruleId) {
        ruleService.deleteRule(ruleId);
    }

    @PutMapping("/{ruleId}")
    public void disableRule(@PathVariable("ruleId") Long ruleId) {
        ruleService.disableRule(ruleId);
    }

    private static final List<Thread> threads = new ArrayList<>();

    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            Thread thread = new Thread(() -> {
                System.out.println("Running task in thread: " + Thread.currentThread().getName());
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            threads.add(thread);
            thread.start();
        }

        Thread monitorThread = new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(5000); // 每5秒检查一次
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                synchronized (threads) {
                    threads.removeIf(thread -> !thread.isAlive());
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.start();
    }
}
