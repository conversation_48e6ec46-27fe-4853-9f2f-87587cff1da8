package com.rc.admin.easyapi.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.easyapi.model.req.DqmDeviceQueryReq;
import com.rc.admin.easyapi.service.ExternalInterfacesService;
import com.rc.admin.ors.quality.dao.BaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.dao.OtDeviceAllMapper;
import com.rc.admin.ors.quality.entity.BaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsDeviceCheckConfig;
import com.rc.admin.ors.quality.entity.OrsModelPropertiesConfig;
import com.rc.admin.ors.quality.excel.OrsDeviceCheckConfigImportExcel;
import com.rc.admin.ors.quality.excel.UnImportDataExcel;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.ors.quality.service.OrsDeviceLedgerService;
import com.rc.admin.ors.quality.utils.EasyPoiUtils;
import com.rc.admin.util.ShiroUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExternalInterfacesServiceImpl implements ExternalInterfacesService {


    @Resource
    private OrsModelPropertiesConfigMapper orsModelPropertiesConfigMapper;

    @Resource
    private OrsDeviceLedgerService orsDeviceLedgerService;

    @Resource
    private OrsDeviceCheckConfigService orsDeviceCheckConfigService;

    @Resource
    private BaseDeviceInfoMapper baseDeviceInfoMapper;

    @Resource
    private OtDeviceAllMapper otDeviceAllMapper;

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    private final int MAX_IMPORT = 2000;
    private final int MIN_IMPORT = 500;
    private final String SUFFIX = "_H";


    @Override
    public Page<DeviceLedgerResp> getDevicePage(Page<DqmDeviceQueryReq> reqPage,DqmDeviceQueryReq dqmDeviceQueryReq) {
        Page<DeviceLedgerResp> page = new Page<>(reqPage.getCurrent(), reqPage.getSize());

        DeviceLedgerReq deviceLedgerResp = new  DeviceLedgerReq();
        deviceLedgerResp.setCreatedBegin(dqmDeviceQueryReq.getCreatedBegin());
        deviceLedgerResp.setCreatedEnd(dqmDeviceQueryReq.getCreatedEnd());
        deviceLedgerResp.setModelId(orsDeviceLedgerService.getHXModelStr());
        return orsDeviceLedgerService.page(page,deviceLedgerResp);
    }

    @Override
    public void importCheckConfig(MultipartFile file, HttpServletResponse response) {
        log.info("接口导入设备检查项开始");
        if (file.isEmpty()) {
            throw new IllegalArgumentException("请上传文件");
        }
        String createUser = ShiroUtil.getCurrentUser().getUsername();
        List<UnImportDataExcel> unImportDataExcels = new CopyOnWriteArrayList<>();
        List<OrsDeviceCheckConfig> list =  new CopyOnWriteArrayList<>();
        try {
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            List<OrsDeviceCheckConfigImportExcel> objects = ExcelImportUtil.importExcel(file.getInputStream(), OrsDeviceCheckConfigImportExcel.class, params);
            if (objects.isEmpty()) {
                throw new IllegalArgumentException("请上传文件类容");
            }
            objects = objects.stream()
                    .filter(obj -> StringUtils.isNotEmpty(obj.getDeviceCode()) ||
                            StringUtils.isNotEmpty(obj.getPropertyName()) ||
                            StringUtils.isNotEmpty(obj.getExcludeResean()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    obj -> obj.getDeviceCode() + "-" + obj.getPropertyName(),
                                    obj -> obj,
                                    (existing, replacement) -> existing // 保留第一个出现的值
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            // 获得所有的属性配置
            List<OrsModelPropertiesConfig> configs = orsModelPropertiesConfigMapper.selectList(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .select(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
                            .groupBy(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
            );

            List<List<OrsDeviceCheckConfigImportExcel>> split = CollectionUtil.split(objects, MAX_IMPORT);
            List<CompletableFuture<String>> threads = new ArrayList<>();

            // 外层查询全量设备信息并构建 Map
            Map<String,BaseDeviceInfo> deviceInfoMap = baseDeviceInfoMapper.selectBaseDeviceInfo().stream()
                    .collect(Collectors.toConcurrentMap(BaseDeviceInfo::getAssetId, i->i,(k1, k2)->k1));

            // 开始导入 这里为了提高导入的效率，将数据拆分后使用多线程导入
            for (List<OrsDeviceCheckConfigImportExcel> excels : split) {
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    // 这里为了避免使用复杂的多线程事务，导致数据不一致的问题，这里只处理数据，不对数据入库，待所有数据处理完成无误后，统一入库，便于事务管理
                    handleImport(excels, list, unImportDataExcels, configs,deviceInfoMap,createUser);
                    return "success";
                },asyncExecutor);
                threads.add(future);
            }
            //allOf()等待所有线程执行完毕
            CompletableFuture<Void> allFuture = CompletableFuture.allOf(threads.toArray(new CompletableFuture[threads.size()]));
            allFuture.join();
        } catch (Exception e) {
            log.error("导入设备检查项异常", e);
            throw new IllegalStateException("文件解析错误");
        }

        // 如果有处理不成功的数据，以表格的形式返回给用户数据明细
        if (!unImportDataExcels.isEmpty()) {
            ExportParams exportParams = new ExportParams("设备剔除检查导入失败项", "导入失败项");
            exportParams.setCreateHeadRows(true);
            log.info("导入设备检查项失败项数量:{}", unImportDataExcels.size());
            List<UnImportDataExcel> unImportDataExcelsCopy = new ArrayList<>(unImportDataExcels);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, UnImportDataExcel.class, unImportDataExcelsCopy);
            if (workbook != null) {
                try {
                    String encodedFileName = URLEncoder.encode("设备剔除检查导入失败项.xlsx", "UTF-8");
                    response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
                    EasyPoiUtils.downLoadExcel(encodedFileName, response, workbook);
                } catch (UnsupportedEncodingException e) {
                    log.error("文件名编码错误", e);
                    throw new IllegalStateException("文件名编码错误");
                }
            }
        }
        // 数据统一入库
        if(!list.isEmpty()) {
            //去重复 反正excel 同时存在 '_H和不带H的'
            ArrayList<OrsDeviceCheckConfig> collect = list.stream()
                    .filter(obj -> StringUtils.isNotEmpty(obj.getDeviceCode()) ||
                            StringUtils.isNotEmpty(obj.getPropertyName()) ||
                            StringUtils.isNotEmpty(obj.getExcludeResean()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    obj -> obj.getDeviceCode() + "-" + obj.getParamCode(),
                                    obj -> obj,
                                    (existing, replacement) -> existing // 保留第一个出现的值
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            log.info("数据统一入库saveBatch开始");
            orsDeviceCheckConfigService.saveBatch(collect);
            log.info("数据统一入库saveBatch结束");
        }
        log.info("导入设备检查项结束");
    }


    @Override
    public void importCheckConfigDelete(MultipartFile file, HttpServletResponse response) {
        log.info("导入设备检查项-删除处理 开始");
        if (file.isEmpty()) {
            throw new IllegalArgumentException("请上传文件");
        }
        String createUser = ShiroUtil.getCurrentUser().getUsername();
        try {
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            List<OrsDeviceCheckConfigImportExcel> objects = ExcelImportUtil.importExcel(file.getInputStream(), OrsDeviceCheckConfigImportExcel.class, params);

            log.info("同步数量:{}",objects.size());
            objects = objects.stream()
                    .filter(obj -> StringUtils.isNotEmpty(obj.getDeviceCode()) ||
                            StringUtils.isNotEmpty(obj.getPropertyName()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    obj -> obj.getDeviceCode() + "-" + obj.getPropertyName(),
                                    obj -> obj,
                                    (existing, replacement) -> existing // 保留第一个出现的值
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            log.info("去重后数量:{}",objects.size());

            List<UnImportDataExcel> unImportDataExcels = Collections.synchronizedList(new ArrayList<>());
            List<List<OrsDeviceCheckConfigImportExcel>> split = CollectionUtil.split(objects, MIN_IMPORT);
            log.info("分批后的数量:{}",split.size());

            // 获得所有的属性配置
            List<OrsModelPropertiesConfig> configs = orsModelPropertiesConfigMapper.selectList(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .select(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
                            .groupBy(OrsModelPropertiesConfig::getProperty, OrsModelPropertiesConfig::getPropertyName)
            );

            // 外层查询全量设备信息并构建 Map
            Map<String,BaseDeviceInfo> deviceInfoMap = baseDeviceInfoMapper.selectBaseDeviceInfo().stream()
                    .collect(Collectors.toMap(BaseDeviceInfo::getAssetId, i->i,(k1, k2)->k1));


            if(CollUtil.isNotEmpty(split)){
                // 创建固定大小的线程池
                ExecutorService executorService = Executors.newFixedThreadPool(5);

                // 使用CountDownLatch来等待所有任务完成
                CountDownLatch latch = new CountDownLatch(split.size());

                for (List<OrsDeviceCheckConfigImportExcel> item : split) {
                    executorService.submit(() -> {
                        try {
                            //做检查
                            List<OrsDeviceCheckConfigImportExcel> orsDeviceCheckConfigImportExcels = CollUtil.newArrayList();
                            handleDeleteImport(item, configs, unImportDataExcels, deviceInfoMap,orsDeviceCheckConfigImportExcels);
                            if(CollUtil.isNotEmpty(orsDeviceCheckConfigImportExcels)){
                                orsDeviceCheckConfigService.importDeleteInsertHistroy(orsDeviceCheckConfigImportExcels,createUser);
                                orsDeviceCheckConfigService.importDelete(orsDeviceCheckConfigImportExcels);
                            }
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                // 等待所有任务完成
                latch.await();
                executorService.shutdown();
            }else{
                throw new IllegalArgumentException("请上传文件内容");
            }

            // 如果有处理不成功的数据，以表格的形式返回给用户数据明细
            if (!unImportDataExcels.isEmpty()) {
                ExportParams exportParams = new ExportParams("设备剔除删除检查导入失败项", "导入失败项");
                exportParams.setCreateHeadRows(true);
                log.info("导入设备检查删除项失败项数量:{}", unImportDataExcels.size());
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, UnImportDataExcel.class, unImportDataExcels);
                if (workbook != null) {
                    try {
                        String encodedFileName = URLEncoder.encode("设备剔除检查删除导入失败项.xlsx", "UTF-8");
                        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
                        EasyPoiUtils.downLoadExcel(encodedFileName, response, workbook);
                    } catch (UnsupportedEncodingException e) {
                        log.error("文件名编码错误", e);
                        throw new IllegalStateException("文件名编码错误");
                    }
                }
            }
        } catch (Exception e) {
            log.error("导入设备检查项-删除处理 异常", e);
            throw new IllegalStateException("文件解析错误");
        }
        log.info("导入设备检查项-删除处理 结束");
    }

    @Override
    public OtDevicePage<OtDeviceAllResp> getAllDevice(OtDeviceAllReq req) {
        req.setD365Exist(true);
        req.setNgcExist(false);
        req.setEviExist(true);
        OtDevicePage<OtDeviceAllResp> otDeviceAllRespOtDevicePage
                = otDeviceAllMapper.selectPageList(new OtDevicePage<>(req.getCurrent(), req.getSize()), req);
        otDeviceAllRespOtDevicePage.setDeviceCount(otDeviceAllMapper.getDeviceCount(req)+"");
        return otDeviceAllRespOtDevicePage;
    }


    private void handleDeleteImport(List<OrsDeviceCheckConfigImportExcel> objects,
                                    List<OrsModelPropertiesConfig> configs,
                                    List<UnImportDataExcel> unImportDataExcels,
                                    Map<String,BaseDeviceInfo> deviceInfoMap,
                                    List<OrsDeviceCheckConfigImportExcel> list){
        for (int i = 0; i < objects.size(); i++) {
            OrsDeviceCheckConfigImportExcel x = objects.get(i);
            if (StringUtils.isBlank(x.getDeviceCode()) ) {
                unImportDataExcels.add(buildUnimportData(x, "关键字不能为空或不存在"));
                continue;
            }
            String key = String.format("%s%s", x.getDeviceCode(), SUFFIX);
            BaseDeviceInfo info = deviceInfoMap.get(key);
            if (null == info) {
                info = deviceInfoMap.get(x.getDeviceCode());
                if(null == info) {
                    unImportDataExcels.add(buildUnimportData(x, "不是华兴所属模型的设备,或者设备不存在"));
                    continue;
                }
            }
            String excludType;
            if (!x.getPropertyName().equals("整机")) {
                OrsModelPropertiesConfig config = configs.stream().filter(c -> c.getPropertyName().equals(x.getPropertyName())).findFirst().orElse(null);
                if (null == config) {
                    unImportDataExcels.add(buildUnimportData(x, "剔除类型不能为空或不存在"));
                    continue;
                }
                excludType = config.getProperty();
            } else {
                excludType = "WHOLE";
            }
            // 判断该code是否已存在
            OrsDeviceCheckConfig code = orsDeviceCheckConfigService.getOne(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .eq(OrsDeviceCheckConfig::getAssetId, info.getAssetId())
                            .eq(OrsDeviceCheckConfig::getExcludeType, excludType).last("limit 1")
            );
            if (null == code) {
                unImportDataExcels.add(buildUnimportData(x, x.getDeviceCode()+"剔除不存在无法删除"));
                continue;
            }
            //防止匹配上 _H的数据
            x.setDeviceCode(info.getDeviceCode());
            list.add(x);
        }
    }



    private void handleImport(List<OrsDeviceCheckConfigImportExcel> objects,
                              List<OrsDeviceCheckConfig> list,
                              List<UnImportDataExcel> unImportDataExcels,
                              List<OrsModelPropertiesConfig> configs,
                              Map<String,BaseDeviceInfo> deviceInfoMap,
                              String createUser){
        for (int i = 0; i < objects.size(); i++) {
            OrsDeviceCheckConfigImportExcel x = objects.get(i);
            if (StringUtils.isBlank(x.getDeviceCode()) ) {
                unImportDataExcels.add(buildUnimportData(x, "关键字不能为空或不存在"));
                continue;
            }
            String key = String.format("%s%s", x.getDeviceCode(), SUFFIX);

            BaseDeviceInfo info = deviceInfoMap.get(key);
            if (null == info) {
                info = deviceInfoMap.get(x.getDeviceCode());
                if(null == info) {
                    unImportDataExcels.add(buildUnimportData(x, "不是华兴所属模型的设备,或者设备不存在"));
                    continue;
                }
            }
            String excludType;
            if (!x.getPropertyName().equals("整机")) {
                OrsModelPropertiesConfig config = configs.stream().filter(c -> c.getPropertyName().equals(x.getPropertyName())).findFirst().orElse(null);
                if (null == config) {
                    unImportDataExcels.add(buildUnimportData(x, "剔除类型不能为空或不存在"));
                    continue;
                }
                excludType = config.getProperty();
            } else {
                excludType = "WHOLE";
            }
            // 判断是否已有整机剔除
            OrsDeviceCheckConfig whole = orsDeviceCheckConfigService.getOne(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .eq(OrsDeviceCheckConfig::getAssetId, info.getAssetId())
                            .eq(OrsDeviceCheckConfig::getExcludeType, "WHOLE").last("limit 1")
            );
            if (null != whole) {
                if ("WHOLE".equals(excludType)) {
                    unImportDataExcels.add(buildUnimportData(x, "已有整机剔除，不可重复添加"));
                    continue;
                }else{
                    unImportDataExcels.add(buildUnimportData(x, "已有整机剔除，不可再添加属性剔除"));
                    continue;
                }
            }else{
                // 判断该code是否已存在
                OrsDeviceCheckConfig code = orsDeviceCheckConfigService.getOne(
                        new QueryWrapper<OrsDeviceCheckConfig>()
                                .lambda()
                                .eq(OrsDeviceCheckConfig::getAssetId, info.getAssetId())
                                .eq(OrsDeviceCheckConfig::getExcludeType, excludType).last("limit 1")

                );
                if (null != code) {
                    unImportDataExcels.add(buildUnimportData(x, code.getParamName()+"已存在，不可再重复添加"));
                    continue;
                }
            }
            // 数据处理
            OrsModelPropertiesConfig modelConfig = orsModelPropertiesConfigMapper.selectOne(
                    new QueryWrapper<OrsModelPropertiesConfig>()
                            .lambda()
                            .eq(OrsModelPropertiesConfig::getModelId, info.getModelId())
                            .eq(OrsModelPropertiesConfig::getProperty, excludType)
            );
            if (StringUtils.isBlank(excludType) || (!"WHOLE".equals(excludType) && null == modelConfig)) {
                unImportDataExcels.add(buildUnimportData(x, "剔除类型不能为空或不存在"));
                continue;
            }
            if (StringUtils.isBlank(x.getExcludeResean()) || x.getExcludeResean().length()>200){
                unImportDataExcels.add(buildUnimportData(x, "剔除原因不能为空或输入超长"));
                continue;
            }

            // 数据组装
            OrsDeviceCheckConfig newdata = getOrsDeviceCheckConfig(x, info,createUser);
            newdata.setAssetId(info.getAssetId());
            newdata.setDeviceName(info.getDeviceName());
            if (!"WHOLE".equals(excludType)) {
                newdata.setExcludeType(modelConfig.getProperty());
                newdata.setParamCode(modelConfig.getParamCode());
                newdata.setPropertyName(modelConfig.getPropertyName());
            }
            if ("WHOLE".equals(excludType)) {
                newdata.setExcludeType("WHOLE");
                newdata.setParamCode(8601);
                newdata.setPropertyName("整机");
            }
            newdata.setParamName(newdata.getPropertyName());
            list.add(newdata);
        }
    }


    private static OrsDeviceCheckConfig getOrsDeviceCheckConfig(OrsDeviceCheckConfigImportExcel x, BaseDeviceInfo ledger,String createUser) {
        OrsDeviceCheckConfig config = new OrsDeviceCheckConfig();
        config.setUuid(ledger.getThingId());
        config.setAssetId(ledger.getAssetId());
        config.setDelFlag(0);
        config.setModelId(ledger.getModelId());
        config.setDeviceCode(ledger.getDeviceCode());
        config.setExcludeResean(x.getExcludeResean());
        config.setCreateTime(new Date());
        log.info("鉴权信息:{}", ShiroUtil.getCurrentUser().getUsername());
        log.info("传递得用户信息:{}", createUser);
        config.setCreateUser(createUser);
        config.setUpdateUser(config.getCreateUser());
        config.setUpdateTime(config.getCreateTime());
        return config;
    }


    private UnImportDataExcel buildUnimportData(OrsDeviceCheckConfigImportExcel x, String errorMsg) {
        return UnImportDataExcel
                .builder()
                .code(x.getDeviceCode())
                .name(x.getPropertyName())
                .detail(x.getExcludeResean())
                .resean(errorMsg)
                .build();
    }

}
