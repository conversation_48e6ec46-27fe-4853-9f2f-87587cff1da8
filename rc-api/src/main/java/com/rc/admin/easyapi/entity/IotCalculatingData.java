package com.rc.admin.easyapi.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IotCalculatingData {
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    @ApiModelProperty(value = "被赋值点位")
    private String destAddressName;
    @ApiModelProperty(value = "触发方式")
    private String eventMode;
    @ApiModelProperty(value = "表达式/值触发点位项/计算时间")
    private String eventCondition;
    @ApiModelProperty(value = "点位值运算")
    private String express;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "来源")
    private String source;
}
