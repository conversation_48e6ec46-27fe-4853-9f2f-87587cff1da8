package com.rc.admin.easyapi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.auth.model.entity.RealTimeDeviceWorkConditionResp;
import com.rc.admin.easyapi.entity.*;
import com.rc.admin.easyapi.model.req.IotDeviceInfoReq;
import com.rc.admin.easyapi.model.req.PointResultListReq;
import com.rc.admin.easyapi.model.resp.IotDeviceInfoResp;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.model.DeviceProfile;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

//@Mapper
public interface IotDeviceInfoMapper extends BaseMapper<IotDeviceInfo> {

    List<IotDeviceInfoResp> selectPages(@Param("req") IotDeviceInfoReq req);
    int selectPagesCount(@Param("req") IotDeviceInfoReq req);

    /**
     * 批量写入设备信息
     * @param list 设备信息集合
     */
    @Insert("<script> replace into rc_iot_device_info(" +
            "device_code,name,child_company_name,factory,work_center,work_group,protocol_type,paraItem_value,station_number,scan_interval_time,con_timeout,recon_delay,customer_param,descript,collection_type,source,first_process,first_process_name,second_process,second_process_name,device_id) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.name},#{item.childCompanyName},#{item.factory},#{item.workCenter}, #{item.workGroup}, #{item.protocolType}, #{item.paraItemValue}, #{item.stationNumber}, #{item.scanIntervalTime}, #{item.conTimeout}, #{item.reconDelay}, #{item.customerParam},#{item.descript},#{item.collectionType},#{item.source},#{item.firstProcess},#{item.firstProcessName},#{item.secondProcess},#{item.secondProcessName},#{item.deviceId})" +
            "  </foreach> " +
            "</script>")
    void DeviceInfoSave(@Param("list") List<IotDeviceInfo> list);
    /**
     * 批量写入根云设备物模型信息
     * @param list 设备信息集合
     */
    @Insert("<script> replace into rc_iot_root_cloud_device_info(" +
            "asset_id,model_id,thing_id,class_id,protocol,phase) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.assetId},#{item.modelId},#{item.thingId},#{item.classId},#{item.protocol}, #{item.phase})" +
            "  </foreach> " +
            "</script>")
    void rootCloudDeviceInfoSave(@Param("list") List<IotRootCloudDeviceInfo> list);
    /**
     * 批量写入点位计算数据
     * @param list 点位计算信息集合
     */
    @Insert("<script> replace into rc_iot_calculating_data(" +
            "device_code,dest_address_name,event_mode,event_condition,express,description,source) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.destAddressName},#{item.eventMode},#{item.eventCondition}, #{item.express}, #{item.description},#{item.source})" +
            "  </foreach> " +
            "</script>")
    void IotCalculatingDataSave(@Param("list") List<IotCalculatingData> list);
    /**
     * 批量写入自定义点位数据
     * @param list 自定义点位信息集合
     */
    @Insert("<script> replace into rc_iot_customize_point(" +
            "device_code,dest_address_name,type,conversion_type,expression,gather_min,gather_max,shift_min,shift_max,precision_type,decimal_place,exponent,unit,description,source) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.destAddressName},#{item.type},#{item.conversionType}, #{item.expression}, #{item.gatherMin}, #{item.gatherMax}, #{item.shiftMin}, #{item.shiftMax}, #{item.precisionType}, #{item.decimalPlace}, #{item.exponent},#{item.unit},#{item.description},#{item.source})" +
            "  </foreach> " +
            "</script>")
    void IotCustomizePointSave(@Param("list") List<IotCustomizePoint> list);
    /**
     * 批量写入采集点位数据
     * @param list 采集点位信息集合
     */
    @Insert("<script> replace into rc_iot_collection_point(" +
            "device_code,dest_address_name,point_address,register_class,starting_address,offset,type,length,block_address,block_type,byte_exchange,read_write_power,conversion_type,expression,gather_min,gather_max,shift_min,shift_max,precision_type,decimal_place,exponent,unit,description,frequency_acquisition,acquisition_frequency,source) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.destAddressName},#{item.pointAddress},#{item.registerClass},#{item.startingAddress},#{item.offset},#{item.type},#{item.length},#{item.blockAddress},#{item.blockType},#{item.byteExchange},#{item.readWritePower},#{item.conversionType}, #{item.expression}, #{item.gatherMin}, #{item.gatherMax}, #{item.shiftMin}, #{item.shiftMax}, #{item.precisionType}, #{item.decimalPlace}, #{item.exponent},#{item.unit},#{item.description},#{item.frequencyAcquisition},#{item.acquisitionFrequency},#{item.source})" +
            "  </foreach> " +
            "</script>")
    void IotCollectionPointSave(@Param("list") List<IotCollectionPoint> list);
    /**
     * 移除指定采集区域设备点位计算数据
     * @param source 区域ip地址
     */
    @Delete("delete from rc_iot_calculating_data where source = #{source}")
    int deleteIotCalculatingData(@Param("source")String source);
    /**
     * 获取设备点位名称列表
     */
    @Select("select DISTINCT device_code,dest_address_name from  rc_iot_customize_point")
    List<Map<String,String>> getDestAddressNameList();
    /**
     * 获取区域ip
     */
    @Select("select ip from rc_iot_region_config where delete_flag = '1'")
    List<String> getRegionValues();
    /**
     * 获取指定设备所属区域地址信息
     * @param deviceCode 设备编号
     */
    @Select("select irc.ip,irc.value from rc_iot_device_info as idi \n" +
            " left join rc_iot_region_config as irc on idi.source = irc.ip\n" +
            " where idi.device_code = #{deviceCode} and irc.delete_flag = '1' limit 1")
    Map<String,String> getRegionValuesInfo(@Param("deviceCode") String deviceCode);
    /**
     * 获取所有区域信息
     */
    @Select("select DISTINCT value from rc_iot_region_config")
    List<String> getValues();
    /**
     * 获取指定设备信息列表
     * @param deviceCode 设备编号
     */
    List<Map<String,String>> getDeviceCodeList(@Param("deviceCode") String deviceCode);
    /**
     * 获取指定设备物模型信息
     * @param deviceCode 设备编号
     */
    @Select("select   asset_id as  assetId,  model_id as modelId,  thing_id as thingId from rc_iot_root_cloud_device_info where asset_id  = #{deviceCode} limit 1")
    IotRootCloudDeviceInfo getRootCloudDeviceInfo(@Param("deviceCode") String deviceCode);

    @Insert("<script> INSERT INTO rc_iot_data_quality_inspection_history(" +
            "device_code,dest_address_name,description,gl_value,gy_value,scan_interval,update_time,change_time,gy_time_cloud,gy_time_local,gy_write_time,rule_subject,rule_script,rule_remarks,create_time) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.name},#{item.Explain},#{item.Value},#{item.rootCloudValue}, #{item.ScanInterval}, #{item.UpdateTime}, #{item.ChangeTime}, #{item.rootCloudTimeCloud}, #{item.rootCloudTimeLocal}, #{item.rootCloudWriteTime}, #{item.ruleSubject}, #{item.ruleScript},#{item.ruleRemarks},NOW())" +
            "  </foreach> " +
            "</script>")
    void realTimeDeviceWorkConditionSave(@Param("list") List<RealTimeDeviceWorkConditionResp> list);

    @Select("select first_process as firstProcess, first_process_name as firstProcessName, second_process as secondProcess, second_process_name as secondProcessName, device_code as deviceCode from rc_iot_device_profile where device_code = #{deviceCode}")
    Map<String,String> selectProcessInfo(@Param("deviceCode") String deviceCode);
    /**
     * 获取点位信息列表
     * @param dto 入参信息
     */
    List<PointResultListResp> getPointList(@Param("dto") PointResultListReq dto);
    /**
     * 获取点位属信息总数
     * @param dto 入参信息
     */
    int getPointListCount(@Param("dto") PointResultListReq dto);

    /**
     * 获取设备子公司列表
     */
    @Select("select DISTINCT child_company_name from rc_iot_device_info ")
    List<String> getChildCompanyNameList();
    /**
     * 获取点位信息
     * @param deviceCode 设备编号
     */
    List<PointResultListResp> getPointInfo(@Param("deviceCode") String deviceCode);
    /**
     * 获取点位信息
     * @param secondProcess 二级工艺编码
     * @param protocolName 协议名
     */
    List<PointResultListResp> getPointInfoBySecondProcess(@Param("secondProcess") String secondProcess,@Param("protocolName") String protocolName);
    /**
     * 获取无点位信息设备
     * @param secondProcess 二级工艺编码
     * @param protocolName 协议名
     */
    List<PointResultListResp> getNotPointInfoBySecondProcess(@Param("secondProcess") String secondProcess,@Param("protocolName") String protocolName);

    /**
     * 获取点位模板列表
     */
    @Select("select ipv.dest_address_name,ipv.description,ipv.data_type from rc_iot_device_info as idi\n" +
            "left join rc_iot_point_verification as ipv on idi.second_process = ipv.second_process\n" +
            "where idi.device_code = #{deviceCode}")
    List<RcIotPointVerification> getPointTemplateList(@Param("deviceCode") String deviceCode);
}
