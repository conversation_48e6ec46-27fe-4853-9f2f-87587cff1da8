package com.rc.admin.easyapi.constants;
// String[] abnormalCodeList = {"9001","9002","9004","9007","9008"};
//
//         //检查项
//         String[] paramCodeList = {"8503","8501","8105","8102","8201","8401"," 8403"};
public enum ParamAbnormalCode {
    /**
     * 位置
     */
    ABNORMAL_8501("8501","经纬度"),
    /**
     * 状态
     */
    ABNORMAL_8503("8503","设备状态"),
    /**
     * 发动机小时
     */
    ABNORMAL_8105("8105","发动机工作时间"),
    /**
     * 工作小时
     */
    ABNORMAL_8102("8102","工作时间"),
    /**
     * 方量
     */
    ABNORMAL_8401("8401","方量"),
    /**
     * 总油耗
     */
    ABNORMAL_8201("8201","总油耗"),
    /**
     * 行驶里程
     */
    ABNORMAL_8403("8403","行驶里程"),

    PARAM_9001("9001","属性值异常"),
    PARAM_9002("9002","属性值超限"),
    PARAM_9003("9003","属性值日变化量异常"),
    PARAM_9004("9004","属性值逆增长"),
    PARAM_9005("9005","属性值跳变"),
    PARAM_9006("9006","属性值长期定值"),
    PARAM_9007("9007","位置漂移"),
    PARAM_9008("9008","属性值从未上报"),
    PARAM_9009("9009","属性值长期未上报"),

    PARAM_9101("9101","怠速时长变化怠速油耗未变化"),
    PARAM_9102("9102","怠速油耗变化怠速时长未变化"),
    PARAM_9103("9103","工作时间变化总油耗未变化"),
    PARAM_9104("9104","总油耗变化工作时长未变化"),
    PARAM_9105("9105","当日怠速油耗增长不合理"),
    PARAM_9106("9106","当日怠速时长增长不合理"),
    PARAM_9107("9107","当日里程变化无车速"),
    PARAM_9108("9108","当日有持续车速里程未变化"),
    PARAM_9109("9109","设备里程变化定位不变"),
    PARAM_9100("9100","关联工况变化不合理");
    private String code;
    private String name;

    ParamAbnormalCode(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }


}
