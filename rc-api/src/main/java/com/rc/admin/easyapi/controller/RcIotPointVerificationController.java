package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.easyapi.entity.RcIotPointVerification;
import com.rc.admin.easyapi.service.RcIotPointVerificationService;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.rc.admin.core.annotation.ResponseResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 数据核验标准模板表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@ResponseResult
@RequestMapping("/api/rc/iot/point/verification")
public class RcIotPointVerificationController {

    /**
     * 数据核验标准模板表 service
     */
    @Autowired
    private RcIotPointVerificationService service;
    @Autowired
    private HttpServletResponse httpServletResponse;

    /**
     * 列表
     *
     * @param rcIotPointVerification 查询条件
     * @param page 分页
     * @return Page<RcIotPointVerification>
     */
    @GetMapping()
    public Page<RcIotPointVerification> select(RcIotPointVerification rcIotPointVerification, Page<RcIotPointVerification> page){
        return service.select(rcIotPointVerification, page);

    }

    /**
     * 详情
     *
     * @param id id
     * @return RcIotPointVerification
     */
    @GetMapping("{id}")
    public RcIotPointVerification get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return RcIotPointVerification
     */
    @GetMapping("add")
    public RcIotPointVerification add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param rcIotPointVerification 表单内容
     * @return RcIotPointVerification
     */
    @PostMapping()
    public RcIotPointVerification saveData(@Valid @RequestBody RcIotPointVerification rcIotPointVerification){
        return service.saveData(rcIotPointVerification);
    }
    /**
     * 导出数据
     *
     * @return 文件下载id
     */
    @GetMapping("export/data")
    public Response exportData(){
        service.exportData( httpServletResponse,null);
        return Response.success();
    }
    /**
     * 导入数据
     *
     * @param files 查询条件
     * @return 文件下载id
     */
    @ApiOperation(value = "数据核验导入")
    @PostMapping("importData/data")
    public Response importData(@RequestParam("files") MultipartFile files){
        String s = service.importData(files);
        return s != null ? Response.failError("500",s) :Response.success();
    }
}
