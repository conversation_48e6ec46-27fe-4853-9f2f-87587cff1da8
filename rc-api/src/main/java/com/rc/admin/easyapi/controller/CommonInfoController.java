package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.base.BaseController;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.easyapi.job.SyncDeviceInfoJob;
import com.rc.admin.easyapi.model.resp.PointResp;
import com.rc.admin.easyapi.service.OrgAndProcessDictService;
import com.rc.admin.easyapi.service.RcIotPointVerificationService;
import com.rc.admin.sys.model.SysDict;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@ResponseResult
@RequestMapping("/common")
@Api(value = "公共信息相关接口")
public class CommonInfoController extends BaseController {

    private final OrgAndProcessDictService orgAndProcessDictService;

    private final RcIotPointVerificationService pointVerificationService;

    private final SyncDeviceInfoJob syncDeviceInfoJob;

    @Autowired
    public CommonInfoController(OrgAndProcessDictService orgAndProcessDictService, RcIotPointVerificationService pointVerificationService, SyncDeviceInfoJob syncDeviceInfoJob) {
        this.orgAndProcessDictService = orgAndProcessDictService;
        this.pointVerificationService = pointVerificationService;
        this.syncDeviceInfoJob = syncDeviceInfoJob;
    }

    @GetMapping("/orgs")
    public List<SysDict> getOrgs() {
        return orgAndProcessDictService.selectOrgs();
    }

    @GetMapping("/companies/{orgCode}")
    public List<SysDict> getCompanies(@PathVariable("orgCode") String orgCode) {
        return orgAndProcessDictService.selectCompanies(orgCode);
    }

    @GetMapping("/firstProcesses")
    public List<SysDict> getFirstProcesses() {
        return orgAndProcessDictService.selectFirstProcesses();
    }

    @GetMapping("/secondProcesses/{firstProcess}")
    public List<SysDict> getSecondProcesses(@PathVariable("firstProcess") String firstProcess) {
        return orgAndProcessDictService.selectSecondProcesses(firstProcess);
    }

    @GetMapping("/secondProcesses")
    public List<SysDict> getSecondProcesses() {
        return orgAndProcessDictService.selectSecondProcesses();
    }

    @GetMapping("/points/{secondProcess}/{deviceCode}")
    public Set<PointResp> getPoints(@PathVariable("secondProcess") String secondProcess, @PathVariable("deviceCode") String deviceCode) {
        return pointVerificationService.selectPoints(secondProcess, deviceCode);
    }
    @GetMapping("/a")
    public void a() {
        syncDeviceInfoJob.syncDeviceJob();
    }
    @GetMapping("/b")
    public void b() {
        //syncDeviceInfoJob.synBigdataInfoAllJob();
    }

}
