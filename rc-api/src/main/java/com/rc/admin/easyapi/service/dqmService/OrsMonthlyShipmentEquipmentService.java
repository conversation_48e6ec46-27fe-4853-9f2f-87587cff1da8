package com.rc.admin.easyapi.service.dqmService;

import cn.hutool.core.lang.Validator;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.easyapi.util.StringUtil;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsMonthlyShipmentEquipmentMapper;
import com.rc.admin.ors.quality.entity.OrsBaseDeviceInfo;
import com.rc.admin.ors.quality.entity.OrsMonthlyShipmentEquipment;
import com.rc.admin.ors.quality.model.OrsMonthlyShipmentEquipmentReq;
import com.rc.admin.ors.quality.model.OrsMonthlyShipmentEquipmentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service

public class OrsMonthlyShipmentEquipmentService {


    @Resource
    OrsMonthlyShipmentEquipmentMapper mapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    public String getReadString(Object o){
        if(Objects.nonNull(o)){
            String s = String.valueOf(o);

            if(StringUtils.isNotEmpty(s)){
                String trim = s.trim();
                if(StringUtils.isNotEmpty(trim)){
                    return trim;
                }
            }
        }
        return null;
    }

    public List<OrsMonthlyShipmentEquipmentResp> importEquipment(OrsMonthlyShipmentEquipmentReq req) {
        try {
            List<OrsMonthlyShipmentEquipmentResp> list = new ArrayList<>();
            if (Objects.isNull(req.getFile().getOriginalFilename())) {
                list.add(new OrsMonthlyShipmentEquipmentResp("文件格式异常",null,null,null, "导入异常，文件格式不正确！"));
                return list;
            }
            String[] split = req.getFile().getOriginalFilename().split("\\.");
            if ((split.length == 2 && !split[1].equalsIgnoreCase("xlsx"))) {
                list.add(new OrsMonthlyShipmentEquipmentResp("文件格式异常", null,null,null,"导入异常，文件格式不正确！"));
                return list;
            } else if (split.length != 2) {
                list.add(new OrsMonthlyShipmentEquipmentResp("文件格式异常", null,null,null,"导入异常，文件格式不正确！"));
                return list;
            }

            ExcelReader reader = ExcelUtil.getReader(req.getFile().getInputStream(), 0);
            List<Map<String, Object>> read = reader.read(0, 1, Integer.MAX_VALUE);
            boolean hasError = false;
            for (int i = 0; i < read.size(); i++) {

                String deviceName = getReadString(read.get(i).get("设备编号"));
                String region = getReadString(read.get(i).get("海外大区"));
                String division = getReadString(read.get(i).get("事业部"));
                String productGroup = getReadString(read.get(i).get("产品组"));

                StringBuffer errorMsg = new StringBuffer();
                if (Objects.isNull(deviceName) || deviceName.length() < 5) {
                    errorMsg = getErrorMsg(errorMsg,"设备编号不能为空");
                } else {
                    boolean hasD = false;
                    for (int i1 = i + 1; i1 < read.size(); i1++) {
                        if (deviceName.equals(read.get(i1).get("设备编号"))) {
                            hasD = true;
                        }
                    }
                    if(hasD){
                        errorMsg = getErrorMsg(errorMsg,"待导入清单中设备编号重复");
                    }
                    if (deviceName.contains(" ")) {
                        errorMsg = getErrorMsg(errorMsg,"设备编号待导入文本存在空格");
                    }
                }
                if (Objects.isNull(region) || region.toString().length() < 2) {
                    errorMsg = getErrorMsg(errorMsg,"海外大区不能为空");
                } else {
                    if (String.valueOf(region).contains(" ")) {
                        errorMsg = getErrorMsg(errorMsg,"海外大区待导入文本存在空格");
                    }
                }
                if (Objects.isNull(division) || division.length() < 2) {
                    errorMsg = getErrorMsg(errorMsg,"事业部不能为空");
                } else {
                    if (division.contains(" ")) {
                        errorMsg = getErrorMsg(errorMsg,"事业部待导入文本存在空格");
                    }
                }
                if (Objects.isNull(productGroup) || productGroup.length() < 2) {
                    errorMsg = getErrorMsg(errorMsg,"产品组不能为空");
                } else {
                    if (productGroup.contains(" ")) {
                        errorMsg = getErrorMsg(errorMsg,"产品组待导入文本存在空格");
                    }
                }
                if(errorMsg.length()>0){
                    hasError = true;
                    list.add(new OrsMonthlyShipmentEquipmentResp(StringUtils.isNotEmpty(deviceName)?deviceName:"",StringUtils.isNotEmpty(region)?region:"",StringUtils.isNotEmpty(division)?division:"",StringUtils.isNotEmpty(productGroup)?productGroup:"", errorMsg.toString()));
                }else{
                    list.add(new OrsMonthlyShipmentEquipmentResp(String.valueOf(deviceName),String.valueOf(region),String.valueOf(division),String.valueOf(productGroup), ""));
                }
            }
            if (hasError) {
                return list;
            }
            int index = 2;

            list = new ArrayList<>();
            for (Map<String, Object> stringObjectMap : read) {
                int count = mapper.getDeviceCount(stringObjectMap.get("设备编号").toString().trim(), req.getTime());
                if (count > 0) {
                    hasError = true;
                    list.add(new OrsMonthlyShipmentEquipmentResp(stringObjectMap.get("设备编号").toString(),stringObjectMap.get("海外大区").toString(),stringObjectMap.get("事业部").toString(),stringObjectMap.get("产品组").toString(), "待导入设备变化在当月记录中已存在"));
                }else{
                    list.add(new OrsMonthlyShipmentEquipmentResp(stringObjectMap.get("设备编号").toString(),stringObjectMap.get("海外大区").toString(),stringObjectMap.get("事业部").toString(),stringObjectMap.get("产品组").toString(), ""));
                }
                index++;
            }
            if (hasError) {
                return list;
            }

            List<OrsMonthlyShipmentEquipment> equipmentList = new ArrayList<>();
            // 获得导入的大区信息
            List<String> regiones = read.stream().map(x -> x.get("海外大区").toString().trim()).distinct().collect(Collectors.toList());
            List<OrsBaseDeviceInfo> regionInfos = orsBaseDeviceInfoMapper.selectList(
                    new QueryWrapper<OrsBaseDeviceInfo>()
                            .lambda()
                            .select(OrsBaseDeviceInfo::getRegion, OrsBaseDeviceInfo::getRegionCode)
                            .in(OrsBaseDeviceInfo::getRegion, regiones)
                            .groupBy(OrsBaseDeviceInfo::getRegion, OrsBaseDeviceInfo::getRegionCode)
            );

            // 获得导入的产品组信息
            List<String> productes = read.stream().map(x -> x.get("产品组").toString().trim()).distinct().collect(Collectors.toList());
            List<OrsBaseDeviceInfo> productInfos = orsBaseDeviceInfoMapper.selectList(
                    new QueryWrapper<OrsBaseDeviceInfo>()
                            .lambda()
                            .select(OrsBaseDeviceInfo::getProductGroupCode, OrsBaseDeviceInfo::getProductGroup)
                            .in(OrsBaseDeviceInfo::getProductGroup, productes)
                            .groupBy(OrsBaseDeviceInfo::getProductGroupCode, OrsBaseDeviceInfo::getProductGroup)
            );

            // 获得导入的事业部信息
            List<String> divisions = read.stream().map(x -> x.get("事业部").toString().trim()).distinct().collect(Collectors.toList());
            List<OrsBaseDeviceInfo> divisionInfos = orsBaseDeviceInfoMapper.selectList(
                    new QueryWrapper<OrsBaseDeviceInfo>()
                            .lambda()
                            .select(OrsBaseDeviceInfo::getDivision, OrsBaseDeviceInfo::getDivisionCode)
                            .in(OrsBaseDeviceInfo::getDivision, divisions)
                            .groupBy(OrsBaseDeviceInfo::getDivision, OrsBaseDeviceInfo::getDivisionCode)
            );

            read.forEach(s -> {
                OrsMonthlyShipmentEquipment build = new OrsMonthlyShipmentEquipment();
                build.setImportTime(req.getTime());
                build.setDeviceName(s.get("设备编号").toString().trim());
                build.setAssetId(build.getAssetId());
                build.setDivision(s.get("海外大区").toString().trim());
                build.setProductGroup(s.get("产品组").toString().trim());
                build.setRegion(s.get("事业部").toString().trim());
                build.setDivisionCode(build.getDivision());
                build.setProductGroupCode(build.getProductGroup());
                build.setRegionCode(build.getRegion());

                OrsBaseDeviceInfo region = regionInfos.stream().filter(x -> x.getDeviceCode().equals(s.get("海外大区").toString().trim())).findFirst().orElse(null);
                if (null != region) {
                    build.setRegion(region.getRegion());
                    build.setRegionCode(region.getRegionCode());
                }
                OrsBaseDeviceInfo product = productInfos.stream().filter(x -> x.getDeviceCode().equals(s.get("产品组").toString().trim())).findFirst().orElse(null);
                if (null != product) {
                    build.setProductGroup(product.getProductGroup());
                    build.setProductGroupCode(product.getProductGroupCode());
                }

                OrsBaseDeviceInfo division = divisionInfos.stream().filter(x -> x.getDeviceCode().equals(s.get("事业部").toString().trim())).findFirst().orElse(null);
                if (null != division) {
                    build.setDivision(division.getDivision());
                    build.setDivisionCode(division.getDivisionCode());
                }

                equipmentList.add(build);
            });
            mapper.saveList(equipmentList);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private StringBuffer getErrorMsg(StringBuffer errorMsg,String error) {
        if(errorMsg.length()>0){
            errorMsg.append("|");
        }
        errorMsg.append(error);
        return errorMsg;
    }

    public List<String> getDeviceParamList(String time, String paramType, String paramValue) {

        String param = "";
        if ("name".equals(paramType)) {
            param = "t.region";
        } else if ("sybbh".equals(paramType)) {
            param = "t.division";
        } else if ("agent".equals(paramType)) {
            param = "t.product_group";
        } else {
            throw new EasyException("未知的参数类型");
        }
        return mapper.getParamList(time, param, paramValue);
    }



    public Page<OrsMonthlyShipmentEquipmentResp> getDeviceInfo(OrsMonthlyShipmentEquipmentReq dto) throws Exception {

        if (StringUtil.isEmpty(dto.getTime())) {
            throw new Exception("时间期间范围不能为空！");
        }
        Page<OrsMonthlyShipmentEquipmentResp> pageResp = new Page<>();
        if (dto.getCurrent() <= 0) {
            dto.setCurrent(1);
        }
        if (dto.getPageSize() <= 0) {
            dto.setPageSize(10);
        }
        pageResp.setCurrent(dto.getCurrent());
        pageResp.setPageSize(dto.getPageSize());
        int page = dto.getCurrent();
        int size = dto.getPageSize();
        dto.setPageSize(size);
        dto.setCurrent((page - 1) * size);

        try {
            if (Validator.isNotEmpty(dto.getName())) {
                if (dto.getName().contains(CommonConst.SPLIT)) {
                    List<String> splitList = StringUtil.getSplitList(dto.getName());
                    dto.setNameList(splitList);
                    dto.setName(null);
                }
            }
            if (Validator.isNotEmpty(dto.getSybbh())) {
                if (dto.getSybbh().contains(CommonConst.SPLIT)) {
                    List<String> splitList = StringUtil.getSplitList(dto.getSybbh());
                    dto.setSybbhList(splitList);
                    dto.setSybbh(null);
                }
            }
            if (Validator.isNotEmpty(dto.getAgent())) {
                if (dto.getAgent().contains(CommonConst.SPLIT)) {
                    List<String> splitList = StringUtil.getSplitList(dto.getAgent());
                    dto.setAgentList(splitList);
                    dto.setAgent(null);
                }
            }
            int pointListCount = mapper.getDeviceInfoCount(dto);
            if (pointListCount > 0) {
                List<OrsMonthlyShipmentEquipmentResp> pointList = mapper.getDeviceInfo(dto);

                pageResp.setRecords(pointList);

            }
            pageResp.setSize(size);
            pageResp.setTotal(pointListCount);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pageResp;

    }

    public void deleteDeviceInfo(OrsMonthlyShipmentEquipmentReq dto) throws Exception {
        mapper.deleteDeviceInfo(dto);
    }

}
