package com.rc.admin.easyapi.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSONObject;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisUtil;
import com.rc.admin.easyapi.constants.ParamAbnormalCode;
import com.rc.admin.easyapi.constants.ServiceNameAndUrl;
import com.rc.admin.easyapi.dao.IotDeviceInfoMapper;
import com.rc.admin.easyapi.entity.*;
import com.rc.admin.easyapi.model.req.IotDeviceInfoReq;
import com.rc.admin.easyapi.model.req.PointResultListReq;
import com.rc.admin.easyapi.model.resp.DeviceIdResp;
import com.rc.admin.easyapi.model.resp.DeviceInfoAllResp;
import com.rc.admin.easyapi.model.resp.IotDeviceInfoResp;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.easyapi.service.IotDataQualityInspectionHistoryService;
import com.rc.admin.easyapi.service.IotDeviceInfoService;
import com.rc.admin.easyapi.service.dqmService.OrsBigdataEquipmentBaseInfoAllService;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.DeviceDataRuleMapper;
import com.rc.admin.ors.quality.dao.OrsBigdataEquipmentBaseInfoAllMapper;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.utils.BusinessConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public class WorkbookUtil {

    @Resource
    private OrsBigdataEquipmentBaseInfoAllService infoAllService;

    public static void fileExportDeviceSave(List<InputStream> file, IotDeviceInfoMapper mapper,String url) {
        List<IotDeviceInfo> infos = new ArrayList<>(11);
        List<IotCalculatingData> icd = new ArrayList<>(11);
        List<IotCustomizePoint> icp = new ArrayList<>(11);
        List<IotCollectionPoint> cicp = new ArrayList<>(11);
        //获取设备deviceId
        Map<String, List<DeviceIdResp>> deviceListMap = null;
        String s = HttpRequest.sendGet("http://"+url+ ServiceNameAndUrl.DEVICE_ALL_INFO.getUrl(),  null);
        if (s!=null){
            Map map = JSONObject.parseObject(s, Map.class);
            if (Validator.isNotEmpty(map.get("code")) && (int)map.get("code") == 0){
                List<DeviceIdResp> idResps = JSONObject.parseArray(JSONObject.toJSONString(map.get("data")), DeviceIdResp.class);
                if (idResps.size()>0) {
                    deviceListMap = idResps.stream().collect(Collectors.groupingBy(DeviceIdResp::getDeviceCode));
                }
            }
        }
        mapper.deleteIotCalculatingData(url);
        for (InputStream inputStream : file) {
            try {
                // 创建excel工作簿
                Workbook  work = new XSSFWorkbook(inputStream);
                Sheet deviceInfo = work.getSheet("设备导出数据");
                IotDeviceInfo  info = deviceInfoSave(deviceInfo,url);
                if (deviceListMap!=null && !CollectionUtils.isEmpty(deviceListMap.get(info.getDeviceCode()))){
                    info.setDeviceId(deviceListMap.get(info.getDeviceCode()).get(0).getDeviceId());
                }
                if (StringUtils.isNotBlank(info.getDeviceCode())) {
                    try {
                        setProcessInfo(mapper, info);
                    } catch (Exception e) {
                        log.error("设置二级工艺编码异常:{}",e.getMessage());
                    }

                    infos.add(info);
                    Sheet iotCollectionPointDataSave = work.getSheet("采集点位");
                    List<IotCollectionPoint> iotCollectionPoints = IotCollectionPointSave(iotCollectionPointDataSave, info, cicp, url);
                    Sheet IotCalculatingDataSave = work.getSheet("计算导出数据");
                    List<IotCalculatingData> iotCalculatingData = IotCalculatingDataSave(IotCalculatingDataSave, info, icd, url);
                    Sheet IotCustomizePointSave = work.getSheet("自定义点位");
                    List<IotCustomizePoint> iotCustomizePoints = IotCustomizePointSave(IotCustomizePointSave, info, icp, url);

                    try {
                        RedisUtil.set(ServiceEnum.DEVICE_INFO+":"+info.getDeviceCode(), JSONObject.toJSONString(new DeviceInfoAllResp(info,iotCollectionPoints,iotCustomizePoints,iotCalculatingData)),0);
                        } catch (Exception e) {
                            log.error("redis缓存异常"+e.getMessage());
                        }

                    if (infos.size()>1000){
                        mapper.DeviceInfoSave(infos);
                        infos = new ArrayList<>(11);
                    }
                    if (cicp.size()>1000){
                        mapper.IotCollectionPointSave(cicp);
                        cicp = new ArrayList<>(11);
                    }
                    if (icd.size()>1000){
                        mapper.IotCalculatingDataSave(icd);
                        icd = new ArrayList<>(11);
                    }
                    if (icp.size()>1000){
                        mapper.IotCustomizePointSave(icp);
                        icp = new ArrayList<>(11);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if(!CollectionUtils.isEmpty(infos)){
            mapper.DeviceInfoSave(infos);
        }
        if(!CollectionUtils.isEmpty(icd)){
            mapper.IotCalculatingDataSave(icd);
        }
        if(!CollectionUtils.isEmpty(icp)){
            mapper.IotCustomizePointSave(icp);
        }
        if(!CollectionUtils.isEmpty(cicp)){
            mapper.IotCollectionPointSave(cicp);
        }
    }

    private static void setProcessInfo(IotDeviceInfoMapper mapper, IotDeviceInfo info) {
        Map<String,String> deviceProfile = mapper.selectProcessInfo(info.getDeviceCode());
        if (Objects.nonNull(deviceProfile)) {
            info.setFirstProcess(deviceProfile.get("firstProcess"));
            info.setFirstProcessName(deviceProfile.get("firstProcessName"));
            info.setSecondProcess(deviceProfile.get("secondProcess"));
            info.setSecondProcessName(deviceProfile.get("secondProcessName"));
        }
    }

    private static IotDeviceInfo deviceInfoSave(Sheet sheet,String url) {
        Row row;
        // 滤过第一行标题
        IotDeviceInfo info = new IotDeviceInfo();
        for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
            row = sheet.getRow(j);
            if (row == null || row.getFirstCellNum() == j) {
                continue;
            }
            if (j >1) {
                break;
            }
            info.setDeviceCode(row.getCell(1).getStringCellValue());
            info.setName(row.getCell(0).getStringCellValue());
            info.setChildCompanyName(row.getCell(2).getStringCellValue());
            info.setFactory(row.getCell(3).getStringCellValue());
            info.setWorkCenter(row.getCell(4).getStringCellValue());
            info.setWorkGroup(row.getCell(5).getStringCellValue());
            info.setProtocolType(row.getCell(6).getStringCellValue());
            info.setParaItemValue(row.getCell(7).getStringCellValue());
            info.setStationNumber(row.getCell(8).getStringCellValue());
            info.setScanIntervalTime(row.getCell(9).getStringCellValue());
            info.setConTimeout(row.getCell(10).getStringCellValue());
            info.setReconDelay(row.getCell(11).getStringCellValue());
            info.setCustomerParam(row.getCell(12).getStringCellValue());
            info.setDescript(row.getCell(13).getStringCellValue());
            if (row.getCell(14) != null) {
                info.setCollectionType(row.getCell(14).getStringCellValue());
            }
            info.setSource(url);
        }
        return info;
    }
    private static List<IotCalculatingData> IotCalculatingDataSave(Sheet sheet,IotDeviceInfo  info,List<IotCalculatingData> icdList,String url) {
        Row row;
        // 滤过第一行标题
        List<IotCalculatingData> list = new ArrayList<>();
        for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
            row = sheet.getRow(j);
            if (row == null || row.getFirstCellNum() == j) {
                continue;
            }
            IotCalculatingData icd = new IotCalculatingData();
            icd.setDeviceCode(info.getDeviceCode());
            icd.setDestAddressName(row.getCell(0).getStringCellValue());
            icd.setEventMode(row.getCell(1).getStringCellValue());
            icd.setEventCondition(row.getCell(2).getStringCellValue());
            icd.setExpress(row.getCell(3).getStringCellValue());
            icd.setDescription(row.getCell(4).getStringCellValue());
            icd.setSource(url);
            icdList.add(icd);
            list.add(icd);
        }
        return list;
    }
    private static List<IotCustomizePoint> IotCustomizePointSave(Sheet sheet,IotDeviceInfo  info,List<IotCustomizePoint> infoList,String url) {
        Row row;
        // 滤过第一行标题
        List<IotCustomizePoint> list = new ArrayList<>();
        for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
            row = sheet.getRow(j);
            if (row == null || row.getFirstCellNum() == j) {
                continue;
            }
            IotCustomizePoint icp = new IotCustomizePoint();
            icp.setDeviceCode(info.getDeviceCode());
            icp.setDestAddressName(row.getCell(0).getStringCellValue());
            icp.setType(row.getCell(1).getStringCellValue());
            icp.setConversionType(row.getCell(2).getStringCellValue());
            icp.setExpression(row.getCell(3).getStringCellValue());
            icp.setGatherMin(row.getCell(4).getStringCellValue());
            icp.setGatherMax(row.getCell(5).getStringCellValue());
            icp.setShiftMin(row.getCell(6).getStringCellValue());
            icp.setShiftMax(row.getCell(7).getStringCellValue());
            icp.setPrecisionType(row.getCell(8).getStringCellValue());
            icp.setDecimalPlace(row.getCell(9).getStringCellValue());
            icp.setExponent(row.getCell(10).getStringCellValue());
            icp.setUnit(row.getCell(11).getStringCellValue());
            icp.setDescription(row.getCell(12).getStringCellValue());
            icp.setSource(url);
            infoList.add(icp);
            list.add(icp);
        }
        return  list;
    }
    private static List<IotCollectionPoint> IotCollectionPointSave(Sheet sheet,IotDeviceInfo  info,List<IotCollectionPoint> infoList,String url) {
        Row row;
        // 滤过第一行标题
        List<IotCollectionPoint> list = new ArrayList<>();
        for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
            row = sheet.getRow(j);
            if (row == null || row.getFirstCellNum() == j) {
                continue;
            }
            IotCollectionPoint icp = new IotCollectionPoint();
            icp.setDeviceCode(info.getDeviceCode());
            icp.setDestAddressName(row.getCell(0).getStringCellValue());
            icp.setPointAddress(row.getCell(1).getStringCellValue());
            icp.setRegisterClass(row.getCell(2).getStringCellValue());
            icp.setStartingAddress(row.getCell(3).getStringCellValue());
            icp.setOffset(row.getCell(4).getStringCellValue());
            icp.setType(row.getCell(5).getStringCellValue());
            icp.setLength(row.getCell(6).getStringCellValue());
            icp.setBlockAddress(row.getCell(7).getStringCellValue());
            icp.setBlockType(row.getCell(8).getStringCellValue());
            icp.setByteExchange(row.getCell(9).getStringCellValue());
            icp.setReadWritePower(row.getCell(10).getStringCellValue());
            icp.setConversionType(row.getCell(11).getStringCellValue());
            icp.setExpression(row.getCell(12).getStringCellValue());
            icp.setGatherMin(row.getCell(13).getStringCellValue());
            icp.setGatherMax(row.getCell(14).getStringCellValue());
            icp.setShiftMin(row.getCell(15).getStringCellValue());
            icp.setShiftMax(row.getCell(16).getStringCellValue());
            icp.setPrecisionType(row.getCell(17).getStringCellValue());
            icp.setDecimalPlace(row.getCell(18).getStringCellValue());
            icp.setExponent(row.getCell(19).getStringCellValue());
            icp.setUnit(row.getCell(20).getStringCellValue());
            icp.setDescription(row.getCell(21).getStringCellValue());
            if (row.getCell(22) != null) {
                icp.setFrequencyAcquisition(row.getCell(22).getStringCellValue());
            }
            if (row.getCell(23) != null) {
                icp.setAcquisitionFrequency(Validator.isNotEmpty(row.getCell(23).getStringCellValue()) ? row.getCell(23).getStringCellValue() : info.getScanIntervalTime());
            }
            icp.setSource(url);
            infoList.add(icp);
            list.add(icp);
        }
        return list;
    }
    public static void IotDeviceInfoExport(HttpServletResponse response, IotDeviceInfoService service, IotDeviceInfoReq dataReq) throws Exception {
        String fileName = "自定义点位计算规则";
        // 告诉浏览器用什么软件可以打开此文件
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
        response.setCharacterEncoding("utf-8");
        DeviceInfoExport(response.getOutputStream(), service, dataReq);
    }
    static void DeviceInfoExport(OutputStream out, IotDeviceInfoService service, IotDeviceInfoReq dataReq) throws Exception {
        dataReq.setPageSize(500000);
        Page<IotDeviceInfoResp> iotDeviceInfoRespPage = service.selectPages(dataReq);
        List<IotDeviceInfoResp> records = iotDeviceInfoRespPage.getRecords();

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("自定义点位计算规则", "自定义点位计算规则"), IotDeviceInfoResp.class, records);
        try {
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new EasyException("文件写入失败[" + e.getMessage() + "]");
        }

    }


    public static void pageHistoryDataExport(HttpServletResponse response, IotDataQualityInspectionHistoryService service, String startTime, String endTime) throws Exception {
        String fileName = "数据质量检查历史信息";
        // 告诉浏览器用什么软件可以打开此文件
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
        response.setCharacterEncoding("utf-8");
        HistoryDataExport(response.getOutputStream(), service, startTime,endTime);
    }
    static void HistoryDataExport(OutputStream out, IotDataQualityInspectionHistoryService service,String startTime, String endTime) throws Exception {
        int index = 1;
        List<IotDataQualityInspectionHistory> records = service.pageHistoryData(startTime, endTime, index, 500000);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "数据质量检查历史信息"), IotDataQualityInspectionHistory.class, records);
        try {
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new EasyException("文件写入失败[" + e.getMessage() + "]");
        }
    }

    public static void pagePointDataExport(HttpServletResponse response, IotDeviceInfoService service, PointResultListReq req) throws Exception {
        String fileName = "iot设备点位信息";
        // 告诉浏览器用什么软件可以打开此文件
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
        response.setCharacterEncoding("utf-8");
        PointDataExport(response.getOutputStream(), service,  req);
    }
    static void PointDataExport(OutputStream out, IotDeviceInfoService service, PointResultListReq req) throws Exception {
        req.setCurrent(0);
        req.setPageSize(500000);
        Page<PointResultListResp> pointList = service.getPointList(req);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "iot设备点位信息"), PointResultListResp.class, pointList.getRecords());
        try {
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new EasyException("文件写入失败[" + e.getMessage() + "]");
        }
    }


    public static Workbook exportSaleDayReportNew(List<DeviceCountModelNew> dataExceptionsInfo,String sybbh) {
        //分析报表结构
        if (dataExceptionsInfo == null || dataExceptionsInfo.isEmpty()) {
            return null;
        }
        // 1.创建工作簿
        XSSFWorkbook workBook = new XSSFWorkbook();
        XSSFSheet sheet = workBook.createSheet("数据质量月度报表");
        AtomicReference<XSSFRow> row = new AtomicReference<>(sheet.createRow(0));
        String[] title;
        if ("1".equalsIgnoreCase(sybbh)){
            title = new String[]{"序号", "事业部", "模型", "设备台数", "注册台数",
                    "激活台数", "上数台数","设备剔除数", "检查项", "准确性异常数量", "完整性异常数量", "属性剔除设备数",
                    "检查项准确率", "检查项完整率", "总准确性异常设备数", "总完整性异常设备数",
                    "总准确性异常率", "总完整性异常率"};
        }else{
            title = new String[]{"序号", "大区", "模型", "设备台数", "注册台数",
                    "激活台数", "上数台数","设备剔除数", "检查项", "准确性异常数量", "完整性异常数量", "属性剔除设备数",
                    "检查项准确率", "检查项完整率", "总准确性异常设备数", "总完整性异常设备数",
                    "总准确性异常率", "总完整性异常率"};
        }
        XSSFCell cell = null;
        for (int i = 0; i < title.length; i++) {
            cell = row.get().createCell(i);
            cell.setCellValue(title[i]);
        }

        AtomicInteger index = new AtomicInteger(1);

        dataExceptionsInfo.forEach(ds -> {

            int mark = index.get();
            row.set(sheet.createRow(index.get()));
            row.get().createCell(0).setCellValue(index.get());

            row.get().createCell(1).setCellValue(ds.getDivision());
            row.get().createCell(2).setCellValue(ds.getModelName());
            row.get().createCell(3).setCellValue(ds.getTotal());
            row.get().createCell(4).setCellValue(ds.getRegistNum());

            //row.get().createCell(5).setCellValue(ds.getUnRegistNum());
            //BigDecimal registerRate = new BigDecimal(ds.getRegistRate()*100).setScale(BigDecimal.ROUND_HALF_UP,0);
            //row.get().createCell(6).setCellValue(registerRate.stripTrailingZeros().toPlainString() + "%");

            row.get().createCell(5).setCellValue(ds.getActiveNum());
            row.get().createCell(6).setCellValue(ds.getTotalReportNum());
            row.get().createCell(7).setCellValue(ds.getWholeExcludNum());

            row.get().createCell(8).setCellValue(ds.getPropertyName());
            row.get().createCell(9).setCellValue(String.valueOf(ds.getPropertyParamAbnormalTotalNum()));
            row.get().createCell(10).setCellValue(String.valueOf(ds.getPropertyNullAbnormalTotalNum()));
            row.get().createCell(11).setCellValue(String.valueOf(ds.getPropertyExcludNum()));

            DecimalFormat df = new DecimalFormat("#.##");
            row.get().createCell(12).setCellValue(df.format(ds.getCheckParamAbnormalRate() * 100) + "%");
            row.get().createCell(13).setCellValue(df.format(ds.getCheckNullAbnormalRate() * 100) + "%");
            row.get().createCell(14).setCellValue(ds.getParamAbnormalTotalNum());
            row.get().createCell(15).setCellValue(ds.getNullAbnormalTotalNum());
            row.get().createCell(16).setCellValue(df.format(ds.getParamAbnormalRate() * 100) + "%");
            row.get().createCell(17).setCellValue(df.format(ds.getNullAbnormalRate() * 100) + "%");

            if (mark != index.get()) {
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 2, 2));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 4, 4));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 5, 5));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 6, 6));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 7, 7));
                //sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 8, 8));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 14, 14));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 15, 15));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 16, 16));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 17, 17));
            }

            index.getAndIncrement();

        });

        return workBook;
    }


    public static Workbook exportSaleDayReport(List<DeviceCountModel> dataExceptionsInfo,String sybbh) {
        //分析报表结构
        if (dataExceptionsInfo == null || dataExceptionsInfo.isEmpty()) {
            return null;
        }
        // 1.创建工作簿
        XSSFWorkbook workBook = new XSSFWorkbook();
        XSSFSheet sheet = workBook.createSheet("数据质量月度报表");
        AtomicReference<XSSFRow> row = new AtomicReference<>(sheet.createRow(0));
        String[] title;
        if ("1".equalsIgnoreCase(sybbh)){
             title = new String[]{"序号", "事业部", "产品组", "设备台数", "注册台数",
                 "激活台数", "上数台数","设备剔除数", "检查项", "准确性异常数量", "完整性异常数量", "属性剔除设备数",
                 "检查项准确率", "检查项完整率", "总准确性异常设备数", "总完整性异常设备数",
                 "总准确性异常率", "总完整性异常率"};
        }else{
           title = new String[]{"序号", "大区", "产品组", "设备台数", "注册台数",
               "激活台数", "上数台数","设备剔除数", "检查项", "准确性异常数量", "完整性异常数量", "属性剔除设备数",
               "检查项准确率", "检查项完整率", "总准确性异常设备数", "总完整性异常设备数",
               "总准确性异常率", "总完整性异常率"};
        }
        XSSFCell cell = null;
        for (int i = 0; i < title.length; i++) {
            cell = row.get().createCell(i);
            cell.setCellValue(title[i]);
        }

        AtomicInteger index = new AtomicInteger(1);

        dataExceptionsInfo.forEach(ds -> {

            int mark = index.get();
            row.set(sheet.createRow(index.get()));
            row.get().createCell(0).setCellValue(index.get());

            row.get().createCell(1).setCellValue(ds.getDivision());
            row.get().createCell(2).setCellValue(ds.getProductGroup());
            row.get().createCell(3).setCellValue(ds.getTotal());
            row.get().createCell(4).setCellValue(ds.getRegistNum());

            //row.get().createCell(5).setCellValue(ds.getUnRegistNum());
            //BigDecimal registerRate = new BigDecimal(ds.getRegistRate()*100).setScale(BigDecimal.ROUND_HALF_UP,0);
            //row.get().createCell(6).setCellValue(registerRate.stripTrailingZeros().toPlainString() + "%");

            row.get().createCell(5).setCellValue(ds.getActiveNum());
            row.get().createCell(6).setCellValue(ds.getTotalReportNum());
            row.get().createCell(7).setCellValue(ds.getWholeExcludNum());

            row.get().createCell(8).setCellValue(ds.getPropertyName());
            row.get().createCell(9).setCellValue(String.valueOf(ds.getPropertyParamAbnormalTotalNum()));
            row.get().createCell(10).setCellValue(String.valueOf(ds.getPropertyNullAbnormalTotalNum()));
            row.get().createCell(11).setCellValue(String.valueOf(ds.getPropertyExcludNum()));

            DecimalFormat df = new DecimalFormat("#.##");
            row.get().createCell(12).setCellValue(df.format(ds.getCheckParamAbnormalRate() * 100) + "%");
            row.get().createCell(13).setCellValue(df.format(ds.getCheckNullAbnormalRate() * 100) + "%");
            row.get().createCell(14).setCellValue(ds.getParamAbnormalTotalNum());
            row.get().createCell(15).setCellValue(ds.getNullAbnormalTotalNum());
            BigDecimal paramAbnormalRate = new BigDecimal(ds.getParamAbnormalRate()*100).setScale(BigDecimal.ROUND_HALF_UP,0);
            row.get().createCell(16).setCellValue(paramAbnormalRate.stripTrailingZeros().toPlainString() + "%");
            BigDecimal nullAbnormalRate = new BigDecimal(ds.getNullAbnormalRate()*100).setScale(BigDecimal.ROUND_HALF_UP,0);
            row.get().createCell(17).setCellValue(nullAbnormalRate.stripTrailingZeros().toPlainString() + "%");

            if (mark != index.get()) {
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 2, 2));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 4, 4));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 5, 5));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 6, 6));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 7, 7));
                //sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 8, 8));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 14, 14));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 15, 15));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 16, 16));
                sheet.addMergedRegion(new CellRangeAddress(mark, index.get(), 17, 17));
            }

                index.getAndIncrement();

        });

        return workBook;
    }

    public static Workbook exportImportDevice(List<OrsMonthlyShipmentEquipmentResp> dataExceptionsInfo) {
        // 1.创建工作簿
        XSSFWorkbook workBook = new XSSFWorkbook();
        XSSFSheet sheet = workBook.createSheet(dataExceptionsInfo == null || dataExceptionsInfo.isEmpty() ? "月度发货导入模板" : "月度发货导入异常消息");
        AtomicReference<XSSFRow> row = new AtomicReference<>(sheet.createRow(0));
        String[] title = {"设备编号", "海外大区","事业部","产品组","错误原因"};
        if (dataExceptionsInfo == null || dataExceptionsInfo.isEmpty()) {
            title = new String[]{"设备编号","海外大区","事业部","产品组"};
        }

        XSSFCell cell = null;
        for (int i = 0; i < title.length; i++) {
            cell = row.get().createCell(i);
            cell.setCellValue(title[i]);
        }
        if (dataExceptionsInfo == null || dataExceptionsInfo.isEmpty()) {
            return workBook;
        }
        AtomicInteger index = new AtomicInteger(1);
        dataExceptionsInfo.forEach( ds -> {
            row.set(sheet.createRow(index.get()));
            row.get().createCell(0).setCellValue(ds.getDeviceName());
            row.get().createCell(1).setCellValue(ds.getMsg());
            index.getAndIncrement();
        });

        return workBook;
    }

    public static Workbook deviceDataRuleDetailExceptionInfoExport(DqmDeviceDataRuleDetailExceptionsReq req, DeviceDataRuleMapper mapper) throws Exception {
        return deviceDataRuleDetailExport(req,mapper);
    }

    static Workbook deviceDataRuleDetailExport(DqmDeviceDataRuleDetailExceptionsReq dto, DeviceDataRuleMapper mapper){
        List<DqmDeviceDataRuleDetailExceptionsResp> copyOnWriteArrayList
                = mapper.findAbnormalDetail(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, -1), dto).getRecords();

        // 创建工作簿对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 创建工作表对象
        XSSFSheet sheet = workbook.createSheet("规则异常数据统计明细");
        // 写入表头
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("事业部");
        header.createCell(1).setCellValue("模型ID");
        header.createCell(2).setCellValue("数据中心");
        header.createCell(3).setCellValue("设备编号");
        header.createCell(4).setCellValue("物联盒ID");
        header.createCell(5).setCellValue("检查规则");
        header.createCell(6).setCellValue("异常现象");
        header.createCell(7).setCellValue("异常数据发生时间");
        header.createCell(8).setCellValue("实例名称");
        header.createCell(9).setCellValue("模型名称");
        header.createCell(10).setCellValue("产品组");
        header.createCell(11).setCellValue("国家");
        header.createCell(12).setCellValue("异常数据");

        for (int i = 0; i < copyOnWriteArrayList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(copyOnWriteArrayList.get(i).getSybbh());
            row.createCell(1).setCellValue(copyOnWriteArrayList.get(i).getModelId());
            row.createCell(2).setCellValue (BusinessConst.dataCenterMap.get(copyOnWriteArrayList.get(i).getDataCenterId()));
            row.createCell(3).setCellValue (copyOnWriteArrayList.get(i).getDeviceName());
            row.createCell(4).setCellValue (copyOnWriteArrayList.get(i).getRcAssetId());

            row.createCell(5).setCellValue (copyOnWriteArrayList.get(i).getCheckRuleName());
            row.createCell(6).setCellValue (copyOnWriteArrayList.get(i).getAbnormalPhenomenonName());

            row.createCell(7).setCellValue (copyOnWriteArrayList.get(i).getStatDate());
            row.createCell(8).setCellValue(copyOnWriteArrayList.get(i).getDeviceName());
            row.createCell(9).setCellValue(copyOnWriteArrayList.get(i).getModelName());
            row.createCell(10).setCellValue(copyOnWriteArrayList.get(i).getZehdSpartdesc());
            row.createCell(11).setCellValue(copyOnWriteArrayList.get(i).getCountry());
            row.createCell(12).setCellValue(copyOnWriteArrayList.get(i).getAbnormalData());
        }
        return workbook;
    }


    public static Workbook deviceDataRuleExceptionInfoExport(DqmDeviceDataRuleExceptionsReq req, DeviceDataRuleMapper mapper) throws Exception {
        return deviceDataRuleExport(req,mapper);
    }

    static Workbook deviceDataRuleExport(DqmDeviceDataRuleExceptionsReq dto, DeviceDataRuleMapper mapper){
        List<DqmDeviceDataRuleExceptionsResp> copyOnWriteArrayList
                = mapper.findAbnormal(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, -1), dto).getRecords();

        // 创建工作簿对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 创建工作表对象
        XSSFSheet sheet = workbook.createSheet("规则异常数据统计");
        // 写入表头
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("事业部");
        header.createCell(1).setCellValue("模型ID");
        header.createCell(2).setCellValue("数据中心");
        header.createCell(3).setCellValue("设备编号");
        header.createCell(4).setCellValue("物联盒ID");
        header.createCell(5).setCellValue("检查规则");
        header.createCell(6).setCellValue("异常数量");
        header.createCell(7).setCellValue("模型名称");
        header.createCell(8).setCellValue("产品组");
        header.createCell(9).setCellValue("国家");

        for (int i = 0; i < copyOnWriteArrayList.size(); i++) {
            Row row = sheet.createRow(i + 1);

            AtomicReference<String> abnormalCount = new AtomicReference<>("");
            DqmDeviceDataRuleExceptionsResp dqmDeviceDataRuleExceptionsResp = copyOnWriteArrayList.get(i);
            List<DqmRuleAbnormalModel> ruleAbnormalModelList = dqmDeviceDataRuleExceptionsResp.getRuleAbnormalModelList();
            if(CollUtil.isNotEmpty(ruleAbnormalModelList)){
                for (DqmRuleAbnormalModel item : ruleAbnormalModelList) {
                    abnormalCount.set(String.format("%s %s", abnormalCount, item.getAbnormalNameAndCount()));
                }
            }else{
                abnormalCount.set(copyOnWriteArrayList.get(i).getAbnormalData());
            }
            row.createCell(0).setCellValue(copyOnWriteArrayList.get(i).getSybbh());
            row.createCell(1).setCellValue(copyOnWriteArrayList.get(i).getModelId());
            row.createCell(2).setCellValue (BusinessConst.dataCenterMap.get(copyOnWriteArrayList.get(i).getDataCenterId()));
            row.createCell(3).setCellValue (copyOnWriteArrayList.get(i).getDeviceName());
            row.createCell(4).setCellValue (copyOnWriteArrayList.get(i).getRcAssetId());
            row.createCell(5).setCellValue (copyOnWriteArrayList.get(i).getCheckRuleName());
            row.createCell(6).setCellValue (abnormalCount.get());
            row.createCell(7).setCellValue (copyOnWriteArrayList.get(i).getModelName());
            row.createCell(8).setCellValue(copyOnWriteArrayList.get(i).getZehdSpartdesc());
            row.createCell(9).setCellValue(copyOnWriteArrayList.get(i).getCountry());
        }
        return workbook;
    }



    public static Workbook deviceDataExceptionInfoExport(DqmDeviceDataExceptionsReq req, DeviceDataAbnormalDetailDayMapper mapper) throws Exception {
        return exceptionInfoExport( req,mapper);
    }
    public static Workbook historyDeviceDataExceptionInfoExport(DqmHistoryDeviceDataExceptionsReq req, OrsBigdataEquipmentBaseInfoAllMapper mapper) throws Exception {
        return exceptionHistoryInfoExport( req,mapper);
    }
    static Workbook exceptionInfoExport( DqmDeviceDataExceptionsReq dto,DeviceDataAbnormalDetailDayMapper mapper) throws Exception {
        CopyOnWriteArrayList<DqmDeviceDataExceptionsResp> copyOnWriteArrayList = new CopyOnWriteArrayList<>();

        copyOnWriteArrayList.addAll(mapper.findAbnormalDetail(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, -1), dto).getRecords());

        // 创建工作簿对象
        XSSFWorkbook workbook = new XSSFWorkbook();

// 创建工作表对象
        XSSFSheet sheet = workbook.createSheet("设备数据异常明细");

// 写入表头
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("事业部");
        header.createCell(1).setCellValue("模型ID");
        header.createCell(2).setCellValue("模型名称");
        header.createCell(3).setCellValue("数据中心");
        header.createCell(4).setCellValue("设备编号");
        header.createCell(5).setCellValue("检查项");
        header.createCell(6).setCellValue("异常项");
        header.createCell(7).setCellValue("异常数量");
        header.createCell(8).setCellValue("最后异常内容");
        header.createCell(9).setCellValue("数据发生时间");
        header.createCell(10).setCellValue("物标识");
        header.createCell(11).setCellValue("实例名称");
        header.createCell(12).setCellValue("固件版本号");
        header.createCell(13).setCellValue("硬件版本");
        header.createCell(14).setCellValue("认证秘钥");
        header.createCell(15).setCellValue("产品组");


        for (int i = 0; i < copyOnWriteArrayList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(copyOnWriteArrayList.get(i).getSybbh());
            row.createCell(1).setCellValue(copyOnWriteArrayList.get(i).getModelId());
            row.createCell(2).setCellValue(copyOnWriteArrayList.get(i).getModelName());
            row.createCell(3).setCellValue (BusinessConst.dataCenterMap.get(copyOnWriteArrayList.get(i).getDataCenterId()));
            row.createCell(4).setCellValue (copyOnWriteArrayList.get(i).getDeviceCode());
            row.createCell(5).setCellValue (copyOnWriteArrayList.get(i).getPropertyName());
            row.createCell(6).setCellValue (copyOnWriteArrayList.get(i).getAbnormalName());
            row.createCell(7).setCellValue (copyOnWriteArrayList.get(i).getAbnormalNum());
            row.createCell(8).setCellValue (copyOnWriteArrayList.get(i).getAbnormalData());
            row.createCell(9).setCellValue (copyOnWriteArrayList.get(i).getAbnormalTime());
            row.createCell(10).setCellValue (copyOnWriteArrayList.get(i).getAssetId());
            row.createCell(11).setCellValue (copyOnWriteArrayList.get(i).getThingId());
            row.createCell(12).setCellValue(copyOnWriteArrayList.get(i).getFwVersion());
            row.createCell(13).setCellValue(copyOnWriteArrayList.get(i).getHwVersion());
            row.createCell(14).setCellValue(copyOnWriteArrayList.get(i).getAuthToken());
            row.createCell(15).setCellValue(copyOnWriteArrayList.get(i).getZehdSpartdesc());
        }
        return workbook;
    }

    static void setHistoryDeviceData(DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp,String row,Map<String,String> abnormalDataMap) {
        String deviceCode = dqmHistoryDeviceDataExceptionResp.getDeviceCode();
        long hasRecord = dqmHistoryDeviceDataExceptionResp.getHasRecord();
        long notRecord = dqmHistoryDeviceDataExceptionResp.getNotRecord();

        //设备状态 8503              device_status_9008    [9008]
        List<DqmDeviceDataAbnormalModel> deviceStatusModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDeviceStatus9008() > 0) {
            getDqmDeviceDataAbnormalModels(deviceStatusModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceStatus9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8503(deviceStatusModelList);
        //设备位置 8501             device_location_9008  [9008, 9001, 9002, 9007]
        List<DqmDeviceDataAbnormalModel> deviceLocationModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9001() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8501.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setDeviceLocation9001Data(abnormalData);
                }
            }
        }

        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9002() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9002.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9002()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8501.getCode() + "_" + ParamAbnormalCode.PARAM_9002.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setDeviceLocation9002Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9007() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9007.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9007()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8501.getCode() + "_" + ParamAbnormalCode.PARAM_9007.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setDeviceLocation9007Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9008() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8501(deviceLocationModelList);
        //发动机工作时间 8105         engine_worktime  [9008, 9001, 9004]
        List<DqmDeviceDataAbnormalModel> engineWorktimeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9001() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8105.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setEngineWorktime9001Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9004() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9004()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8105.getCode() + "_" + ParamAbnormalCode.PARAM_9004.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setEngineWorktime9004Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9008() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8105(engineWorktimeModelList);
        //工作时间 8102             working_time [9008 9001, 9004]
        List<DqmDeviceDataAbnormalModel> worktimeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9001() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8102.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setWorkingTime9001Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9004() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9004()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8102.getCode() + "_" + ParamAbnormalCode.PARAM_9004.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setWorkingTime9004Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9008() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8102(worktimeModelList);
//                总油耗 8201              total_fuel_consumption [9008 9001, 9004]
        List<DqmDeviceDataAbnormalModel> totalFuelConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8201.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setTotalFuelConsumption9001Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9004()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8201.getCode() + "_" + ParamAbnormalCode.PARAM_9004.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setTotalFuelConsumption9004Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8201(totalFuelConsumptionModelList);
//                泵送方量 8401             pumping_volume [9008 9001, 9004]
        List<DqmDeviceDataAbnormalModel> pumpingVolumeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9001() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8401.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setPumpingVolume9001Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9004() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9004()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8401.getCode() + "_" + ParamAbnormalCode.PARAM_9004.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setPumpingVolume9004Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9008() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8401(pumpingVolumeModelList);
//                行驶里程 8403             driving_mileage [9008 9001, 9004]
        List<DqmDeviceDataAbnormalModel> drivingMileageModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9001() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9001()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8403.getCode() + "_" + ParamAbnormalCode.PARAM_9001.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setDrivingMileage9001Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9004() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9004()));
            if("1".equals(row)){
                String abnormalData = abnormalDataMap.get(deviceCode + "_" + ParamAbnormalCode.ABNORMAL_8403.getCode() + "_" + ParamAbnormalCode.PARAM_9004.getCode());
                if(StringUtils.isNotEmpty(abnormalData)){
                    dqmHistoryDeviceDataExceptionResp.setDrivingMileage9004Data(abnormalData);
                }
            }
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9008() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9008()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8403(drivingMileageModelList);
    }

    static List<DqmDeviceDataAbnormalModel> getDqmDeviceDataAbnormalModels(List<DqmDeviceDataAbnormalModel> models,String code,String count){
        DqmDeviceDataAbnormalModel dqmDeviceDataAbnormalModel = new DqmDeviceDataAbnormalModel();
        dqmDeviceDataAbnormalModel.setAbnormalCode(code);
        dqmDeviceDataAbnormalModel.setAbnormalCount(count);
        models.add(dqmDeviceDataAbnormalModel);
        return models;
    }

    static Workbook exceptionHistoryInfoExport( DqmHistoryDeviceDataExceptionsReq dto,OrsBigdataEquipmentBaseInfoAllMapper mapper) throws Exception {
        if (StringUtil.isEmpty(dto.getStartTime()) && StringUtil.isEmpty(dto.getEndTime())){
            throw  new Exception("时间期间范围不能为空！");
        }
        dto.setCurrent(1);
        dto.setPageSize(50000);
        if ("其他".equalsIgnoreCase(dto.getSybbh())) {
            dto.setSybbh("");
        }
        dto.setCreateTime_start(DateUtil.parseDate(dto.getStartTime()));
        dto.setCreateTime_end(DateUtil.parseDate(dto.getEndTime()));
        if (dto.getCurrent() <= 0) {
            dto.setCurrent(1);
        }
        if (dto.getPageSize() <= 0) {
            dto.setPageSize(10);
        }
        if (Validator.isEmpty(dto.getSortField())) {
            dto.setSortField("name");
            dto.setSortOrder("asc");
        }
        if("0".equalsIgnoreCase(dto.getInstallType())){
            dto.setInstallType("");
        }


        if (StringUtils.isNotBlank(dto.getModelId())){
            if (dto.getModelId().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getModelId());
                dto.setModelIdList(splitList);
                dto.setModelId(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getModelName())){
            if (dto.getModelName().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getModelName());
                dto.setModelNameList(splitList);
                dto.setModelName(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getSybbh())){
            if (dto.getSybbh().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getSybbh());
                dto.setSybbhList(splitList);
                dto.setSybbh(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getZehdSpartdesc())){
            if (dto.getZehdSpartdesc().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getZehdSpartdesc());
                dto.setZehdSpartdescList(splitList);
                dto.setZehdSpartdesc(null);
            }
        }

        if (Validator.isNotEmpty(dto.getDeviceName())) {
            if (dto.getDeviceName().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getDeviceName());
                dto.setDeviceNameList(splitList);
                dto.setDeviceNameLast(null);
            }
        }
        if (Validator.isNotEmpty(dto.getDeviceCode())) {
            if (dto.getDeviceCode().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getDeviceCode());
                dto.setDeviceCodeList(splitList);
                dto.setDeviceCode(null);
            }
        }

        String hasHuaXin = dto.getHasHuaXin();
        if(!"1".equals(hasHuaXin)){
            List<String> huaXinModelIdList = mapper.queryHuaXinModelId();
            if(!CollectionUtils.isEmpty(huaXinModelIdList) && huaXinModelIdList.size()>0){
                dto.setHuaXinModelIdList(huaXinModelIdList);
            }
        }

        //多行
        List<DqmHistoryDeviceDataExceptionResp> rowLine = new ArrayList<>();
//        String[] paramCodeList = {"8503","8501","8105","8102","8201","8401","8403"};
        if("1".equals(dto.getRow())){
            //一行
            rowLine = mapper.getHistoryDeviceDataExceptionsInfoForOneAll(dto);
        }else{
            //多行
            rowLine = mapper.getHistoryDeviceDataExceptionsInfoForListAll(dto);
        }

        // 创建工作簿对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        // 创建工作表对象
        XSSFSheet sheet = workbook.createSheet("异常统计");
        // 写入表头
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("事业部");
        header.createCell(1).setCellValue("产品组");
        header.createCell(2).setCellValue("所属大区");
        header.createCell(3).setCellValue("设备编号");
        header.createCell(4).setCellValue("物联盒ID");
        header.createCell(5).setCellValue("开始日期");
        header.createCell(6).setCellValue("结束日期");
        header.createCell(7).setCellValue("设备状态(异常次数)");
        header.createCell(8).setCellValue("总油耗(异常次数)");

        header.createCell(9).setCellValue("总油耗属性值异常数据");
        header.createCell(10).setCellValue("总油耗属性值逆增长数据");
        header.createCell(11).setCellValue("经纬度(异常次数)");
        header.createCell(12).setCellValue("设备位置属性值异常数据");
        header.createCell(13).setCellValue("设备位置属性值超限数据");
        header.createCell(14).setCellValue("设备位置漂移数据");
        header.createCell(15).setCellValue("发动机工作时间(异常次数)");
        header.createCell(16).setCellValue("发动机工作时间属性值异常数据");
        header.createCell(17).setCellValue("发动机工作时间属性值逆增长数据");
        header.createCell(18).setCellValue("行驶里程(异常次数)");
        header.createCell(19).setCellValue("行驶里程属性值异常数据");
        header.createCell(20).setCellValue("行驶里程属性值逆增长数据");
        header.createCell(21).setCellValue("工作时间(异常次数)");
        header.createCell(22).setCellValue("工作时间属性值异常数据");
        header.createCell(23).setCellValue("工作时间属性值逆增长数据");
        header.createCell(24).setCellValue("方量(异常次数)");
        header.createCell(25).setCellValue("泵送方量属性值异常数据");
        header.createCell(26).setCellValue("泵送方量属性值逆增长数据");

        header.createCell(27).setCellValue("固件版本");
        header.createCell(28).setCellValue("硬件版本");
        header.createCell(29).setCellValue("认证秘钥");
        header.createCell(30).setCellValue("模型ID");
        header.createCell(31).setCellValue("模型名称");
        if(!CollectionUtils.isEmpty(rowLine) && rowLine.size()>0){
            Map<String,String> abnormalDataMap = new HashMap<>();
            if("1".equals(dto.getRow())) {
                List<DqmHistoryDeviceDataExceptionResp> deviceHistoryAbnormalData = mapper.getDeviceHistoryAbnormalData(dto);
                if(!CollectionUtils.isEmpty(deviceHistoryAbnormalData)){
                    for (DqmHistoryDeviceDataExceptionResp deviceHistoryAbnormalDatum : deviceHistoryAbnormalData) {
                        String deviceCode = deviceHistoryAbnormalDatum.getDeviceCode();
                        String paramCode = deviceHistoryAbnormalDatum.getParamCode();
                        String abnormalCode = deviceHistoryAbnormalDatum.getAbnormalCode();
                        abnormalDataMap.put(deviceCode+"_"+paramCode+"_"+abnormalCode,deviceHistoryAbnormalDatum.getAbnormalData());
                    }
                }
            }
            for (int i = 0; i < rowLine.size(); i++) {
                DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp = rowLine.get(i);
                setHistoryDeviceData(dqmHistoryDeviceDataExceptionResp,dto.getRow(),abnormalDataMap);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(dqmHistoryDeviceDataExceptionResp.getSybbh());
                row.createCell(1).setCellValue(dqmHistoryDeviceDataExceptionResp.getZehdSpartdesc());
                row.createCell(2).setCellValue (dqmHistoryDeviceDataExceptionResp.getZehdsvReg());
                row.createCell(3).setCellValue (dqmHistoryDeviceDataExceptionResp.getDeviceName());
                row.createCell(4).setCellValue (dqmHistoryDeviceDataExceptionResp.getDeviceCode());
                row.createCell(5).setCellValue (dqmHistoryDeviceDataExceptionResp.getStartStatDate());
                row.createCell(6).setCellValue (dqmHistoryDeviceDataExceptionResp.getEndStatDate());
                List<DqmDeviceDataAbnormalModel> abnormal8503 = dqmHistoryDeviceDataExceptionResp.getAbnormal8503();
                if(!CollectionUtils.isEmpty(abnormal8503)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8503);
                    Cell cell = row.createCell(7);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }

                List<DqmDeviceDataAbnormalModel> abnormal8201 = dqmHistoryDeviceDataExceptionResp.getAbnormal8201();
                if(!CollectionUtils.isEmpty(abnormal8201)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8201);
                    Cell cell = row.createCell(8);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(9).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9001Data()));
                row.createCell(10).setCellValue(replaceStr(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9004Data()));
                List<DqmDeviceDataAbnormalModel> abnormal8501 = dqmHistoryDeviceDataExceptionResp.getAbnormal8501();
                if(!CollectionUtils.isEmpty(abnormal8501)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8501);
                    Cell cell = row.createCell(11);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(12).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9001Data()));
                row.createCell(13).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9002Data()));
                row.createCell(14).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9007Data()));
                List<DqmDeviceDataAbnormalModel> abnormal8105 = dqmHistoryDeviceDataExceptionResp.getAbnormal8105();
                if(!CollectionUtils.isEmpty(abnormal8105)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8105);
                    Cell cell = row.createCell(15);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(16).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9001Data()));
                row.createCell(17).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9004Data()));
                List<DqmDeviceDataAbnormalModel> abnormal8403 = dqmHistoryDeviceDataExceptionResp.getAbnormal8403();
                if(!CollectionUtils.isEmpty(abnormal8403)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8403);
                    Cell cell = row.createCell(18);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(19).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9001Data()));
                row.createCell(20).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9004Data()));
                List<DqmDeviceDataAbnormalModel> abnormal8102 = dqmHistoryDeviceDataExceptionResp.getAbnormal8102();
                if(!CollectionUtils.isEmpty(abnormal8102)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8102);
                    Cell cell = row.createCell(21);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(22).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getWorkingTime9001Data()));
                row.createCell(23).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getWorkingTime9004Data()));
                List<DqmDeviceDataAbnormalModel> abnormal8401 = dqmHistoryDeviceDataExceptionResp.getAbnormal8401();
                if(!CollectionUtils.isEmpty(abnormal8401)){
                    StringBuffer stringBuffer = getAbnormalStr( abnormal8401);
                    Cell cell = row.createCell(24);
                    cell.setCellValue (stringBuffer.toString());
                    cell.setCellStyle(cellStyle);
                }
                row.createCell(25).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9001Data()));
                row.createCell(26).setCellValue (replaceStr(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9004Data()));
                row.createCell(27).setCellValue (dqmHistoryDeviceDataExceptionResp.getFwVersion());
                row.createCell(28).setCellValue (dqmHistoryDeviceDataExceptionResp.getHwVersion());
                row.createCell(29).setCellValue (dqmHistoryDeviceDataExceptionResp.getAuthToken());
                row.createCell(30).setCellValue (dqmHistoryDeviceDataExceptionResp.getModelId());
                row.createCell(31).setCellValue (dqmHistoryDeviceDataExceptionResp.getModelName());
            }
        }



//        // 创建工作表对象
//        XSSFSheet sheet1 = workbook.createSheet("异常数据");
//        // 写入表头
//        Row header1 = sheet1.createRow(0);
//        header1.createCell(0).setCellValue("事业部");
//        header1.createCell(1).setCellValue("产品组");
//        header1.createCell(2).setCellValue("所属大区");
//        header1.createCell(3).setCellValue("设备编号");
//        header1.createCell(4).setCellValue("物联盒ID");
//        header1.createCell(5).setCellValue("异常发生时间");
//        header1.createCell(6).setCellValue("属性");
//        header1.createCell(7).setCellValue("异常项");
//        header1.createCell(8).setCellValue("异常数据");
//
//        for (int i = 0; i < pointList.size(); i++) {
//            Row row = sheet1.createRow(i + 1);
//            row.createCell(0).setCellValue(pointList.get(i).getSybbh());
//            row.createCell(1).setCellValue(pointList.get(i).getZehdSpartdesc());
//            row.createCell(2).setCellValue (pointList.get(i).getZehdsvReg());
//            row.createCell(3).setCellValue (pointList.get(i).getDeviceName());
//            row.createCell(4).setCellValue (pointList.get(i).getThingId());
//            row.createCell(5).setCellValue (pointList.get(i).getAbnormalTime());
//            String paramCode = pointList.get(i).getParamCode();
//            row.createCell(6).setCellValue (getParamAbnormalName(paramCode));
//            String abnormalCode = pointList.get(i).getAbnormalCode();
//            row.createCell(7).setCellValue (getParamAbnormalName(abnormalCode));
//            row.createCell(8).setCellValue (pointList.get(i).getAbnormalData());
//        }

        return workbook;
    }

    private static String replaceStr(String abnormalData){
        if(StringUtils.isNotEmpty(abnormalData)){
            abnormalData = abnormalData.replace("\"", "");
            abnormalData = abnormalData.replace("[","");
            abnormalData = abnormalData.replace("]","");

        }
        return abnormalData;
    }

    private static String getParamAbnormalName(String paramCode) {
        if(ParamAbnormalCode.PARAM_9001.getCode().equals(paramCode)){
            return ParamAbnormalCode.PARAM_9001.getName();
        }else if(ParamAbnormalCode.PARAM_9002.getCode().equals(paramCode)){
            return ParamAbnormalCode.PARAM_9002.getName();
        }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(paramCode)){
            return ParamAbnormalCode.PARAM_9004.getName();
        }else if(ParamAbnormalCode.PARAM_9007.getCode().equals(paramCode)){
            return ParamAbnormalCode.PARAM_9007.getName();
        }else if(ParamAbnormalCode.PARAM_9008.getCode().equals(paramCode)){
            return ParamAbnormalCode.PARAM_9008.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8501.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8501.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8503.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8503.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8105.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8105.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8102.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8102.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8401.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8401.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8201.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8201.getName();
        }else if(ParamAbnormalCode.ABNORMAL_8403.getCode().equals(paramCode)){
            return ParamAbnormalCode.ABNORMAL_8403.getName();
        }else{
            return null;
        }
    }

    @NotNull
    private static StringBuffer getAbnormalStr( List<DqmDeviceDataAbnormalModel> abnormal8503) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < abnormal8503.size(); i++) {
            DqmDeviceDataAbnormalModel abnormalModel = abnormal8503.get(i);
            String abnormalCode = abnormalModel.getAbnormalCode();
            String str = "";
            String name = "";
            if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                name = ParamAbnormalCode.PARAM_9001.getName();
            }else if(ParamAbnormalCode.PARAM_9002.getCode().equals(abnormalCode)){
                name = ParamAbnormalCode.PARAM_9002.getName();
            }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                name = ParamAbnormalCode.PARAM_9004.getName();
            }else if(ParamAbnormalCode.PARAM_9007.getCode().equals(abnormalCode)){
                name = ParamAbnormalCode.PARAM_9007.getName();
            }else if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                name = ParamAbnormalCode.PARAM_9008.getName();
            }
            String abnormalCount = abnormalModel.getAbnormalCount();
            str = name +"("+abnormalCount+")";
            if(i==0){
                stringBuffer.append(str);
            }else{
                stringBuffer.append("\n"+str);
            }
        }
        return stringBuffer;
    }

}
