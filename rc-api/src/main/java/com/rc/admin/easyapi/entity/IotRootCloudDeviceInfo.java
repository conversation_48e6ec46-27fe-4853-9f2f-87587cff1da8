package com.rc.admin.easyapi.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IotRootCloudDeviceInfo {
    @ApiModelProperty(value = "物标识")
    private  String assetId;
    @ApiModelProperty(value = "物模型id")
    private  String modelId;
    @ApiModelProperty(value = "物实例id")
    private  String thingId;
    @ApiModelProperty(value = "设备类型")
    private  String classId;
    @ApiModelProperty(value = "设备协议")
    private  String protocol;
    @ApiModelProperty(value = "同步")
    private  String phase;
}
