package com.rc.admin.easyapi.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.easyapi.model.req.DqmDeviceQueryReq;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;
import com.rc.admin.ors.quality.model.OtDeviceAllResp;
import com.rc.admin.ors.quality.model.OtDevicePage;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface ExternalInterfacesService {


    Page<DeviceLedgerResp> getDevicePage(Page<DqmDeviceQueryReq> reqPage,DqmDeviceQueryReq dqmDeviceQueryReq);




    void importCheckConfig(@RequestParam("file") MultipartFile file, HttpServletResponse response);



    void importCheckConfigDelete(@RequestParam("file") MultipartFile file, HttpServletResponse response);


    OtDevicePage<OtDeviceAllResp>  getAllDevice(OtDeviceAllReq req);

}
