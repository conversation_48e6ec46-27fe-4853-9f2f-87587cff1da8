package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "ors_model_eliminate_flag_config")
@ApiModel(value = "ors_model_eliminate_flag_config对象", description = "设备属性eliminate_flag配置表")
public class IotEliminateFlagConfig {

    @TableField(value = "model_id")
    @ApiModelProperty(value = "模型ID")
    private String modelId;
    @TableField(value = "flag_length")
    @ApiModelProperty(value = "标签长度")
    private Integer flagLength;
    @TableField(value = "flag_config")
    @ApiModelProperty(value = "标签信息")
    private String flagConfig;


    @TableField(value = "flag_enable")
    @ApiModelProperty(value = "是否启用:1启用 0禁用")
    private Integer flagEnable;
    @TableField(value = "flag_desc")
    @ApiModelProperty(value = "描述")
    private String flagDesc;
    /**
     * 数据生成时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}
