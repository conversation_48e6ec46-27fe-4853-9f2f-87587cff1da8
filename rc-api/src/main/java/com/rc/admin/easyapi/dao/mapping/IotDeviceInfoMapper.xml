<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.easyapi.dao.IotDeviceInfoMapper">

    <resultMap id="PointResultListMap" type="com.rc.admin.easyapi.model.resp.PointResultListResp">
        <result column="region" property="region" jdbcType="VARCHAR" />
        <result column="child_company_name" property="childCompanyName" jdbcType="VARCHAR" />
        <result column="pointType" property="pointType" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="device_code" property="deviceCode" jdbcType="VARCHAR" />
        <result column="dest_address_name" property="destAddressName" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="point_address" property="pointAddress" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="read_write_power" property="readWritePower" jdbcType="VARCHAR" />
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="register_class" property="registerClass" jdbcType="VARCHAR" />
        <result column="starting_address" property="startingAddress" jdbcType="VARCHAR" />
        <result column="offset" property="offset" jdbcType="VARCHAR" />
        <result column="express" property="express" jdbcType="VARCHAR" />
        <result column="event_mode" property="eventMode" jdbcType="VARCHAR" />
        <result column="event_condition" property="eventCondition" jdbcType="VARCHAR" />
        <result column="frequency_acquisition" property="frequencyAcquisition" jdbcType="VARCHAR" />
        <result column="acquisition_frequency" property="acquisitionFrequency" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="PointResultMap" type="com.rc.admin.easyapi.model.resp.PointResultListResp">
        <result column="region" property="region" jdbcType="VARCHAR" />
        <result column="child_company_name" property="childCompanyName" jdbcType="VARCHAR" />
        <result column="pointType" property="pointType" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="device_code" property="deviceCode" jdbcType="VARCHAR" />
        <result column="dest_address_name" property="destAddressName" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="point_address" property="pointAddress" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="express" property="express" jdbcType="VARCHAR" />
        <result column="event_mode" property="eventMode" jdbcType="VARCHAR" />
        <result column="event_condition" property="eventCondition" jdbcType="VARCHAR" />
        <result column="acquisition_frequency" property="acquisitionFrequency" jdbcType="VARCHAR" />
        <result column="second_process" property="secondProcess" jdbcType="VARCHAR" />
        <result column="protocol_type" property="protocolType" jdbcType="VARCHAR" />
    </resultMap>


    <sql id = "PointResultListOrderBy">
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='region' and dto.sortOrder == 'asc'">
            ORDER BY  d.region asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='region' and dto.sortOrder == 'desc'">
            ORDER BY  d.region desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='childCompanyName' and dto.sortOrder == 'asc'">
            ORDER BY  d.child_company_name asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='childCompanyName' and dto.sortOrder == 'desc'">
            ORDER BY  d.child_company_name desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='destAddressName' and dto.sortOrder == 'asc'">
            ORDER BY  d.dest_address_name asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='destAddressName' and dto.sortOrder == 'desc'">
            ORDER BY  d.dest_address_name desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='name' and dto.sortOrder == 'asc'">
            ORDER BY  d.name asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='name' and dto.sortOrder == 'desc'">
            ORDER BY  d.name desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='pointType' and dto.sortOrder == 'asc'">
            ORDER BY  d.pointType asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='pointType' and dto.sortOrder == 'desc'">
            ORDER BY  d.pointType desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='deviceCode' and dto.sortOrder == 'asc'">
            ORDER BY  d.device_code asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='deviceCode' and dto.sortOrder == 'desc'">
            ORDER BY  d.device_code desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='description' and dto.sortOrder == 'asc'">
            ORDER BY  d.description asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='description' and dto.sortOrder == 'desc'">
            ORDER BY  d.description desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='eventMode' and dto.sortOrder == 'asc'">
            ORDER BY  d.event_mode asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='eventMode' and dto.sortOrder == 'desc'">
            ORDER BY  d.event_mode desc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='express' and dto.sortOrder == 'asc'">
            ORDER BY  d.express asc
        </if>
        <if test="dto.sortField !='' and dto.sortField != null and  dto.sortField=='express' and dto.sortOrder == 'desc'">
            ORDER BY  d.express desc
        </if>
    </sql>

        <select id="selectPages"  resultType="com.rc.admin.easyapi.model.resp.IotDeviceInfoResp">
            select
            da.device_code as deviceCode,
            da.dest_address_name as destAddressName,
            da.event_mode as eventMode,
            da.event_condition as eventCondition,
            da.express,
            da.description,
            da.name,
            da.child_company_name as childCompanyName,
            da.factory,
            da.work_center as workCenter,
            da.work_group as workGroup,
            da.protocol_type as protocolType,
            da.paraItem_value as paraItemValue,
            da.station_number as stationNumber,
            da.collection_type as collectionType,
            da.region
            from (
            select
            icp.device_code ,
            icp.dest_address_name ,
            icd.event_mode ,
            icd.event_condition ,
            icd.express,
            icp.description,
            idi.name,
            idi.child_company_name ,
            idi.factory,
            idi.work_center ,
            idi.work_group ,
            idi.protocol_type ,
            idi.paraItem_value ,
            idi.station_number ,
            idi.collection_type,
            irc.region
            from
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation == 1 ">
                (select DISTINCT
                a.*
                from (
                select b.* from rc_iot_customize_point as a
                left join rc_iot_calculating_data as b on a.dest_address_name = b.dest_address_name and a.device_code =
                b.device_code ) as a
                ,(
                select b.* from rc_iot_customize_point as a
                left join rc_iot_calculating_data as b on a.dest_address_name = b.dest_address_name and a.device_code =
                b.device_code ) as b
                where a.express != '' and a.device_code = b.device_code and a.express like
                concat('%','{',b.dest_address_name,'}','%')) as icd
                left join rc_iot_device_info as idi on icd.device_code = idi.device_code
                left join rc_iot_region_config as irc on idi.source = irc.ip
                left join rc_iot_customize_point as icp on icd.device_code = icp.device_code and icd.dest_address_name = icp.dest_address_name
            </if>
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">
                rc_iot_customize_point as  icp
                left join rc_iot_calculating_data as icd on icd.device_code = icp.device_code and icp.dest_address_name = icd.dest_address_name
                left join rc_iot_device_info as idi on idi.device_code = icp.device_code
                left join rc_iot_region_config as irc on idi.source = irc.ip
            </if>

            where 1=1
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation == 1 ">
            <if test="req.destAddressName !='' and req.destAddressName != null">
                and icd.dest_address_name like concat('%',#{req.destAddressName},'%')
            </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icd.device_code like concat('%',#{req.deviceCode},'%')
                </if>
            </if>
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">
                <if test="req.destAddressName !='' and req.destAddressName != null">
                    and icp.dest_address_name like concat('%',#{req.destAddressName},'%')
                </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icp.device_code like concat('%',#{req.deviceCode},'%')
                </if>
            </if>

            <if test="req.name !='' and req.name != null">
                and idi.name like concat('%',#{req.name},'%')
            </if>
            <if test="req.protocolType !='' and req.protocolType != null">
                and idi.protocol_type like concat('%',#{req.protocolType},'%')
            </if>
            <if test="req.factory !='' and req.factory != null">
                and idi.factory like concat('%',#{req.factory},'%')
            </if>
            <if test="req.eventMode !='' and req.eventMode != null">
                and icd.event_mode like concat('%',#{req.eventMode},'%')
            </if>
            <if test="req.express !='' and req.express != null ">
                and icd.express like concat('%',#{req.express},'%')
            </if>

            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">

            UNION ALL

            select
            icp.device_code ,
            icp.dest_address_name ,
            '' as event_mode ,
            '' as event_condition ,
            '' as express,
            icp.description,
            idi.name,
            idi.child_company_name ,
            idi.factory,
            idi.work_center ,
            idi.work_group ,
            idi.protocol_type ,
            idi.paraItem_value ,
            idi.station_number ,
            idi.collection_type,
            irc.region
            FROM rc_iot_collection_point icp
            left join rc_iot_device_info as idi on icp.device_code = idi.device_code
            left join rc_iot_region_config as irc on idi.source = irc.ip
            where 1=1
                <if test="req.destAddressName !='' and req.destAddressName != null">
                    and icp.dest_address_name  like concat('%',#{req.destAddressName},'%')
                </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icp.device_code like concat('%',#{req.deviceCode},'%')
                </if>
                <if test="req.name !='' and req.name != null">
                    and idi.name like concat('%',#{req.name},'%')
                </if>
                <if test="req.protocolType !='' and req.protocolType != null">
                    and idi.protocol_type like concat('%',#{req.protocolType},'%')
                </if>
                <if test="req.factory !='' and req.factory != null">
                    and idi.factory like concat('%',#{req.factory},'%')
                </if>
                <if test="req.eventMode !='' and req.eventMode != null">
                    and '' like concat('%',#{req.eventMode},'%')
                </if>
                <if test="req.express !='' and req.express != null ">
                    and '' like concat('%',#{req.express},'%')
                </if>
            </if>
            ) as da
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='name' and req.sortOrder == 'asc'">
                ORDER BY  da.name asc
            </if>
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='name' and req.sortOrder == 'desc'">
                ORDER BY  da.name desc
            </if>
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='protocolType' and req.sortOrder == 'asc'">
                ORDER BY  da.protocol_type asc
            </if>
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='protocolType' and req.sortOrder == 'desc'">
                ORDER BY  da.protocol_type desc
            </if>
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='destAddressName' and req.sortOrder == 'asc'">
                ORDER BY  da.dest_address_name asc
            </if>
            <if test="req.sortField !='' and req.sortField != null and  req.sortField=='destAddressName' and req.sortOrder == 'desc'">
                ORDER BY  da.dest_address_name desc
            </if>
            limit #{req.current},#{req.pageSize}
        </select>

        <select id="selectPagesCount"  resultType="java.lang.Integer">
            select
            count(1)
            from (
            select
            idi.device_code ,
            icd.dest_address_name ,
            icd.event_mode ,
            icd.event_condition ,
            icd.express,
            icd.description,
            idi.name,
            idi.child_company_name ,
            idi.factory,
            idi.work_center ,
            idi.work_group ,
            idi.protocol_type ,
            idi.paraItem_value ,
            idi.station_number ,
            idi.collection_type
            from
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation == 1 ">
                (select DISTINCT
                a.*
                from (
                select b.* from rc_iot_customize_point as a
                left join rc_iot_calculating_data as b on a.dest_address_name = b.dest_address_name and a.device_code =
                b.device_code ) as a
                ,(
                select b.* from rc_iot_customize_point as a
                left join rc_iot_calculating_data as b on a.dest_address_name = b.dest_address_name and a.device_code =
                b.device_code ) as b
                where a.express != '' and a.device_code = b.device_code and a.express like
                concat('%','{',b.dest_address_name,'}','%')) as icd
                left join rc_iot_device_info as idi on icd.device_code = idi.device_code
            </if>
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">
                rc_iot_customize_point as  icp
                left join rc_iot_calculating_data as icd on icd.device_code = icp.device_code and icp.dest_address_name = icd.dest_address_name
                left join rc_iot_device_info as idi on idi.device_code = icp.device_code
                left join rc_iot_region_config as irc on idi.source = irc.ip
            </if>

            where 1=1
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation == 1 ">
                <if test="req.destAddressName !='' and req.destAddressName != null">
                    and icd.dest_address_name like concat('%',#{req.destAddressName},'%')
                </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icd.device_code like concat('%',#{req.deviceCode},'%')
                </if>
            </if>
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">
                <if test="req.destAddressName !='' and req.destAddressName != null">
                    and icp.dest_address_name like concat('%',#{req.destAddressName},'%')
                </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icp.device_code like concat('%',#{req.deviceCode},'%')
                </if>
            </if>
            <if test="req.name !='' and req.name != null">
                and idi.name like concat('%',#{req.name},'%')
            </if>
            <if test="req.protocolType !='' and req.protocolType != null">
                and idi.protocol_type like concat('%',#{req.protocolType},'%')
            </if>
            <if test="req.factory !='' and req.factory != null">
                and idi.factory like concat('%',#{req.factory},'%')
            </if>
            <if test="req.eventMode !='' and req.eventMode != null">
                and icd.event_mode like concat('%',#{req.eventMode},'%')
            </if>
            <if test="req.express !='' and req.express != null ">
            and icd.express like concat('%',#{req.express},'%')
            </if>
            <if test="req.secondaryCalculation !='' and req.secondaryCalculation != null and req.secondaryCalculation != 1 ">

            UNION ALL

            select
            icp.device_code ,
            icp.dest_address_name ,
            '' as event_mode ,
            '' as event_condition ,
            '' as express,
            icp.description,
            idi.name,
            idi.child_company_name ,
            idi.factory,
            idi.work_center ,
            idi.work_group ,
            idi.protocol_type ,
            idi.paraItem_value ,
            idi.station_number ,
            idi.collection_type
            FROM rc_iot_collection_point icp
            left join rc_iot_device_info as idi on icp.device_code = idi.device_code
            where 1=1
                <if test="req.destAddressName !='' and req.destAddressName != null">
                    and icp.dest_address_name  like concat('%',#{req.destAddressName},'%')
                </if>
                <if test="req.deviceCode !='' and req.deviceCode != null">
                    and icp.device_code like concat('%',#{req.deviceCode},'%')
                </if>
                <if test="req.name !='' and req.name != null">
                    and idi.name like concat('%',#{req.name},'%')
                </if>
                <if test="req.protocolType !='' and req.protocolType != null">
                    and idi.protocol_type like concat('%',#{req.protocolType},'%')
                </if>
                <if test="req.factory !='' and req.factory != null">
                    and idi.factory like concat('%',#{req.factory},'%')
                </if>
                <if test="req.eventMode !='' and req.eventMode != null">
                    and '' like concat('%',#{req.eventMode},'%')
                </if>
                <if test="req.express !='' and req.express != null ">
                    and '' like concat('%',#{req.express},'%')
                </if>
            </if>
            ) as da
        </select>


    <select id="getDeviceCodeList" resultType="java.util.Map">
        select idi.device_code as  deviceCode,idi.source,ircdi.model_id as modelId,ircdi.thing_id as thingId, idi.protocol_type as protocolType
        from rc_iot_device_info as idi
        left  join rc_iot_root_cloud_device_info as ircdi on idi.device_code = ircdi.asset_id
where 1=1
        <if test="deviceCode !='' and deviceCode != null ">
            and device_code like concat('%',#{deviceCode},'%')
        </if>
    </select>



    <select id="getPointList" resultMap="PointResultListMap">

        select
            d.region,
            d.child_company_name,
            d.pointType,
            d.name,
            d.device_code,
            d.dest_address_name,
            d.description,
            d.point_address,
            d.type,
            d.read_write_power,
            d.unit,
            d.register_class,
            d.starting_address,
            d.offset,
            d.express,
            d.event_mode,
            d.event_condition,
            case when d.frequency_acquisition = 'NO' then '否'
                 else d.frequency_acquisition
                     end as frequency_acquisition,
            d.acquisition_frequency
        from (
                 select
                     irc.region,
                     idi.child_company_name,
                     '采集点位' as pointType,
                     idi.name,
                     idi.device_code,
                     icp.dest_address_name,
                     icp.description,
                     icp.point_address,
                     icp.type,
                     icp.read_write_power,
                     icp.unit,
                     icp.register_class,
                     icp.starting_address,
                     icp.offset,
                     '' as express,
                     '' as event_mode,
                     '' as event_condition,
                     icp.frequency_acquisition,
                     icp.acquisition_frequency
                 from rc_iot_collection_point as icp
                          LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
                          LEFT JOIN rc_iot_region_config as irc on idi.source = irc.ip
                 where 1=1
        <if test="dto.childCompanyName !='' and dto.childCompanyName != null ">
            and idi.child_company_name like concat('%',#{dto.childCompanyName},'%')
        </if>
        <if test="dto.region !='' and dto.region != null ">
            and irc.region like concat('%',#{dto.region},'%')
        </if>
        <if test="dto.deviceCode !='' and dto.deviceCode != null ">
            and (idi.device_code like concat('%',#{dto.deviceCode},'%') or idi.name like concat('%',#{dto.deviceCode},'%'))
        </if>
        <if test="dto.destAddressName !='' and dto.destAddressName != null ">
            and icp.dest_address_name like concat('%',#{dto.destAddressName},'%')
        </if>
        <if test="dto.description !='' and dto.description != null ">
            and icp.description like concat('%',#{dto.description},'%')
        </if>
                 union ALL
                 select
                     irc.region,
                     idi.child_company_name,
                     '自定义点位' as pointType,
                     idi.name,
                     idi.device_code,
                     icp.dest_address_name,
                     icp.description,
                     '' as point_address,
                     icp.type,
                     '' as read_write_power,
                     icp.unit,
                     '' as register_class,
                     '' as starting_address,
                     '' as offset,
                     icd.express,
                     icd.event_mode,
                     icd.event_condition,
                     idi.collection_type as frequency_acquisition,
                     '' as acquisition_frequency
                 from rc_iot_customize_point as icp
                     LEFT JOIN rc_iot_calculating_data as icd on icd.device_code = icp.device_code and  icp.dest_address_name = icd.dest_address_name
                     LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
                     LEFT JOIN rc_iot_region_config as irc on idi.source = irc.ip
                 where 1=1
        <if test="dto.childCompanyName !='' and dto.childCompanyName != null ">
            and idi.child_company_name like concat('%',#{dto.childCompanyName},'%')
        </if>
        <if test="dto.region !='' and dto.region != null ">
            and irc.region like concat('%',#{dto.region},'%')
        </if>
        <if test="dto.deviceCode !='' and dto.deviceCode != null ">
            and (idi.device_code like concat('%',#{dto.deviceCode},'%') or idi.name like concat('%',#{dto.deviceCode},'%'))
        </if>
        <if test="dto.destAddressName !='' and dto.destAddressName != null ">
            and icd.dest_address_name like concat('%',#{dto.destAddressName},'%')
        </if>
        <if test="dto.description !='' and dto.description != null ">
            and icd.description like concat('%',#{dto.description},'%')
        </if>             ) as d
        <include refid="PointResultListOrderBy" />
            limit #{dto.current},#{dto.pageSize}
    </select>



    <select id="getPointListCount" resultType="java.lang.Integer">

        select
       sum(d.counts)
        from (
        select
        count(1) as counts
        from rc_iot_collection_point as icp
        LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
        LEFT JOIN rc_iot_region_config as irc on idi.source = irc.ip
        where 1=1
        <if test="dto.childCompanyName !='' and dto.childCompanyName != null ">
            and idi.child_company_name like concat('%',#{dto.childCompanyName},'%')
        </if>
        <if test="dto.region !='' and dto.region != null ">
            and irc.region like concat('%',#{dto.region},'%')
        </if>
        <if test="dto.deviceCode !='' and dto.deviceCode != null ">
            and (idi.device_code like concat('%',#{dto.deviceCode},'%') or idi.name like concat('%',#{dto.deviceCode},'%'))
        </if>
        <if test="dto.destAddressName !='' and dto.destAddressName != null ">
            and icp.dest_address_name like concat('%',#{dto.destAddressName},'%')
        </if>
        <if test="dto.description !='' and dto.description != null ">
            and icp.description like concat('%',#{dto.description},'%')
        </if>
        union ALL
        select
        count(1) as counts
        from rc_iot_customize_point as icp
        LEFT JOIN rc_iot_calculating_data as icd on icd.device_code = icp.device_code and  icp.dest_address_name = icd.dest_address_name
        LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
        LEFT JOIN rc_iot_region_config as irc on idi.source = irc.ip
        where 1=1
        <if test="dto.childCompanyName !='' and dto.childCompanyName != null ">
            and idi.child_company_name like concat('%',#{dto.childCompanyName},'%')
        </if>
        <if test="dto.region !='' and dto.region != null ">
            and irc.region like concat('%',#{dto.region},'%')
        </if>
        <if test="dto.deviceCode !='' and dto.deviceCode != null ">
            and (idi.device_code like concat('%',#{dto.deviceCode},'%') or idi.name like concat('%',#{dto.deviceCode},'%'))
        </if>
        <if test="dto.destAddressName !='' and dto.destAddressName != null ">
            and icd.dest_address_name like concat('%',#{dto.destAddressName},'%')
        </if>
        <if test="dto.description !='' and dto.description != null ">
            and icd.description like concat('%',#{dto.description},'%')
        </if>             ) as d
    </select>


    <select id="getPointInfo" resultMap="PointResultListMap">

        select
        d.pointType,
        d.device_code,
        d.dest_address_name,
        d.description,
        d.point_address,
        d.type,
        d.read_write_power,
        d.unit,
        d.register_class,
        d.starting_address,
        d.offset,
        d.express,
        d.event_mode,
        d.event_condition,
        d.frequency_acquisition,
        d.acquisition_frequency
        from (
        select
        '采集点位' as pointType,
        icp.device_code,
        icp.dest_address_name,
        icp.description,
        icp.point_address,
        icp.type,
        icp.read_write_power,
        icp.unit,
        icp.register_class,
        icp.starting_address,
        icp.offset,
        '' as express,
        '' as event_mode,
        '' as event_condition,
        icp.frequency_acquisition,
        case when (icp.acquisition_frequency = '' or icp.acquisition_frequency is null) then idi.scan_interval_time else icp.acquisition_frequency end as acquisition_frequency
        from rc_iot_collection_point as icp
        LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code

        where icp.device_code = #{deviceCode}
        union ALL
        select
        '自定义点位' as pointType,
        icp.device_code,
        icp.dest_address_name,
        icp.description,
        '' as point_address,
        icp.type,
        '' as read_write_power,
        icp.unit,
        '' as register_class,
        '' as starting_address,
        '' as offset,
        icd.express,
        icd.event_mode,
        icd.event_condition,
        case when idi.collection_type = 'NO' then '否' else idi.collection_type end as frequency_acquisition,
        idi.scan_interval_time as acquisition_frequency
        from rc_iot_customize_point as icp
       LEFT JOIN rc_iot_calculating_data as icd on icd.device_code = icp.device_code and icp.dest_address_name = icd.dest_address_name
        LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
        where idi.device_code = #{deviceCode}) as d
    </select>


    <select id="getPointInfoBySecondProcess" resultMap="PointResultMap">
        select
            d.region,
            d.second_process,
            d.name,
            d.device_code,
            d.child_company_name,
            d.pointType,
            d.dest_address_name,
            d.description,
            d.type,
            d.acquisition_frequency,
            d.point_address,
            d.event_mode,
            d.express,
            d.event_condition,
        d.protocol_type
        from (
                 select
                     irc.region,
                     idi.second_process,
                     idi.name,
                     idi.device_code,
                     idi.child_company_name,
                     '采集点位' as pointType,
                     icp.dest_address_name,
                     icp.description,
                     icp.type,
        case when (icp.acquisition_frequency = '' or icp.acquisition_frequency is null) then idi.scan_interval_time else icp.acquisition_frequency end as acquisition_frequency,
                     icp.point_address,
                     '' as event_mode,
                     '' as express,
                     '' as event_condition,
        idi.protocol_type
                 from rc_iot_collection_point as icp
                          LEFT JOIN rc_iot_device_info as idi  on icp.device_code = idi.device_code
                          LEFT JOIN rc_iot_region_config as irc  on irc.ip = idi.source
                 where idi.second_process = #{secondProcess}
        <if test="protocolName !='' and protocolName != null ">
            and idi.protocol_type = #{protocolName}
        </if>
                 union ALL
        SELECT
        irc.region,
        idi.second_process,
        idi. NAME,
        idi.device_code,
        idi.child_company_name,
        '自定义点位' AS pointType,
        icp.dest_address_name,
        icp.description,
        icp.type,
        idi.scan_interval_time AS acquisition_frequency,
        '' AS point_address,
        '' AS event_mode,
        '' AS express,
        '' AS event_condition,
        idi.protocol_type
        FROM
        rc_iot_customize_point AS icp
        LEFT JOIN rc_iot_device_info AS idi ON icp.device_code = idi.device_code
        LEFT JOIN rc_iot_region_config AS irc ON irc.ip = idi.source
                 where idi.second_process = #{secondProcess}
        <if test="protocolName !='' and protocolName != null ">
            and idi.protocol_type = #{protocolName}
        </if>
        union ALL
        SELECT
        irc.region,
        idi.second_process,
        idi. NAME,
        idi.device_code,
        idi.child_company_name,
        '计算点位' AS pointType,
        icd.dest_address_name,
        icd.description,
        icp.type,
        idi.scan_interval_time AS acquisition_frequency,
        '' AS point_address,
        icd.event_mode,
        icd.express,
        icd.event_condition,
        idi.protocol_type

        FROM
        rc_iot_calculating_data AS icd
        LEFT JOIN rc_iot_customize_point AS icp ON icp.device_code = icd.device_code
        AND icp.dest_address_name = icd.dest_address_name
        LEFT JOIN rc_iot_device_info AS idi ON icp.device_code = idi.device_code
        LEFT JOIN rc_iot_region_config AS irc ON irc.ip = idi.source
        where idi.second_process = #{secondProcess}
        <if test="protocolName !='' and protocolName != null ">
            and idi.protocol_type = #{protocolName}
        </if>
                 ) as d

    </select>

    <select id="getNotPointInfoBySecondProcess" resultMap="PointResultMap">
        select
            d.region,
            d.second_process,
            d.name,
            d.device_code,
            d.child_company_name,
            d.pointType,
            d.dest_address_name,
            d.description,
            d.type,
            d.acquisition_frequency,
            d.point_address,
            d.event_mode,
            d.express,
            d.event_condition,
        d.protocol_type
        from (
                 select
                     irc.region,
                     idi.second_process,
                     idi.name,
                     idi.device_code,
                     idi.child_company_name,
                     '采集点位' as pointType,
                     icp.dest_address_name,
                     icp.description,
                     icp.type,
        case when (icp.acquisition_frequency = '' or icp.acquisition_frequency is null) then idi.scan_interval_time else icp.acquisition_frequency end as acquisition_frequency,
                     icp.point_address,
                     '' as event_mode,
                     '' as express,
                     '' as event_condition,
        idi.protocol_type
                 from rc_iot_device_info as idi
                          LEFT JOIN rc_iot_collection_point as icp on icp.device_code = idi.device_code
                          LEFT JOIN rc_iot_region_config as irc  on irc.ip = idi.source
                 where icp.device_code = '' or icp.device_code is null AND idi.second_process = #{secondProcess}
        <if test="protocolName !='' and protocolName != null ">
            and idi.protocol_type = #{protocolName}
        </if>
                 union ALL
                 select
                     irc.region,
                     idi.second_process,
                     idi.name,
                     idi.device_code,
                     idi.child_company_name,
                     '自定义点位' as pointType,
                     icd.dest_address_name,
                     icd.description,
                     icp.type,
        idi.scan_interval_time as acquisition_frequency,
        '' as point_address,
                     icd.event_mode,
                     icd.express,
                     icd.event_condition,
        idi.protocol_type
                 from rc_iot_device_info as idi
                          LEFT JOIN rc_iot_customize_point as icp on idi.device_code = icp.device_code
                          LEFT JOIN rc_iot_calculating_data as icd  on idi.device_code = icd.device_code
                          LEFT JOIN rc_iot_region_config as irc  on irc.ip = idi.source
                 where icd.device_code = '' or icd.device_code is null AND idi.second_process = #{secondProcess}
        <if test="protocolName !='' and protocolName != null ">
            and idi.protocol_type = #{protocolName}
        </if>
                 ) as d
    </select>

</mapper>
