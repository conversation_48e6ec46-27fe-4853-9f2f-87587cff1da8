package com.rc.admin.easyapi.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.dao.DeviceProfileMapper;
import com.rc.admin.exception.CustomException;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.util.office.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeviceProfileService {

    private final DeviceProfileMapper deviceProfileMapper;

    private final OrgAndProcessDictService orgAndProcessDictService;

    @Autowired
    public DeviceProfileService(DeviceProfileMapper deviceProfileMapper, OrgAndProcessDictService orgAndProcessDictService) {
        this.deviceProfileMapper = deviceProfileMapper;
        this.orgAndProcessDictService = orgAndProcessDictService;
    }

    /**
     * 同步设备档案
     */
    public void syncDeviceProfiles() {
        int total = getTotal();
        if (total > 0) {
            deviceProfileMapper.clearDeviceProfile();
        }
        int pageSize = 3000;
        int pages = (total + pageSize - 1) / pageSize;

        Map<String, Integer> params = new HashMap<>(2);
        params.put("pageSize", pageSize);
        for (int i = 1; i <= pages; i++) {
            params.put("pageNum", i);
            JSONObject responseBody = requestOpenApi(params);
            List<JSONObject> data = responseBody.getJSONObject("data").getBeanList("list", JSONObject.class);
            List<DeviceProfile> deviceProfiles = data.stream().map(item -> {
                JSONObject owner = item.getJSONObject("owner");
                return DeviceProfile.builder()
                        .deviceCode(owner.getStr("machineNo"))
                        .deviceName(owner.getStr("machineName"))
                        .orgCode(owner.getStr("orgCode"))
                        .orgName(owner.getStr("orgName"))
                        .companyCode(owner.getStr("companyCode"))
                        .companyName(owner.getStr("companyName"))
                        .firstProcess(owner.getStr("firstGroupCode"))
                        .firstProcessName(owner.getStr("firstGroupName"))
                        .secondProcess(owner.getStr("secondGroupCode"))
                        .secondProcessName(owner.getStr("secondGroupName"))
                        .location(owner.getStr("equipLocation"))
                        .ipAddress(owner.getStr("ip"))
                        .build();
            }).collect(Collectors.toList());
            deviceProfileMapper.upsertDeviceProfiles(deviceProfiles);
            // 处理机构与工艺字典信息
            orgAndProcessDictService.dealOrgAndProcessDict(deviceProfiles);
        }
    }

    private int getTotal() {
        Map<String, Integer> params = new HashMap<>(2);
        params.put("pageNum", 1);
        params.put("pageSize", 1);
        JSONObject responseBody = requestOpenApi(params);
        return responseBody.getJSONObject("data").getInt("total");
    }

    private JSONObject requestOpenApi(Map<String, Integer> params) {
        String url = "http://***********:13110/datamanage/openapi/machineinfo/find/list";
        HttpResponse httpResponse = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("access_token", getAccessToken())
                .body(JSONUtil.toJsonStr(params))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "/datamanage/openapi/machineinfo/find/list 请求失败");
        }
        return getResponseBody(httpResponse);
    }

    private String getAccessToken() {
        String url = "http://***********:13110/datamanage/openapi/oauth/token";
        Map<String, String> params = new HashMap<>(2);
        params.put("systemSign", "sany.sbhledge");
        params.put("systemKey", "sany#sbhledge");
        HttpResponse httpResponse = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(params))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "Token 获取失败");
        }
        JSONObject responseBody = getResponseBody(httpResponse);
        return responseBody.getJSONObject("data").getStr("access_token");
    }

    @NotNull
    private JSONObject getResponseBody(HttpResponse httpResponse) {
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        if (responseBody.getInt("code") != 200) {
            throw new CustomException(responseBody.getInt("code"), responseBody.getStr("msg"));
        }
        return responseBody;
    }

    public Page<DeviceProfile> selectPage(Page<DeviceProfile> page, DeviceProfile deviceProfile) {
        String lastSql = "";
        if (StringUtils.isNotBlank(page.getSortField())) {
            lastSql = "order by " + StrUtil.toUnderlineCase(page.getSortField()) + " " + page.getSortOrder();
        }
        return deviceProfileMapper.selectPage(page, new QueryWrapper<DeviceProfile>().lambda()
            .eq(StringUtils.isNotBlank(deviceProfile.getOrgCode()), DeviceProfile::getOrgCode, deviceProfile.getOrgCode())
            .eq(StringUtils.isNotBlank(deviceProfile.getCompanyCode()), DeviceProfile::getCompanyCode, deviceProfile.getCompanyCode())
            .eq(StringUtils.isNotBlank(deviceProfile.getSecondProcess()), DeviceProfile::getSecondProcess, deviceProfile.getSecondProcess())
            .eq(StringUtils.isNotBlank(deviceProfile.getSecondProcessName()), DeviceProfile::getSecondProcessName, deviceProfile.getSecondProcessName())
            .like(StringUtils.isNotBlank(deviceProfile.getDeviceCode()), DeviceProfile::getDeviceCode, deviceProfile.getDeviceCode())
            .orderByAsc(StringUtils.isBlank(page.getSortField()), DeviceProfile::getDeviceCode)
            .last(StringUtils.isNotBlank(lastSql), lastSql)
        );
    }

    public String exportData(DeviceProfile deviceProfile) {
        List<DeviceProfile> deviceProfiles = deviceProfileMapper.selectList(new QueryWrapper<DeviceProfile>().lambda()
                .eq(StringUtils.isNotBlank(deviceProfile.getCompanyCode()), DeviceProfile::getCompanyCode, deviceProfile.getCompanyCode())
                .eq(StringUtils.isNotBlank(deviceProfile.getSecondProcess()), DeviceProfile::getSecondProcess, deviceProfile.getSecondProcess())
                .eq(StringUtils.isNotBlank(deviceProfile.getOrgCode()), DeviceProfile::getOrgCode, deviceProfile.getOrgCode())
                .like(StringUtils.isNotBlank(deviceProfile.getDeviceCode()), DeviceProfile::getDeviceCode, deviceProfile.getDeviceCode())
                .eq(StringUtils.isNotBlank(deviceProfile.getSecondProcessName()), DeviceProfile::getSecondProcessName, deviceProfile.getSecondProcessName())
                .orderByAsc(DeviceProfile::getDeviceCode)
        );
        return ExcelUtil.writeAndGetDownloadId("设备档案", "设备档案", deviceProfiles, DeviceProfile.class);
    }
}
