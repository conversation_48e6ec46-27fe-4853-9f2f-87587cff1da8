package com.rc.admin.easyapi.constants;

public enum ServiceNameAndUrl {
    /**
     * 根连数采设备信息导出
     */
    FILE_EXPORT_DEVICE("10.16.2.37",":9882/api/File/v2/ExportDevice"),
    /**
     * 根云设备列表获取
     */
    ROOT_CLOUD_DEVICE("根云全设备列表获取","http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn/thing-instance/v1/device/device-instances-query"),
    /**
     * 根云登录
     */
    ROOT_CLOUD_LOGIN("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn","/account-manage/v2/auth/login"),
    /**
     * 根云实时工况
     */
    REAL_TIME_WORKING_CONDITION("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn","/realtime-manage/v1/realtime/models/{modelId}/things"),
    /**
     * 根云历史工况
     */
    HISTORY_WORKING_CONDITION("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn","/historian-manage/v1/historian/models/{modelId}/things"),
    /**
     * 根连实时工况
     */
    ROOT_CONNECTION_REAL_TIME_WORKING_CONDITION("10.16.2.37",":9886/api/RootInterface/GetDeviceValuesByCode"),
    /**
     * 获取区域所有设备信息
     */
    DEVICE_ALL_INFO("获取区域所有设备信息",":9886/api/RootInterface/GetAllDevices"),

    /**
     * 批量修改实例静态属性uploadWorkingConditionDataToPlatform
     */
    UPLOAD_WORKING_CONDITION_STATIC_DATA_TO_PLATFORM("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn","/thing-instance/v1/thing/thing-instances/bulk-staticProperties")
    ;
    private String name;
    private String url;

    ServiceNameAndUrl(String name, String url) {
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }


}
