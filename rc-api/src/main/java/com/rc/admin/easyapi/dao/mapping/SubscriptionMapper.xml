<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.easyapi.dao.SubscriptionMapper">

    <!-- MyBatis-Plus 自动处理CRUD操作，无需手动编写SQL -->
    <select id="getSubscriptionPage" resultType="com.rc.admin.easyapi.model.resp.SubscriptionResp">
        SELECT
            su.nickname AS nickname,
            su.email AS email,
            sd.NAME AS simpleName,
            su.phone_number AS phone,
            su.username AS account,
            os.is_enabled AS isEnabled,
            os.notification_channels AS notificationChannels,
            os.subscribed_departments AS subscribedDepartments,
            os.subscription_period AS subscriptionPeriod,
            os.created_at AS createdAt,
            os.updated_at AS updatedAt
        FROM
            dqm.sys_user su
                LEFT JOIN dqm.orc_subscriptions os ON su.username = os.account
                LEFT JOIN dqm.sys_dept sd ON su.dept_id = sd.ID
                left join sys_user_role sur on sur.user_id = su.id
                left join sys_role sr on sr.id = sur.role_id
        where su.status='1'
        <if test="req.account != null and req.account != ''">
            and su.username like concat('%',#{req.account},'%')
        </if>
        <if test="req.sys != null and req.sys != ''">
            and  sr.sys = #{req.sys}
        </if>
        <if test="req.deptId != null and req.deptId != ''">
            and  su.dept_id = #{req.deptId}
        </if>
    </select>
</mapper>
