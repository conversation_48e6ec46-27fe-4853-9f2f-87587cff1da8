package com.rc.admin.easyapi.controller;

import com.rc.admin.common.core.base.BaseController;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.model.HistoryWorkData;
import com.rc.admin.model.HistoryWorkDataRequestDTO;
import com.rc.admin.model.RuleAdaptHistoryData;
import com.rc.admin.service.RuleAdaptHistoryDataService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@ResponseResult
@RequestMapping("/rule-history-data")
@Api(value = "历史数据质量检查相关接口")
public class RuleAdaptHistoryDataController extends BaseController {

    private final RuleAdaptHistoryDataService ruleAdaptHistoryDataService;

    @Autowired
    public RuleAdaptHistoryDataController(RuleAdaptHistoryDataService ruleAdaptHistoryDataService) {
        this.ruleAdaptHistoryDataService = ruleAdaptHistoryDataService;
    }

    @GetMapping("/page")
    public Page<RuleAdaptHistoryData> select(Page<RuleAdaptHistoryData> page, RuleAdaptHistoryData ruleAdaptHistoryData){
        return ruleAdaptHistoryDataService.selectPage(page, ruleAdaptHistoryData);
    }

    @GetMapping("/export")
    public String exportData(RuleAdaptHistoryData ruleAdaptHistoryData) {
        return ruleAdaptHistoryDataService.exportData(ruleAdaptHistoryData);
    }

    @GetMapping("/data")
    public HistoryWorkData getHistoryWorkData(HistoryWorkDataRequestDTO workDataRequestDTO) {
        return ruleAdaptHistoryDataService.selectWorkData(workDataRequestDTO);
    }
}
