package com.rc.admin.easyapi.util;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;

/**
 * @ClassName GeometryUtils
 * @description TODO
 * <AUTHOR> Date 2023/11/30 14:39 Version 1.0
 **/
public class GeometryUtils {
  public static boolean isWithinGeometry(double longitude, double latitude, String[] geometries) {
    GeometryFactory geometryFactory = new GeometryFactory();
    WKTReader reader = new WKTReader(geometryFactory);

    // 将经纬度转换为JTS的点对象
    Coordinate coordinate = new Coordinate(longitude, latitude);
    Point point = geometryFactory.createPoint(coordinate);

    try {
      for (String geometryStr : geometries) {
        // 将几何区域解析为JTS的几何对象
        Geometry geometry = reader.read(geometryStr);

        // 判断点是否在几何区域内
        if (geometry.contains(point)) {
          return true; // 经纬度在几何区域内
        }
      }
    } catch (ParseException e) {
      e.printStackTrace();
    }

    return false; // 经纬度不在任何一个几何区域内
  }
}
