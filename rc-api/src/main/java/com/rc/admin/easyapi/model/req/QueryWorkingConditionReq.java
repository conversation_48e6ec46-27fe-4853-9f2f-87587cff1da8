package com.rc.admin.easyapi.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QueryWorkingConditionReq {
    @ApiModelProperty(value = "模型ID")
    private String modelId;
    @ApiModelProperty(value = "物实例ID")
    private List<String> thingIds;
    @ApiModelProperty(value = "物标识")
    private List<String> assetIds;
    @ApiModelProperty(value = "属性列表; 属性列表中的属性数不能超过2000；")
    private List<String> properties;
    @ApiModelProperty(value = "设置查询策略，只查询正常值（normal）,只查询异常值（abnormal）或查询全部（all），如果参数有误或者不填则默认为normal")
    private String queryStrategy;
    @ApiModelProperty(value = "设置空值返回策略，返回空值(ture), 不返回(false), 默认为false")
    private Boolean nullValueReturn;
    @ApiModelProperty(value = "字段类型:字符串; 字段含义:开始时间；时间范围包含开始时间; ISO格式时间:yyyy-MM-ddTHH:mm:ss.SSSZ; 字段举例:2020-05-01T01:20:30.000Z")
    private Date startTime;
    @ApiModelProperty(value = "字段类型:字符串; 字段含义:结束时间；时间范围不包含结束时间; ISO格式时间:yyyy-MM-ddTHH:mm:ss.SSSZ; 字段举例:2020-05-01T02:20:30.000Z")
    private Date  endTime ;
    @ApiModelProperty(value = "跳过查询返回的数据条数.skip值越大接口返回时间会越长，当达到100000后有接口超时的可能")
    private Integer  skip ;
    @ApiModelProperty(value = "查询返回的数据最大条数，默认是1000，默认配置下limit不能超过100000")
    private Integer  limit ;
    @ApiModelProperty(value = "设置时序排序，升序(ASC)或降序(DESC)")
    private String  sort ;
    @ApiModelProperty(value = "查询区域")
    private String  region ;
}
