package com.rc.admin.easyapi.job;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.rc.admin.ors.quality.dao.*;
import com.rc.admin.ors.quality.service.OrsBasicDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.rc.admin.easyapi.job.SyncAbnormalData.BJ_DATEFORMAT;
import static com.rc.admin.easyapi.job.SyncAbnormalData.BJ_TIMEFORMAT;

@Component
@Slf4j
public class FlowJob {
    @Resource
    SyncAbnormalData syncAbnormalData;

    @Resource
    OrsSyncDataJob orsSyncDataJob;

    @Resource
    private OrsCoreParamStatLatestMapper orsCoreParamStatLatestMapper;

    @Resource
    private DeviceParamReportLogMapper deviceParamReportLogMapper;

    @Resource
    private OrsDeviceDataAbnormalDetailMapper orsDeviceDataAbnormalDetailMapper;
    @Resource
    private OrsBigdataEquipmentBaseInfoAllMapper orsBigdataEquipmentBaseInfoAllMapper;

    @Resource
    private SanydsCoreParamAbnormalDetailMapper sanydsCoreParamAbnormalDetailMapper;

    @Resource
    private OrsBasicDataService orsBasicDataService;

//    @Scheduled(fixedDelay = 50000)
    public void syncJob()
    {
//        Date now = new Date();
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(now);
//        calendar.add(Calendar.DAY_OF_YEAR, -1);
//        Date bizDate = calendar.getTime();
//
//        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
//        String tbsuffix = df.format(bizDate);
//
//        df = new SimpleDateFormat("yyyy-MM-dd");
//        String minDate = df.format(bizDate);
//
//        Calendar calendar2 = Calendar.getInstance();
//        calendar2.setTime(bizDate);
//        calendar2.add(Calendar.DAY_OF_MONTH, 1);
//        Date maxDate = calendar2.getTime();
//
//        df = new SimpleDateFormat("yyyy-MM-dd");
//        String maxDateStr = df.format(maxDate);
//
//        deviceParamReportLogMapper.ors_device_param_report_day(bizDate,minDate,maxDateStr,tbsuffix);
        dagFlow(null);
    }

    public static List<String> statis_log = new ArrayList<>();

    @Value("${spring.profiles.active}")
    private String activeEnv;

    public void dagFlow(Date bizDate)
    {
        try {
            statis_log.clear();
            if(bizDate == null)
            {
                Date now = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.DAY_OF_YEAR, -1);
                bizDate = calendar.getTime();
                //前一天
            }
            SimpleDateFormat df = new SimpleDateFormat(BJ_DATEFORMAT);
            String s = df.format(bizDate);
            String uuid = UUID.randomUUID().toString();
            //1 按日统计设备位置上数条数(位置)
            // syncAbnormalData.aggDeviceHourWorkCnt(bizDate);
            Integer deviceWorkCnt = syncAbnormalData.getDeviceWorkCnt(bizDate);
            Date currentTime = new Date();
            SimpleDateFormat bjTimeformat = new SimpleDateFormat(BJ_TIMEFORMAT);
            String current = bjTimeformat.format(currentTime);
            statis_log.add("sany_data_service.sanyds_device_work_cnt="+deviceWorkCnt + ", 当前时间" + current);
            log.info("====================={} {} 1、按日统计设备位置上数条数:{}条, 用时:{}",uuid,s,deviceWorkCnt, current);
            //2 同步核心工况最新数据(所有属性最新值和时间)
            orsCoreParamStatLatestMapper.syncSanyData(bizDate);
            Integer statLatestCnt = orsCoreParamStatLatestMapper.getStatLatestDayCnt(bizDate);
            currentTime = new Date();
            current = bjTimeformat.format(currentTime);
            statis_log.add("sany_data_service.sanyds_core_param_stat_latest_day="+statLatestCnt + ", 当前时间" + current);
            log.info("====================={} {} 2、同步核心工况最新数据:{}条, 当前时间:{}",uuid,s,statLatestCnt, current);
//            orsSyncDataJob.syncSanyCoreData();
            Integer count = sanydsCoreParamAbnormalDetailMapper.countAbnormalDevice(bizDate);
            currentTime = new Date();
            current = bjTimeformat.format(currentTime);
            statis_log.add("sany_data_service.sanyds_core_param_abnormal_detail="+count + ", 当前时间" + current);
            //3 同步异常数据
            syncAbnormalData.syncAbnormalData_new(bizDate);
            //4 异常未上报，系统补偿
            syncAbnormalData.checkAbnormalData(bizDate);

            // 按天生成设备工况上报记录日志
            deviceParamReportLogMapper.generateReportLog(bizDate);
            Integer reportLogCnt = deviceParamReportLogMapper.getReportLogCnt(bizDate);

            orsBasicDataService.insertDeviceRateDay(bizDate);

            currentTime = new Date();
            current = bjTimeformat.format(currentTime);
            statis_log.add("dqm.ors_device_param_report_log="+reportLogCnt + ", 当前时间" + current);
            log.info("====================={} {} 7、按天生成设备工况上报记录:{}条, 当前时间{}",uuid,s,reportLogCnt, current);

            deviceParamReportLogMapper.job_analysis(bizDate);
            deviceParamReportLogMapper.job_abnormal_analysis(bizDate);
            deviceParamReportLogMapper.job_abnormal_detail(bizDate);

            currentTime = new Date();
            current = bjTimeformat.format(currentTime);
            statis_log.add("dqm.orc_mid_abnormal_detail="+reportLogCnt + ", 当前时间" + current);
            log.info("====================={} {} 同步到推送表, 当前时间{}",uuid,s, current);

            if ("prod".equals(activeEnv) || "pre".equals(activeEnv)) {
                JSONObject json = new JSONObject();
                json.put("msgtype", "markdown");
                JSONObject text = new JSONObject();
                String join = String.join("\n", statis_log);
                String msg = "<font color=\"green\">【"+s+"海外新C链路情况-"+activeEnv+"】</font>\n"+join;
                text.put("mentioned_mobile_list", Collections.singletonList("@all"));
                text.put("content", msg);
                json.put("markdown", text);
                log.info("====================={} 8、发送企业微信链路情况{}",s,json.toJSONString());
                String cpwechatUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b0358386-a64c-44bb-9d61-df5c6e20399b";
                HttpUtil.post(cpwechatUrl, json.toJSONString());
            }
            log.info("====================={} 9、任务执行完成",s);
        }
        catch (Exception e)
        {
            e.printStackTrace();
//            String stackTrace = ExceptionUtils.getStackTrace(e);
            log.error("syncJob发生错误 {} ",e);
        }
    }

//    @Scheduled(fixedDelay = 50000)
    public void sanyDagFlow(Date bizDate)
    {
//        Date bizDate = null;
//        if(bizDate == null)
//        {
//            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
//            Date parse = null;
//            try {
//                bizDate = df.parse("2024-05-05");
//            } catch (ParseException e) {
//                throw new RuntimeException(e);
//            }
//        }
        deviceParamReportLogMapper.job_analysis(bizDate);
        deviceParamReportLogMapper.job_abnormal_analysis(bizDate);
        deviceParamReportLogMapper.job_abnormal_detail(bizDate);
    }
}
