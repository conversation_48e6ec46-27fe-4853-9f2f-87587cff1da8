package com.rc.admin.easyapi.service.dqmService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.easyapi.constants.ParamAbnormalCode;
import com.rc.admin.easyapi.util.DateUtil;
import com.rc.admin.easyapi.util.StringUtil;
import com.rc.admin.ors.quality.dao.OrsBigdataEquipmentBaseInfoAllMapper;
import com.rc.admin.ors.quality.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrsBigdataEquipmentBaseInfoAllService {

    @Resource
    OrsBigdataEquipmentBaseInfoAllMapper mapper;


    private static final Map<String,String> STR = new HashMap<>();

    /**
     * 获取当天sany_server库海外设备
     */
    // @DS("sany_server")
    // public void deviceIncrementalDataSynchronization() {
    //     List<BigdataEquipmentBaseInfoAll> deviceInfoAll = mapper.getDeviceInfoAll(DateUtil.formatDateTime(DateUtil.getBeforeDate(new Date(), 5)));
    //     log.info(JSONObject.toJSONString(deviceInfoAll));
    //     if (deviceInfoAll.size() > 0) {
    //         this.saveDeviceInfo(deviceInfoAll);
    //     }
    // }

    {
        STR.put("1","设备状态");
        STR.put("2","设备位置");
        STR.put("3","发动机工作时间（h）");
        STR.put("4","总油耗（L）");
        STR.put("5","行驶里程（km）");
        STR.put("6","工作时间（h）");
        STR.put("7","泵送方量（方）");

    }
    public List<Map<String,String>> queryAbnormal(String dictType,String paramCode) {
        return mapper.queryAbnormal(dictType,paramCode);
    }
    public List<Map<String,String>> queryParamCode(String modelId,String divisionCode,String dictType) {
        List<Map<String, String>> maps = mapper.queryParamCode(modelId,divisionCode,dictType);
        maps.forEach(d->{
            d.put("dict_desc",d.get("dict_desc").split(":")[0].replace(" ",""));
            d.put("dict_desc",d.get("dict_desc").split("（")[0].replace(" ",""));
        });
        return maps;
    }


    void saveDeviceInfo(List<BigdataEquipmentBaseInfoAll> deviceInfoAll) {
        for (BigdataEquipmentBaseInfoAll bigdataEquipmentBaseInfoAll : deviceInfoAll) {
            mapper.saveDeviceInfo(bigdataEquipmentBaseInfoAll);
        }

    }

    public List<DqmDeviceDataAbnormalModel> getDqmDeviceDataAbnormalModels(List<DqmDeviceDataAbnormalModel> models,String code,String count){
        DqmDeviceDataAbnormalModel dqmDeviceDataAbnormalModel = new DqmDeviceDataAbnormalModel();
        dqmDeviceDataAbnormalModel.setAbnormalCode(code);
        dqmDeviceDataAbnormalModel.setAbnormalCount(count);
        models.add(dqmDeviceDataAbnormalModel);
        return models;
    }

    public List<DqmDeviceDataAbnormalModel> getDqmDeviceDataAbnormalModels(List<DqmDeviceDataAbnormalModel> models,String code,String count,String deviceLocationCnt){
        DqmDeviceDataAbnormalModel dqmDeviceDataAbnormalModel = new DqmDeviceDataAbnormalModel();
        dqmDeviceDataAbnormalModel.setAbnormalCode(code);
        dqmDeviceDataAbnormalModel.setAbnormalCount(count);
        dqmDeviceDataAbnormalModel.setDeviceLocationCnt(deviceLocationCnt);
        models.add(dqmDeviceDataAbnormalModel);
        return models;
    }

    /**
     * 查询历史设备数据异常
     *
     * @param dto
     * @return
     */
    public Page<DqmHistoryDeviceDataExceptionResp> getHistoryDeviceDataExceptionsInfo1(DqmHistoryDeviceDataExceptionsReq dto) {
        dto.setCreateTime_start(DateUtil.parseDate(dto.getStartTime()));
        dto.setCreateTime_end(DateUtil.parseDate(dto.getEndTime()));
        // 设置分页信息
        Page<DqmHistoryDeviceDataExceptionResp> pageResp = new Page<>();
        if (dto.getCurrent() <= 0) {
            dto.setCurrent(1);
        }
        if (dto.getPageSize() <= 0) {
            dto.setPageSize(10);
        }

        // 组装参数
        if (StringUtils.isNotBlank(dto.getModelId())){
            if (dto.getModelId().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getModelId());
                dto.setModelIdList(splitList);
                dto.setModelId(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getModelName())){
            if (dto.getModelName().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getModelName());
                dto.setModelNameList(splitList);
                dto.setModelName(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getSybbh())){
            if (dto.getSybbh().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getSybbh());
                dto.setSybbhList(splitList);
                dto.setSybbh(null);
            }
        }
        if (StringUtils.isNotBlank(dto.getZehdSpartdesc())){
            if (dto.getZehdSpartdesc().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getZehdSpartdesc());
                dto.setZehdSpartdescList(splitList);
                dto.setZehdSpartdesc(null);
            }
        }

        if (Validator.isNotEmpty(dto.getDeviceName())) {
            if (dto.getDeviceName().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getDeviceName());
                dto.setDeviceNameList(splitList);
                dto.setDeviceNameLast(null);
            }
        }
        if (Validator.isNotEmpty(dto.getDeviceCode())) {
            if (dto.getDeviceCode().contains(CommonConst.SPLIT)) {
                List<String> splitList = StringUtil.getSplitList(dto.getDeviceCode());
                dto.setDeviceCodeList(splitList);
                dto.setDeviceCode(null);
            }
        }

        pageResp.setCurrent(dto.getCurrent());
        pageResp.setPageSize(dto.getPageSize());
        int page = dto.getCurrent();
        int size = dto.getPageSize();
        dto.setPageSize(size);
        dto.setCurrent((page - 1) * size);

        pageResp.setSize(dto.getPageSize());

        String row = dto.getRow();
        pageResp.setRecords(new ArrayList<>());
        pageResp.setTotal(0);
        if ("1".equals(row)) {
            //一行
            // int total = mapper.getHistoryDeviceDataExceptionsInfoForOneCount(dto);
            // pageResp.setTotal(total);
            // List<DqmHistoryDeviceDataExceptionResp> historyDeviceDataExceptionsInfoForOne = mapper.getHistoryDeviceDataExceptionsInfoForOne(dto);
            // for (DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp : historyDeviceDataExceptionsInfoForOne) {
            //     setHistoryDeviceData(dqmHistoryDeviceDataExceptionResp);
            //
            // }
            // pageResp.setRecords(historyDeviceDataExceptionsInfoForOne);
            pageResp = mapper.getHistoryDeviceDataExceptionsInfoForOne(pageResp,dto);
            for (DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp : pageResp.getRecords()) {
                setHistoryDeviceData(dqmHistoryDeviceDataExceptionResp);
            }

        } else {
            //多行
            int total = mapper.getHistoryDeviceDataExceptionsInfoForListCount(dto);
            pageResp.setTotal(total);
            List<DqmHistoryDeviceDataExceptionResp> historyDeviceDataExceptionsInfoForOne = mapper.getHistoryDeviceDataExceptionsInfoForList(dto);
            for (DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp : historyDeviceDataExceptionsInfoForOne) {
                setHistoryDeviceData(dqmHistoryDeviceDataExceptionResp);
            }
            pageResp.setRecords(historyDeviceDataExceptionsInfoForOne);
        }
        List<String> excludeModels = Arrays.asList("8701", "8702", "8703","8704","8705","8706");

        List<String> dynamicKeyList = CollUtil.newArrayList();
        dynamicKeyList.add("sybbh");
        dynamicKeyList.add("country");
        dynamicKeyList.add("modelId");
        dynamicKeyList.add("dataCenterId");
        dynamicKeyList.add("deviceName");
        dynamicKeyList.add("deviceCode");
        dynamicKeyList.add("startStatDate");
        dynamicKeyList.add("endStatDate");
        dynamicKeyList.add("deviceLocation");
        dynamicKeyList.add("handle");
        dynamicKeyList.add("fwVersion");
        dynamicKeyList.add("hwVersion");
        dynamicKeyList.add("authToken");
        dynamicKeyList.add("modelName");
        dynamicKeyList.add("zehdSpartdesc");
        if(StringUtils.isBlank(dto.getDictType())){
            mapper.getModelList(dto).stream()
                    .filter(Objects::nonNull)
                    .map(model -> "abnormal" + model)
                    .forEach(dynamicKeyList::add);
        }else{
            if("multiple_param".equals(dto.getDictType())){
                mapper.getModelList(dto).stream()
                        .filter(Objects::nonNull)
                        .filter(excludeModels::contains)
                        .map(model -> "abnormal" + model)
                        .forEach(dynamicKeyList::add);
            }else{
                mapper.getModelList(dto).stream()
                        .filter(Objects::nonNull)
                        .filter(model -> !excludeModels.contains(model))
                        .map(model -> "abnormal" + model)
                        .forEach(dynamicKeyList::add);
            }
        }
        pageResp.setDynamicKeyList(dynamicKeyList);
        return pageResp;
    }

    public void setHistoryDeviceData(DqmHistoryDeviceDataExceptionResp dqmHistoryDeviceDataExceptionResp) {
        //设备状态 8503              device_status_9008    [9008, 9009]
        List<DqmDeviceDataAbnormalModel> deviceStatusModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDeviceStatus9008() > 0) {
            getDqmDeviceDataAbnormalModels(deviceStatusModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceStatus9008()));

        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceStatus9009() > 0) {
            getDqmDeviceDataAbnormalModels(deviceStatusModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceStatus9009()));

        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8503(deviceStatusModelList);
        //设备位置 8501             device_location_9008  [9008, 9001, 9002, 9007, 9009]
        List<DqmDeviceDataAbnormalModel> deviceLocationModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9001() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9002() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9002.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9002()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9007() > 0) {
            long deviceLocationCnt = dqmHistoryDeviceDataExceptionResp.getDeviceLocationCnt();
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9007.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9007()),String.valueOf(deviceLocationCnt));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9008() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDeviceLocation9009() > 0) {
            getDqmDeviceDataAbnormalModels(deviceLocationModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDeviceLocation9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8501(deviceLocationModelList);
        //发动机工作时间 8105         engine_worktime  [9008, 9001, 9004, 9009 ,9005,9003]
        List<DqmDeviceDataAbnormalModel> engineWorktimeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9001() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9004() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9008() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9009() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9003() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9003.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9003()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineWorktime9005() > 0) {
            getDqmDeviceDataAbnormalModels(engineWorktimeModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineWorktime9005()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8105(engineWorktimeModelList);
        //工作时间 8102             working_time [9008 9001, 9004, 9009,9005,9003]
        List<DqmDeviceDataAbnormalModel> worktimeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9001() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9004() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9008() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9009() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9003() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9003.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9003()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkingTime9005() > 0) {
            getDqmDeviceDataAbnormalModels(worktimeModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkingTime9005()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8102(worktimeModelList);
//                总油耗 8201              total_fuel_consumption [9008 9001, 9004, 9009,9005, 9003]
        List<DqmDeviceDataAbnormalModel> totalFuelConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9003() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9003.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9003()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalFuelConsumptionModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalFuelConsumption9005()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8201(totalFuelConsumptionModelList);
//                泵送方量 8401             pumping_volume [9008 9001, 9004, 9009]
        List<DqmDeviceDataAbnormalModel> pumpingVolumeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9001() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9004() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9008() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpingVolume9009() > 0) {
            getDqmDeviceDataAbnormalModels(pumpingVolumeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpingVolume9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8401(pumpingVolumeModelList);
//                行驶里程 8403             driving_mileage [9008 9001, 9004 , 9009, 9005,9003]
        List<DqmDeviceDataAbnormalModel> drivingMileageModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9001() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9004() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9008() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9009() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9003() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9003.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9003()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDrivingMileage9005() > 0) {
            getDqmDeviceDataAbnormalModels(drivingMileageModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDrivingMileage9005()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8403(drivingMileageModelList);
//                油位 8506             fuel_level [9008 9001 9009,9006]
        List<DqmDeviceDataAbnormalModel> fuelLevelModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getFuelLevel9001() > 0) {
            getDqmDeviceDataAbnormalModels(fuelLevelModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getFuelLevel9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getFuelLevel9008() > 0) {
            getDqmDeviceDataAbnormalModels(fuelLevelModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getFuelLevel9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getFuelLevel9009() > 0) {
            getDqmDeviceDataAbnormalModels(fuelLevelModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getFuelLevel9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getFuelLevel9006() > 0) {
            getDqmDeviceDataAbnormalModels(fuelLevelModelList, ParamAbnormalCode.PARAM_9006.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getFuelLevel9006()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8506(fuelLevelModelList);
//                发动机转速 8507            engine_speed [9008 9001 9009,9006]
        List<DqmDeviceDataAbnormalModel> engineSpeedModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getEngineSpeed9001() > 0) {
            getDqmDeviceDataAbnormalModels(engineSpeedModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineSpeed9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineSpeed9008() > 0) {
            getDqmDeviceDataAbnormalModels(engineSpeedModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineSpeed9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineSpeed9009() > 0) {
            getDqmDeviceDataAbnormalModels(engineSpeedModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineSpeed9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getEngineSpeed9006() > 0) {
            getDqmDeviceDataAbnormalModels(engineSpeedModelList, ParamAbnormalCode.PARAM_9006.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineSpeed9006()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8507(engineSpeedModelList);
//                发动机水温 8508          water_temperature [9008 9001 9009,9006]
        List<DqmDeviceDataAbnormalModel> waterTemperatureModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getWaterTemperature9001() > 0) {
            getDqmDeviceDataAbnormalModels(waterTemperatureModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWaterTemperature9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWaterTemperature9008() > 0) {
            getDqmDeviceDataAbnormalModels(waterTemperatureModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWaterTemperature9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWaterTemperature9009() > 0) {
            getDqmDeviceDataAbnormalModels(waterTemperatureModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWaterTemperature9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWaterTemperature9006() > 0) {
            getDqmDeviceDataAbnormalModels(waterTemperatureModelList, ParamAbnormalCode.PARAM_9006.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWaterTemperature9006()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8508(waterTemperatureModelList);
//                当前电量 8509             SOC_stateofcharge [9008 9001 9009]
        List<DqmDeviceDataAbnormalModel> SOCStateofchargeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9001() > 0) {
            getDqmDeviceDataAbnormalModels(SOCStateofchargeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9008() > 0) {
            getDqmDeviceDataAbnormalModels(SOCStateofchargeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9009() > 0) {
            getDqmDeviceDataAbnormalModels(SOCStateofchargeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSOCStateofcharge9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8509(SOCStateofchargeModelList);
//                行驶速度 8510             travel_speed [9008 9001 9009,9006]
        List<DqmDeviceDataAbnormalModel> travelSpeedModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTravelSpeed9001() > 0) {
            getDqmDeviceDataAbnormalModels(travelSpeedModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTravelSpeed9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTravelSpeed9008() > 0) {
            getDqmDeviceDataAbnormalModels(travelSpeedModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTravelSpeed9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTravelSpeed9009() > 0) {
            getDqmDeviceDataAbnormalModels(travelSpeedModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTravelSpeed9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTravelSpeed9006() > 0) {
            getDqmDeviceDataAbnormalModels(travelSpeedModelList, ParamAbnormalCode.PARAM_9006.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTravelSpeed9006()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8510(travelSpeedModelList);
//                总电耗 8511             total_electric_consumption [9008 9001 9004 9009 ,9005,9003]
        List<DqmDeviceDataAbnormalModel> totalElectricConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9003() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9003.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9003()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalElectricConsumptionModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalElectricConsumption9005()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8511(totalElectricConsumptionModelList);

//新增属性
        //怠速油耗       8205    total_idle_fuel_consumption [9001, 9004, 9005,9008, 9009] totalIdleFuelConsumption
        List<DqmDeviceDataAbnormalModel> totalIdleFuelConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleFuelConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleFuelConsumptionModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleFuelConsumptionModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9005()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleFuelConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleFuelConsumptionModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleFuelConsumption9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8205(totalIdleFuelConsumptionModelList);
        //怠速时长       8106    total_idle_time             [9001, 9004, 9005,9008, 9009]
        List<DqmDeviceDataAbnormalModel> totalIdleTimeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleTimeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleTimeModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleTimeModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9005()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleTimeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalIdleTimeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalIdleTime9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8106(totalIdleTimeModelList);
        //档位           8602    gear                        [9008, 9009 ,9001]
        List<DqmDeviceDataAbnormalModel> gearModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getGear9008() > 0) {
            getDqmDeviceDataAbnormalModels(gearModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getGear9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getGear9009() > 0) {
            getDqmDeviceDataAbnormalModels(gearModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getGear9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getGear9001() > 0) {
            getDqmDeviceDataAbnormalModels(gearModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getGear9001()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8602(gearModelList);
        //左行走工时     8107    total_time_left_moving       [9001, 9004,9005, 9008, 9009]
        List<DqmDeviceDataAbnormalModel> totalTimeLeftMovingModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeLeftMovingModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeLeftMovingModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeLeftMovingModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9005()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeLeftMovingModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeLeftMovingModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeLeftMoving9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8107(totalTimeLeftMovingModelList);
        //右行走工时     8108    total_time_right_moving      [9001, 9004, 9005, 9008, 9009]
        List<DqmDeviceDataAbnormalModel> totalTimeRightMovingModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRightMovingModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRightMovingModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRightMovingModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9005()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRightMovingModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRightMovingModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRightMoving9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8108(totalTimeRightMovingModelList);
        //机油压力       8603    oil_pressure                [9001, 9008, 9009]
        List<DqmDeviceDataAbnormalModel> oilPressureModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getOilPressure9001() > 0) {
            getDqmDeviceDataAbnormalModels(oilPressureModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getOilPressure9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getOilPressure9008() > 0) {
            getDqmDeviceDataAbnormalModels(oilPressureModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getOilPressure9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getOilPressure9009() > 0) {
            getDqmDeviceDataAbnormalModels(oilPressureModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getOilPressure9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8603(oilPressureModelList);
        //泵吸收功率     8604    pump_total_absorbed_torque   [9001, 9008, 9009]
        List<DqmDeviceDataAbnormalModel> pumpTotalAbsorbedTorqueModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9001() > 0) {
            getDqmDeviceDataAbnormalModels(pumpTotalAbsorbedTorqueModelList, ParamAbnormalCode.PARAM_9001.getCode(),
                                String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9008() > 0) {
            getDqmDeviceDataAbnormalModels(pumpTotalAbsorbedTorqueModelList, ParamAbnormalCode.PARAM_9008.getCode(),
                                String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9009() > 0) {
            getDqmDeviceDataAbnormalModels(pumpTotalAbsorbedTorqueModelList, ParamAbnormalCode.PARAM_9009.getCode(),
                                String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpTotalAbsorbedTorque9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8604(pumpTotalAbsorbedTorqueModelList);
//2024-12-30
        //电机转速--pump_motor_rotate_speed--pumpMotorRotateSpeedData--8605--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> pumpMotorRotateSpeedModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9001() > 0) {
            getDqmDeviceDataAbnormalModels(pumpMotorRotateSpeedModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9008() > 0) {
            getDqmDeviceDataAbnormalModels(pumpMotorRotateSpeedModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9009() > 0) {
            getDqmDeviceDataAbnormalModels(pumpMotorRotateSpeedModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getPumpMotorRotateSpeed9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8605(pumpMotorRotateSpeedModelList);

        //充电状态--charging_status--chargingStatusData--8606--(9008,9009,9001)
        List<DqmDeviceDataAbnormalModel> chargingStatusModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getChargingStatus9008() > 0) {
            getDqmDeviceDataAbnormalModels(chargingStatusModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargingStatus9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getChargingStatus9009() > 0) {
            getDqmDeviceDataAbnormalModels(chargingStatusModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargingStatus9009()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getChargingStatus9001() > 0) {
            getDqmDeviceDataAbnormalModels(chargingStatusModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargingStatus9001()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8606(chargingStatusModelList);

        //充电剩余时间--charge_time_remain--chargeTimeRemainData--8607--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> chargeTimeRemainModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9001() > 0) {
            getDqmDeviceDataAbnormalModels(chargeTimeRemainModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9008() > 0) {
            getDqmDeviceDataAbnormalModels(chargeTimeRemainModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9009() > 0) {
            getDqmDeviceDataAbnormalModels(chargeTimeRemainModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getChargeTimeRemain9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8607(chargeTimeRemainModelList);

        //单次充电电量--single_charge_capacity--singleChargeCapacityData--8608--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> singleChargeCapacityModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9001() > 0) {
            getDqmDeviceDataAbnormalModels(singleChargeCapacityModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9008() > 0) {
            getDqmDeviceDataAbnormalModels(singleChargeCapacityModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9009() > 0) {
            getDqmDeviceDataAbnormalModels(singleChargeCapacityModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getSingleChargeCapacity9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8608(singleChargeCapacityModelList);

        //当日电耗--day_power_consumption--dayPowerConsumptionData--8609--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> dayPowerConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(dayPowerConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(dayPowerConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9009() > 0) {
            getDqmDeviceDataAbnormalModels(dayPowerConsumptionModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getDayPowerConsumption9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8609(dayPowerConsumptionModelList);

//2025-01-17
        //动作编码--action_code--actionCode--8610--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> actionCodeModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getActionCode9001() > 0) {
            getDqmDeviceDataAbnormalModels(actionCodeModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getActionCode9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getActionCode9008() > 0) {
            getDqmDeviceDataAbnormalModels(actionCodeModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getActionCode9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getActionCode9009() > 0) {
            getDqmDeviceDataAbnormalModels(actionCodeModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getActionCode9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8610(actionCodeModelList);

        //回转时间--total_time_rotation--totalTimeRotation--8611--(9001,9004,9005,9008,9009)
        List<DqmDeviceDataAbnormalModel> totalTimeRotationModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRotationModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRotationModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9005() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRotationModelList, ParamAbnormalCode.PARAM_9005.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9005()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRotationModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalTimeRotationModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalTimeRotation9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8611(totalTimeRotationModelList);

        //怠速电耗--total_no_action_power_consumption--totalNoActionPowerConsumption--8305--(9001,9008,9009)
        List<DqmDeviceDataAbnormalModel> totalNoActionPowerConsumptionModelList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9001() > 0) {
            getDqmDeviceDataAbnormalModels(totalNoActionPowerConsumptionModelList, ParamAbnormalCode.PARAM_9001.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9001()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9004() > 0) {
            getDqmDeviceDataAbnormalModels(totalNoActionPowerConsumptionModelList, ParamAbnormalCode.PARAM_9004.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9004()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9008() > 0) {
            getDqmDeviceDataAbnormalModels(totalNoActionPowerConsumptionModelList, ParamAbnormalCode.PARAM_9008.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9008()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9009() > 0) {
            getDqmDeviceDataAbnormalModels(totalNoActionPowerConsumptionModelList, ParamAbnormalCode.PARAM_9009.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getTotalNoActionPowerConsumption9009()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8305(totalNoActionPowerConsumptionModelList);



        //怠速时长&怠速油耗--idle_time_idle_fuel--idleTimeIdleFuelData--8701--(9101,9102,9100)
        List<DqmDeviceDataAbnormalModel> idleTimeIdleFuelDataList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9101() > 0) {
            getDqmDeviceDataAbnormalModels(idleTimeIdleFuelDataList, ParamAbnormalCode.PARAM_9101.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9101()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9102() > 0) {
            getDqmDeviceDataAbnormalModels(idleTimeIdleFuelDataList, ParamAbnormalCode.PARAM_9102.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9102()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9100() > 0) {
            getDqmDeviceDataAbnormalModels(idleTimeIdleFuelDataList, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdleTimeIdleFuel9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8701(idleTimeIdleFuelDataList);


        //工作时间&总油耗--work_time_fuel--workTimeFuelData--8702--(9103,9104,9100)
        List<DqmDeviceDataAbnormalModel> workTimeFuelDataList = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9103() > 0) {
            getDqmDeviceDataAbnormalModels(workTimeFuelDataList, ParamAbnormalCode.PARAM_9103.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9103()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9104() > 0) {
            getDqmDeviceDataAbnormalModels(workTimeFuelDataList, ParamAbnormalCode.PARAM_9104.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9104()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9100() > 0) {
            getDqmDeviceDataAbnormalModels(workTimeFuelDataList, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getWorkTimeFuel9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8702(workTimeFuelDataList);


        //怠速油耗&总油耗--idel_fuel_fuel--idelFuelFuelData--8703--(9105,9100)
        List<DqmDeviceDataAbnormalModel> idelFuelFuelData = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getIdelFuelFuel9105() > 0) {
            getDqmDeviceDataAbnormalModels(idelFuelFuelData, ParamAbnormalCode.PARAM_9105.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdelFuelFuel9105()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getIdelFuelFuel9100() > 0) {
            getDqmDeviceDataAbnormalModels(idelFuelFuelData, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdelFuelFuel9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8703(idelFuelFuelData);



        //怠速时长&工作时长--idel_time_work_time--idelTimeWorkTimeData--8704--(9106,9100)
        List<DqmDeviceDataAbnormalModel> idelTimeWorkTimeData = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getIdelTimeWorkTime9106() > 0) {
            getDqmDeviceDataAbnormalModels(idelTimeWorkTimeData, ParamAbnormalCode.PARAM_9106.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdelTimeWorkTime9106()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getIdelTimeWorkTime9100() > 0) {
            getDqmDeviceDataAbnormalModels(idelTimeWorkTimeData, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getIdelTimeWorkTime9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8704(idelTimeWorkTimeData);



        //行驶里程&行驶速度--mileage_speed--mileageSpeedData--8705--(9107,9108,9100)
        List<DqmDeviceDataAbnormalModel> mileageSpeedData = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getMileageSpeed9107() > 0) {
            getDqmDeviceDataAbnormalModels(mileageSpeedData, ParamAbnormalCode.PARAM_9107.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getMileageSpeed9107()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getMileageSpeed9108() > 0) {
            getDqmDeviceDataAbnormalModels(mileageSpeedData, ParamAbnormalCode.PARAM_9108.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getMileageSpeed9108()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getMileageSpeed9100() > 0) {
            getDqmDeviceDataAbnormalModels(mileageSpeedData, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getMileageSpeed9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8705(mileageSpeedData);


        //行驶里程&设备位置--mileage_location--mileageLocationData--8706--(9109,9100)
        List<DqmDeviceDataAbnormalModel> mileageLocationData = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getMileageLocation9109() > 0) {
            getDqmDeviceDataAbnormalModels(mileageLocationData, ParamAbnormalCode.PARAM_9109.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getMileageLocation9109()));
        }
        if (dqmHistoryDeviceDataExceptionResp.getMileageLocation9100() > 0) {
            getDqmDeviceDataAbnormalModels(mileageLocationData, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getMileageLocation9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8706(mileageLocationData);


        //行驶里程&设备位置--engine_time_fuel--engineTimeFuelData--8707--(9100)
        List<DqmDeviceDataAbnormalModel> engineTimeFuelData = new ArrayList<>();
        if (dqmHistoryDeviceDataExceptionResp.getEngineTimeFuel9100() > 0) {
            getDqmDeviceDataAbnormalModels(engineTimeFuelData, ParamAbnormalCode.PARAM_9100.getCode(), String.valueOf(dqmHistoryDeviceDataExceptionResp.getEngineTimeFuel9100()));
        }
        dqmHistoryDeviceDataExceptionResp.setAbnormal8707(engineTimeFuelData);

    }

    public void statDeviceHistoryException(Date statDate){
        List<DqmDeviceDataAbnormalStatDay> dqmDeviceDataAbnormalStatDay = mapper.getDqmDeviceDataAbnormalStatDay(statDate);
        Map<String,DqmDeviceDataAbnormalStatDay> dqmDeviceDataAbnormalStatDays = new HashMap<>();
        // 根据异常编码和属性编码组装返回数据
        for (DqmDeviceDataAbnormalStatDay deviceDataAbnormalStatDay : dqmDeviceDataAbnormalStatDay) {
            String modelId = deviceDataAbnormalStatDay.getModelId();
            String deviceName = deviceDataAbnormalStatDay.getDeviceName();
            String key = modelId+"_"+deviceName;
            String paramCode = deviceDataAbnormalStatDay.getParamCode();
            String abnormalCode = deviceDataAbnormalStatDay.getAbnormalCode();
            long abnormalCount = deviceDataAbnormalStatDay.getAbnormalCount();
            String abnormalData = deviceDataAbnormalStatDay.getAbnormalData();
            DqmDeviceDataAbnormalStatDay dqmDeviceDataAbnormalStatDay1 = dqmDeviceDataAbnormalStatDays.get(key);
            if(!Objects.nonNull(dqmDeviceDataAbnormalStatDay1)){
                dqmDeviceDataAbnormalStatDay1 = deviceDataAbnormalStatDay;
            }
            if(ParamAbnormalCode.ABNORMAL_8102.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setWorkingTime9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setWorkingTime9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setWorkingTime9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setWorkingTime9004(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setWorkingTime9004Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8105.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setEngineWorktime9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setEngineWorktime9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setEngineWorktime9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setEngineWorktime9004(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setEngineWorktime9004Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8201.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setTotalFuelConsumption9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setTotalFuelConsumption9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setTotalFuelConsumption9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setTotalFuelConsumption9004(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setTotalFuelConsumption9004Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8401.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setPumpingVolume9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setPumpingVolume9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setPumpingVolume9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setPumpingVolume9004(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setPumpingVolume9004Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8403.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDrivingMileage9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDrivingMileage9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setDrivingMileage9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9004.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDrivingMileage9004(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setDrivingMileage9004Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8501.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9008(abnormalCount);
                }else if(ParamAbnormalCode.PARAM_9001.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9001(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9001Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9002.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9002(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9002Data(abnormalData);
                }else if(ParamAbnormalCode.PARAM_9007.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9007(abnormalCount);
                    dqmDeviceDataAbnormalStatDay1.setDeviceLocation9007Data(abnormalData);
                }
            }else if(ParamAbnormalCode.ABNORMAL_8503.getCode().equals(paramCode)){
                if(ParamAbnormalCode.PARAM_9008.getCode().equals(abnormalCode)){
                    dqmDeviceDataAbnormalStatDay1.setDeviceStatus9008(abnormalCount);
                }
            }
            dqmDeviceDataAbnormalStatDays.put(key,dqmDeviceDataAbnormalStatDay1);
        }
        List<DqmDeviceDataAbnormalStatDay> dataAbnormalStatDays = new ArrayList<>(dqmDeviceDataAbnormalStatDays.values());
        int num = 200;
        int pageNum = dataAbnormalStatDays.size()%num==0?dataAbnormalStatDays.size()/num:dataAbnormalStatDays.size()/num+1;
        for (int i = 0; i < pageNum; i++) {
            List<DqmDeviceDataAbnormalStatDay> mapList = dataAbnormalStatDays.stream().skip(i * num).limit(num).collect(Collectors.toList());
            mapper.insertDataAbnormalStatDays(mapList);
        }
    }

}
