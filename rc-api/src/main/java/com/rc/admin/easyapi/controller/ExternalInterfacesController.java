package com.rc.admin.easyapi.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.core.annotation.SysLog;
import com.rc.admin.easyapi.model.req.DqmDeviceQueryReq;
import com.rc.admin.easyapi.service.ExternalInterfacesService;
import com.rc.admin.ors.quality.model.DeviceLedgerResp;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@ResponseResult
@RequestMapping("/external/interfaces")
@Api(value = "对外接口")
public class ExternalInterfacesController {

    @Resource
    private ExternalInterfacesService externalInterfacesService;


    @ApiOperation(value = "查询D365有但新C没有的华兴设备")
    @PostMapping("/all")
    @SysLog(modular = "api-interfaces", method = "查询D365有但新C没有的华兴设备")
    public Response deviceAllPage(@RequestBody OtDeviceAllReq req) {
        try {
            return Response.success(externalInterfacesService.getAllDevice(req));
        }catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }



    @ApiOperation("华兴设备台账-列表")
    @GetMapping("/equipment")
    @SysLog(modular = "api-interfaces", method = "华兴设备台账-列表")
    public Page<DeviceLedgerResp> getHXEquipment(Page<DqmDeviceQueryReq> page, DqmDeviceQueryReq req) {
        if (req.getCreatedBegin() == null) {
            throw new EasyException(Response.SHOW_TYPE_WARNING,"开始时间不能为空");
        }
        if (req.getCreatedEnd() == null) {
            throw new EasyException(Response.SHOW_TYPE_WARNING,"结束时间不能为空");
        }
        return externalInterfacesService.getDevicePage(page, req);
    }



    @ApiOperation("导入设备检查项")
    @PostMapping("/import")
    @SysLog(modular = "api-interfaces", method = "导入设备检查项")
    @Transactional(rollbackFor = Exception.class)
    public Response importCheckConfig(@RequestParam("file") MultipartFile file, HttpServletResponse response){
        try{
            externalInterfacesService.importCheckConfig(file, response);
        }catch (Exception e){
            return Response.failError(String.format("导入失败:%s", e.getMessage()));
        }
        return Response.success("导入成功");
    }



    @ApiOperation("导入设备检查项删除处理")
    @PostMapping("/deleteImport")
    @SysLog(modular = "api-interfaces", method = "导入设备检查项删除")
    @Transactional(rollbackFor = Exception.class)
    public Response importDeleteCheckConfig(@RequestParam("file") MultipartFile file, HttpServletResponse response){
        try{
            externalInterfacesService.importCheckConfigDelete(file, response);
        }catch (Exception e){
            return Response.failError(String.format("导入删除失败:%s", e.getMessage()));
        }
        return Response.success("导入删除成功");
    }


}
