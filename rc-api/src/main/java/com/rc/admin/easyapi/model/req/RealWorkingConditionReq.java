package com.rc.admin.easyapi.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RealWorkingConditionReq
{
      @ApiModelProperty(value = "属性列表中的属性数不能超过2000")
      private List<String> properties;
      @ApiModelProperty(value = "只查询正常值（normal）,只查询异常值（abnormal）或查询全部（all)")
      private String queryStrategy;
      @ApiModelProperty(value = "物实例ID列表，物实例ID的个数不能超过100")
      private List<String> thingIds;
      @ApiModelProperty(value = "物标识ID列表，物标识ID的个数不能超过100")
      private List<String> assetIds;
      @ApiModelProperty(value = "返回空值(ture), 不返回(false), 默认为false")
      private boolean nullValueReturn;
}
