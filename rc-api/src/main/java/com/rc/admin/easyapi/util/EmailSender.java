package com.rc.admin.easyapi.util;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import javax.mail.Message;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

public class EmailSender {


    private static final Logger log = LoggerFactory.getLogger(EmailSender.class);

    // 加载配置文件
    private static Properties loadProperties() {
        Properties props = new Properties();
        try {
            props.load(EmailSender.class.getClassLoader().getResourceAsStream("config/mail.setting"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return props;
    }

    // 发送邮件的方法
    public static void sendEmail(String to, String subject, String body) {
        Properties props = loadProperties();

        // 创建会话
        Session session = Session.getInstance(props,
                new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(props.getProperty("from"), props.getProperty("pass"));
                    }
                });

        try {
            // 创建消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(props.getProperty("from")));
            message.setFrom(new InternetAddress(props.getProperty("from")));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            message.setSubject(subject);
            message.setSubject(subject);
            message.setText(body);
            message.setText(body);
log.info(JSONObject.toJSONString(message));
            // 发送消息
            Transport.send(message,message.getAllRecipients());

            System.out.println("Sent message successfully....");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // 示例：发送邮件
        sendEmail("<EMAIL>", "<EMAIL>", "Hello, this is a test email sent using SSL/TLS.");
    }
}

