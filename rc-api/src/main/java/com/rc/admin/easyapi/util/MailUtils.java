package com.rc.admin.easyapi.util;



import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Properties;

/**
 * JavaMail工具类，用于在Java中发送邮件
 */
@Component
@Slf4j
public class MailUtils {

    @Value("${mail.username:<EMAIL>}")
    private  String USERNAME ;
    @Value("${mail.password:kulbqbwhvrxybigc}")
    public  String PASSWORD;
    @Value("${mail.host:smtp.qq.com}")
    public  String HOST ;
    @Value("${mail.port:587}")
    public  String PORT ;
    public static Session session = null;

    /**
     * 创建Sesssion
     */
    public  void createSession() {

        if (session != null) return;

        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", HOST); //SMTP主机名
        props.put("mail.smtp.port", PORT);

        session = Session.getInstance(props,
                new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(USERNAME, PASSWORD);
                    }
                });
    }

    /**
     * 发送纯文本邮件，单人发送
     *
     * @param title
     * @param content
     * @param toMail
     */
    public  void postMessage(String title, String content, String toMail) {
        try {
            createSession();
            //构造邮件主体
            MimeMessage message = new MimeMessage(session);
            message.setSubject(title);
            message.setText(content);
            message.setFrom(new InternetAddress(USERNAME));
            message.setRecipient(MimeMessage.RecipientType.TO, new InternetAddress(toMail));
            //发送
            Transport.send(message);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param title
     * @param content
     * @param fileNames
     * @param fileBytes
     * @param toMail
     */
    public  void postMessageWithFile(String title, String content, List<String> fileNames, List<byte[]> fileBytes, String toMail) {

        try {
            createSession();
            //构造邮件主体
            MimeMessage message = new MimeMessage(session);
            message.setSubject(title);
            Multipart multipart = new MimeMultipart();
            //邮件主体
            BodyPart textPart = new MimeBodyPart();
            textPart.setContent(content, "text/html;charset=utf-8");
            multipart.addBodyPart(textPart);
            //邮件附件
            if (CollectionUtils.isNotEmpty(fileBytes)) {
                int index = 0;
                for (byte[] fileByte : fileBytes) {
                    BodyPart filePart = new MimeBodyPart();
                    String encodedFileName = MimeUtility.encodeText(fileNames.get(index), "UTF-8", "B");
                    filePart.setFileName(encodedFileName);
                    DataSource dataSource = new ByteArrayDataSource(new ByteArrayInputStream(fileByte), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    filePart.setDataHandler(new DataHandler(dataSource));
                    multipart.addBodyPart(filePart);
                    index++;
                }
            }
            message.setContent(multipart);

            message.setFrom(new InternetAddress(USERNAME));
            message.setRecipient(MimeMessage.RecipientType.TO, new InternetAddress(toMail));

            //发送
            Transport.send(message);
            log.info("邮件发送成功:"+toMail);
        } catch (Exception e) {
            log.error("邮件发送失败:"+toMail);
        }
    }



}
