package com.rc.admin.easyapi.service.dqmService;

import static com.rc.admin.easyapi.job.SyncAbnormalData.BJ_DATEFORMAT;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.rc.admin.easyapi.util.GeometryUtils;
import com.rc.admin.ors.quality.dao.OrsDeviceLocationMapper;
import com.rc.admin.ors.quality.model.OrsDeviceLocation;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrsDeviceLocationService {

  @Resource
  OrsDeviceLocationMapper orsDeviceLocationMapper;

  private static final String geometries = "{\"coordinates\":[[[114.03310978700006,22.509389982000073],[113.93221000000005,22.354440000000068],[114.19802000000004,22.31806000000006],[114.22225568700003,22.54965763900009],[114.03310978700006,22.509389982000073]],[[121.73471000000006,25.13889000000006],[121.0016700000001,25.001660000000072],[120.11123000000009,23.617080000000044],[120.87859000000003,22.341380000000072],[121.83970000000011,24.476380000000063],[121.73471000000006,25.13889000000006]],[[113.53184811200003,22.19467082300008],[113.55268000000001,22.187010000000043],[113.55447026500008,22.21278980200009],[113.53184811200003,22.19467082300008]],[[110.71583000000004,20.068880000000036],[109.30026000000004,19.919230000000084],[108.62832000000003,19.28028000000006],[108.68706000000003,18.50562000000008],[109.54471000000007,18.221110000000067],[110.52554000000003,18.800830000000076],[110.71583000000004,20.068880000000036]],[[114.22225568700003,22.54965763900009],[116.48171000000002,22.939020000000085],[118.98499000000004,24.92694000000006],[119.66888000000006,26.28180000000009],[121.97436000000005,29.58916000000005],[120.99151000000006,30.568890000000067],[121.89166,30.927910000000054],[121.66081000000008,32.054440000000056],[120.24873000000002,34.311450000000036],[119.19747000000007,34.76971000000009],[119.64745000000005,35.57893000000007],[121.93831,36.933530000000076],[120.73706000000004,37.83499000000006],[119.45305000000008,37.12360000000007],[118.83776,38.15291000000008],[117.67221000000006,38.38666000000006],[117.7438800000001,39.105000000000075],[119.00916000000007,39.18804000000006],[121.17747000000008,40.92193000000009],[122.28249000000005,40.53361000000007],[121.68790000000001,39.11833000000007],[124.1280200000001,39.827770000000044],[126.01179000000002,40.896940000000086],[126.92762000000005,41.796110000000056],[128.92692,42.027360000000044],[131.02137000000005,42.85708000000005],[131.09552000000008,44.69138000000004],[131.83616000000006,45.33430000000004],[132.93146000000002,45.02141000000006],[133.90161,46.253630000000044],[134.41534000000001,48.39112000000006],[132.57788000000005,47.733250000000055],[130.9921700000001,47.68721000000005],[129.4212100000001,49.431340000000034],[127.81832000000009,49.59263000000004],[125.72748000000001,52.981520000000046],[123.61471000000006,53.54361000000006],[120.86651000000006,53.27978000000007],[120.65203000000008,51.91435000000007],[117.87471000000005,49.52058000000005],[116.71138000000008,49.83047000000005],[115.59441000000004,47.91749000000004],[118.53934000000004,47.99475000000007],[119.7274900000001,47.16652000000005],[116.58554000000004,46.29583000000008],[116.03137000000004,45.685550000000035],[114.5452600000001,45.389440000000036],[113.63805000000002,44.74527000000006],[111.78499000000005,45.00055000000003],[111.95972000000006,43.83527000000004],[109.51401000000004,42.45652000000007],[106.78194000000008,42.29555000000005],[104.93054000000006,41.65193000000005],[101.81470000000007,42.50972000000007],[96.38207000000006,42.73499000000004],[95.41061000000008,44.29416000000003],[93.55471000000006,44.95722000000006],[90.89694000000003,45.25305000000009],[90.91360000000003,46.952210000000036],[90.07096000000007,47.88791000000003],[89.08514000000008,47.99374000000006],[87.41220000000004,49.07833000000005],[85.76596000000006,48.393320000000074],[84.96284000000003,46.86450000000008],[83.01887000000005,46.975550000000055],[82.32179000000008,45.58310000000006],[80.07666000000006,45.02470000000005],[80.81527000000006,43.168300000000045],[80.21387000000004,42.03079000000008],[73.99096000000003,40.042020000000036],[73.70859000000007,38.85841000000005],[74.79317000000003,38.52368000000007],[74.90360000000004,37.65243000000004],[76.15637000000004,35.83096000000006],[77.88883000000004,35.44156000000004],[79.36835000000008,35.97680000000008],[80.03006000000005,35.429590000000076],[79.08741000000003,33.623660000000086],[79.31400000000008,32.605360000000076],[78.86825000000005,31.161330000000078],[80.99352000000005,30.26395000000008],[81.63026000000008,30.424720000000036],[85.10332000000005,28.31639000000007],[88.96749000000005,27.466380000000072],[89.99819000000008,28.323690000000056],[92.45248000000004,27.82639000000006],[94.69736000000006,29.33653000000004],[96.34027000000003,28.52500000000009],[97.70610000000005,28.519030000000043],[98.64611000000008,27.60528000000005],[98.38304000000005,25.59194000000008],[97.54304000000008,24.476660000000038],[98.81717000000003,23.76072000000005],[100.57221000000004,21.452360000000056],[102.52138000000008,22.765000000000043],[104.00858000000005,22.523240000000044],[105.35872000000006,23.324160000000063],[106.52196000000004,22.932670000000087],[106.69332000000003,22.030830000000037],[107.96888000000007,21.535690000000045],[108.60471000000007,21.91083000000009],[109.66290000000004,20.924160000000086],[113.53184811200003,22.19467082300008],[113.55447026500008,22.21278980200009],[114.03310978700006,22.509389982000073],[114.22225568700003,22.54965763900009]]]}";

  public void updateDeviceLocation(Date bizDate){

    if(bizDate == null)
    {
      Date now = new Date();
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(now);
      calendar.add(Calendar.DAY_OF_YEAR, -1);
      bizDate = calendar.getTime();
      //前一天
    }

    SimpleDateFormat df = new SimpleDateFormat(BJ_DATEFORMAT);
    String statData = df.format(bizDate);

    JSONObject jsonObject = JSONObject.parseObject(geometries);
    JSONArray coordinates = jsonObject.getJSONArray("coordinates");
    String[] geometries = new String[coordinates.size()];

    for (int j=0;j<coordinates.size();j++){
      JSONArray geometriesList = (JSONArray)coordinates.get(j);
      String polygon="POLYGON((";
      for (int k = 0; k < geometriesList.size(); k++) {
        JSONArray longLatList = (JSONArray)geometriesList.get(k);
        if(k<geometriesList.size()-1) {
          polygon = polygon + longLatList.get(0) + " " + longLatList.get(1) + ",";
        }else {
          polygon = polygon + longLatList.get(0) + " " + longLatList.get(1);
        }
      }
      polygon=polygon+"))";
      geometries[j]=polygon;
    }

    List<OrsDeviceLocation> orsDeviceLocationList = orsDeviceLocationMapper.selectList(statData);
    List<List<OrsDeviceLocation>> partitions = Lists.partition(orsDeviceLocationList, 200);
    partitions.parallelStream().forEach(orsDeviceLocations -> {
      orsDeviceLocations.stream().forEach(orsDeviceLocation -> {
        double longitude = orsDeviceLocation.getLongitude();
        double latitude = orsDeviceLocation.getLatitude();
        if (GeometryUtils.isWithinGeometry(longitude, latitude, geometries)) {
          orsDeviceLocation.setDeviceLocation("国内");
        } else {
          orsDeviceLocation.setDeviceLocation("国外");
        }
      });
      orsDeviceLocationMapper.updateBatch(orsDeviceLocations);
    });
  }
}
