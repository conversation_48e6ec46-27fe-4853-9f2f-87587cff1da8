package com.rc.admin.easyapi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.Subscription;
import com.rc.admin.easyapi.model.req.SubscriptionReq;
import com.rc.admin.easyapi.model.resp.SubscriptionResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SubscriptionMapper extends BaseMapper<Subscription> {
    @Select("select distinct division_name from ors_model_division")
    List<String> getDivisionList();

    @Select("<script> " +
            "select distinct division_code from ors_model_division " +
            "<where> " +
            "  <if test='list != null and list.size > 0'> " +
            "    division_name in " +
            "    <foreach collection='list' item='item' separator=',' open='(' close=')'> " +
            "      #{item} " +
            "    </foreach> " +
            "  </if> " +
            "</where> " +
            "</script>")
    List<String> getDivisionCodeList(@Param("list") List<String> list);



    Page<SubscriptionResp> getSubscriptionPage(Page<?> page, @Param("req") SubscriptionReq req);
}