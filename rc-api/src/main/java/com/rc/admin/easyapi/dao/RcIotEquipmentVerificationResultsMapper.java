package com.rc.admin.easyapi.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备数据核验结果表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface RcIotEquipmentVerificationResultsMapper extends BaseMapper<RcIotEquipmentVerificationResults> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<RcIotEquipmentVerificationResults>
     */
    List<RcIotEquipmentVerificationResults> select(Page<RcIotEquipmentVerificationResults> page, @Param("ew") QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper);
    /**
     * 查询详细信息
     *
     * @param id id
     * @return RcIotEquipmentVerificationResults
     */
    RcIotEquipmentVerificationResults getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<RcIotEquipmentVerificationResults>
     */
    List<RcIotEquipmentVerificationResults> exportData(@Param("ew") QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper);

}