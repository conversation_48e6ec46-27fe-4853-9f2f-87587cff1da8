package com.rc.admin.easyapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.ors.quality.entity.OrsVersionUpdateLog;
import com.rc.admin.ors.quality.entity.OrsVersionUpdateLogRate;
import com.rc.admin.ors.quality.service.OrsVersionUpdateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


@RestController
@ResponseResult
@RequestMapping("/api/version-updatet")
@Api(tags = {"版本更新记录"})
public class OrsVersionUpdateController {

    @Autowired
    private OrsVersionUpdateService orsVersionUpdateService;

    // 新增/修改版本更新记录
    @PostMapping("/logs")
    @ApiOperation("新增/修改版本更新记录")
    public Response createVersionUpdateLog(@ModelAttribute OrsVersionUpdateLog orsVersionUpdateLog, @RequestPart(value = "images", required = false) List<MultipartFile> images) {
        try {
            OrsVersionUpdateLog versionUpdateLog = orsVersionUpdateService.createVersionUpdateLog(orsVersionUpdateLog,images);
            return Response.success(versionUpdateLog);
        } catch (IOException e) {
            return Response.failError(e.getMessage());
        }
    }

    // 删除版本更新记录
    @DeleteMapping("/logs/{id}")
    @ApiOperation("删除版本更新记录")
    public Response deleteVersionUpdateLog(@PathVariable Integer id) {
        try {
            orsVersionUpdateService.removeByVersionUpdateId(id);
            return Response.success("删除成功");
        }catch (Exception e){
            return Response.failError(e.getMessage());
        }
    }


    // 查询单个版本更新记录
    @GetMapping("/logs/{id}")
    @ApiOperation("查询单个版本更新记录")
    public Response getVersionUpdateLog(@PathVariable Integer id) {
        try {
            OrsVersionUpdateLog versionUpdateLog = orsVersionUpdateService.getVersionUpdateLogWithDetails(id);
            return Response.success(versionUpdateLog);
        }catch (Exception e){
            return Response.failError(e.getMessage());
        }
    }



    @GetMapping("/logs/rate")
    @ApiOperation("通过类型查询双率")
    public Response getVersionUpdateLogRate(@RequestParam("id") Integer id,@RequestParam(value = "doubleRateSign", required = false) String doubleRateSign) {
        try {
            List<OrsVersionUpdateLogRate> rateData = orsVersionUpdateService.getRateData(id, doubleRateSign);
            return Response.success(rateData);
        }catch (Exception e){
            return Response.failError(e.getMessage());
        }
    }



    @ApiOperation(value = "版本更新记录分页查询")
    @PostMapping("/page")
    public Response page(@RequestBody OrsVersionUpdateLog req) {
        try {
            return Response.success(orsVersionUpdateService.selectVersionUpdateLog(new Page<>(req.getCurrent(), req.getSize()),req));
        }catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }

}
