<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.easyapi.dao.RcIotEquipmentVerificationResultsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults">
        <id column="id" property="id" />
        <result column="region" property="region" />
        <result column="second_process" property="secondProcess" />
        <result column="device_name" property="deviceName" />
        <result column="device_code" property="deviceCode" />
        <result column="child_company_name" property="childCompanyName" />
        <result column="point_type" property="pointType" />
        <result column="type" property="type" />
        <result column="verification_sum_result" property="verificationSumResult" />
        <result column="dest_address_name" property="destAddressName" />
        <result column="dest_address_name_result" property="destAddressNameResult" />
        <result column="description" property="description" />
        <result column="description_result" property="descriptionResult" />
        <result column="data_type" property="dataType" />
        <result column="data_type_result" property="dataTypeResult" />
        <result column="acquisition_frequency" property="acquisitionFrequency" />
        <result column="acquisition_frequency_result" property="acquisitionFrequencyResult" />
        <result column="point_address" property="pointAddress" />
        <result column="point_address_result" property="pointAddressResult" />
        <result column="event_mode" property="eventMode" />
        <result column="event_mode_result" property="eventModeResult" />
        <result column="express" property="express" />
        <result column="express_result" property="expressResult" />
        <result column="mode" property="mode" />
        <result column="save_policy" property="savePolicy" />
        <result column="data_registration" property="dataRegistration" />
        <result column="event_condition" property="eventCondition" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults">
        select t.* from rc_iot_equipment_verification_results t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getById" resultType="com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults">
        select t.* from rc_iot_equipment_verification_results t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults">
        select t.* from rc_iot_equipment_verification_results t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
