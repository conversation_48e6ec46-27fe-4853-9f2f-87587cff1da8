package com.rc.admin.easyapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.util.StringUtils;
import com.rc.admin.auth.service.SysUserRetrievePasswordService;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.easyapi.dao.SanydsCoreParamAbnormalDetailMapperEx;
import com.rc.admin.easyapi.entity.SanydsCoreParamAbnormalDetailEx;
import com.rc.admin.easyapi.job.FlowJob;
import com.rc.admin.easyapi.job.OrsSyncDataJob;
import com.rc.admin.easyapi.job.SubscriptionJob;
import com.rc.admin.easyapi.job.SyncAbnormalData;
import com.rc.admin.easyapi.service.WorkingConditionService;
import com.rc.admin.easyapi.service.dqmService.OrsDeviceLocationService;
import com.rc.admin.easyapi.util.MailUtils;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.DeviceParamReportLogMapper;
import com.rc.admin.ors.quality.dao.OrsDeviceDataAbnormalDetailMapper;
import com.rc.admin.ors.quality.dao.SanydsCoreParamStatLatestMapper;
import com.rc.admin.ors.quality.entity.*;
import com.rc.admin.ors.quality.service.DeviceDataAbnormalStatDayService;
import com.rc.admin.ors.quality.service.OrsBasicDataService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@ResponseResult
@RequestMapping("/test")
@Api(value = "测试")
public class TestMain {
    public static void main(String[] args) {
        String sql = "id                  | 2580643212\n" +
                "uuid                     | BUFLT_5006_En_826856519466901504\n" +
                "device_code              | HPZX2031002068\n" +
                "device_name              | 1217740044788\n" +
                "model_id                 | BUFLT_5006_Model_10098\n" +
                "model_name               | BUFLT_5006_Model_Dump_Truck_4.0_0425\n" +
                "abnormal_name            | 位置漂移\n" +
                "abnormal_data            | {\"当前时间\":\"2024-04-27 19:45:13.475\",\"采样时差\":\"10s\",\"新经度\":\"35.012620\",\"新纬度\":\"28.128517\",\"\n" +
                "旧经度\":\"35.007859\",\"旧纬度\":\"28.130528\",\"漂移距离\":\"0.51769Km\",\"漂移速度\":\"186.36809Km/h\"}\n" +
                "last_online_time         | \n" +
                "offline_duration         | \n" +
                "last_location            | \n" +
                "inventory_classification | \n" +
                "install_classification   | \n" +
                "create_time              | 2024-04-28 08:01:59.344406\n" +
                "property                 | device_location\n" +
                "property_name            | 设备位置\n" +
                "tenant_id                | 5f867ef96e5dad004d557644\n" +
                "last_pv                  | \n" +
                "last_pt                  | 1714218309693\n" +
                "cur_pv                   | \n" +
                "cur_pt                   | 1714218313475\n" +
                "pv_inc                   | \n" +
                "pt_inc                   | 3782\n" +
                "abnormal_time            | 2024-04-27 19:45:13.475\n" +
                "param_code               | 8501\n" +
                "stat_date                | 2024-04-27\n" +
                "abnormal_code            | 9007\n" +
                "raw_data                 | [this_longitude:35.0126200, this_latitude:28.1285170, this_pt:1714218313475, last_longitude:35.007\n" +
                "8590, last_latitude:28.1305280, last_pt:1714218309693, pt_inc:3782, distance:0.5176891371602808, speed:186.36808937770107, th\n" +
                "is_gps_count:24, last_gps_count:24]\n" +
                "raw_time                 | 2024-04-27 22:43:13.324868\n" +
                "detail_id                | 754977153\n" +
                "abnormal_effective       | 1";

    }

    @Resource
    OrsDeviceDataAbnormalDetailMapper orsDeviceDataAbnormalDetailMapper;

    @Resource
    OrsDeviceLocationService orsDeviceLocationService;

    @Resource
    OrsSyncDataJob orsSyncDataJob;
    @Resource
    SubscriptionJob subscriptionJob;


    @Resource
    OrsBasicDataService orsBasicDataService;

    @Resource
    OrsSyncDeviceService orsSyncDeviceService;

    @ApiOperation("同步设备")
    @GetMapping("/device/sync")
    public void orsSyncDataJob() {
        orsSyncDataJob.syncDeviceInfo();
    }



    @ApiOperation("同步国区")
    @GetMapping("/country/sync")
    public void countryRegion() {
        orsSyncDeviceService.syncNationalRegion();
    }

    @ApiOperation(value = "ors_device_data_abnormal_detail")
    @PostMapping("/ors_device_data_abnormal_detail")
    public Response ors_device_data_abnormal_detail(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time")||key.equals("raw_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        OrsDeviceDataAbnormalDetail orsDeviceDataAbnormalDetail = hashMap.toJavaObject(OrsDeviceDataAbnormalDetail.class);

        int insert = orsDeviceDataAbnormalDetailMapper.insert(orsDeviceDataAbnormalDetail);
        return Response.success(insert);
    }

    @Resource
    DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;
    @ApiOperation(value = "ors_device_data_abnormal_detail_day")
    @PostMapping("/ors_device_data_abnormal_detail_day")
    public Response ors_device_data_abnormal_detail_day(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        DeviceDataAbnormalDetailDay deviceDataAbnormalDetailDay = hashMap.toJavaObject(DeviceDataAbnormalDetailDay.class);
        int insert = deviceDataAbnormalDetailDayMapper.insert(deviceDataAbnormalDetailDay);
        return Response.success(insert);
    }

    @Resource
    DeviceDataAbnormalStatDayService deviceDataAbnormalStatDayService;
    @ApiOperation(value = "ors_device_data_abnormal_stat_day")
    @PostMapping("/ors_device_data_abnormal_stat_day")
    public Response ors_device_data_abnormal_stat_day(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        DeviceDataAbnormalStatDay deviceDataAbnormalDetailDay = hashMap.toJavaObject(DeviceDataAbnormalStatDay.class);
        boolean insert = deviceDataAbnormalStatDayService.save(deviceDataAbnormalDetailDay);
        return Response.success(insert);
    }

    @Resource
    DeviceParamReportLogMapper deviceParamReportLogMapper;
    @ApiOperation(value = "ors_device_param_report_log")
    @PostMapping("/ors_device_param_report_log")
    public Response ors_device_param_report_log(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        DeviceParamReportLog deviceDataAbnormalDetailDay = hashMap.toJavaObject(DeviceParamReportLog.class);
        Integer insert = deviceParamReportLogMapper.insert(deviceDataAbnormalDetailDay);
        return Response.success(insert);
    }

    @Resource()
    SanydsCoreParamAbnormalDetailMapperEx sanydsCoreParamAbnormalDetailMapperEx;
    @ApiOperation(value = "sanyds_core_param_abnormal_detail")
    @PostMapping("/sanyds_core_param_abnormal_detail")
    public Response sanyds_core_param_abnormal_detail(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        SanydsCoreParamAbnormalDetailEx sanydsCoreParamAbnormalDetail = hashMap.toJavaObject(SanydsCoreParamAbnormalDetailEx.class);
        Integer insert = sanydsCoreParamAbnormalDetailMapperEx.insert(sanydsCoreParamAbnormalDetail);
        return Response.success(insert);
    }


    @Resource()
    SanydsCoreParamStatLatestMapper sanydsCoreParamStatLatestMapper;
    @ApiOperation(value = "sanyds_core_param_stat_latest")
    @PostMapping("/sanyds_core_param_stat_latest")
    public Response sanyds_core_param_stat_latest(@RequestBody String data) {
        JSONObject hashMap = new JSONObject();
        String[] lines = data.split("\n");
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                if(StringUtils.isBlank(value) ||key.equals("id") )
                {
                    continue;
                }
                if(key.equals("create_time"))
                {
                    String s = value.split("\\.")[0];
                    hashMap.put(key, s);
                }
                else
                {
                    hashMap.put(key, value);
                }
            }
        }
        SanydsCoreParamStatLatest sanydsCoreParamAbnormalDetail = hashMap.toJavaObject(SanydsCoreParamStatLatest.class);
        Integer insert = sanydsCoreParamStatLatestMapper.insert(sanydsCoreParamAbnormalDetail);
        return Response.success(insert);
    }

    @Resource
    SyncAbnormalData syncAbnormalData;
    @ApiOperation(value = "checkAbnormalData")
    @GetMapping("/checkAbnormalData")
    public Response checkAbnormalData(@RequestParam(value = "bizDate",required = false) Date bizDate ) throws ParseException {
        syncAbnormalData.checkAbnormalData(bizDate);
        return Response.success();
    }



    @ApiOperation(value = "insertDeviceRateDay")
    @GetMapping("/insertDeviceRateDay")
    public Response insertDeviceRateDay(@RequestParam(value = "bizDate",required = false) Date bizDate ){
        if(ObjectUtils.isEmpty(bizDate)){
            throw new EasyException("日期不能为空");
        }
        orsBasicDataService.insertDeviceRateDay(bizDate);
        return Response.success();
    }




    @Resource
    FlowJob flowJob;
    @ApiOperation(value = "dagFlow")
    @GetMapping("/dagFlow")
    public Response dagFlow(String bizDate) {

        new Thread(() -> {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = null;
            try {
                parse = df.parse(bizDate);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            flowJob.dagFlow(parse);
        }).start();

        Response success = Response.success();
        success.setData("任务已经提交");
        return success;
    }

    @ApiOperation(value = "analysis")
    @GetMapping("/analysis")
    public Response analysis(String bizDate, HttpServletRequest request) {
        String token = request.getHeader("token");
        if(!token.equals("86c9291b-5656-b224-a204-1bab86e0637b"))
        {
            return Response.failError("token error");
        }

        if(StringUtils.isBlank(bizDate))
        {
            return Response.failError("bizDate error");
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = df.parse(bizDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> analysis = deviceParamReportLogMapper.analysis(parse);
        Map<String,Object> map = new HashMap<>();
        map.put("total",analysis.size());
        map.put("data",analysis);
        Response success = Response.success();
        success.setData(map);
        return success;
    }

    @ApiOperation(value = "abnormal_analysis")
    @GetMapping("/abnormal_analysis")
    public Response abnormal_analysis(String bizDate, HttpServletRequest request) {

        String token = request.getHeader("token");
        if(!token.equals("86c9291b-5656-b224-a204-1bab86e0637b"))
        {
            return Response.failError("token error");
        }

        if(StringUtils.isBlank(bizDate))
        {
            return Response.failError("bizDate error");
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = df.parse(bizDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> analysis = deviceParamReportLogMapper.abnormal_analysis(parse);
        Map<String,Object> map = new HashMap<>();
        map.put("total",analysis.size());
        map.put("data",analysis);
        Response success = Response.success();
        success.setData(map);
        return success;
    }



    @ApiOperation(value = "generateReportLog")
    @GetMapping("/generateReportLog")
    public Response abnormal_detail(Date bizDate){
        deviceParamReportLogMapper.generateReportLog(bizDate);
        Response success = Response.success();
        return success;
    }

    @ApiOperation(value = "abnormal_detail")
    @GetMapping("/abnormal_detail")
    public Response abnormal_detail(String bizDate, HttpServletRequest request) {

        String token = request.getHeader("token");
        if(!token.equals("86c9291b-5656-b224-a204-1bab86e0637b"))
        {
            return Response.failError("token error");
        }

        if(StringUtils.isBlank(bizDate))
        {
            return Response.failError("bizDate error");
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = df.parse(bizDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> analysis = deviceParamReportLogMapper.abnormal_detail(parse);
        Map<String,Object> map = new HashMap<>();
        map.put("total",analysis.size());
        map.put("data",analysis);
        Response success = Response.success();
        success.setData(map);
        return success;
    }

    @ApiOperation(value = "sanyDagFlow")
    @GetMapping("/sanyDagFlow")
    public Response sanyDagFlow(String bizDate, HttpServletRequest request) {
        new Thread(() -> {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = null;
            try {
                parse = df.parse(bizDate);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            flowJob.sanyDagFlow(parse);
        }).start();

        Response success = Response.success();
        success.setData("任务已经提交");
        return success;
    }

    @ApiOperation(value = "更新设备位置是否在国内")
    @GetMapping("/updateLocation")
    public Response updateLocation(Date bizDate) {

        Response response = Response.success();
        try {
            orsDeviceLocationService.updateDeviceLocation(bizDate);
        } catch (Exception e) {
            e.printStackTrace();
            response = Response.failError("更新设备位置是否在国内失败");
        }
        return response;
    }
    @Resource
    private SysUserRetrievePasswordService sysUserRetrievePasswordService;
    @Resource
    private WorkingConditionService workingConditionService;
    @ApiOperation(value = "上传剔除标签工况值")
    @GetMapping("/uploadWorkingConditionDataToPlatform")
    public Response uploadWorkingConditionDataToPlatform() {
        workingConditionService.uploadWorkingConditionDataToPlatform();
        Response success = Response.success();
        return success;
    }

    @ApiOperation(value = "上传剔除标签工况值-全量和每天删除的")
    @GetMapping("/uploadWorkingConditionDataToPlatformCheck")
    public Response uploadWorkingConditionDataToPlatformCheck(@RequestParam(value = "sign", required = false) String sign,
                                                              @RequestParam(value = "date", required = false) String date,
                                                              @RequestParam(value = "assetId", required = false) String assetId,
                                                              @RequestParam(value = "modelId", required = false) String modelId) {
        workingConditionService.allUploadWorkingConditionDataToPlatform(sign,date,assetId,modelId);
        Response success = Response.success();
        return success;
    }


    @Resource
    private MailUtils mailUtils;
    @ApiOperation(value = "邮件测试发送")
    @GetMapping("/mailText")
    public Response mailText(String mail) {
        mailUtils.postMessageWithFile("月的海外设备数据质量月报", "<EMAIL>", null,null, mail);
        Response success = Response.success();
        return success;
    }
    @ApiOperation(value = "月报消息提醒发送测试")
    @GetMapping("/subscriptionSendAllText")
    public Response subscriptionSendAll( ) {
        subscriptionJob.subscriptionSendAll();
        Response success = Response.success();
        return success;
    }
}
