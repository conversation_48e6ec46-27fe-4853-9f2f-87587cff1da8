package com.rc.admin.easyapi.job;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lmax.disruptor.RingBuffer;
import com.rc.admin.ors.quality.dao.DeviceDataAbnormalDetailDayMapper;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.dao.OrsCoreParamStatLatestMapper;
import com.rc.admin.ors.quality.dao.OrsModelPropertiesConfigMapper;
import com.rc.admin.ors.quality.entity.*;
import com.rc.admin.ors.quality.model.DeviceDataModel;
import com.rc.admin.ors.quality.service.DeviceQuestionService;
import com.rc.admin.ors.quality.service.OrsDeviceCheckConfigService;
import com.rc.admin.ors.quality.service.QuestionLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/25 14:44
 * @describe 异常数据检查
 */
@Component
@Slf4j
public class UbnormalDataCheck {

    @Resource(name = "workIotRingBuffer")
    private RingBuffer<DeviceDataModel> ringBuffer;

    @Resource
    private DeviceQuestionService deviceQuestionService;

    @Resource
    private DeviceDataAbnormalDetailDayMapper deviceDataAbnormalDetailDayMapper;

    @Resource
    private QuestionLogService questionLogService;

    @Resource
    private OrsCoreParamStatLatestMapper orsCoreParamStatLatestMapper;

    @Resource
    private OrsModelPropertiesConfigMapper orsModelPropertiesConfigMapper;

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource
    private OrsDeviceCheckConfigService orsDeviceCheckConfigService;

    /**
     * 问题跟进自动验证
     */
//    @Scheduled(fixedDelay = 5 * 100000L)
    public void autoVerifyQuestion(){
        // 先查询所有为关闭的任务，检查是否有剔除的，这部分问题直接关闭
        List<DeviceQuestion> notClosed = deviceQuestionService.list(
                new QueryWrapper<DeviceQuestion>()
                        .lambda()
                        .ne(DeviceQuestion::getCurStep, "close")
        );
        for (DeviceQuestion question : notClosed) {
            String[] cks = question.getCheckItem().split(",");

            // 查看是否有整机剔除
            OrsDeviceCheckConfig whole = orsDeviceCheckConfigService.getOne(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .eq(OrsDeviceCheckConfig::getExcludeType, "WHOLE")
                            .eq(OrsDeviceCheckConfig::getAssetId, question.getAssetId())
            );

            // 如果只有一个检查项且该检查项已剔除，该任务直接关闭
            OrsDeviceCheckConfig config = null;
            if (cks.length == 1) {
                config = orsDeviceCheckConfigService.getOne(
                        new QueryWrapper<OrsDeviceCheckConfig>()
                                .lambda()
                                .eq(OrsDeviceCheckConfig::getPropertyName, cks[0])
                                .eq(OrsDeviceCheckConfig::getAssetId, question.getAssetId())
                );
            }

            // 如果有剔除项或者整机剔除，则直接关闭问题
            if (null != config || null != whole) {
                QuestionLog log = new QuestionLog();
                log.setCreateTime(new Date());
                log.setCurStep("close");
                log.setCurStepName("问题关闭");
                log.setQuestionId(question.getId());
                log.setDeviceCode(question.getDeviceCode());
                log.setAssetId(question.getAssetId());
                log.setHandleState(2);
                log.setVerifyResult("设备检查项剔除考核自动关闭");
                log.setUserAccount("sys");
                log.setUserName("系统自动验证");
                questionLogService.save(log);

                deviceQuestionService.update(
                        new UpdateWrapper<DeviceQuestion>()
                                .lambda()
                                .eq(DeviceQuestion::getId, question.getId())
                                .set(DeviceQuestion::getCurStep, "close")
                                .set(DeviceQuestion::getCurStepName, "问题关闭")
                                .set(DeviceQuestion::getUserAccount, null)
                                .set(DeviceQuestion::getUserName, null)
                                .set(DeviceQuestion::getUpdateTime, new Date())
                );

                questionLogService.update(
                        new UpdateWrapper<QuestionLog>()
                                .lambda()
                                .eq(QuestionLog::getQuestionId, question.getId())
                                .eq(QuestionLog::getHandleState, 1)
                                .set(QuestionLog::getHandleState, 2)
                                .set(QuestionLog::getNextStep,"close")
                                .set(QuestionLog::getUserAccount, "sys")
                                .set(QuestionLog::getUserName, "系统自动验证")
                                .set(QuestionLog::getNextStepName, "问题关闭")
                );
            }
        }

        // 查询在待验证环节超过三天未处理的问题
        List<DeviceQuestion> list = deviceQuestionService.list(
                new QueryWrapper<DeviceQuestion>()
                        .lambda()
                        .eq(DeviceQuestion::getCurStep, "wait_verify")
                        .apply("update_time::DATE < CURRENT_DATE - INTERVAL '3 days'")
        );
        /**
         * 1. 从进入验证开始至当前时间，如果没有出现相同异常，则任务已处理，问题关闭
         * 2. 若从进入验证开始至当前时间，异常项依然出现，则认为验证失败，转回上一节点处理，增加处理意见：{转验证时间} 到 {验证时间} 期间内设备 {属性} 未上报数据无法自动化处理
         * 3. ≥30*24时间范围，仍然无数据回传，自动将时间线节点转向“待启机/待剔除中”，并指定“肖雷”作为对应责任人
         */
        for (DeviceQuestion question : list) {
            // 查看当前设备的属性剔除
            List<OrsDeviceCheckConfig> excludProperties = orsDeviceCheckConfigService.list(
                    new QueryWrapper<OrsDeviceCheckConfig>()
                            .lambda()
                            .ne(OrsDeviceCheckConfig::getExcludeType, "WHOLE")
                            .eq(OrsDeviceCheckConfig::getAssetId, question.getAssetId())
            );
            List<Integer> paramcodes = excludProperties.stream().map(OrsDeviceCheckConfig::getParamCode).collect(Collectors.toList());
            List<String> propertiNames = excludProperties.stream().map(OrsDeviceCheckConfig::getPropertyName).collect(Collectors.toList());

            // 查询最近三天有没有异常
            List<DeviceDataAbnormalDetailDay> abnormalDetails = deviceDataAbnormalDetailDayMapper.selectList(
                    new QueryWrapper<DeviceDataAbnormalDetailDay>()
                            .lambda()
                            .select(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
                            .notIn(!paramcodes.isEmpty(), DeviceDataAbnormalDetailDay::getParamCode, paramcodes)
                            .eq(DeviceDataAbnormalDetailDay::getDeviceName, question.getAssetId())
                            .apply("abnormal_time::DATE > {0}::DATE", question.getUpdateTime())
                            .apply("abnormal_time::DATE < CURRENT_DATE")
                            .groupBy(DeviceDataAbnormalDetailDay::getAbnormalName, DeviceDataAbnormalDetailDay::getPropertyName)
            );

            String checkItem = abnormalDetails.stream().map(DeviceDataAbnormalDetailDay::getPropertyName).distinct().collect(Collectors.joining(","));

            String[] cks = question.getCheckItem().split(",");
            List<String> exceItems = Arrays.asList(cks);
            // 过滤问题检查中的剔除项
            if (!propertiNames.isEmpty()) {
                exceItems = new ArrayList<>();
                for (String s : cks) {
                    if (!propertiNames.contains(s)) {
                        exceItems.add(s);
                    }
                }
            }
            // 如果存在异常记录，则判断新产生的异常项是否与原来记录的异常项一致，不一致则认为没有新的异常产生，关闭问题
            boolean exce = true;
            if (StringUtils.isNotBlank(checkItem)) {

                String[] split = checkItem.split(",");
                for (String ck : exceItems) {
                    if (!exce) {
                        break;
                    }
                    for (String s : split) {
                        if (ck.equals(s)) {
                            exce = false;
                            break;
                        }
                    }
                }
            }

            // 确定最几天有没有上报过工况，以此判定是否开机，如果在这段时间没有上报工况，则认为是关机
            List<OrsCoreParamStatLatest> latests = orsCoreParamStatLatestMapper.selectList(
                    new QueryWrapper<OrsCoreParamStatLatest>()
                            .eq("device_name", question.getAssetId())
                            .apply("param_value_latest_time::DATE >= {0}::DATE", question.getUpdateTime())
                            .apply("param_value_latest_time::DATE <= CURRENT_DATE")
            );

            // 判断最近是否有工况上报
            boolean isReport = false;
            if (!latests.isEmpty()) {
                isReport = true;
                List<OrsBaseDeviceInfo> infos = orsBaseDeviceInfoMapper.selectList(new QueryWrapper<OrsBaseDeviceInfo>().lambda().eq(OrsBaseDeviceInfo::getAssetId, question.getAssetId()));
                if (infos.isEmpty()) {
                    // 没有对应的台账，不处理
                    continue;
                }
                List<OrsModelPropertiesConfig> configs = orsModelPropertiesConfigMapper.selectList(
                        new QueryWrapper<OrsModelPropertiesConfig>()
                                .lambda()
                                .in(OrsModelPropertiesConfig::getPropertyName, Arrays.asList(cks))
                                .eq(OrsModelPropertiesConfig::getModelId, infos.get(0).getModelId())
                );
                List<Integer> collect = latests.stream().map(OrsCoreParamStatLatest::getParamCode).distinct().collect(Collectors.toList());
                for (OrsModelPropertiesConfig config : configs) {
                    if (!collect.contains(config.getParamCode())) {
                        isReport = false;
                    }
                }
            }

            Date date2 = new Date();

            // 如果没有异常，关闭问题
            if ((abnormalDetails.isEmpty() && isReport) || (exce && isReport)) {
                String msg = "问题修复";
                if (!propertiNames.isEmpty()) {
                    msg = " 部分设备检查项剔除考核，未剔除考核项系统自动验证通过，关闭问题";
                }
                QuestionLog log = new QuestionLog();
                log.setCreateTime(new Date());
                log.setCurStep("close");
                log.setCurStepName("问题关闭");
                log.setQuestionId(question.getId());
                log.setDeviceCode(question.getDeviceCode());
                log.setAssetId(question.getAssetId());
                log.setHandleState(2);
                log.setVerifyResult(msg);
                log.setUserAccount("sys");
                log.setUserName("系统自动验证");
                questionLogService.save(log);

                deviceQuestionService.update(
                        new UpdateWrapper<DeviceQuestion>()
                                .lambda()
                                .eq(DeviceQuestion::getId, question.getId())
                                .set(DeviceQuestion::getCurStep, "close")
                                .set(DeviceQuestion::getCurStepName, "问题关闭")
                                .set(DeviceQuestion::getUserAccount, null)
                                .set(DeviceQuestion::getUserName, null)
                                .set(DeviceQuestion::getUpdateTime, new Date())
                );

                questionLogService.update(
                        new UpdateWrapper<QuestionLog>()
                                .lambda()
                                .eq(QuestionLog::getQuestionId, question.getId())
                                .eq(QuestionLog::getCurStep, "wait_verify")
                                .eq(QuestionLog::getHandleState, 1)
                                .set(QuestionLog::getHandlIdea, DateUtil.format(question.getUpdateTime(), "yyyy-MM-dd") + " 转验证到 "+DateUtil.format(date2, "yyyy-MM-dd")+msg)
                                .set(QuestionLog::getHandleState, 2)
                                .set(QuestionLog::getNextStep,"close")
                                .set(QuestionLog::getUserAccount, "sys")
                                .set(QuestionLog::getUserName, "系统自动验证")
                                .set(QuestionLog::getNextStepName, "问题关闭")
                );

                // 将截止到今天的数据标记为
//                markQuestion(question);
                continue;
            }


            long diffInMillies = Math.abs(date2.getTime() - question.getUpdateTime().getTime());
            long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
            // 如果存在异常，且在30天内，转回上一环节的人处理
            if (isReport && diffInDays <= 30 && !exce) {
                List<QuestionLog> logs = questionLogService.list(
                        new QueryWrapper<QuestionLog>()
                                .lambda()
                                .eq(QuestionLog::getQuestionId, question.getId())
                                .eq(QuestionLog::getNextStep, "wait_verify")
                );

                QuestionLog questionLog = logs.get(0);
                QuestionLog log = questionLogService.getOne(
                        new QueryWrapper<QuestionLog>()
                                .lambda()
                                .eq(QuestionLog::getQuestionId, question.getId())
                                .eq(QuestionLog::getHandleState, 1)
                                .eq(QuestionLog::getCurStep, "wait_verify")
                );
                log.setCreateTime(new Date());
                log.setCurStep("wait_verify");
                log.setCurStepName("系统自动验证");
                log.setUserAccount("sys");
                log.setUserName("系统自动检查");
                log.setQuestionId(question.getId());
                log.setDeviceCode(question.getDeviceCode());
                log.setAssetId(question.getAssetId());
                log.setHandleState(2);
                log.setNextStep(questionLog.getCurStep());
                log.setNextUser(questionLog.getUserAccount());
                log.setNextStepName(questionLog.getCurStepName());
                log.setNextUserName(questionLog.getUserName());
                log.setVerifyResult("问题修复");
                log.setHandlIdea(DateUtil.format(question.getUpdateTime(), "yyyy-MM-dd") + " 到 "+DateUtil.format(date2, "yyyy-MM-dd")+" 期间内设备重复出现相关异常");
                questionLogService.updateById(log);

                log = new QuestionLog();
                log.setCreateTime(new Date());
                log.setCurStep(questionLog.getCurStep());
                log.setCurStepName(questionLog.getCurStepName());
                log.setUserAccount(questionLog.getUserAccount());
                log.setUserName(questionLog.getUserName());
                log.setQuestionId(question.getId());
                log.setDeviceCode(question.getDeviceCode());
                log.setAssetId(question.getAssetId());
                log.setHandleState(1);
                questionLogService.save(log);

                // 更新问题的下一处理信息
                question.setCurStep(log.getCurStep());
                question.setCurStepName(log.getCurStepName());
                question.setUserAccount(log.getUserAccount());
                question.setUserName(log.getUserName());
                question.setUpdateTime(new Date());
                deviceQuestionService.updateById(question);

//                markQuestion(question);
                continue;
            }

            // 如果最新工况没有数据 则查询更久之前的，30*24时间范围，仍然无数据回传，自动将时间线节点转向“待启机/待剔除中”，并指定“肖雷”作为对应责任人
            if (diffInDays > 30) {
                latests = orsCoreParamStatLatestMapper.selectList(
                        new QueryWrapper<OrsCoreParamStatLatest>()
                                .eq("device_name", question.getAssetId())
                                .apply("param_value_earliest_time::DATE >= CURRENT_DATE - INTERVAL '31 days'")
                                .apply("param_value_earliest_time::DATE <= CURRENT_DATE")
                );
                if (latests.isEmpty()) {
                    QuestionLog log = new QuestionLog();
                    log.setCreateTime(new Date());
                    log.setCurStep("wait_verify");
                    log.setCurStepName("待验证");
                    log.setUserAccount("sys");
                    log.setUserName("系统自动检查");
                    log.setQuestionId(question.getId());
                    log.setDeviceCode(question.getDeviceCode());
                    log.setAssetId(question.getAssetId());
                    log.setHandlIdea("设备超过30天未上传数据，请检查设备");
                    log.setHandleState(2);
                    log.setNextStep("wait_start");
                    log.setNextUser("lei.xiao");
                    log.setNextStepName("待启机/待剔除");
                    log.setNextUserName("肖雷");
                    log.setVerifyResult("问题修复");
                    questionLogService.save(log);

                    // 更新问题的下一处理信息
                    question.setCurStep(log.getNextStep());
                    question.setCurStepName(log.getNextStepName());
                    question.setUserAccount(log.getNextUser());
                    question.setUserName(log.getNextUserName());
                    question.setUpdateTime(new Date());
                    deviceQuestionService.updateById(question);

//                    markQuestion(question);
                }
            }
        }
    }
}
