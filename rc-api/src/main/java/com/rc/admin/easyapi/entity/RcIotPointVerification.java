package com.rc.admin.easyapi.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 数据核验标准模板表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@TableName("rc_iot_point_verification")
public class RcIotPointVerification extends Model<RcIotPointVerification> {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 二级工艺编码
     */
//    @Excel(name = "二级工艺编码", width = 10, orderNum = "0" )
//    @NotBlank(message = "二级工艺编码不能为空")
    @TableField(value = "second_process")
    private String secondProcess;
    /**
     * 类型
     */
    @Excel(name = "类型(必填)", width = 12, orderNum = "0")
//    @NotBlank(message = "类型不能为空")
    @TableField(value = "type")
    private String type;
    /**
     * 点位描述
     */
    @Excel(name = "点位描述(必填)", width = 25, orderNum = "1")
//    @NotBlank(message = "点位描述不能为空")
    @TableField(value = "description")
    private String description;
    /**
     * 点位名称
     */
    @Excel(name = "点位名称(必填)", width = 25, orderNum = "2")
//    @NotBlank(message = "点位名称不能为空")
    @TableField(value = "dest_address_name")
    private String destAddressName;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 25, orderNum = "3")
    @TableField(value = "remark")
    private String remark;
    /**
     * 数据类型(值类型)
     */
    @Excel(name = "数据类型(必填)", width = 9, orderNum = "4")
    @TableField(value = "data_type")
    private String dataType;
    /**
     * 传输方式
     */
    @Excel(name = "传输方式(必填)", width = 5, orderNum = "5")
    @TableField(value = "mode")
    private String mode;
    /**
     * 采集频率
     */
    @Excel(name = "采集频率(毫秒)", width = 5, orderNum = "6")
    @TableField(value = "acquisition_frequency")
    private String acquisitionFrequency;
    /**
     * 保存策略
     */
    @Excel(name = "保存策略", width = 5, orderNum = "7")
    @TableField(value = "save_policy")
    private String savePolicy;
    /**
     * 数据等级
     */
    @Excel(name = "数据等级", width = 5, orderNum = "8")
    @TableField(value = "data_registration")
    private String dataRegistration;
    /**
     * 点位地址
     */
    @Excel(name = "点位地址", width = 25, orderNum = "9")
    @TableField(value = "point_address")
//    @NotBlank(message = "点位地址不能为空")
    private String pointAddress;

    /**
     * 触发方式
     */
    @Excel(name = "触发方式", width = 25, orderNum = "10")
    @TableField(value = "event_mode")
    private String eventMode;
    /**
     * 逻辑表达式
     */
    @Excel(name = "逻辑表达式", width = 25, orderNum = "11")
    @TableField(value = "expression")
    private String expression;
    /**
     * 点位值运算
     */
    @Excel(name = "点位值运算", width = 25, orderNum = "12")
    @TableField(value = "express")
    private String express;
    /**
     * 协议名称
     */
    @Excel(name = "协议名称", width = 25, orderNum = "13")
    @TableField(value = "protocol_name")
    private String protocolName;
    /**
     * 创建时间
     */
//    @Excel(name = "创建时间", width = 20, orderNum = "15", exportFormat = "yyyy-MM-dd HH:mm:ss")
//    @NotNull(message = "创建时间不能为空")
    @TableField(value = "create_time")
    private Date createTime;//    @NotNull(message = "创建时间不能为空")

    @Excel(name = "点位类型(必填)", width = 25, orderNum = "14")
    @TableField(value = "point_type")
    private String pointType;

    // 非表字段
    /**
     * 创建时间 - 开始时间
     */
    @TableField(exist=false)
    private Date startCreateTime;
    /**
     * 创建时间 - 结束时间
     */
    @TableField(exist=false)
    private Date endCreateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public String getPointType() {
        return pointType;
    }

    public void setPointType(String pointType) {
        this.pointType = pointType;
    }

    public String getExpress() {
        return express;
    }

    public void setExpress(String express) {
        this.express = express;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getEventMode() {
        return eventMode;
    }

    public void setEventMode(String eventMode) {
        this.eventMode = eventMode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getSecondProcess() {
        return secondProcess;
    }

    public void setSecondProcess(String secondProcess) {
        this.secondProcess = secondProcess;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public String getDestAddressName() {
        return destAddressName;
    }

    public void setDestAddressName(String destAddressName) {
        this.destAddressName = destAddressName;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }
    public String getPointAddress() {
        return pointAddress;
    }

    public void setPointAddress(String pointAddress) {
        this.pointAddress = pointAddress;
    }
    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
    public String getAcquisitionFrequency() {
        return acquisitionFrequency;
    }

    public void setAcquisitionFrequency(String acquisitionFrequency) {
        this.acquisitionFrequency = acquisitionFrequency;
    }
    public String getSavePolicy() {
        return savePolicy;
    }

    public void setSavePolicy(String savePolicy) {
        this.savePolicy = savePolicy;
    }
    public String getDataRegistration() {
        return dataRegistration;
    }

    public void setDataRegistration(String dataRegistration) {
        this.dataRegistration = dataRegistration;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(Date startCreateTime) {
        this.startCreateTime = startCreateTime;
    }
    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }
}
