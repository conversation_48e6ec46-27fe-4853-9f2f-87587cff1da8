package com.rc.admin.easyapi.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.common.redis.cache.AIValueOperations;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisUtil;
import com.rc.admin.easyapi.dao.IotDeviceInfoMapper;
import com.rc.admin.easyapi.dao.RcIotEquipmentVerificationResultsMapper;
import com.rc.admin.easyapi.dao.RcIotPointVerificationMapper;
import com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults;
import com.rc.admin.easyapi.entity.RcIotPointVerification;
import com.rc.admin.easyapi.model.resp.PointResultListResp;
import com.rc.admin.easyapi.service.RcIotEquipmentVerificationResultsService;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.sys.service.SysRedisService;
import com.rc.admin.util.ToolUtil;
import com.rc.admin.util.office.ExcelUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备数据核验结果表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class RcIotEquipmentVerificationResultsServiceImpl extends ServiceImpl<RcIotEquipmentVerificationResultsMapper, RcIotEquipmentVerificationResults> implements RcIotEquipmentVerificationResultsService, ImportService {


    @Resource
    RcIotPointVerificationMapper verificationMapper;
    @Resource
    IotDeviceInfoMapper infoMapper;

    @Resource
    private SysRedisService serviceRedis;
    @Autowired
    private AIValueOperations valueOperations;
    /**
     * 列表
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     * @param page   分页
     * @return Page<RcIotEquipmentVerificationResults>
     */
    @Override
    public Page<RcIotEquipmentVerificationResults> select(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults, Page<RcIotEquipmentVerificationResults> page) {
        QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper = getQueryWrapper(rcIotEquipmentVerificationResults);
        page.setSortOrder("asc".equals(page.getSortOrder()) ? "ascend" : "descend");
//        queryWrapper.isNotNull("t.device_code");
//        queryWrapper.orderByAsc("t.device_code");
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    /**
     * 获取查询条件
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     * @return QueryWrapper<RcIotEquipmentVerificationResults>
     */
    private QueryWrapper<RcIotEquipmentVerificationResults> getQueryWrapper(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults){
        QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper = new QueryWrapper<>();
        if(rcIotEquipmentVerificationResults != null){
            // 查询条件
            // 核验结果
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getVerificationSumResult())) {
                queryWrapper.eq("t.verification_sum_result", rcIotEquipmentVerificationResults.getVerificationSumResult());
            }
            // 数采平台
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getRegion())) {
                queryWrapper.like("t.region", rcIotEquipmentVerificationResults.getRegion());
            }

            // 协议
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getProtocolName())) {
                queryWrapper.like("t.protocol_name", rcIotEquipmentVerificationResults.getProtocolName());
            }
            // 二级工艺编码
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getSecondProcess())) {
                queryWrapper.like("t.second_process", rcIotEquipmentVerificationResults.getSecondProcess());
            }
            // 设备名称
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDeviceName())) {
                queryWrapper.like("t.device_name", rcIotEquipmentVerificationResults.getDeviceName());
            }
            // 设备编号
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDeviceCode())) {
                queryWrapper.like("t.device_code", rcIotEquipmentVerificationResults.getDeviceCode());
            }
            // 子公司
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getChildCompanyName())) {
                queryWrapper.like("t.child_company_name", rcIotEquipmentVerificationResults.getChildCompanyName());
            }
            // 点位类型
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getPointType())) {
                queryWrapper.like("t.point_type", rcIotEquipmentVerificationResults.getPointType());
            }
            // 类型
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getType())) {
                queryWrapper.like("t.type", rcIotEquipmentVerificationResults.getType());
            }
            // 模板-点位名称
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDestAddressName())) {
                queryWrapper.like("t.dest_address_name", rcIotEquipmentVerificationResults.getDestAddressName());
            }

            // 点位检查结果
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDestAddressNameResult())) {
                queryWrapper.like("t.dest_address_name_result", rcIotEquipmentVerificationResults.getDestAddressNameResult());
            }
            // 模板-描述
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDescription())) {
                queryWrapper.like("t.description", rcIotEquipmentVerificationResults.getDescription());
            }

            // 描述检查结果
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDescriptionResult())) {
                queryWrapper.like("t.description_result", rcIotEquipmentVerificationResults.getDescriptionResult());
            }
            // 模板-值类型
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDataType())) {
                queryWrapper.like("t.data_type", rcIotEquipmentVerificationResults.getDataType());
            }

            // 值类型检查结果
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDataTypeResult())) {
                queryWrapper.like("t.data_type_result", rcIotEquipmentVerificationResults.getDataTypeResult());
            }
            // 模板-采集频率
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getAcquisitionFrequency())) {
                queryWrapper.like("t.acquisition_frequency", rcIotEquipmentVerificationResults.getAcquisitionFrequency());
            }

            // 采集频率检查结果
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getAcquisitionFrequencyResult())) {
                queryWrapper.like("t.acquisition_frequency_result", rcIotEquipmentVerificationResults.getAcquisitionFrequencyResult());
            }
            // 传输方式
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getMode())) {
                queryWrapper.like("t.mode", rcIotEquipmentVerificationResults.getMode());
            }
            // 保存策略
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getSavePolicy())) {
                queryWrapper.like("t.save_policy", rcIotEquipmentVerificationResults.getSavePolicy());
            }
            // 数据登记
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getDataRegistration())) {
                queryWrapper.like("t.data_registration", rcIotEquipmentVerificationResults.getDataRegistration());
            }
            // 逻辑表达式
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getEventCondition())) {
                queryWrapper.like("t.expression", rcIotEquipmentVerificationResults.getEventCondition());
            }
            // 点位地址
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getPointAddress())) {
                queryWrapper.like("t.point_address", rcIotEquipmentVerificationResults.getPointAddress());
            }
            // 备注
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getRemark())) {
                queryWrapper.like("t.remark", rcIotEquipmentVerificationResults.getRemark());
            }
            // 创建时间 - 开始时间
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getStartCreateTime())) {
                queryWrapper.ge("t.create_time", rcIotEquipmentVerificationResults.getStartCreateTime());
            }
            // 创建时间 - 结束时间
            if (Validator.isNotEmpty(rcIotEquipmentVerificationResults.getEndCreateTime())) {
                queryWrapper.le("t.create_time", rcIotEquipmentVerificationResults.getEndCreateTime());
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return RcIotEquipmentVerificationResults
     */
    @Override
    public RcIotEquipmentVerificationResults get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return RcIotEquipmentVerificationResults
     */
    @Override
    public RcIotEquipmentVerificationResults add() {
        RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults =new RcIotEquipmentVerificationResults();
        // 设置默认值
        return rcIotEquipmentVerificationResults;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param rcIotEquipmentVerificationResults 表单内容
     * @return RcIotEquipmentVerificationResults
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RcIotEquipmentVerificationResults saveData(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults) {
        ToolUtil.checkParams(rcIotEquipmentVerificationResults);
        if (Validator.isEmpty(rcIotEquipmentVerificationResults.getId())) {
            // 新增,设置默认值
        }
        return (RcIotEquipmentVerificationResults) ToolUtil.checkResult(saveOrUpdate(rcIotEquipmentVerificationResults), rcIotEquipmentVerificationResults);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public void exportData(HttpServletResponse httpServletResponse,RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults) {
        QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper = getQueryWrapper(rcIotEquipmentVerificationResults);
        List<RcIotEquipmentVerificationResults> list = baseMapper.exportData(queryWrapper);

//        if (CollectionUtils.isEmpty(list)){
//            return Response.failError("500","查无数据");
//        }
//        String downloadId = ExcelUtil.writeAndGetDownloadId("设备数据核验结果", "设备数据核验结果", list, RcIotEquipmentVerificationResults.class);
//        return Response.success(downloadId);

        String fileName = "设备数据核验结果表";
        try {
            httpServletResponse.setContentType("application/octet-stream");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            httpServletResponse.setCharacterEncoding("utf-8");
            // Workbook workbook = ExcelExportUtil.exportBigExcel(new ExportParams(null, fileName), RcIotEquipmentVerificationResults.class, list);

            //大前提，很重要！！假设导出的Excel数据是List<A> aList
            //分别是 totalPage是总页数，pageSize 页码长度
            int totalPage = (list.size() / 10000) + 1;
            int pageSize = 10000;
            ExportParams exportParams = new ExportParams(null, fileName);
            Workbook workbook = null;
            workbook = ExcelExportUtil.exportBigExcel(exportParams, RcIotEquipmentVerificationResults.class, new IExcelExportServer() {
                /**
                 * obj 就是下面的totalPage，限制条件
                 * page 是页数，他是在分页进行文件转换，page每次+1
                 */
                @Override
                public List<Object> selectListForExcelExport(Object obj, int page) {
                    //很重要！！这里面整个方法体，其实就是将所有的数据aList分批返回处理
                    //最大每批10000
                    if (page > totalPage) {
                        return null;
                    }
                    int fromIndex = (page - 1) * pageSize;
                    int toIndex = page != totalPage ? fromIndex + pageSize : list.size();

                    List<Object> subList = new ArrayList<>();
                    subList.addAll(list.subList(fromIndex, toIndex));

                    return subList;
                }
            }, totalPage);

            ServletOutputStream outputStream = httpServletResponse.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
            workbook.close();
            workbook = null;
        } catch (IOException e) {
            throw new EasyException("文件写入失败[" + e.getMessage() + "]");
        }
    }

    @Override

    public String pointVerification(List<String> secondProcesslist,List<String> field) {
        if(CollectionUtils.isEmpty(secondProcesslist)){
            secondProcesslist = verificationMapper.getVerificationSecondProcess();
        }
            boolean b = secondProcesslist.stream().allMatch(m -> {
                return m.equals(RedisUtil.get(ServiceEnum.POINT_VERIFICATION+":"+m));
            });
            if (b){
                return "所选二级工艺正在进行核验";
            }

        QueryWrapper<RcIotPointVerification> qw = new QueryWrapper<>();
        qw.in(!CollectionUtils.isEmpty(secondProcesslist),"second_process",secondProcesslist);

        return dataProcessing( qw,secondProcesslist,field);

    }

    @Nullable
    @Transactional
     String dataProcessing(QueryWrapper<RcIotPointVerification> qw,List<String> secondProcesslist,List<String> field) {
        Date date = new Date();
        //获取点位模板数据
        qw.last("and (protocol_name = '' or protocol_name is  null)");
        List<RcIotPointVerification> rcIotPointVerifications = verificationMapper.selectList(qw);
        Map<String, List<RcIotPointVerification>> collect = rcIotPointVerifications.stream().collect(Collectors.groupingBy(RcIotPointVerification::getSecondProcess));

        List<String> secondProcess = new ArrayList<>(collect.keySet());

        qw = new QueryWrapper<>();

        qw.in(!CollectionUtils.isEmpty(secondProcesslist),"second_process",secondProcesslist);
        qw.last("and (protocol_name != '' and protocol_name is not null)");
        List<RcIotPointVerification> haveProtocolName = verificationMapper.selectList(qw);
        Map<String, List<RcIotPointVerification>> haveProtocolNameCollect = haveProtocolName.stream().collect(Collectors.groupingBy(RcIotPointVerification::getSecondProcess));
        List<String> haveProtocolNameSecondProcess = new ArrayList<>(haveProtocolNameCollect.keySet());


        if (!CollectionUtils.isEmpty(secondProcesslist)){
            for (String s : secondProcesslist) {
                try {
                    RedisUtil.set(ServiceEnum.POINT_VERIFICATION+":"+s,s);
                } catch (Exception e) {
                    log.error("点位核验异常:二级工艺编码设置redis失败");
                    return "redis数据库链接异常";
                }
            }
        }
        List<Map<String,RcIotEquipmentVerificationResults>> listMaps = new ArrayList<>();
        List<RcIotEquipmentVerificationResults> results = new ArrayList<>();
        //无协议名称处理
        secondProcess.forEach(str -> {

                    baseMapper.delete(new QueryWrapper<RcIotEquipmentVerificationResults>().lambda().eq( RcIotEquipmentVerificationResults::getSecondProcess, str).last("and (protocol_name = '' or protocol_name is  null)"));

                    //获取所有点位信息
                    List<PointResultListResp> pointInfoBySecondProcess = infoMapper.getPointInfoBySecondProcess(str,null);
                    //获取无点位设备信息
                    List<PointResultListResp> notPointInfoDevice = infoMapper.getNotPointInfoBySecondProcess(str,null);
                    //设备分组
                    Map<String, List<PointResultListResp>> deviceMap = pointInfoBySecondProcess.stream().collect(Collectors.groupingBy(PointResultListResp::getDeviceCode));

                    List<RcIotEquipmentVerificationResults> list = new ArrayList<>();
                    Map<String,RcIotEquipmentVerificationResults> map = new HashMap<>();
                    List<RcIotPointVerification> ripvs = collect.get(str);

                    //区分点位类型
                    //获取匹配不上的点位
                    //模板点位
            dataProcessings(date, notPointInfoDevice, deviceMap, ripvs, list, map,field);
            results.addAll(list);
            listMaps.add(map);
        });

        haveProtocolNameSecondProcess.forEach(str->{
            List<RcIotPointVerification> verifications = haveProtocolNameCollect.get(str);
            if (!CollectionUtils.isEmpty(verifications)){
                Map<String, List<RcIotPointVerification>> listMap = verifications.stream().collect(Collectors.groupingBy(RcIotPointVerification::getProtocolName));

                listMap.keySet().forEach(strs->{
                    baseMapper.delete(new QueryWrapper<RcIotEquipmentVerificationResults>().lambda().eq( RcIotEquipmentVerificationResults::getSecondProcess,str).eq(RcIotEquipmentVerificationResults::getProtocolName,strs));
                    //获取所有点位信息
                    List<PointResultListResp> pointInfoBySecondProcess = infoMapper.getPointInfoBySecondProcess(str,strs);
                    //获取无点位设备信息
                    List<PointResultListResp> notPointInfoDevice = infoMapper.getNotPointInfoBySecondProcess(str,strs);
                    //设备分组
                    Map<String, List<PointResultListResp>> deviceMap = pointInfoBySecondProcess.stream().collect(Collectors.groupingBy(PointResultListResp::getDeviceCode));

                    List<RcIotPointVerification> verificationList = listMap.get(strs);

                    List<RcIotEquipmentVerificationResults> list = new ArrayList<>();
                    Map<String,RcIotEquipmentVerificationResults> map = new HashMap<>();

                    dataProcessings(date, notPointInfoDevice, deviceMap, verificationList, list, map,field);
                    results.addAll(list);
                    listMaps.add(map);
                });
            }
        });

        List<RcIotEquipmentVerificationResults> resultsList = new ArrayList<>();
        for (Map<String, RcIotEquipmentVerificationResults> map : listMaps) {
            if (map.keySet().size() > 0) {
                map.keySet().forEach(s -> {
                    if (results.stream().noneMatch(Rie -> s.equals(Rie.getSecondProcess() + Rie.getDeviceCode() + Rie.getPointType() + Rie.getDestAddressName() + Rie.getEventMode() + Rie.getExpress() + Rie.getEventCondition()))) {
                        resultsList.add(map.get(s));
                    }
                });
            }
        }
        List<RcIotEquipmentVerificationResults> resultsArrayList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(resultsList)) {
            List<RcIotEquipmentVerificationResults> collect1 = resultsList.stream().distinct().collect(Collectors.toList());
            for (RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults : collect1) {
                QueryWrapper<RcIotEquipmentVerificationResults> queryWrapper = new QueryWrapper<RcIotEquipmentVerificationResults>();
                queryWrapper.eq("second_process", rcIotEquipmentVerificationResults.getSecondProcess());
                queryWrapper.eq("device_code", rcIotEquipmentVerificationResults.getDeviceCode());
                queryWrapper.eq("point_type", rcIotEquipmentVerificationResults.getPointType());
                queryWrapper.eq("dest_address_name", rcIotEquipmentVerificationResults.getDestAddressName());
                if (baseMapper.selectCount(queryWrapper) == 0) {
                    resultsArrayList.add(rcIotEquipmentVerificationResults);
                }
            }
            if (!CollectionUtils.isEmpty(resultsArrayList)) {
                saveBatch(resultsArrayList);
            }
        }
        secondProcesslist.forEach(str->{
            RedisUtil.del(ServiceEnum.POINT_VERIFICATION+":"+str);
        });
        return null;
    }

    /**
     * 点位归仓
     * @param date 时间
     * @param notPointInfoDevice 无点位设备
     * @param deviceMap 分组后的设备map
     * @param verificationList 模板点位列
     * @param list 归仓容器
     * @param map 中继器
     * @param field 核验项
     */
    private void dataProcessings(Date date, List<PointResultListResp> notPointInfoDevice,
                                 Map<String, List<PointResultListResp>> deviceMap,
                                 List<RcIotPointVerification> verificationList,
                                 List<RcIotEquipmentVerificationResults> list,
                                 Map<String, RcIotEquipmentVerificationResults> map,
                                 List<String> field) {
        verificationList.forEach(ipv->{

            deviceMap.forEach((deviceCode, point)->{
                //获取自定义点位
                 List<PointResultListResp> customPoint = point.stream().filter(p -> "自定义点位".equals(p.getPointType())).collect(Collectors.toList());
                 Map<String, List<PointResultListResp>> customPointMap = customPoint.stream().collect(Collectors.groupingBy(PointResultListResp::getDestAddressName));

                //获取计算点位
                List<PointResultListResp> caculatePoint = point.stream().filter(p -> "计算点位".equals(p.getPointType())).collect(Collectors.toList());
                Map<String, List<PointResultListResp>> caculatetMap = caculatePoint.stream().collect(Collectors.groupingBy(PointResultListResp::getDestAddressName));

                 //获取采集点位
                List<PointResultListResp> standardPoint = point.stream().filter(p -> "采集点位".equals(p.getPointType())).collect(Collectors.toList());
                Map<String, List<PointResultListResp>> standardPointMap = standardPoint.stream().collect(Collectors.groupingBy(PointResultListResp::getDestAddressName));

                //处理计算点位
                 if (!CollectionUtils.isEmpty(caculatePoint)) {
                   setData(date, map, caculatetMap, caculatePoint, list, ipv, null,field,"计算点位");
                }
                //处理自定义点位
                if (!CollectionUtils.isEmpty(customPoint)) {
                    setData(date, map, customPointMap, customPoint, list, ipv, null,field,"自定义点位");
                }
                //处理采集点位
                if (!CollectionUtils.isEmpty(standardPoint)) {
                    setData(date, map, standardPointMap, standardPoint, list, ipv, null,field,"采集点位");
                }
            });
            //处理无点位设备
            if (!CollectionUtils.isEmpty(notPointInfoDevice)) {
                setData(date, null, null, null, list, ipv, notPointInfoDevice,field,"无点位");
            }
        });

        if (!CollectionUtils.isEmpty(list)){
            saveBatch(list);
        }
    }

    @Override
    public List<Map<String, String>> getSecondProcess() {
        List<Map<String, String>> secondProcess = verificationMapper.getSecondProcess();
        if (CollectionUtils.isEmpty(secondProcess)){
            return null;
        }
        return secondProcess.stream().filter(m -> !m.get("secondProcess").equals(RedisUtil.get(ServiceEnum.POINT_VERIFICATION+":"+m.get("secondProcess")))
        ).collect(Collectors.toList());
    }

    //数据核验
    private static void setData(Date date,Map<String,RcIotEquipmentVerificationResults> map,
                                Map<String, List<PointResultListResp>> customPointMap,
                                List<PointResultListResp> point,
                                List<RcIotEquipmentVerificationResults> list,
                                RcIotPointVerification ipv,
                                List<PointResultListResp> notPointInfoDevice,
                                List<String> field,
                                String pointType) {
        List<PointResultListResp> pointResultListResps;
        if (customPointMap!=null){
             pointResultListResps = customPointMap.get(ipv.getDestAddressName());
        }else{
            if (notPointInfoDevice == null) {
                return;
            }
            pointResultListResps = notPointInfoDevice;
        }

        if (pointResultListResps == null) {
            dataProcessings(date,map ,list, ipv, point.get(0));
        }else {
            dataProcessing(date, list, ipv, pointResultListResps,field,pointType);
        }

    }

    private static void dataProcessing(Date date, List<RcIotEquipmentVerificationResults> list,
                                       RcIotPointVerification ipv,
                                       List<PointResultListResp> pointResultListResps,
                                       List<String> field,
                                       String pointType) {
        StringBuilder sb = new StringBuilder();
        pointResultListResps.forEach(prr -> {
            if (!pointType.equals(ipv.getPointType())) {
            return;
            }

            if (StringUtils.isNotBlank(ipv.getProtocolName())) {
                if (pointType.equals("自定义点位") || pointType.equals("采集点位")) {
                    if (list.stream().anyMatch(ric -> (prr.getSecondProcess().equals(ric.getSecondProcess())
                            && prr.getDeviceCode().equals(ric.getDeviceCode())
                            && prr.getDestAddressName().equals(ric.getDestAddressName())
                            && pointType.equals(prr.getPointType()) && ric.getProtocolName().equals(prr.getProtocolType())))) {
                        return;
                    }
                }

                if (pointType.equals("计算点位")) {
                    if (list.stream().anyMatch(ric -> (
                            ric.getPointType().equals("计算点位")
                            && prr.getSecondProcess().equals(ric.getSecondProcess())
                            && prr.getDeviceCode().equals(ric.getDeviceCode())
                            && prr.getDestAddressName().equals(ric.getDestAddressName())
                            && pointType.equals(prr.getPointType())) && ric.getExpress().equals(prr.getExpress())
                            && ric.getEventMode().equals(prr.getEventMode())  && ric.getProtocolName().equals(prr.getProtocolType()))) {
                        return;
                    }
                }
            }else{
                if (pointType.equals("自定义点位") || pointType.equals("采集点位")) {
                    if (list.stream().anyMatch(ric -> (prr.getSecondProcess().equals(ric.getSecondProcess())
                            && prr.getDeviceCode().equals(ric.getDeviceCode())
                            && prr.getDestAddressName().equals(ric.getDestAddressName())
                            && pointType.equals(prr.getPointType())))) {
                        return;
                    }
                }

                if (pointType.equals("计算点位")) {
                    if (list.stream().anyMatch(ric -> (
                            ric.getPointType().equals("计算点位")
                            && prr.getSecondProcess().equals(ric.getSecondProcess())
                            && prr.getDeviceCode().equals(ric.getDeviceCode())
                            && prr.getDestAddressName().equals(ric.getDestAddressName())
                            && pointType.equals(prr.getPointType())) && ric.getExpress().equals(prr.getExpress())
                            && ric.getEventMode().equals(prr.getEventMode()))) {
                        return;
                    }
                }
            }

                RcIotEquipmentVerificationResults riev = new RcIotEquipmentVerificationResults();
                riev.setRegion(prr.getRegion());
                riev.setSecondProcess(prr.getSecondProcess());
                riev.setProtocolName(ipv.getProtocolName());
                riev.setDeviceName(prr.getName());
                riev.setDeviceCode(prr.getDeviceCode());
                riev.setChildCompanyName(prr.getChildCompanyName());
                riev.setPointType(prr.getPointType());
                riev.setType(ipv.getType());
                riev.setDestAddressName(ipv.getDestAddressName());
                riev.setDestAddressNameResult((Validator.isNotEmpty(ipv.getDestAddressName()) && Validator.isNotEmpty(prr.getDestAddressName())) ? (ipv.getDestAddressName().equals(prr.getDestAddressName()) ? "√" : "X(" + prr.getDestAddressName() + ")") : "X(" + (Validator.isNotEmpty(prr.getDestAddressName()) ? prr.getDestAddressName() : "缺少点位") + ")");
                sb.append(riev.getDestAddressNameResult()).append(",");
//                riev.setDescription(ipv.getDescription());
//                riev.setDescriptionResult((Validator.isNotEmpty(ipv.getDescription()) && Validator.isNotEmpty(prr.getDescription())) ? (ipv.getDescription().equals(prr.getDescription()) ? "√" : "X(" + prr.getDescription() + ")") : "X(" + (Validator.isNotEmpty(prr.getDescription()) ? prr.getDescription() : "缺少点位") + ")");
//                sb.append(riev.getDescriptionResult()).append(",");

                riev.setMode(ipv.getMode());
                riev.setSavePolicy(ipv.getSavePolicy());
                riev.setDataRegistration(ipv.getDataRegistration());
                riev.setRemark(ipv.getRemark());
                riev.setCreateTime(date);

                riev.setDescription(ipv.getDescription());
                riev.setDataType(ipv.getDataType());
                riev.setAcquisitionFrequency(ipv.getAcquisitionFrequency());
                riev.setPointAddress(ipv.getPointAddress());
                riev.setEventMode(ipv.getEventMode());
                riev.setExpress(ipv.getExpress());


                if(field.size()==7) {
                    riev.setAcquisitionFrequency(ipv.getAcquisitionFrequency());
                    riev.setAcquisitionFrequencyResult((Validator.isNotEmpty(ipv.getAcquisitionFrequency()) && Validator.isNotEmpty(prr.getAcquisitionFrequency())) ? (ipv.getAcquisitionFrequency().equals(prr.getAcquisitionFrequency()) ? "√" : "X(" + prr.getAcquisitionFrequency() + ")") : "X(" + (Validator.isNotEmpty(prr.getAcquisitionFrequency()) ? prr.getAcquisitionFrequency() : "缺少点位") + ")");
                    riev.setDescription(ipv.getDescription());
                    riev.setDescriptionResult((Validator.isNotEmpty(ipv.getDescription()) && Validator.isNotEmpty(prr.getDescription())) ? (ipv.getDescription().equals(prr.getDescription()) ? "√" : "X(" + prr.getDescription() + ")") : "X(" + (Validator.isNotEmpty(prr.getDescription()) ? prr.getDescription() : "缺少点位") + ")");
                    sb.append(riev.getDescriptionResult()).append(",");

                    riev.setDataType(ipv.getDataType());
                    riev.setDataTypeResult((Validator.isNotEmpty(ipv.getDataType()) && Validator.isNotEmpty(prr.getType())) ? (ipv.getDataType().equals(prr.getType()) ? "√" : "X(" + prr.getType() + ")") : "X(" + (Validator.isNotEmpty(prr.getType()) ? prr.getType() : "缺少点位") + ")");
                    if (prr.getPointType().equals("采集点位")) {
                        if (Validator.isNotEmpty(ipv.getAcquisitionFrequency())) {
                        }
                        if (Validator.isNotEmpty(ipv.getPointAddress())) {
                            riev.setPointAddress(ipv.getPointAddress());
                            riev.setPointAddressResult((Validator.isNotEmpty(ipv.getPointAddress()) && Validator.isNotEmpty(prr.getPointAddress())) ? (ipv.getPointAddress().equals(prr.getPointAddress()) ? "√" : "X(" + prr.getPointAddress() + ")") : "X(" + (Validator.isNotEmpty(prr.getPointAddress()) ? prr.getPointAddress() : "缺少点位") + ")");
                        }
                        if (Validator.isEmpty(ipv.getAcquisitionFrequency()) && Validator.isNotEmpty(ipv.getPointAddress())) {
                            riev.setVerificationSumResult(("√".equals(riev.getDescriptionResult()) && "√".equals(riev.getDestAddressNameResult()) && "√".equals(riev.getDataTypeResult()) && "√".equals(riev.getPointAddressResult()) ? "通过" : "不通过"));
                        } else if (Validator.isNotEmpty(ipv.getAcquisitionFrequency()) && Validator.isEmpty(ipv.getPointAddress())) {
                            riev.setVerificationSumResult(("√".equals(riev.getDescriptionResult()) && "√".equals(riev.getDestAddressNameResult()) && "√".equals(riev.getDataTypeResult()) && "√".equals(riev.getAcquisitionFrequencyResult()) ? "通过" : "不通过"));
                        } else {
                            riev.setVerificationSumResult(("√".equals(riev.getDescriptionResult()) && "√".equals(riev.getDestAddressNameResult()) && "√".equals(riev.getDataTypeResult()) && "√".equals(riev.getAcquisitionFrequencyResult()) && "√".equals(riev.getPointAddressResult())) ? "通过" : "不通过");
                        }
                    }
                    if (pointType.equals("计算点位")) {
                        riev.setPointType(pointType);
                        if (ipv.getExpression().equals(prr.getEventCondition())) {
                            riev.setEventCondition(prr.getEventCondition());
                            riev.setEventMode(ipv.getEventMode());
                            riev.setEventModeResult((Validator.isNotEmpty(ipv.getEventMode()) && Validator.isNotEmpty(prr.getEventMode())) ? (ipv.getEventMode().equals(prr.getEventMode()) ? "√" : "X(" + prr.getEventMode() + ")") : "X(" + (Validator.isNotEmpty(prr.getEventMode()) ? prr.getEventMode() : "缺少点位") + ")");
                            riev.setExpress(ipv.getExpress());
                            riev.setExpressResult((Validator.isNotEmpty(ipv.getExpress()) && Validator.isNotEmpty(prr.getExpress())) ? (ipv.getExpress().equals(prr.getExpress()) ? "√" : "X(" + prr.getExpress() + ")") : "X(" + (Validator.isNotEmpty(prr.getExpress()) ? prr.getExpress() : "缺少点位") + ")");
                            riev.setVerificationSumResult(("√".equals(riev.getDescriptionResult()) && "√".equals(riev.getDestAddressNameResult()) && "√".equals(riev.getExpressResult()) && "√".equals(riev.getEventModeResult()) && "√".equals(riev.getAcquisitionFrequencyResult()) ? "通过" : "不通过"));//)
                        }else{
                            return;
                        }
                    }
                    if (pointType.equals("自定义点位")) {
                        riev.setVerificationSumResult(("√".equals(riev.getDescriptionResult()) && "√".equals(riev.getDestAddressNameResult()) && "√".equals(riev.getAcquisitionFrequencyResult()) ? "通过" : "不通过"));//&& "√".equals(riev.getExpressResult()) && "√".equals(riev.getEventModeResult()))
                    }
                }else{
                    for (String s : field) {
                        switch (s){
                            case "description":
                                riev.setDescriptionResult((Validator.isNotEmpty(ipv.getDescription()) && Validator.isNotEmpty(prr.getDescription())) ? (ipv.getDescription().equals(prr.getDescription()) ? "√" : "X(" + prr.getDescription() + ")") : "X(" + (Validator.isNotEmpty(prr.getDescription()) ? prr.getDescription() : "缺少点位") + ")");
                                sb.append(riev.getDescriptionResult()).append(",");
                                break;
                            case "dataType":
                                riev.setDataTypeResult((Validator.isNotEmpty(ipv.getDataType()) && Validator.isNotEmpty(prr.getType())) ? (ipv.getDataType().equals(prr.getType()) ? "√" : "X(" + prr.getType() + ")") : "X(" + (Validator.isNotEmpty(prr.getType()) ? prr.getType() : "缺少点位") + ")");
                                sb.append(riev.getDataTypeResult()).append(",");
                                break;
                            case "acquisitionFrequency":
                                riev.setAcquisitionFrequencyResult((Validator.isNotEmpty(ipv.getAcquisitionFrequency()) && Validator.isNotEmpty(prr.getAcquisitionFrequency())) ? (ipv.getAcquisitionFrequency().equals(prr.getAcquisitionFrequency()) ? "√" : "X(" + prr.getAcquisitionFrequency() + ")") : "X(" + (Validator.isNotEmpty(prr.getAcquisitionFrequency()) ? prr.getAcquisitionFrequency() : "缺少点位") + ")");
                                sb.append(riev.getAcquisitionFrequencyResult()).append(",");
                                break;
                            case "pointAddress":
                                if (pointType.equals("采集点位")) {
                                    riev.setPointAddressResult((Validator.isNotEmpty(ipv.getPointAddress()) && Validator.isNotEmpty(prr.getPointAddress())) ? (ipv.getPointAddress().equals(prr.getPointAddress()) ? "√" : "X(" + prr.getPointAddress() + ")") : "X(" + (Validator.isNotEmpty(prr.getPointAddress()) ? prr.getPointAddress() : "缺少点位") + ")");
                                    sb.append(riev.getPointAddressResult()).append(",");
                                }
                                break;
                            case "eventMode":
                                if (pointType.equals("计算点位")) {
                                    riev.setEventModeResult((Validator.isNotEmpty(ipv.getEventMode()) && Validator.isNotEmpty(prr.getEventMode())) ? (ipv.getEventMode().equals(prr.getEventMode()) ? "√" : "X(" + prr.getEventMode() + ")") : "X(" + (Validator.isNotEmpty(prr.getEventMode()) ? prr.getEventMode() : "缺少点位") + ")");
                                    sb.append(riev.getEventModeResult()).append(",");
                                    break;
                                }
                            case "express":
                                if (pointType.equals("计算点位")) {
                                    riev.setExpressResult((Validator.isNotEmpty(ipv.getExpress()) && Validator.isNotEmpty(prr.getExpress())) ? (ipv.getExpress().equals(prr.getExpress()) ? "√" : "X(" + prr.getExpress() + ")") : "X(" + (Validator.isNotEmpty(prr.getExpress()) ? prr.getExpress() : "缺少点位") + ")");
                                    sb.append(riev.getExpressResult()).append(",");
                                    break;
                                }
                        }
                    }
                    if (sb.toString().contains("X")){
                        riev.setVerificationSumResult("不通过");
                    }else{
                        riev.setVerificationSumResult("通过");
                    }
                }
                list.add(riev);
            });
    }

    private static void dataProcessings(Date date,Map<String,RcIotEquipmentVerificationResults> map, List<RcIotEquipmentVerificationResults> list, RcIotPointVerification ipv,PointResultListResp prr) {
        if (Objects.nonNull(map.get(prr.getSecondProcess()+prr.getDeviceCode()+ipv.getDestAddressName()))){
            return;
        }
            RcIotEquipmentVerificationResults riev = new RcIotEquipmentVerificationResults();
            riev.setRegion(prr.getRegion());
            riev.setSecondProcess(prr.getSecondProcess());
            riev.setProtocolName(ipv.getProtocolName());
            riev.setDeviceName(prr.getName());
            riev.setDeviceCode(prr.getDeviceCode());
            riev.setChildCompanyName(prr.getChildCompanyName());
            riev.setPointType(ipv.getPointType());
            riev.setType(ipv.getType());
            riev.setDestAddressName(ipv.getDestAddressName());
            riev.setDestAddressNameResult("X(缺少点位)");
            riev.setDescription(ipv.getDescription());
            riev.setDescriptionResult("X(缺少点位)");
            riev.setDataType(ipv.getDataType());
            riev.setDataTypeResult("X(缺少点位)");
//            riev.setEventCondition("-");
            riev.setMode(ipv.getMode());
            riev.setSavePolicy(ipv.getSavePolicy());
            riev.setDataRegistration(ipv.getDataRegistration());
            riev.setRemark(ipv.getRemark());
            riev.setCreateTime(date);
            riev.setAcquisitionFrequency(ipv.getAcquisitionFrequency());
            riev.setAcquisitionFrequencyResult("X(缺少点位)");
            riev.setPointAddress(ipv.getPointAddress());
            riev.setPointAddressResult("X(缺少点位)");
            riev.setVerificationSumResult("不通过");
            riev.setEventMode(ipv.getEventMode());
            riev.setEventModeResult("X(缺少点位)");
            riev.setExpress(ipv.getExpress());
            riev.setEventCondition(ipv.getExpression());
            riev.setExpressResult("X(缺少点位)");
        map.put(prr.getSecondProcess()+prr.getDeviceCode()+ipv.getPointType()+ipv.getDestAddressName()+ipv.getEventMode()+ipv.getExpress()+ipv.getExpression(),riev);
    }
}