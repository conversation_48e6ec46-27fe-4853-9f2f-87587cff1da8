package com.rc.admin.easyapi.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.model.req.IotDeviceInfoReq;
import com.rc.admin.easyapi.model.req.PointResultListReq;
import com.rc.admin.easyapi.model.resp.IotDeviceInfoResp;
import com.rc.admin.easyapi.model.resp.PointResultListResp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *     设备服务类.
 * </p>
 * <AUTHOR>
 * @since 2023-07-24
 */
public interface IotDeviceInfoService {
    /**
     * iot数据采集规则分页查询
     * @param req 入参
     * @return 分页结果
     */
    Page<IotDeviceInfoResp> selectPages(IotDeviceInfoReq req);

    /**
     * 获取设备列表
     * @param deviceCode 设备编号
     * @return 列表
     */
    List<Map<String,String>> getDeviceCodeList(String deviceCode);

    /**
     * iot设备点位分页查询
     * @param dto 入参
     * @return 分页结果
     */
    Page<PointResultListResp> getPointList(PointResultListReq dto);

    /**
     * iot点位的查询子公司列表
     * @return 子公司列表
     */
    List<String>  getChildCompanyNameList();
    String pagePointDataExport(PointResultListReq req);
}
