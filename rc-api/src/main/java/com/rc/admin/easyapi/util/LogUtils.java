package com.rc.admin.easyapi.util;


import org.slf4j.LoggerFactory;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;

//import java.util.logging.Level;
//import java.util.logging.Logger;

import ch.qos.logback.classic.Level;

public class LogUtils {

    public static void LoggerOpen()
    {
        setLoggerLevel("org.apache.ibatis", "INFO"); // 或者其他默认级别
        setLoggerLevel("com.alibaba.druid.proxy.jdbc", "INFO"); // 或者其他默认级别
    }

    public static void LoggerOff()
    {
//        Logger.getLogger("org.apache.ibatis").setLevel(Level.OFF);
//        Logger.getLogger("com.alibaba.druid.proxy.jdbc").setLevel(Level.OFF);

        setLoggerLevel("org.apache.ibatis", "OFF"); // 或者其他默认级别
        setLoggerLevel("com.alibaba.druid.proxy.jdbc", "OFF"); // 或者其他默认级别

        ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger("ROOT");
        ch.qos.logback.classic.Logger logger2 = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger("ROOT");
        logger.setLevel(Level.OFF);
        logger2.setLevel(Level.OFF);
    }

    public static void setLoggerLevel(String loggerName, String level) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = loggerContext.getLogger(loggerName);
        logger.setLevel(ch.qos.logback.classic.Level.toLevel(level));
    }

    public static void disableLogger(String loggerName) {
        setLoggerLevel(loggerName, "OFF");
    }

    public static void enableLogger(String loggerName) {
        setLoggerLevel(loggerName, "INFO"); // 或者其他默认级别
    }
}
