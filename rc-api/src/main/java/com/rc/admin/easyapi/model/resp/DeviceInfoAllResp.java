package com.rc.admin.easyapi.model.resp;

import com.rc.admin.easyapi.entity.IotCalculatingData;
import com.rc.admin.easyapi.entity.IotCollectionPoint;
import com.rc.admin.easyapi.entity.IotCustomizePoint;
import com.rc.admin.easyapi.entity.IotDeviceInfo;
import lombok.Data;

import java.util.List;


@Data
public class DeviceInfoAllResp {
    private IotDeviceInfo info;
    private List<IotCollectionPoint> iotCollectionPoints;
    private List<IotCustomizePoint> iotCustomizePoints;
    private List<IotCalculatingData> iotCalculatingData;

    public DeviceInfoAllResp() {
    }

    public DeviceInfoAllResp(IotDeviceInfo info, List<IotCollectionPoint> iotCollectionPoints, List<IotCustomizePoint> iotCustomizePoints, List<IotCalculatingData> iotCalculatingData) {
        this.info = info;
        this.iotCollectionPoints = iotCollectionPoints;
        this.iotCustomizePoints = iotCustomizePoints;
        this.iotCalculatingData = iotCalculatingData;
    }
}
