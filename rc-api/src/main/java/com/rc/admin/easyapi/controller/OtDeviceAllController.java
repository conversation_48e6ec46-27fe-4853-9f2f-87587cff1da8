package com.rc.admin.easyapi.controller;


import com.rc.admin.common.core.util.Response;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.core.annotation.SysLog;
import com.rc.admin.ors.quality.dao.OtDeviceAllMapper;
import com.rc.admin.ors.quality.model.OtDeviceAllReq;
import com.rc.admin.ors.quality.model.OtDeviceAllResp;
import com.rc.admin.ors.quality.model.OtDevicePage;
import com.rc.admin.ors.quality.service.OtDeviceAllService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Slf4j
@RestController
@ResponseResult
@RequestMapping("/api/v1/ot/device")
public class OtDeviceAllController {

    @Resource
    private OtDeviceAllService otDeviceAllService;

    @Resource
    private OtDeviceAllMapper otDeviceAllMapper;


    @ApiOperation(value = "全量设备台账查询")
    @PostMapping("/all")
    @SysLog(modular = "application-interfaces", method = "全量设备台账查询")
    public Response deviceAllPage(@RequestBody OtDeviceAllReq req) {
        try {
            if(StringUtils.isNotBlank(req.getSyb())) {
                boolean contains = Arrays.asList(req.getSyb().split(",")).contains("1");
                if (contains) {
                    req.setSybCheck("1");
                }
            }

            if(StringUtils.isNotBlank(req.getBuInnerDesc())) {
                boolean contains = Arrays.asList(req.getBuInnerDesc().split(",")).contains("1");
                if (contains) {
                    req.setSybCheck("1");
                }
            }

            if(StringUtils.isNotBlank(req.getD365Spart())) {
                boolean contains = Arrays.asList(req.getD365Spart().split(",")).contains("1");
                if (contains) {
                    req.setD365SpartCheck("1");
                }
            }
            if(StringUtils.isNotBlank(req.getD365SpartDesc())) {
                boolean contains = Arrays.asList(req.getD365SpartDesc().split(",")).contains("1");
                if (contains) {
                    req.setD365SpartCheck("1");
                }
            }
            OtDevicePage<OtDeviceAllResp> otDeviceAllRespOtDevicePage
                    = otDeviceAllMapper.selectPageList(new OtDevicePage<>(req.getCurrent(), req.getSize()), req);
            otDeviceAllRespOtDevicePage.setDeviceCount(otDeviceAllMapper.getDeviceCount(req)+"");
            return Response.success(otDeviceAllRespOtDevicePage);
        }catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }


    @ApiOperation(value = "全量设备台账查询导出")
    @PostMapping("/allExport")
    public void deviceAllPageExport(HttpServletResponse response, @RequestBody OtDeviceAllReq req) {
        if(StringUtils.isNotBlank(req.getSyb())) {
            boolean contains = Arrays.asList(req.getSyb().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getBuInnerDesc())) {
            boolean contains = Arrays.asList(req.getBuInnerDesc().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getD365Spart())) {
            boolean contains = Arrays.asList(req.getD365Spart().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        if(StringUtils.isNotBlank(req.getD365SpartDesc())) {
            boolean contains = Arrays.asList(req.getD365SpartDesc().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        otDeviceAllService.exportExcelOtDeviceAll(response,req);
    }



    @ApiOperation(value = "全量设备台账-报表查询")
    @PostMapping("/report")
    public Response deviceAllReport(@RequestBody OtDeviceAllReq req) {
        if(StringUtils.isNotBlank(req.getSyb())) {
            boolean contains = Arrays.asList(req.getSyb().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getBuInnerDesc())) {
            boolean contains = Arrays.asList(req.getBuInnerDesc().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getD365Spart())) {
            boolean contains = Arrays.asList(req.getD365Spart().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        if(StringUtils.isNotBlank(req.getD365SpartDesc())) {
            boolean contains = Arrays.asList(req.getD365SpartDesc().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        try {
            return Response.success(otDeviceAllMapper.selectPageReportList(req));
        }catch (Exception e) {
            return Response.failError(e.getMessage());
        }
    }


    @ApiOperation(value = "全量设备台账-报表查询导出")
    @PostMapping("/reportExport")
    public void deviceAllReportExport(HttpServletResponse response,@RequestBody OtDeviceAllReq req) {
        if(StringUtils.isNotBlank(req.getSyb())) {
            boolean contains = Arrays.asList(req.getSyb().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getBuInnerDesc())) {
            boolean contains = Arrays.asList(req.getBuInnerDesc().split(",")).contains("1");
            if (contains) {
                req.setSybCheck("1");
            }
        }

        if(StringUtils.isNotBlank(req.getD365Spart())) {
            boolean contains = Arrays.asList(req.getD365Spart().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        if(StringUtils.isNotBlank(req.getD365SpartDesc())) {
            boolean contains = Arrays.asList(req.getD365SpartDesc().split(",")).contains("1");
            if (contains) {
                req.setD365SpartCheck("1");
            }
        }
        otDeviceAllService.exportExcelOtDeviceAllReport(response,req);
    }







}
