package com.rc.admin.easyapi.controller;

import cn.hutool.core.lang.Validator;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.easyapi.entity.RcIotEquipmentVerificationResults;
import com.rc.admin.easyapi.service.RcIotEquipmentVerificationResultsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.common.core.common.pagination.Page;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备数据核验结果表
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@ResponseResult
@RequestMapping("/api/rc/iot/equipment/verification/results")
public class RcIotEquipmentVerificationResultsController {

    @Autowired
    public HttpServletResponse httpServletResponse;
    /**
     * 设备数据核验结果表 service
     */
    @Autowired
    private RcIotEquipmentVerificationResultsService service;

    /**
     * 列表
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     * @param page 分页
     * @return Page<RcIotEquipmentVerificationResults>
     */
    @GetMapping()
    public Page<RcIotEquipmentVerificationResults> select(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults, Page<RcIotEquipmentVerificationResults> page){
        return service.select(rcIotEquipmentVerificationResults, page);
    }

    /**
     * 详情
     *
     * @param id id
     * @return RcIotEquipmentVerificationResults
     */
    @GetMapping("{id}")
    public RcIotEquipmentVerificationResults get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return RcIotEquipmentVerificationResults
     */
    @GetMapping("add")
    public RcIotEquipmentVerificationResults add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param rcIotEquipmentVerificationResults 表单内容
     * @return RcIotEquipmentVerificationResults
     */
    @PostMapping()
    public RcIotEquipmentVerificationResults saveData(@Valid @RequestBody RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults){
        return service.saveData(rcIotEquipmentVerificationResults);
    }
    /**
     * 导出数据
     *
     * @param rcIotEquipmentVerificationResults 查询条件
     * @return 执行结果
     */
    @GetMapping("export/data")
    public Response exportData(RcIotEquipmentVerificationResults rcIotEquipmentVerificationResults){
         service.exportData( httpServletResponse,rcIotEquipmentVerificationResults);
        return Response.success();
    }
    /**
     * 点位核验
     * @return 执行结果
     */
    @GetMapping("point/verification")
    public Response pointVerification(@RequestParam(value = "list", required = false)String list,@RequestParam(value = "field", required = false)String field){
        List<String> data = new ArrayList<>();
        if (Validator.isNotEmpty(list)){
            String[] split = list.split(",");
            data = Arrays.asList(split);
        }
        List<String> fields = new ArrayList<>();
        if (Validator.isNotEmpty(field)){
            String[] split = field.split(",");
            fields = Arrays.asList(split);
        }
        String  result = service.pointVerification(data,fields);

        return result != null ? Response.failError("500",result) : Response.success();
    }
    /**
     * 点位核对 二级工艺列表
     * @return 执行结果
     */
    @GetMapping("getSecondProcess")
    public Response getSecondProcess(){
        return Response.success(service.getSecondProcess());
    }
}
