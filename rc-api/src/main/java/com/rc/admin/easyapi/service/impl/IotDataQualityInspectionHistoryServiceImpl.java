package com.rc.admin.easyapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.dao.IotDataQualityInspectionHistoryMapper;
import com.rc.admin.easyapi.entity.IotDataQualityInspectionHistory;
import com.rc.admin.easyapi.service.IotDataQualityInspectionHistoryService;
import com.rc.admin.easyapi.util.DateUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@Log4j2
public class IotDataQualityInspectionHistoryServiceImpl implements IotDataQualityInspectionHistoryService {

    @Resource
    IotDataQualityInspectionHistoryMapper historyMapper;
    @Override
    public Page<IotDataQualityInspectionHistory> selectHistoryData(String deviceCode,int pageNo,int pageSize) {
        Page<IotDataQualityInspectionHistory> historyPage = new Page<>();
        historyPage.setCurrent(pageNo);
        historyPage.setPageSize(pageSize);

        QueryWrapper<IotDataQualityInspectionHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_code",deviceCode);
        queryWrapper.gt("create_time", DateUtil.formatDate(new Date(),"yyyy-MM-dd")+ " 00:00:01");
        queryWrapper.lt("create_time",DateUtil.formatDate(new Date(),"yyyy-MM-dd") + " 23:59:59");
        queryWrapper.orderByDesc("create_time");
        int page = (pageNo - 1) * pageSize;
        long aLong = historyMapper.selectCount(queryWrapper);
        if (aLong>0){
            queryWrapper.last("limit "+page+","+pageSize);
            historyPage.setRecords(historyMapper.selectList(queryWrapper));
        }
        historyPage.setTotal(aLong);

        return historyPage;
    }

    @Override
    public List<IotDataQualityInspectionHistory> pageHistoryData( String startTime, String endTime, int pageNo, int size) {
        QueryWrapper<IotDataQualityInspectionHistory> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)){
            queryWrapper.gt("create_time", DateUtil.formatDate(new Date(),"yyyy-MM-dd")+ " 00:00:01");
            queryWrapper.lt("create_time",DateUtil.formatDate(new Date(),"yyyy-MM-dd") + " 23:59:59");
        }else{
            queryWrapper.gt("create_time", startTime);
            queryWrapper.lt("create_time",endTime);
        }
        queryWrapper.orderByDesc("create_time");
        int page = (pageNo - 1) * size;
        queryWrapper.last("limit "+page+","+size);
        return historyMapper.selectList(queryWrapper);
    }

    /**
     *
     * @param iotDataQualityInspectionHistory 入参
     * @return 历史工况信息
     */
    @Override
    public IotDataQualityInspectionHistory selectByCondition(IotDataQualityInspectionHistory iotDataQualityInspectionHistory) {
        QueryWrapper<IotDataQualityInspectionHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_code",iotDataQualityInspectionHistory.getDeviceCode());
        queryWrapper.eq("dest_address_name",iotDataQualityInspectionHistory.getDestAddressName());
        queryWrapper.eq("rule_subject",iotDataQualityInspectionHistory.getRuleSubject());
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        String createTime = sf.format(iotDataQualityInspectionHistory.getCreateTime());
        queryWrapper.like("create_time", createTime);
        queryWrapper.eq("device_code",iotDataQualityInspectionHistory.getDeviceCode());
        return historyMapper.selectOne(queryWrapper);
    }
}
