package com.rc.admin.easyapi.job;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.easyapi.model.req.SubscriptionReq;
import com.rc.admin.easyapi.model.resp.SubscriptionResp;
import com.rc.admin.easyapi.service.SubscriptionService;
import com.rc.admin.easyapi.service.dqmService.OrsBigdataEquipmentBaseInfoAllService;
import com.rc.admin.easyapi.util.MailUtils;
import com.rc.admin.ors.quality.dao.OrsBaseDeviceInfoMapper;
import com.rc.admin.ors.quality.excel.DeviceExceptInfoCountExcel;
import com.rc.admin.ors.quality.model.*;
import com.rc.admin.ors.quality.service.OrsDeviceLedgerService;
import com.rc.admin.ors.quality.utils.BusinessConst;
import com.rc.admin.util.FeishuUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Log4j2
public class SubscriptionJob {

    @Resource
    private SubscriptionService subscriptionService;
    @Resource
    private OrsBigdataEquipmentBaseInfoAllService infoAllService;
    @Resource
    private FeishuUtil feishuUtil;
    @Resource
    private MailUtils mailUtils;
    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;
    @Resource
    private OrsDeviceLedgerService orsDeviceLedgerService;
    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;
    public void subscriptionSendAll() {
        // 获取上个月的年月
        try {
            log.info("开始执行订阅任务");

            Page<SubscriptionResp> page = subscriptionService.getSubscription(new SubscriptionReq(0, 9999));
            //按事业部分组
            List<SubscriptionResp> collect1 = page.getRecords().stream().filter(subscriptionResp -> subscriptionResp.getIsEnabled() == 1).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect1)) {
                log.info("无接收人员结束任务");
                return;
            }
            log.info("开始获取数据");
            DeviceQuelityCountQuery deviceQuelityCountQuery = new DeviceQuelityCountQuery();
            deviceQuelityCountQuery.setSwitchSign("1");
            deviceQuelityCountQuery.setIotBoxType("All");
            //上个月

            YearMonth previousYearMonth = YearMonth.now().minusMonths(1);
            //当月数据
            log.info("开始获取当月概率数据");
            List<DeviceRatioReportEntryResp> sameMonthDivisionStatistics = sameMonthDivisionStatistics(previousYearMonth,deviceQuelityCountQuery);
            //取上上个月
            log.info("开始获取上月概率数据");
            List<DeviceRatioReportEntryResp> lastMonthDivisionStatistics = sameMonthDivisionStatistics(previousYearMonth.minusMonths(1),deviceQuelityCountQuery);
            //分组当月
            log.info("开始分组当月概率数据");
            Map<String, DeviceRatioReportEntryResp> currentMonthRates = sameMonthDivisionStatistics.stream().collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, entry -> entry));
            //分组上月
            log.info("开始分组上月概率数据");
            Map<String, DeviceRatioReportEntryResp> lastMonthRates = lastMonthDivisionStatistics.stream().collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, entry -> entry));

            //较上月
            log.info("开始计算当月与上月占比数据");
            Map<String, Map<String,Double>> comparisonMap = compareRates(currentMonthRates, lastMonthRates);

            //历史异常统计导出
            //总值
            DecimalFormat df = new DecimalFormat("#.00");
            log.info("开始执行人员循环逻辑");
            for (SubscriptionResp subscription : collect1) {
                String month = previousYearMonth.toString();
                String nickname = subscription.getNickname();
                StringBuilder emailContent = new StringBuilder();
                StringBuilder emailContentEmail = new StringBuilder();
                double checkNullAbnormalRateSum = 0;
                double checkParamAbnormalRateSum = 0;
                int checkNullAbnormalCount = 0;
                int checkParamAbnormalCount = 0;
                log.info("开始构建发送数据");
                for (String sybbh : subscription.getSubscribedDepartments().split(",")) {

                    if (sybbh.contains("rootcloud") || sybbh.contains("树根互联") || sybbh.contains("三一集团")) {
                        continue;
                    }
                    if (Objects.nonNull(comparisonMap.get(sybbh)) && Objects.nonNull(lastMonthRates.get(sybbh)) ) {
                        Map<String, Double> dbavg = comparisonMap.get(sybbh);
                        DeviceRatioReportEntryResp currentMonthRatioReportEntryResp = currentMonthRates.get(sybbh);
                        DeviceRatioReportEntryResp lastMonthRatioReportEntryResp = lastMonthRates.get(sybbh);
                        //平均完整
                        double avgCompleteness = Double.parseDouble(currentMonthRatioReportEntryResp.getIntegrityRate());
                        if (avgCompleteness > 0) {
                            checkNullAbnormalCount++;
                        }
                        checkNullAbnormalRateSum += avgCompleteness;
                        double completenessChange =  dbavg.get("integrityRateChange");
                        //平均准确
                        double avgAccuracy = Double.parseDouble(currentMonthRatioReportEntryResp.getAccuracy());
                        if (avgAccuracy > 0) {
                            checkParamAbnormalCount++;
                        }
                        checkParamAbnormalRateSum += avgAccuracy;
                        double accuracyChange =  dbavg.get("accuracyChange");
                                emailContent.append(String.format(
                                "%s事业部当月的数据质量情况如下:平均完整率为%.2f%%，较上月%s%.2f%%;平均准确率为%.2f%%，较上月%s%.2f%%\n",
                                sybbh,
                                avgCompleteness,
                                completenessChange >= 0 ? "提升" : "下降",
                                Math.abs(completenessChange),
                                avgAccuracy,
                                accuracyChange >= 0 ? "提升" : "下降",
                                Math.abs(accuracyChange)
                        ));
                        emailContentEmail.append(String.format(
                                "<p>%s事业部当月的数据质量情况如下:平均完整率为%.2f%%，较上月%s%.2f%%;平均准确率为%.2f%%，较上月%s%.2f%%</p>\n",
                                sybbh,
                                avgCompleteness,
                                completenessChange >= 0 ? "提升" : "下降",
                                Math.abs(completenessChange),
                                avgAccuracy,
                                accuracyChange >= 0 ? "提升" : "下降",
                                Math.abs(accuracyChange)
                        ));
                    }
                }
                if (checkNullAbnormalCount > 0) {
                    checkNullAbnormalRateSum = checkNullAbnormalRateSum / checkNullAbnormalCount;
                }
                if (checkParamAbnormalCount > 0) {
                    checkParamAbnormalRateSum = checkParamAbnormalRateSum / checkParamAbnormalCount;

                }

                //消息处理
                Map<String, String> msg = getMsgFeiShu(nickname,
                        month, emailContent.toString(),
                        checkNullAbnormalRateSum,
                        checkParamAbnormalRateSum);

                if (subscription.getNotificationChannels().contains("飞书")) {
                    //发送飞书
                    Map<String, Object> json = new HashMap<>();
                    String feishuToken = feishuUtil.getFeishuToken();
                    if (StringUtils.isEmpty(feishuToken)) {
                        log.info("获取飞书token失败请检查配置！！！");
                    } else {
                        json.put("content", msg);
                        json.put("uuid", UUID.randomUUID());
                        json.put("msg_type", "text");
                        json.put("user_ids", (Object) subscriptionSend(feishuToken, subscription.getPhone()));
                        String result = feishuUtil.doPostFeishuSend(feishuToken, json);
                        log.info("发送内容:" + msg);
                        if (result != null && JSONObject.parseObject(result).get("code").equals(0)) {
                            log.info("发送结果:" + result);
                        } else {
                            log.info("推送飞书失败：" + result);
                        }
                    }
                }
                if (subscription.getNotificationChannels().contains("邮件")) {
                    List<byte[]> byteList = new ArrayList<>();
                    List<String> fileNames = new ArrayList<>();
                    List<String> divisionCodeList = subscriptionService.getDivisionCodeList(Arrays.asList(subscription.getSubscribedDepartments().split(",")));
                    ByteArrayOutputStream byteArrayOutputStream = generateExcel(previousYearMonth,divisionCodeList );
                    if (byteArrayOutputStream !=null ) {
                        byteList.add(byteArrayOutputStream.toByteArray());
                        fileNames.add(month + "月的海外设备数据质量月报.xlsx");
                    }
                    ByteArrayOutputStream exportOfflineDevicesToPath = orsDeviceLedgerService.exportOfflineDevicesToPath( String.join(",", divisionCodeList));
                    if(exportOfflineDevicesToPath!=null && exportOfflineDevicesToPath.size()>0){
                        byteList.add(exportOfflineDevicesToPath.toByteArray());
                        fileNames.add( "长期离线设备清单.xlsx");
                    }
                    ByteArrayOutputStream exportNotActiveDevicesToPath = orsDeviceLedgerService.exportNotActiveDevicesToPath( String.join(",", divisionCodeList));
                    if(exportOfflineDevicesToPath!=null && exportOfflineDevicesToPath.size()>0){
                        byteList.add(exportNotActiveDevicesToPath.toByteArray());
                        fileNames.add("长期未激活设备清单.xlsx");
                    }
                    //消息处理
                    Map<String, String> msgEmail = getMsgEmail(nickname,
                            month, emailContentEmail.toString(),
                            checkNullAbnormalRateSum,
                            checkParamAbnormalRateSum);
                    log.info("邮件发送内容:" + msgEmail.get("text"));
                    //发送邮件
                        mailUtils.postMessageWithFile(month + "月的海外设备数据质量月报", msgEmail.get("text"), fileNames, byteList, subscription.getEmail());
                }
            }
        } catch (NumberFormatException e) {
            log.error("海外数据质量月报subscriptionSendAll异常:{}", e);
        }
    }

    private ByteArrayOutputStream  generateExcel(YearMonth previousYearMonth, List<String> sybbhList) {
        DqmHistoryDeviceDataExceptionsReq dataExceptionsReq = new DqmHistoryDeviceDataExceptionsReq();
        String previousMonthFirstDay = formatDate(previousYearMonth.atDay(1),LocalTime.MAX);
        String previousMonthLastDay = formatDate(previousYearMonth.atEndOfMonth(),LocalTime.MIN);
        dataExceptionsReq.setStartTime(previousMonthFirstDay);
        dataExceptionsReq.setEndTime(previousMonthLastDay);
        dataExceptionsReq.setHasHuaXin("1");
        dataExceptionsReq.setRow("1");
        dataExceptionsReq.setPageSize(1);
        dataExceptionsReq.setCurrent(1);
        dataExceptionsReq.setSybbh(sybbhList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        Page<DqmHistoryDeviceDataExceptionResp> exceptions = infoAllService.getHistoryDeviceDataExceptionsInfo1(dataExceptionsReq);
            int totalCount = (int) exceptions.getTotal();
            log.info("历史异常统计总记录数：{}", totalCount);

            if (totalCount == 0) {
               return null;
            }
        //生成文件
           return getExcelStream(dataExceptionsReq,totalCount);
    }


    public List<String> subscriptionSend(String token,String phone){
        List<String> userId = new ArrayList<>();
        Map<String,Object> map= new HashMap<>();
        map.put("mobiles",Collections.singletonList(phone));
        map.put("user_id_type","user_id");
        map.put("include_resigned",false);
        if (StringUtils.isEmpty(token)) {
            log.info("获取飞书token失败请检查配置！！！");
        }else {
            userId.addAll( feishuUtil.getHttpOpenId(token, map));
        }
        return userId;
    }


    private static  Map<String,String> getMsgFeiShu(String nickname,
                                 String month,
                                 String content,
                                 double checkNullAbnormalRateSum,
                                 double checkParamAbnormalRateSum) {
        Map<String,String> map = new HashMap<>();
        String card = "[姓名]，您好!\n" +
                "[月份]月的海外设备数据质量月报已经生成\n" +
                content +
                "当月所有事业部整体的数据质量情况:平均完整率为[当月平均完整率]%，平均准确率为[当月平均准确率]%。\n" +
                "具体详情请点击链接进入工具页面进行查看:http://dqm-newc.irootech.com/#/dashboard" ;
        String replace = null;
        try {
            replace = card.replace("[姓名]", nickname)
                    .replace("[月份]", month)
                    .replace("[当月平均完整率]",checkNullAbnormalRateSum>0 ?  String.format("%.2f", checkNullAbnormalRateSum): checkNullAbnormalRateSum+"")
                    .replace("[当月平均准确率]", checkParamAbnormalRateSum >0 ?  String.format("%.2f", checkParamAbnormalRateSum) : checkParamAbnormalRateSum+"")
                    .replace("[标题]", "海外设备数据质量月报");
            map.put("text",replace);

        } catch (Exception e) {
            log.error("消息处理异常,{}",e);
        }
        //log.info("消息处理:"+replace);
        return map;
    }
    private static  Map<String,String> getMsgEmail(String nickname,
                                 String month,
                                 String content,
                                 double checkNullAbnormalRateSum,
                                 double checkParamAbnormalRateSum) {
        Map<String,String> map = new HashMap<>();
        String card = "<html><body><p>[姓名]，您好!</p>\n" +
                "<p>[月份]月的海外设备数据质量月报已经生成</p>\n" +
                content +
                "<p>当月所有事业部整体的数据质量情况:平均完整率为[当月平均完整率]%，平均准确率为[当月平均准确率]%。</p>\n" +
                "<p>具体详情请点击链接进入工具页面进行查看:<a href='http://dqm-newc.irootech.com/#/dashboard'>http://dqm-newc.irootech.com/#/dashboard</a></p></body></html>" ;
        String replace = null;
        try {
            replace = card.replace("[姓名]", nickname)
                    .replace("[月份]", month)
                    .replace("[当月平均完整率]",checkNullAbnormalRateSum>0 ?  String.format("%.2f", checkNullAbnormalRateSum): checkNullAbnormalRateSum+"")
                    .replace("[当月平均准确率]", checkParamAbnormalRateSum >0 ?  String.format("%.2f", checkParamAbnormalRateSum) : checkParamAbnormalRateSum+"")
                    .replace("[标题]", "海外设备数据质量月报");
            map.put("text",replace);

        } catch (Exception e) {
            log.error("消息处理异常,{}",e);
        }
       // log.info("消息处理:"+replace);
        return map;
    }

    public ByteArrayOutputStream getExcelStream(DqmHistoryDeviceDataExceptionsReq dataExceptionsReq,int totalCount) {
        // 设置文件名
        String fileName;
        try {
            fileName = URLEncoder.encode("历史异常统计", StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            throw new RuntimeException("文件名编码失败", e);
        }

        try  {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 每个Sheet最大行数
            int sheetDataRows = 1000000;
            // 每次写入的最大行数
            int writeDataRows = 100000;

            // 计算总Sheet数
            int sheetNum = (totalCount + sheetDataRows - 1) / sheetDataRows;
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // 创建一个 CompletableFuture 数组来存储每个任务的 Future 对象
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            try {
                for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
                    // 当前Sheet的数据量
                    int currentSheetSize = (sheetIndex == sheetNum - 1)
                            ? (totalCount % sheetDataRows == 0 ? sheetDataRows : totalCount % sheetDataRows)
                            : sheetDataRows;
                    // 当前Sheet需要写入的次数
                    int writeCount = (currentSheetSize + writeDataRows - 1) / writeDataRows;

                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, "历史异常统计" + (sheetIndex + 1))
                            .head(DeviceExceptInfoCountExcel.class)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        int finalSheetIndex = sheetIndex;
                        int finalWriteIndex = writeIndex;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            long writeIndexTime = System.currentTimeMillis();
                            // 计算全局分页参数
                            int currentPage = finalSheetIndex * (sheetDataRows / writeDataRows) + finalWriteIndex + 1;

                            dataExceptionsReq.setPageSize(writeDataRows);
                            dataExceptionsReq.setCurrent(currentPage);
                            List<DqmHistoryDeviceDataExceptionResp> data =
                                    infoAllService.getHistoryDeviceDataExceptionsInfo1(dataExceptionsReq).getRecords();
                            List<DeviceExceptInfoCountExcel> countExcels = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(data)) {
                                // 构建导出数据
                                buildDeviceExceptInfoCountExcelData(data, countExcels);

                                log.info("第{}个Sheet第{}次,第{}页查询耗时：{}", finalSheetIndex, finalWriteIndex, currentPage, System.currentTimeMillis() - writeIndexTime);
                                synchronized (excelWriter) {
                                    excelWriter.write(countExcels, writeSheet);
                                }
                            }
                        }, asyncExecutor);
                        futureList.add(future);
                    }
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
            return outputStream;
        } catch (Exception e) {
            return null;
        }

    }


    private void updateReportNum(List<DeviceRatioReportEntryResp> regionStatistics, List<DeviceRatioReportEntryResp> regionCountStatistics) {
        // 创建一个映射，键是title，值是对应的DeviceRatioReportEntryResp对象
        Map<String, DeviceRatioReportEntryResp> countMap = regionCountStatistics.stream()
                .collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, entry -> entry));

        // 更新regionStatistics中具有相同title的元素的reportNum
        for (DeviceRatioReportEntryResp entry : regionStatistics) {
            if (countMap.containsKey(entry.getTitle())) {
                entry.setReportNum(countMap.get(entry.getTitle()).getReportNum());
                entry.setAbnormalParamNum(countMap.get(entry.getTitle()).getAbnormalParamNum());
                entry.setNullParamNum(countMap.get(entry.getTitle()).getNullParamNum());
            }
        }
    }

    public static String formatDate(LocalDate previousMonthFirstDay ,LocalTime min) {
        try {
            // 将 LocalDate 转换为 LocalDateTime，并设置时间为 23:59:59
            LocalDateTime localDateTime = previousMonthFirstDay.atTime(min);

            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 格式化 LocalDateTime 为字符串
            String formattedDateTime = localDateTime.format(formatter);
            return formattedDateTime;
        } catch (DateTimeException e) {
            // 处理异常，例如记录日志或返回默认值
            System.err.println("日期格式化失败: " + e.getMessage());
            return null;
        }
    }
    public  List<DeviceRatioReportEntryResp>  sameMonthDivisionStatistics( YearMonth previousYearMonth,DeviceQuelityCountQuery deviceQuelityCountQuery) {
        String previousMonthFirstDay = formatDate(previousYearMonth.atDay(1),LocalTime.MAX);
        String previousMonthLastDay = formatDate(previousYearMonth.atEndOfMonth(),LocalTime.MIN);
        deviceQuelityCountQuery.setStartTime(previousMonthFirstDay);
        deviceQuelityCountQuery.setEndTime(previousMonthLastDay);
        List<DeviceRatioReportEntryResp> divisionStatistics = orsBaseDeviceInfoMapper.divisionStatistics(deviceQuelityCountQuery);
        List<DeviceRatioReportEntryResp> divisionCountStatistics = orsBaseDeviceInfoMapper.divisionStatistics(deviceQuelityCountQuery);
        divisionStatistics = divisionStatistics.stream().filter(entry -> entry.getTitle() != null).collect(Collectors.toList());
        updateReportNum(divisionStatistics, divisionCountStatistics);
        return divisionStatistics;
    }
    Map<String, Map<String,Double>> compareRates(Map<String, DeviceRatioReportEntryResp> currentMonthRates, Map<String, DeviceRatioReportEntryResp> lastMonthRates) {
        Map<String, Map<String,Double>> comparisonMap = new HashMap<>();
        for (String title : currentMonthRates.keySet()) {
            Map<String,Double> map= new HashMap<>();
            DeviceRatioReportEntryResp currentMonthEntry = currentMonthRates.get(title);
            DeviceRatioReportEntryResp lastMonthRatesEntry = lastMonthRates.get(title);

            // 当月完整率
            double currentIntegrityRate = (Objects.nonNull(currentMonthEntry) && Objects.nonNull(currentMonthEntry.getIntegrityRate()))
                    ?Double.parseDouble(currentMonthEntry.getIntegrityRate()):0;
            // 上月完整率
            double lastIntegrityRate = (Objects.nonNull(lastMonthRatesEntry) && Objects.nonNull(lastMonthRatesEntry.getIntegrityRate()))
                    ?Double.parseDouble(lastMonthRatesEntry.getIntegrityRate()):0;

            // 当月准确率
            double currentAccuracy = (Objects.nonNull(currentMonthEntry) && Objects.nonNull(currentMonthEntry.getAccuracy()))
                    ?Double.parseDouble(currentMonthEntry.getAccuracy()):0;
            // 上月准确率
            double lastAccuracy = (Objects.nonNull(lastMonthRatesEntry) && Objects.nonNull(lastMonthRatesEntry.getAccuracy()))
                    ?Double.parseDouble(lastMonthRatesEntry.getAccuracy()):0;

            map.put("integrityRateChange", currentIntegrityRate - lastIntegrityRate);
            map.put("accuracyChange", currentAccuracy - lastAccuracy);
            comparisonMap.put(title, map);
        }
        return comparisonMap;
    }
    Map<String, DeviceRatioReportEntryResp> calculateRates(List<DeviceRatioReportEntryResp> data) {

        Map<String, DeviceRatioReportEntryResp> collect = data.stream().collect(Collectors.toMap(DeviceRatioReportEntryResp::getTitle, entry -> entry));
        Map<String, DeviceRatioReportEntryResp> ratesMap = new HashMap<>();
        for (DeviceRatioReportEntryResp entry : data) {
            String title = entry.getTitle();
            if (!ratesMap.containsKey(title)) {
                ratesMap.put(title, new DeviceRatioReportEntryResp(title, "0", "0"));
            }
            DeviceRatioReportEntryResp existingEntry = ratesMap.get(title);
            existingEntry.setIntegrityRate(existingEntry.getIntegrityRate() + entry.getIntegrityRate());
            existingEntry.setAccuracy(existingEntry.getAccuracy() + entry.getAccuracy());
        }
        // 平均值
        for (DeviceRatioReportEntryResp entry : ratesMap.values()) {
            // 假设我们计算的是总和而不是平均值
            // 如果需要平均值，需要除以分组内的条目数

        }
        return ratesMap;
    }
    private String convertNullData(String data) {
        if (StringUtils.isBlank(data)) {
            return "";
        }
        return data;
    }
    private void buildDeviceExceptInfoCountExcelData(List<DqmHistoryDeviceDataExceptionResp> excels, List<DeviceExceptInfoCountExcel> countExcels) {
        long startTime = System.currentTimeMillis();
        DeviceExceptInfoCountExcel countExcel;
        AtomicInteger i = new AtomicInteger(1);
        for (DqmHistoryDeviceDataExceptionResp record : excels) {
            countExcel = new DeviceExceptInfoCountExcel();
            countExcel.setSort(i.getAndIncrement());
            countExcel.setDivision(convertNullData(record.getSybbh()));
            countExcel.setProductGroup(convertNullData(record.getZehdSpartdesc()));
            countExcel.setDataCenter(convertNullData(BusinessConst.dataCenterMap.get(record.getDataCenterId())));
            countExcel.setDeviceName(convertNullData(record.getDeviceName()));
            countExcel.setDeviceCode(convertNullData(record.getDeviceCode()));
            countExcel.setStartTime(convertNullData(record.getStartStatDate()));
            countExcel.setEndTime(convertNullData(record.getEndStatDate()));
            countExcel.setDeviceLocation(convertNullData(record.getDeviceLocation()));
            countExcel.setFwVersion(convertNullData(record.getFwVersion()));
            countExcel.setHwVersion(convertNullData(record.getHwVersion()));
            countExcel.setAuthToken(convertNullData(record.getAuthToken()));
            countExcel.setModelId(convertNullData(record.getModelId()));
            countExcel.setModelName(convertNullData(record.getModelName()));
            countExcel.setCountry(convertNullData(record.getCountry()));

            // 找到对应设备异常数据
            //List<DqmDeviceDataExceptionsResp> exc = resps.stream().filter(x -> StringUtils.isNotBlank(x.getDeviceCode()) &&  x.getDeviceCode().equals(record.getDeviceName())).collect(Collectors.toList());

            // 工作时间异常
            if (!record.getAbnormal8102().isEmpty()) {
                String collect = record.getAbnormal8102().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setWorkingTimeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8102()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8102".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8102");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 发动机工作时间
            if (!record.getAbnormal8105().isEmpty()) {
                String collect = record.getAbnormal8105().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineWorktimeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8105()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8105".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8105");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 总油耗
            if (!record.getAbnormal8201().isEmpty()) {
                String collect = record.getAbnormal8201().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalFuelConsumptionExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8201()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8201".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8201");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 方量
            if (!record.getAbnormal8401().isEmpty()) {
                String collect = record.getAbnormal8401().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setPumpingVolumeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8401()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8401".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8401");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 行驶里程
            if (!record.getAbnormal8403().isEmpty()) {
                String collect = record.getAbnormal8403().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDrivingMileageExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8403()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8403".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8403");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 设备位置
            if (!record.getAbnormal8501().isEmpty()) {
                String collect = record.getAbnormal8501().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDeviceLocationExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8501()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8501".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8501");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 设备状态
            if (!record.getAbnormal8503().isEmpty()) {
                String collect = record.getAbnormal8503().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setDeviceStateExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8503()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8503".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8503");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            // 行驶速度
            if (!record.getAbnormal8510().isEmpty()) {
                String collect = record.getAbnormal8510().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTravelSpeedExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8510()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8510".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8510");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 油位
            if (!record.getAbnormal8506().isEmpty()) {
                String collect = record.getAbnormal8506().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setFuelLevelExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8506()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8506".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8506");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 发动机转速
            if (!record.getAbnormal8507().isEmpty()) {
                String collect = record.getAbnormal8507().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineSpeedExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8507()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8507".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8507");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 发动机水温
            if (!record.getAbnormal8508().isEmpty()) {
                String collect = record.getAbnormal8508().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setEngineTemperatureExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8508()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8508".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8508");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 当前电量
            if (!record.getAbnormal8509().isEmpty()) {
                String collect = record.getAbnormal8509().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setStateOfChargeExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8509()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8509".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8509");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            // 总电耗
            if (!record.getAbnormal8511().isEmpty()) {
                String collect = record.getAbnormal8511().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8511()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8511".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8511");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
//新增属性
            //怠速油耗
            if (!record.getAbnormal8205().isEmpty()) {
                String collect = record.getAbnormal8205().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8205()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8205".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8205");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //怠速时长
            if (!record.getAbnormal8106().isEmpty()) {
                String collect = record.getAbnormal8106().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8106()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8106".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8106");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //档位
            if (!record.getAbnormal8602().isEmpty()) {
                String collect = record.getAbnormal8602().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8602()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8602".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8602");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //左行走工时
            if (!record.getAbnormal8108().isEmpty()) {
                String collect = record.getAbnormal8108().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8108()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8108".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8108");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //右行走工时
            if (!record.getAbnormal8107().isEmpty()) {
                String collect = record.getAbnormal8107().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8107()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8107".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8107");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //机油压力
            if (!record.getAbnormal8603().isEmpty()) {
                String collect = record.getAbnormal8603().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8603()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8603".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8603");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //泵吸收功率
            if (!record.getAbnormal8604().isEmpty()) {
                String collect = record.getAbnormal8604().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8604()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8604".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8604");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
//2024-12-30
            //电机转速
            if (!record.getAbnormal8605().isEmpty()) {
                String collect = record.getAbnormal8605().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8605()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8605".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8605");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //充电状态
            if (!record.getAbnormal8606().isEmpty()) {
                String collect = record.getAbnormal8606().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8606()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8606".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8606");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //充电剩余时间
            if (!record.getAbnormal8607().isEmpty()) {
                String collect = record.getAbnormal8607().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8607()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8607".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8607");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //单次充电电量
            if (!record.getAbnormal8608().isEmpty()) {
                String collect = record.getAbnormal8608().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8608()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8608".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8608");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }
            //当日电耗
            if (!record.getAbnormal8609().isEmpty()) {
                String collect = record.getAbnormal8609().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalElectricExceptCount(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8609()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8609");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            //动作编码
            if (!record.getAbnormal8610().isEmpty()) {
                String collect = record.getAbnormal8610().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setActionCode(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8610()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8610");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            //回转时间
            if (!record.getAbnormal8611().isEmpty()) {
                String collect = record.getAbnormal8611().stream().map(DqmDeviceDataAbnormalModel::getAbnormalNameAndCount).collect(Collectors.joining(",\n"));
                countExcel.setTotalTimeRotation(collect);

                // for (DqmDeviceDataAbnormalModel model : record.getAbnormal8611()) {
                //     // 找到对应设备 对应属性 对应异常的数据
                //     //List<DqmDeviceDataExceptionsResp> exc1 = exc.stream().filter(x -> x.getAbnormalCode().equals(model.getAbnormalCode()) && "8609".equals(x.getParamCode())).collect(Collectors.toList());
                //     List<DqmDeviceDataExceptionsResp> exc1 = excMap.get(record.getDeviceName() + "-" + model.getAbnormalCode() + "-" + "8611");
                //     if(exc1 == null) continue;
                //     for (DqmDeviceDataExceptionsResp e : exc1) {
                //         genarateData(countExcel, e, ic, exceptInfoDetailExcels);
                //     }
                //
                // }
            }

            countExcels.add(countExcel);
        }

        log.info("处理完成耗时：{}", System.currentTimeMillis() - startTime);
    }
}
