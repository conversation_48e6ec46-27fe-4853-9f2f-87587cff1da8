package com.rc.admin.divisionalproductgroup.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 质量戳DTO
 */
@Data
@ApiModel("质量戳DTO")
public class QualityStampDTO {
    
    @NotBlank(message = "物模型ID不能为空")
    private String modelId;
    
    @ApiModelProperty(value = "质量戳键值", required = true)
    @NotBlank(message = "质量戳键值不能为空")
    private String bitKey;
    
    @ApiModelProperty(value = "质量戳描述")
    private String bitDesc;
    
    @ApiModelProperty(value = "位序号", required = true)
    @NotNull(message = "位序号不能为空")
    private Integer bitIndex;
    
    @ApiModelProperty("最大值")
    private String maxValue;
    
    @ApiModelProperty("最小值")
    private String minValue;
    
    @ApiModelProperty("字典ID")
    private Integer dictId;
}


