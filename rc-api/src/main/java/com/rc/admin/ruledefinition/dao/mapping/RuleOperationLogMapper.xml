<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleOperationLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleOperationLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="operation_user" property="operationUser" jdbcType="VARCHAR"/>
        <result column="operation_time" property="operationTime" jdbcType="TIMESTAMP"/>
        <result column="operation_desc" property="operationDesc" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, operation_type, operation_user, operation_time, operation_desc
    </sql>

    <!-- 根据规则ID查询操作日志 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_operation_log
        WHERE rule_id = #{ruleId}
        ORDER BY operation_time DESC
    </select>

    <!-- 记录操作日志 -->
    <insert id="insertOperationLog">
        INSERT INTO rule_operation_log (
            rule_id, operation_type, operation_user, operation_time, operation_desc
        ) VALUES (
            #{ruleId}, #{operationType}, #{operationUser}, #{operationTime}, #{operationDesc}
        )
    </insert>

</mapper>
