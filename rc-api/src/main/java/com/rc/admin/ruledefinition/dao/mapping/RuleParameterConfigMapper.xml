<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleParameterConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleParameterConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="param_name" property="paramName" jdbcType="VARCHAR"/>
        <result column="param_desc" property="paramDesc" jdbcType="VARCHAR"/>
        <result column="param_type" property="paramType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, param_name, param_desc, param_type, create_time, update_time
    </sql>

    <!-- 根据规则ID查询参数配置列表 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_parameter_config
        WHERE rule_id = #{ruleId}
        ORDER BY id ASC
    </select>

    <!-- 根据规则ID删除参数配置 -->
    <delete id="deleteByRuleId">
        DELETE FROM rule_parameter_config
        WHERE rule_id = #{ruleId}
    </delete>

    <!-- 批量插入参数配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rule_parameter_config (
            rule_id, param_name, param_desc, param_type, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ruleId}, #{item.paramName}, #{item.paramDesc}, #{item.paramType},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
