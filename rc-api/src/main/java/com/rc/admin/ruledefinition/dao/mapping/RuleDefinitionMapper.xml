<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleDefinitionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleDefinition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_code" property="ruleCode" jdbcType="INTEGER"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="rule_type" property="ruleType" jdbcType="SMALLINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="SMALLINT"/>
    </resultMap>

    <!-- 列表查询结果映射 -->
    <resultMap id="ListResultMap" type="com.rc.admin.ruledefinition.vo.RuleDefinitionListVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_code" property="ruleCode" jdbcType="INTEGER"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="rule_type" property="ruleType" jdbcType="SMALLINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 详情查询结果映射 -->
    <resultMap id="DetailResultMap" type="com.rc.admin.ruledefinition.vo.RuleDetailVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_code" property="ruleCode" jdbcType="INTEGER"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="rule_type" property="ruleType" jdbcType="SMALLINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_code, rule_name, rule_type, description, status, tenant_id, 
        create_time, update_time, create_by, update_by, is_deleted
    </sql>

    <!-- 列表查询字段 -->
    <sql id="List_Column_List">
        id, rule_code, rule_name, rule_type, description, status, 
        update_by, update_time
    </sql>

    <!-- 详情查询字段 -->
    <sql id="Detail_Column_List">
        id, rule_code, rule_name, rule_type, description, status, 
        create_by, create_time, update_by, update_time
    </sql>

    <!-- 检查规则名称唯一性 -->
    <select id="countByRuleName" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM rule_definition
        WHERE rule_name = #{ruleName}
        AND is_deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取最大规则编码 -->
    <select id="selectMaxRuleCode" resultType="java.lang.Integer">
        SELECT MAX(rule_code)
        FROM rule_definition
        WHERE is_deleted = 0
    </select>

    <!-- 分页查询规则列表 -->
    <select id="selectRuleDefinitionList" resultMap="ListResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM rule_definition
        <where>
            is_deleted = 0
            <if test="query.ruleName != null and query.ruleName != ''">
                AND rule_name LIKE CONCAT('%', #{query.ruleName}, '%')
            </if>
            <if test="query.ruleType != null">
                AND rule_type = #{query.ruleType}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.tenantId != null and query.tenantId != ''">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

    <!-- 查询规则详情 -->
    <select id="selectRuleDetailById" resultMap="DetailResultMap">
        SELECT
        <include refid="Detail_Column_List"/>
        FROM rule_definition
        WHERE id = #{id}
        AND is_deleted = 0
    </select>

    <!-- 检查规则是否被引用 -->
    <select id="selectRuleReferences" resultType="java.lang.String">
        SELECT DISTINCT 'rule_condition_group' as table_name
        FROM rule_condition_group
        WHERE rule_id = #{ruleId}
        UNION ALL
        SELECT DISTINCT 'rule_parameter_config' as table_name
        FROM rule_parameter_config
        WHERE rule_id = #{ruleId}
        UNION ALL
        SELECT DISTINCT 'rule_output_config' as table_name
        FROM rule_output_config
        WHERE rule_id = #{ruleId}
    </select>

</mapper>
