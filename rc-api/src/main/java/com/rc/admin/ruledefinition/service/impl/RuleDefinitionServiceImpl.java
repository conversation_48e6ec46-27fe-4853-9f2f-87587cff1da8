package com.rc.admin.ruledefinition.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ruledefinition.constant.RuleDefinitionConstants;
import com.rc.admin.ruledefinition.dto.*;
import com.rc.admin.ruledefinition.dto.ValidationResult;
import com.rc.admin.ruledefinition.entity.*;
import com.rc.admin.ruledefinition.mapper.*;
import com.rc.admin.ruledefinition.service.RuleDefinitionService;
import com.rc.admin.ruledefinition.util.RuleCodeGenerator;
import com.rc.admin.ruledefinition.util.RuleDefinitionConverter;
import com.rc.admin.ruledefinition.util.RuleDefinitionValidator;
import com.rc.admin.ruledefinition.service.RuleValidationService;
import com.rc.admin.ruledefinition.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则定义服务实现类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Service
@Transactional
public class RuleDefinitionServiceImpl implements RuleDefinitionService {
    
    @Autowired
    private RuleDefinitionMapper ruleDefinitionMapper;
    
    @Autowired
    private RuleConditionGroupMapper conditionGroupMapper;
    
    @Autowired
    private RuleConditionMapper conditionMapper;
    
    @Autowired
    private RuleParameterConfigMapper parameterConfigMapper;
    
    @Autowired
    private RuleOutputConfigMapper outputConfigMapper;
    
    @Autowired
    private RuleOperationLogMapper operationLogMapper;
    
    @Autowired
    private RuleCodeGenerator ruleCodeGenerator;
    
    @Autowired
    private RuleDefinitionValidator validator;
    
    @Autowired
    private RuleValidationService ruleValidationService;
    
    @Autowired
    private RuleDefinitionConverter converter;
    
    @Override
    public void addRuleDefinition(RuleDefinitionDTO dto) {
        // 1. 参数校验
        validateRuleDefinition(dto);
        
        // 2. 检查规则名称唯一性
        if (!checkRuleNameUnique(dto.getRuleName(), null)) {
            throw new RuntimeException("规则名称已存在");
        }
        
        // 3. 生成规则编码
        Integer ruleCode = ruleCodeGenerator.generateRuleCode();
        
        // 4. 保存规则定义
        RuleDefinition ruleDefinition = converter.convertToEntity(dto);
        ruleDefinition.setRuleCode(ruleCode);
        ruleDefinition.setStatus(RuleDefinitionConstants.STATUS_ENABLED);
        ruleDefinition.setCreateTime(LocalDateTime.now());
        ruleDefinition.setUpdateTime(LocalDateTime.now());
        
        ruleDefinitionMapper.insert(ruleDefinition);
        
        // 5. 保存关联数据
        saveRelatedData(ruleCode, dto);
        
        // 6. 记录操作日志
        recordOperationLog(ruleCode, RuleDefinitionConstants.OPERATION_CREATE, 
            "新增规则定义：" + dto.getRuleName());
        
        log.info("新增规则定义成功，规则编码：{}", ruleCode);
    }
    
    @Override
    public void updateRuleDefinition(RuleDefinitionDTO dto) {
        // 1. 参数校验
        if (dto.getRuleCode() == null) {
            throw new RuntimeException("规则编码不能为空");
        }
        validateRuleDefinition(dto);
        
        // 2. 检查规则是否存在
        RuleDefinition existingRule = ruleDefinitionMapper.selectByRuleCode(dto.getRuleCode());
        if (existingRule == null) {
            throw new RuntimeException("规则不存在");
        }
        
        // 3. 检查规则名称唯一性
        if (!checkRuleNameUnique(dto.getRuleName(), dto.getRuleCode())) {
            throw new RuntimeException("规则名称已存在");
        }
        
        // 4. 更新规则定义
        RuleDefinition ruleDefinition = converter.convertToEntity(dto);
        ruleDefinition.setUpdateTime(LocalDateTime.now());
        
        // 使用自定义的更新方法（因为主键是rule_code）
        updateRuleDefinitionByCode(ruleDefinition);
        
        // 5. 删除并重新保存关联数据
        deleteRelatedData(dto.getRuleCode());
        saveRelatedData(dto.getRuleCode(), dto);
        
        // 6. 记录操作日志
        recordOperationLog(dto.getRuleCode(), RuleDefinitionConstants.OPERATION_UPDATE, 
            "修改规则定义：" + dto.getRuleName());
        
        log.info("修改规则定义成功，规则编码：{}", dto.getRuleCode());
    }
    
    @Override
    public void deleteRuleDefinition(Integer ruleCode) {
        // 1. 检查规则是否存在
        RuleDefinition rule = ruleDefinitionMapper.selectByRuleCode(ruleCode);
        if (rule == null) {
            throw new RuntimeException("规则不存在");
        }
        
        // 2. 检查规则是否被引用
        List<String> references = ruleDefinitionMapper.selectRuleReferences(ruleCode);
        if (!references.isEmpty()) {
            throw new RuntimeException("规则正在被使用，无法删除");
        }
        
        // 3. 软删除规则定义
        deleteRuleDefinitionByCode(ruleCode);
        
        // 4. 删除关联数据
        deleteRelatedData(ruleCode);
        
        // 5. 记录操作日志
        recordOperationLog(ruleCode, RuleDefinitionConstants.OPERATION_DELETE, 
            "删除规则定义：" + rule.getRuleName());
        
        log.info("删除规则定义成功，规则编码：{}", ruleCode);
    }
    
    @Override
    public void batchDeleteRuleDefinition(List<Integer> ruleCodes) {
        if (ruleCodes == null || ruleCodes.isEmpty()) {
            return;
        }
        
        for (Integer ruleCode : ruleCodes) {
            try {
                deleteRuleDefinition(ruleCode);
            } catch (Exception e) {
                log.error("批量删除规则定义失败，规则编码：{}，错误：{}", ruleCode, e.getMessage());
                throw new RuntimeException("批量删除失败：" + e.getMessage());
            }
        }
        
        log.info("批量删除规则定义成功，共删除{}条", ruleCodes.size());
    }
    
    @Override
    public Page<RuleDefinitionListVO> pageRuleDefinitionList(RuleDefinitionQueryDTO query, int pageNum, int pageSize) {
        Page<RuleDefinitionListVO> page = new Page<>(pageNum, pageSize);
        
        Page<RuleDefinitionListVO> resultPage = ruleDefinitionMapper.selectRuleDefinitionList(page, query);
        
        // 转换VO对象
        List<RuleDefinitionListVO> records = resultPage.getRecords().stream()
            .map(this::convertToListVO)
            .collect(Collectors.toList());
        
        resultPage.setRecords(records);
        return resultPage;
    }
    
    @Override
    public RuleDetailVO getRuleDetail(Integer ruleCode) {
        // 1. 查询规则基本信息
        RuleDetailVO detailVO = ruleDefinitionMapper.selectRuleDetailByCode(ruleCode);
        if (detailVO == null) {
            throw new RuntimeException("规则不存在");
        }
        
        // 2. 查询条件组列表
        List<RuleConditionGroup> conditionGroups = conditionGroupMapper.selectByRuleCode(ruleCode);
        List<ConditionGroupVO> conditionGroupVOs = new ArrayList<>();
        
        for (RuleConditionGroup group : conditionGroups) {
            List<RuleCondition> conditions = conditionMapper.selectByRuleCodeAndGroupOrder(
                ruleCode, group.getGroupOrder());
            ConditionGroupVO groupVO = converter.convertToConditionGroupVO(group, conditions);
            conditionGroupVOs.add(groupVO);
        }
        detailVO.setConditionGroups(conditionGroupVOs);
        
        // 3. 查询参数配置列表
        List<RuleParameterConfig> parameterConfigs = parameterConfigMapper.selectByRuleCode(ruleCode);
        List<ParameterConfigVO> parameterConfigVOs = parameterConfigs.stream()
            .map(converter::convertToParameterConfigVO)
            .collect(Collectors.toList());
        detailVO.setParameterConfigs(parameterConfigVOs);
        
        // 4. 查询输出配置列表
        List<RuleOutputConfig> outputConfigs = outputConfigMapper.selectByRuleCode(ruleCode);
        List<OutputConfigVO> outputConfigVOs = outputConfigs.stream()
            .map(converter::convertToOutputConfigVO)
            .collect(Collectors.toList());
        detailVO.setOutputConfigs(outputConfigVOs);
        
        // 5. 查询操作日志列表
        List<RuleOperationLog> operationLogs = operationLogMapper.selectByRuleCode(ruleCode);
        List<OperationLogVO> operationLogVOs = operationLogs.stream()
            .map(converter::convertToOperationLogVO)
            .collect(Collectors.toList());
        detailVO.setOperationLogs(operationLogVOs);
        
        return detailVO;
    }
    
    @Override
    public void updateRuleStatus(Integer ruleCode, Integer status) {
        // 1. 检查规则是否存在
        RuleDefinition rule = ruleDefinitionMapper.selectByRuleCode(ruleCode);
        if (rule == null) {
            throw new RuntimeException("规则不存在");
        }
        
        // 2. 更新状态
        rule.setStatus(status);
        rule.setUpdateTime(LocalDateTime.now());
        
        updateRuleDefinitionByCode(rule);
        
        // 3. 记录操作日志
        String operationDesc = status == RuleDefinitionConstants.STATUS_ENABLED ? "启用规则" : "禁用规则";
        recordOperationLog(ruleCode, RuleDefinitionConstants.OPERATION_UPDATE, operationDesc);
        
        log.info("更新规则状态成功，规则编码：{}，新状态：{}", ruleCode, status);
    }
    
    @Override
    public Integer generateRuleCode() {
        return ruleCodeGenerator.generateRuleCode();
    }
    
    @Override
    public boolean checkRuleNameUnique(String ruleName, Integer excludeRuleCode) {
        Long count = ruleDefinitionMapper.countByRuleName(ruleName, excludeRuleCode);
        return count == 0;
    }
    
    @Override
    public List<String> getFunctionLibrary() {
        // 返回预定义的函数库
        List<String> functions = new ArrayList<>();
        functions.add("CURRENT_VALUE");
        functions.add("DAILY_INCREMENT");
        functions.add("WEEKLY_AVERAGE");
        functions.add("MONTHLY_MAX");
        functions.add("YEARLY_MIN");
        return functions;
    }
    
    // 私有方法
    
    /**
     * 校验规则定义参数
     */
    private void validateRuleDefinition(RuleDefinitionDTO dto) {
        if (dto == null) {
            throw new RuntimeException("规则定义不能为空");
        }
        
        if (StrUtil.isBlank(dto.getRuleName())) {
            throw new RuntimeException("规则名称不能为空");
        }
        
        if (dto.getRuleType() == null) {
            throw new RuntimeException("规则类型不能为空");
        }
        
        if (dto.getConditionGroups() == null || dto.getConditionGroups().isEmpty()) {
            throw new RuntimeException("条件组不能为空");
        }
        
        // 校验规则表达式
        ValidationResult validationResult = ruleValidationService.validateRuleDefinition(dto.getConditionGroups());
        if (!validationResult.isValid()) {
            throw new RuntimeException("规则校验失败：" + validationResult.getErrorMessage());
        }
    }
    
    /**
     * 保存关联数据 - 基于规则编码
     */
    private void saveRelatedData(Integer ruleCode, RuleDefinitionDTO dto) {
        // 保存条件组和条件
        if (dto.getConditionGroups() != null) {
            List<RuleConditionGroup> conditionGroups = new ArrayList<>();
            List<RuleCondition> allConditions = new ArrayList<>();
            
            for (ConditionGroupDTO groupDTO : dto.getConditionGroups()) {
                // 保存条件组
                RuleConditionGroup group = new RuleConditionGroup();
                group.setRuleCode(ruleCode);
                group.setGroupOrder(groupDTO.getGroupOrder());
                group.setGroupLogic(groupDTO.getGroupLogic());
                group.setCreateTime(LocalDateTime.now());
                group.setUpdateTime(LocalDateTime.now());
                conditionGroups.add(group);
                
                // 保存条件
                if (groupDTO.getConditions() != null) {
                    for (ConditionDTO conditionDTO : groupDTO.getConditions()) {
                        RuleCondition condition = new RuleCondition();
                        condition.setRuleCode(ruleCode);
                        condition.setGroupOrder(groupDTO.getGroupOrder());
                        condition.setConditionOrder(conditionDTO.getConditionOrder());
                        condition.setLeftOperand(conditionDTO.getLeftOperand());
                        condition.setLeftOperandType(conditionDTO.getLeftOperandType());
                        condition.setOperator(conditionDTO.getOperator());
                        condition.setRightOperand(conditionDTO.getRightOperand());
                        condition.setRightOperandType(conditionDTO.getRightOperandType());
                        condition.setLogicRelation(conditionDTO.getLogicRelation());
                        condition.setCreateTime(LocalDateTime.now());
                        condition.setUpdateTime(LocalDateTime.now());
                        allConditions.add(condition);
                    }
                }
            }
            
            conditionGroupMapper.batchInsert(conditionGroups);
            if (!allConditions.isEmpty()) {
                conditionMapper.batchInsert(allConditions);
            }
        }
        
        // 保存参数配置
        if (dto.getParameterConfigs() != null) {
            List<RuleParameterConfig> parameterConfigs = new ArrayList<>();
            for (ParameterConfigDTO paramDTO : dto.getParameterConfigs()) {
                RuleParameterConfig param = new RuleParameterConfig();
                param.setRuleCode(ruleCode);
                param.setParamName(paramDTO.getParamName());
                param.setParamDesc(paramDTO.getParamDesc());
                param.setParamType(paramDTO.getParamType());
                param.setCreateTime(LocalDateTime.now());
                param.setUpdateTime(LocalDateTime.now());
                parameterConfigs.add(param);
            }
            parameterConfigMapper.batchInsert(parameterConfigs);
        }
        
        // 保存输出配置
        if (dto.getOutputConfigs() != null) {
            List<RuleOutputConfig> outputConfigs = new ArrayList<>();
            for (OutputConfigDTO outputDTO : dto.getOutputConfigs()) {
                RuleOutputConfig output = new RuleOutputConfig();
                output.setRuleCode(ruleCode);
                output.setOutputOrder(outputDTO.getOutputOrder());
                output.setOutputType(outputDTO.getOutputType());
                output.setOutputContent(outputDTO.getOutputContent());
                output.setCreateTime(LocalDateTime.now());
                output.setUpdateTime(LocalDateTime.now());
                outputConfigs.add(output);
            }
            outputConfigMapper.batchInsert(outputConfigs);
        }
    }
    
    /**
     * 删除关联数据 - 基于规则编码
     */
    private void deleteRelatedData(Integer ruleCode) {
        conditionMapper.deleteByRuleCode(ruleCode);
        conditionGroupMapper.deleteByRuleCode(ruleCode);
        parameterConfigMapper.deleteByRuleCode(ruleCode);
        outputConfigMapper.deleteByRuleCode(ruleCode);
    }
    
    /**
     * 记录操作日志 - 基于规则编码
     */
    private void recordOperationLog(Integer ruleCode, String operationType, String operationDesc) {
        RuleOperationLog log = new RuleOperationLog();
        log.setRuleCode(ruleCode);
        log.setOperationType(operationType);
        log.setOperationUser("system"); // TODO: 从当前用户上下文获取
        log.setOperationTime(LocalDateTime.now());
        log.setOperationDesc(operationDesc);
        
        operationLogMapper.insert(log);
    }
    
    /**
     * 自定义更新方法（因为主键是rule_code）
     */
    private void updateRuleDefinitionByCode(RuleDefinition ruleDefinition) {
        QueryWrapper<RuleDefinition> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_code", ruleDefinition.getRuleCode());
        ruleDefinitionMapper.update(ruleDefinition, wrapper);
    }
    
    /**
     * 自定义删除方法（因为主键是rule_code）
     */
    private void deleteRuleDefinitionByCode(Integer ruleCode) {
        QueryWrapper<RuleDefinition> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_code", ruleCode);
        ruleDefinitionMapper.delete(wrapper);
    }
    
    /**
     * 转换为列表VO
     */
    private RuleDefinitionListVO convertToListVO(RuleDefinitionListVO vo) {
        // 设置类型名称和状态名称
        vo.setRuleTypeName(getRuleTypeName(vo.getRuleType()));
        vo.setStatusName(getStatusName(vo.getStatus()));
        return vo;
    }
    
    private String getRuleTypeName(Integer ruleType) {
        if (ruleType == null) return "";
        switch (ruleType) {
            case 1: return "准确性异常";
            case 2: return "完整性异常";
            default: return "未知";
        }
    }
    
    private String getStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "禁用";
            case 1: return "启用";
            default: return "未知";
        }
    }
}
