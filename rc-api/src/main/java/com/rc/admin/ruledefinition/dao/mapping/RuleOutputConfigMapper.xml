<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleOutputConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleOutputConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="output_type" property="outputType" jdbcType="VARCHAR"/>
        <result column="output_content" property="outputContent" jdbcType="VARCHAR"/>
        <result column="output_order" property="outputOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, output_type, output_content, output_order, create_time, update_time
    </sql>

    <!-- 根据规则ID查询输出配置列表 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_output_config
        WHERE rule_id = #{ruleId}
        ORDER BY output_order ASC
    </select>

    <!-- 根据规则ID删除输出配置 -->
    <delete id="deleteByRuleId">
        DELETE FROM rule_output_config
        WHERE rule_id = #{ruleId}
    </delete>

    <!-- 批量插入输出配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rule_output_config (
            rule_id, output_type, output_content, output_order, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ruleId}, #{item.outputType}, #{item.outputContent}, #{item.outputOrder},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
