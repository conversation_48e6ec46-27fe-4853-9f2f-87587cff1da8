package com.rc.admin.ruledefinition.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ruledefinition.dto.RuleDefinitionQueryDTO;
import com.rc.admin.ruledefinition.entity.RuleDefinition;
import com.rc.admin.ruledefinition.vo.RuleDefinitionListVO;
import com.rc.admin.ruledefinition.vo.RuleDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 规则定义 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024
 */
@Mapper
public interface RuleDefinitionMapper extends BaseMapper<RuleDefinition> {
    
    /**
     * 检查规则名称唯一性
     */
    Long countByRuleName(@Param("ruleName") String ruleName, @Param("excludeRuleCode") Integer excludeRuleCode);
    
    /**
     * 获取最大规则编码
     */
    Integer selectMaxRuleCode();
    
    /**
     * 分页查询规则列表
     */
    Page<RuleDefinitionListVO> selectRuleDefinitionList(
        Page<RuleDefinitionListVO> page, 
        @Param("query") RuleDefinitionQueryDTO query
    );
    
    /**
     * 根据规则编码查询规则详情
     */
    RuleDetailVO selectRuleDetailByCode(@Param("ruleCode") Integer ruleCode);
    
    /**
     * 根据规则编码查询规则基本信息
     */
    RuleDefinition selectByRuleCode(@Param("ruleCode") Integer ruleCode);
    
    /**
     * 检查规则是否被引用
     */
    List<String> selectRuleReferences(@Param("ruleCode") Integer ruleCode);
}
