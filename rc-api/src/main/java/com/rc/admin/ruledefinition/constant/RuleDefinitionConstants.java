package com.rc.admin.ruledefinition.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 规则定义常量类
 * 
 * <AUTHOR>
 * @since 2024
 */
public class RuleDefinitionConstants {
    
    /** 规则类型 */
    public static final int RULE_TYPE_ACCURACY = 1;    // 准确性异常
    public static final int RULE_TYPE_INTEGRITY = 2;   // 完整性异常
    
    /** 状态 */
    public static final int STATUS_DISABLED = 0;       // 禁用
    public static final int STATUS_ENABLED = 1;        // 启用
    
    /** 最大条件数量 */
    public static final int MAX_CONDITIONS = 10;
    
    /** 长度限制 */
    public static final int MAX_RULE_NAME_LENGTH = 20;
    public static final int MAX_DESCRIPTION_LENGTH = 200;
    public static final int MAX_PARAM_DESC_LENGTH = 50;
    
    /** 规则编码生成 */
    public static final int RULE_CODE_START = 9100;  // 起始编码
    
    /** 操作数类型 */
    public static final String OPERAND_TYPE_FUNCTION = "FUNCTION";
    public static final String OPERAND_TYPE_VALUE = "VALUE";
    
    /** 逻辑关系 */
    public static final String LOGIC_AND = "AND";
    public static final String LOGIC_OR = "OR";
    
    /** 核心运算符 */
    public static final Set<String> CORE_OPERATORS = new HashSet<>(Arrays.asList(
        "GTE", "GT", "EQ", "NE", "LT", "LTE", "IS_NULL", "IS_NOT_NULL"
    ));
    
    /** 空值运算符 */
    public static final Set<String> NULL_OPERATORS = new HashSet<>(Arrays.asList("IS_NULL", "IS_NOT_NULL"));
    
    /** 参数类型 */
    public static final String PARAM_TYPE_INSPECTION_ITEM = "INSPECTION_ITEM";
    public static final String PARAM_TYPE_VALUE = "VALUE";
    
    /** 输出类型 */
    public static final String OUTPUT_TYPE_TIME = "TIME";
    public static final String OUTPUT_TYPE_DATA = "DATA";
    
    /** 操作类型 */
    public static final String OPERATION_CREATE = "CREATE";
    public static final String OPERATION_UPDATE = "UPDATE";
    public static final String OPERATION_DELETE = "DELETE";
    
    /** 函数库配置 */
    public static final String FUNCTION_CURRENT_VALUE = "CURRENT_VALUE";
    public static final String FUNCTION_DAILY_INCREMENT = "DAILY_INCREMENT";
}
