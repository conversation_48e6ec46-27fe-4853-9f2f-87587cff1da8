package com.rc.admin.ruledefinition.service.impl;

import com.rc.admin.ruledefinition.dto.ConditionDTO;
import com.rc.admin.ruledefinition.dto.ConditionGroupDTO;
import com.rc.admin.ruledefinition.dto.ValidationResult;
import com.rc.admin.ruledefinition.service.RuleValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 规则校验服务实现类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Service
public class RuleValidationServiceImpl implements RuleValidationService {

    // 支持的比较运算符
    private static final Set<String> COMPARISON_OPERATORS = new HashSet<>(Arrays.asList(
        "GTE", "GT", "EQ", "NE", "LT", "LTE", "IS_NULL", "IS_NOT_NULL"
    ));

    // 支持的数学运算符
    private static final Set<String> MATH_OPERATORS = new HashSet<>(Arrays.asList("+", "-", "*", "/"));

    // 支持的条件逻辑关系
    private static final Set<String> LOGIC_RELATIONS = new HashSet<>(Arrays.asList("AND", "OR"));

    // 支持的条件组逻辑关系
    private static final Set<String> GROUP_LOGIC_RELATIONS = new HashSet<>(Arrays.asList("AND", "OR"));

    // 函数名称模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile(
        "^(当前值|日增量|周平均值|月最大值|年最小值)\\(([^)]+)\\)$"
    );

    // 检查项模式
    private static final Pattern INSPECTION_ITEM_PATTERN = Pattern.compile("^检查项\\d+$");

    // 数值模式
    private static final Pattern VALUE_PATTERN = Pattern.compile("^数值\\d+$");

    @Override
    public ValidationResult validateRuleDefinition(List<ConditionGroupDTO> conditionGroups) {
        ValidationResult result = new ValidationResult(true);

        if (conditionGroups == null || conditionGroups.isEmpty()) {
            result.addError("条件组列表不能为空");
            return result;
        }

        // 检查条件总数是否超过限制
        ValidationResult countResult = checkConditionCountLimit(conditionGroups, 10);
        result.merge(countResult);

        // 验证每个条件组
        for (int i = 0; i < conditionGroups.size(); i++) {
            ConditionGroupDTO group = conditionGroups.get(i);
            ValidationResult groupResult = validateConditionGroup(group);
            if (!groupResult.isValid()) {
                groupResult.addError("条件组" + (i + 1) + "校验失败");
            }
            result.merge(groupResult);
        }

        return result;
    }

    @Override
    public ValidationResult validateConditionGroup(ConditionGroupDTO group) {
        ValidationResult result = new ValidationResult(true);

        if (group == null) {
            result.addError("条件组不能为空");
            return result;
        }

        // 验证条件组逻辑关系
        if (group.getGroupLogic() != null && 
            !GROUP_LOGIC_RELATIONS.contains(group.getGroupLogic())) {
            result.addError("无效的条件组逻辑关系：" + group.getGroupLogic() + "，只支持：AND、OR");
        }

        // 验证条件列表
        if (group.getConditions() == null || group.getConditions().isEmpty()) {
            result.addError("条件组不能为空");
            return result;
        }

        // 验证每个条件
        for (int i = 0; i < group.getConditions().size(); i++) {
            ConditionDTO condition = group.getConditions().get(i);
            ValidationResult conditionResult = validateCondition(condition);
            if (!conditionResult.isValid()) {
                conditionResult.addError("条件" + (i + 1) + "校验失败");
            }
            result.merge(conditionResult);
        }

        return result;
    }

    @Override
    public ValidationResult validateCondition(ConditionDTO condition) {
        ValidationResult result = new ValidationResult(true);

        if (condition == null) {
            result.addError("条件不能为空");
            return result;
        }

        // 验证条件顺序
        if (condition.getConditionOrder() == null) {
            result.addError("条件顺序不能为空");
        }

        // 验证左操作数
        if (condition.getLeftOperand() == null || condition.getLeftOperand().trim().isEmpty()) {
            result.addError("左操作数不能为空");
        } else {
            ValidationResult leftResult = validateOperand(condition.getLeftOperand());
            if (!leftResult.isValid()) {
                leftResult.addError("左操作数格式错误");
            }
            result.merge(leftResult);
        }

        // 验证运算符
        if (condition.getOperator() == null || condition.getOperator().trim().isEmpty()) {
            result.addError("运算符不能为空");
        } else if (!COMPARISON_OPERATORS.contains(condition.getOperator())) {
            result.addError("无效的运算符：" + condition.getOperator() + 
                "，只支持：GTE、GT、EQ、NE、LT、LTE、IS_NULL、IS_NOT_NULL");
        }

        // 验证右操作数（除了is null和is not null）
        if (!isNullOperator(condition.getOperator())) {
            if (condition.getRightOperand() == null || condition.getRightOperand().trim().isEmpty()) {
                result.addError("非空值运算符必须包含右操作数");
            } else {
                ValidationResult rightResult = validateOperand(condition.getRightOperand());
                if (!rightResult.isValid()) {
                    rightResult.addError("右操作数格式错误");
                }
                result.merge(rightResult);
            }
        }

        // 验证逻辑关系
        if (condition.getLogicRelation() != null && 
            !LOGIC_RELATIONS.contains(condition.getLogicRelation())) {
            result.addError("无效的逻辑关系：" + condition.getLogicRelation() + "，只支持：AND、OR");
        }

        return result;
    }

    @Override
    public ValidationResult validateExpression(String expression) {
        ValidationResult result = new ValidationResult(true);

        if (expression == null || expression.trim().isEmpty()) {
            result.addError("表达式不能为空");
            return result;
        }

        // 解析表达式组件
        List<String> components = parseExpressionComponents(expression);
        
        if (components.isEmpty()) {
            result.addError("表达式格式错误");
            return result;
        }

        // 验证表达式结构
        for (int i = 0; i < components.size(); i++) {
            String component = components.get(i);
            
            if (i % 2 == 0) {
                // 偶数位置应该是操作数（函数或数值）
                ValidationResult operandResult = validateOperand(component);
                if (!operandResult.isValid()) {
                    operandResult.addError("操作数格式错误：" + component);
                }
                result.merge(operandResult);
            } else {
                // 奇数位置应该是数学运算符
                if (!MATH_OPERATORS.contains(component)) {
                    result.addError("无效的数学运算符：" + component + "，只支持：+、-、*、/");
                }
            }
        }

        return result;
    }

    @Override
    public ValidationResult checkConditionCountLimit(List<ConditionGroupDTO> conditionGroups, int maxConditions) {
        ValidationResult result = new ValidationResult(true);

        if (conditionGroups == null) {
            return result;
        }

        int totalConditions = 0;
        for (ConditionGroupDTO group : conditionGroups) {
            if (group.getConditions() != null) {
                totalConditions += group.getConditions().size();
            }
        }

        if (totalConditions > maxConditions) {
            result.addError("条件总数超过限制，当前：" + totalConditions + "，最大：" + maxConditions);
        }

        return result;
    }

    /**
     * 验证操作数是否有效
     */
    private ValidationResult validateOperand(String operand) {
        ValidationResult result = new ValidationResult(true);

        if (operand == null || operand.trim().isEmpty()) {
            result.addError("操作数不能为空");
            return result;
        }

        // 检查是否是函数
        if (FUNCTION_PATTERN.matcher(operand).matches()) {
            // 验证函数参数
            String functionParam = operand.substring(operand.indexOf("(") + 1, operand.indexOf(")"));
            if (!INSPECTION_ITEM_PATTERN.matcher(functionParam).matches()) {
                result.addError("函数参数格式错误：" + functionParam + "，应为：检查项1、检查项2等");
            }
            return result;
        }

        // 检查是否是检查项（带数字后缀）
        if (INSPECTION_ITEM_PATTERN.matcher(operand).matches()) {
            return result;
        }

        // 检查是否是数值（带数字后缀）
        if (VALUE_PATTERN.matcher(operand).matches()) {
            return result;
        }

        // 检查是否是复杂表达式
        if (operand.contains("+") || operand.contains("-") || 
            operand.contains("*") || operand.contains("/")) {
            return validateExpression(operand);
        }

        result.addError("无效的操作数格式：" + operand + 
            "，应为：当前值(检查项1)、检查项1、数值1等");
        return result;
    }

    /**
     * 检查是否是空值运算符
     */
    private boolean isNullOperator(String operator) {
        return "IS_NULL".equals(operator) || "IS_NOT_NULL".equals(operator);
    }

    /**
     * 解析表达式组件
     */
    private List<String> parseExpressionComponents(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> components = new ArrayList<>();
        String[] parts = expression.split("\\s+");
        
        for (String part : parts) {
            if (!part.trim().isEmpty()) {
                components.add(part.trim());
            }
        }

        return components;
    }
}
