package com.rc.admin.ruledefinition.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.ruledefinition.entity.FunctionLibrary;
import com.rc.admin.ruledefinition.mapper.FunctionLibraryMapper;
import com.rc.admin.ruledefinition.service.FunctionLibraryService;
import com.rc.admin.ruledefinition.vo.FunctionLibraryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 函数库服务实现类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Service
public class FunctionLibraryServiceImpl implements FunctionLibraryService {

    @Autowired
    private FunctionLibraryMapper functionLibraryMapper;

    @Override
    public List<FunctionLibraryVO> getFunctionLibrary() {
        log.info("获取所有启用的函数库列表");
        
        try {
            List<FunctionLibrary> functions = functionLibraryMapper.selectEnabledFunctions();
            return convertToVOList(functions);
        } catch (Exception e) {
            log.error("获取函数库列表失败", e);
            throw new RuntimeException("获取函数库列表失败", e);
        }
    }

    @Override
    public List<FunctionLibraryVO> getFunctionLibraryByCategory(String category) {
        log.info("根据分类获取函数库列表，分类：{}", category);
        
        try {
            List<FunctionLibrary> functions = functionLibraryMapper.selectByCategory(category);
            return convertToVOList(functions);
        } catch (Exception e) {
            log.error("根据分类获取函数库列表失败，分类：{}", category, e);
            throw new RuntimeException("根据分类获取函数库列表失败", e);
        }
    }

    @Override
    public List<String> getFunctionCategories() {
        log.info("获取所有函数分类");
        
        try {
            QueryWrapper<FunctionLibrary> wrapper = new QueryWrapper<>();
            wrapper.select("DISTINCT category")
                  .eq("status", 1)
                  .orderByAsc("category");
            
            List<FunctionLibrary> categories = functionLibraryMapper.selectList(wrapper);
            return categories.stream()
                    .map(FunctionLibrary::getCategory)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取函数分类失败", e);
            throw new RuntimeException("获取函数分类失败", e);
        }
    }

    @Override
    public FunctionLibraryVO getFunctionByCode(String functionCode) {
        log.info("根据函数编码获取函数库信息，编码：{}", functionCode);
        
        try {
            FunctionLibrary function = functionLibraryMapper.selectByFunctionCode(functionCode);
            if (function == null) {
                return null;
            }
            return convertToVO(function);
        } catch (Exception e) {
            log.error("根据函数编码获取函数库信息失败，编码：{}", functionCode, e);
            throw new RuntimeException("根据函数编码获取函数库信息失败", e);
        }
    }

    @Override
    public List<FunctionLibraryVO> getFunctionLibraryForDropdown() {
        log.info("获取用于下拉选择的函数库列表");
        
        try {
            List<FunctionLibrary> functions = functionLibraryMapper.selectEnabledFunctions();
            return convertToVOList(functions);
        } catch (Exception e) {
            log.error("获取函数库下拉选项失败", e);
            throw new RuntimeException("获取函数库下拉选项失败", e);
        }
    }

    /**
     * 将实体列表转换为VO列表
     */
    private List<FunctionLibraryVO> convertToVOList(List<FunctionLibrary> functions) {
        if (CollectionUtils.isEmpty(functions)) {
            return new ArrayList<>();
        }
        
        return functions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为VO
     */
    private FunctionLibraryVO convertToVO(FunctionLibrary function) {
        if (function == null) {
            return null;
        }
        
        FunctionLibraryVO vo = new FunctionLibraryVO();
        BeanUtils.copyProperties(function, vo);
        
        // 设置状态名称
        if (function.getStatus() != null) {
            vo.setStatusName(function.getStatus() == 1 ? "启用" : "禁用");
        }
        
        // 设置分类名称
        if (function.getCategory() != null) {
            vo.setCategoryName(getCategoryDisplayName(function.getCategory()));
        }
        
        return vo;
    }

    /**
     * 获取分类显示名称
     */
    private String getCategoryDisplayName(String category) {
        if (category == null) {
            return "";
        }
        
        switch (category) {
            case "工况":
                return "工况";
            case "定位":
                return "定位";
            case "统计":
                return "统计";
            case "数值":
                return "数值";
            default:
                return category;
        }
    }
}
