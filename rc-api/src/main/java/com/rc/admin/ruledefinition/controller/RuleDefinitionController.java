package com.rc.admin.ruledefinition.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.ruledefinition.dto.RuleDefinitionDTO;
import com.rc.admin.ruledefinition.dto.RuleDefinitionQueryDTO;
import com.rc.admin.ruledefinition.service.RuleDefinitionService;
import com.rc.admin.ruledefinition.service.FunctionLibraryService;
import com.rc.admin.ruledefinition.vo.RuleDefinitionListVO;
import com.rc.admin.ruledefinition.vo.RuleDetailVO;
import com.rc.admin.ruledefinition.vo.FunctionLibraryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 规则定义控制器
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@RestController
@RequestMapping("/api/ruledefinition")
@Api(tags = "规则定义管理")
@Validated
public class RuleDefinitionController {
    
    @Autowired
    private RuleDefinitionService ruleDefinitionService;
    
    @Autowired
    private FunctionLibraryService functionLibraryService;
    
    @ApiOperation("新增规则定义")
    @PostMapping("/add")
    public Response addRuleDefinition(@Valid @RequestBody RuleDefinitionDTO dto) {
        try {
            ruleDefinitionService.addRuleDefinition(dto);
            return Response.success("新增规则定义成功");
        } catch (Exception e) {
            log.error("新增规则定义失败", e);
            return Response.error("新增规则定义失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("修改规则定义")
    @PostMapping("/update")
    public Response updateRuleDefinition(@Valid @RequestBody RuleDefinitionDTO dto) {
        try {
            ruleDefinitionService.updateRuleDefinition(dto);
            return Response.success("修改规则定义成功");
        } catch (Exception e) {
            log.error("修改规则定义失败", e);
            return Response.error("修改规则定义失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("删除规则定义")
    @PostMapping("/delete/{ruleCode}")
    public Response deleteRuleDefinition(@PathVariable @NotNull Integer ruleCode) {
        try {
            ruleDefinitionService.deleteRuleDefinition(ruleCode);
            return Response.success("删除规则定义成功");
        } catch (Exception e) {
            log.error("删除规则定义失败", e);
            return Response.error("删除规则定义失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("批量删除规则定义")
    @PostMapping("/batchDelete")
    public Response batchDeleteRuleDefinition(@RequestBody @NotEmpty List<Integer> ruleCodes) {
        try {
            ruleDefinitionService.batchDeleteRuleDefinition(ruleCodes);
            return Response.success("批量删除规则定义成功");
        } catch (Exception e) {
            log.error("批量删除规则定义失败", e);
            return Response.error("批量删除规则定义失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("分页查询规则列表")
    @PostMapping("/pageList")
    public Response pageRuleDefinitionList(
            @RequestBody(required = false) RuleDefinitionQueryDTO query,
            @RequestParam(defaultValue = "1") @ApiParam("页码") int pageNum,
            @RequestParam(defaultValue = "10") @ApiParam("每页大小") int pageSize) {
        try {
            if (query == null) {
                query = new RuleDefinitionQueryDTO();
            }
            
            Page<RuleDefinitionListVO> page = ruleDefinitionService.pageRuleDefinitionList(query, pageNum, pageSize);
            return Response.success(page);
        } catch (Exception e) {
            log.error("分页查询规则列表失败", e);
            return Response.error("查询规则列表失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("查询规则详情")
    @GetMapping("/detail/{ruleCode}")
    public Response getRuleDetail(@PathVariable @NotNull Integer ruleCode) {
        try {
            RuleDetailVO detail = ruleDefinitionService.getRuleDetail(ruleCode);
            return Response.success(detail);
        } catch (Exception e) {
            log.error("查询规则详情失败", e);
            return Response.error("查询规则详情失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("启用/禁用规则")
    @PostMapping("/updateStatus")
    public Response updateRuleStatus(
            @RequestParam @NotNull @ApiParam("规则编码") Integer ruleCode,
            @RequestParam @NotNull @ApiParam("状态：0-禁用，1-启用") Integer status) {
        try {
            ruleDefinitionService.updateRuleStatus(ruleCode, status);
            String message = status == 1 ? "启用规则成功" : "禁用规则成功";
            return Response.success(message);
        } catch (Exception e) {
            log.error("更新规则状态失败", e);
            return Response.error("更新规则状态失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("生成规则编码")
    @GetMapping("/generateCode")
    public Response generateRuleCode() {
        try {
            Integer ruleCode = ruleDefinitionService.generateRuleCode();
            return Response.success(ruleCode);
        } catch (Exception e) {
            log.error("生成规则编码失败", e);
            return Response.error("生成规则编码失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("检查规则名称唯一性")
    @GetMapping("/checkNameUnique")
    public Response checkRuleNameUnique(
            @RequestParam @NotNull @ApiParam("规则名称") String ruleName,
            @RequestParam(required = false) @ApiParam("排除的规则编码") Integer excludeRuleCode) {
        try {
            boolean isUnique = ruleDefinitionService.checkRuleNameUnique(ruleName, excludeRuleCode);
            return Response.success(isUnique);
        } catch (Exception e) {
            log.error("检查规则名称唯一性失败", e);
            return Response.error("检查规则名称唯一性失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("获取函数库列表")
    @GetMapping("/functionLibrary")
    public Response getFunctionLibrary() {
        try {
            List<FunctionLibraryVO> functions = functionLibraryService.getFunctionLibrary();
            return Response.success(functions);
        } catch (Exception e) {
            log.error("获取函数库列表失败", e);
            return Response.error("获取函数库列表失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("获取函数库下拉选项")
    @GetMapping("/functionLibrary/dropdown")
    public Response getFunctionLibraryForDropdown() {
        try {
            List<FunctionLibraryVO> functions = functionLibraryService.getFunctionLibraryForDropdown();
            return Response.success(functions);
        } catch (Exception e) {
            log.error("获取函数库下拉选项失败", e);
            return Response.error("获取函数库下拉选项失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("根据分类获取函数库")
    @GetMapping("/functionLibrary/category/{category}")
    public Response getFunctionLibraryByCategory(@PathVariable String category) {
        try {
            List<FunctionLibraryVO> functions = functionLibraryService.getFunctionLibraryByCategory(category);
            return Response.success(functions);
        } catch (Exception e) {
            log.error("根据分类获取函数库失败，分类：{}", category, e);
            return Response.error("根据分类获取函数库失败：" + e.getMessage());
        }
    }
    
    @ApiOperation("获取所有函数分类")
    @GetMapping("/functionLibrary/categories")
    public Response getFunctionCategories() {
        try {
            List<String> categories = functionLibraryService.getFunctionCategories();
            return Response.success(categories);
        } catch (Exception e) {
            log.error("获取函数分类失败", e);
            return Response.error("获取函数分类失败：" + e.getMessage());
        }
    }
    
    // 响应结果类
    public static class Response {
        private String code;
        private String message;
        private Object data;
        
        public Response() {}
        
        public Response(String code, String message, Object data) {
            this.code = code;
            this.message = message;
            this.data = data;
        }
        
        public static Response success() {
            return new Response("200", "success", null);
        }
        
        public static Response success(Object data) {
            return new Response("200", "success", data);
        }
        
        public static Response success(String message, Object data) {
            return new Response("200", message, data);
        }
        
        public static Response error(String message) {
            return new Response("500", message, null);
        }
        
        public static Response error(String code, String message) {
            return new Response(code, message, null);
        }
        
        // getter和setter方法
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public Object getData() {
            return data;
        }
        
        public void setData(Object data) {
            this.data = data;
        }
    }
}
