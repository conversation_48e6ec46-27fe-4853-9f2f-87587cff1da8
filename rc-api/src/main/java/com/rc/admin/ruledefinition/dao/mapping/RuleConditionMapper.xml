<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleConditionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleCondition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="condition_order" property="conditionOrder" jdbcType="INTEGER"/>
        <result column="left_operand" property="leftOperand" jdbcType="VARCHAR"/>
        <result column="left_operand_type" property="leftOperandType" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="right_operand" property="rightOperand" jdbcType="VARCHAR"/>
        <result column="right_operand_type" property="rightOperandType" jdbcType="VARCHAR"/>
        <result column="logic_relation" property="logicRelation" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, group_id, condition_order, left_operand, left_operand_type, operator,
        right_operand, right_operand_type, logic_relation, create_time, update_time
    </sql>

    <!-- 根据条件组ID查询条件列表 -->
    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_condition
        WHERE group_id = #{groupId}
        ORDER BY condition_order ASC
    </select>

    <!-- 根据规则ID查询所有条件 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT c.id, c.group_id, c.condition_order, c.left_operand, c.left_operand_type,
               c.operator, c.right_operand, c.right_operand_type, c.logic_relation,
               c.create_time, c.update_time
        FROM rule_condition c
        INNER JOIN rule_condition_group g ON c.group_id = g.id
        WHERE g.rule_id = #{ruleId}
        ORDER BY g.group_order ASC, c.condition_order ASC
    </select>

    <!-- 根据条件组ID删除条件 -->
    <delete id="deleteByGroupId">
        DELETE FROM rule_condition
        WHERE group_id = #{groupId}
    </delete>

    <!-- 根据规则ID删除所有条件 -->
    <delete id="deleteByRuleId">
        DELETE c FROM rule_condition c
        INNER JOIN rule_condition_group g ON c.group_id = g.id
        WHERE g.rule_id = #{ruleId}
    </delete>

    <!-- 批量插入条件 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rule_condition (
            group_id, condition_order, left_operand, left_operand_type, operator,
            right_operand, right_operand_type, logic_relation, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.groupId}, #{item.conditionOrder}, #{item.leftOperand}, #{item.leftOperandType},
                #{item.operator}, #{item.rightOperand}, #{item.rightOperandType}, #{item.logicRelation},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
