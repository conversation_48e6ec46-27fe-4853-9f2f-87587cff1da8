package com.rc.admin.ruledefinition.service;

import com.rc.admin.ruledefinition.vo.FunctionLibraryVO;

import java.util.List;

/**
 * 函数库服务接口
 * 
 * <AUTHOR>
 * @since 2024
 */
public interface FunctionLibraryService {

    /**
     * 获取所有启用的函数库列表
     * 
     * @return 函数库列表
     */
    List<FunctionLibraryVO> getFunctionLibrary();

    /**
     * 根据分类获取函数库列表
     * 
     * @param category 函数分类
     * @return 函数库列表
     */
    List<FunctionLibraryVO> getFunctionLibraryByCategory(String category);

    /**
     * 获取所有函数分类
     * 
     * @return 函数分类列表
     */
    List<String> getFunctionCategories();

    /**
     * 根据函数编码获取函数库信息
     * 
     * @param functionCode 函数编码
     * @return 函数库信息
     */
    FunctionLibraryVO getFunctionByCode(String functionCode);

    /**
     * 获取用于下拉选择的函数库列表（简化版）
     * 
     * @return 函数库下拉选项列表
     */
    List<FunctionLibraryVO> getFunctionLibraryForDropdown();
}
