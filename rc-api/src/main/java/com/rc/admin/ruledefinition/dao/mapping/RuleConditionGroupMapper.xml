<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.RuleConditionGroupMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.RuleConditionGroup">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="group_logic" property="groupLogic" jdbcType="VARCHAR"/>
        <result column="group_order" property="groupOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, group_logic, group_order, create_time, update_time
    </sql>

    <!-- 根据规则ID查询条件组列表 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_condition_group
        WHERE rule_id = #{ruleId}
        ORDER BY group_order ASC
    </select>

    <!-- 根据规则ID删除条件组 -->
    <delete id="deleteByRuleId">
        DELETE FROM rule_condition_group
        WHERE rule_id = #{ruleId}
    </delete>

    <!-- 批量插入条件组 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rule_condition_group (
            rule_id, group_logic, group_order, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ruleId}, #{item.groupLogic}, #{item.groupOrder},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
