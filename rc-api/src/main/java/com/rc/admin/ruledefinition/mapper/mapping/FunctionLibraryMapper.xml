<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.ruledefinition.mapper.FunctionLibraryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.ruledefinition.entity.FunctionLibrary">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="function_name" property="functionName" jdbcType="VARCHAR"/>
        <result column="function_code" property="functionCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, function_name, function_code, description, category, status, sort_order
    </sql>

    <!-- 根据分类查询函数库列表 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM function_library
        WHERE category = #{category}
        AND status = 1
        ORDER BY sort_order ASC, function_name ASC
    </select>

    <!-- 查询所有启用的函数库 -->
    <select id="selectEnabledFunctions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM function_library
        WHERE status = 1
        ORDER BY sort_order ASC, category ASC, function_name ASC
    </select>

    <!-- 根据函数编码查询函数库 -->
    <select id="selectByFunctionCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM function_library
        WHERE function_code = #{functionCode}
        AND status = 1
        LIMIT 1
    </select>

</mapper>
