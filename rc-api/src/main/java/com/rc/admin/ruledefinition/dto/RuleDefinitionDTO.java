package com.rc.admin.ruledefinition.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 规则定义数据传输对象
 * 用于新增和修改规则的请求参数
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "RuleDefinitionDTO", description = "规则定义数据传输对象")
public class RuleDefinitionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("规则编码（修改时必填）")
    private Integer ruleCode;

    @ApiModelProperty(value = "规则名称", required = true, example = "温度异常检测规则")
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 20, message = "规则名称长度不能超过20字符")
    private String ruleName;

    @ApiModelProperty(value = "规则类型：1-准确性异常，2-完整性异常", required = true, example = "1")
    @NotNull(message = "规则类型不能为空")
    @Min(value = 1, message = "规则类型值无效")
    @Max(value = 2, message = "规则类型值无效")
    private Integer ruleType;

    @ApiModelProperty(value = "规则描述", example = "监控设备温度异常情况")
    @Size(max = 200, message = "规则描述长度不能超过200字符")
    private String description;

    @ApiModelProperty("条件组列表")
    @Valid
    @Size(min = 1, message = "至少需要配置一个条件组")
    private List<ConditionGroupDTO> conditionGroups;

    @ApiModelProperty("参数配置列表")
    @Valid
    private List<ParameterConfigDTO> parameterConfigs;

    @ApiModelProperty("输出配置列表")
    @Valid
    private List<OutputConfigDTO> outputConfigs;
}
