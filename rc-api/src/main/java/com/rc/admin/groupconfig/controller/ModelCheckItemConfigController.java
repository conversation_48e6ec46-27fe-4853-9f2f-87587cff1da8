package com.rc.admin.groupconfig.controller;

import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.dto.ModelCheckItemConfigDTO;
import com.rc.admin.groupconfig.service.ModelCheckItemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物模型检查项配置控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "物模型检查项配置管理")
@RestController
@RequestMapping("/api/groupConfig/modelConfig")
public class ModelCheckItemConfigController {

    @Autowired
    private ModelCheckItemConfigService modelCheckItemConfigService;

    @ApiOperation("保存物模型检查项配置")
    @PostMapping("/save")
    public Response saveConfig(@ApiParam("配置信息") @RequestBody ModelCheckItemConfigDTO dto) {
        try {
            modelCheckItemConfigService.saveConfig(dto);
            return Response.success("保存物模型检查项配置成功");
        } catch (Exception e) {
            log.error("保存物模型检查项配置失败", e);
            return Response.failError("保存物模型检查项配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取物模型检查项配置")
    @GetMapping("/get/{doubleRateSign}")
    public Response getConfig(@ApiParam("分组标识") @PathVariable String doubleRateSign) {
        try {
            List<ModelCheckItemConfigDTO.ConfigItemDTO> result = modelCheckItemConfigService.getConfig(doubleRateSign);
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取物模型检查项配置失败", e);
            return Response.failError("获取物模型检查项配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除物模型检查项配置")
    @DeleteMapping("/delete/{doubleRateSign}")
    public Response deleteConfig(@ApiParam("分组标识") @PathVariable String doubleRateSign) {
        try {
            modelCheckItemConfigService.deleteConfig(doubleRateSign);
            return Response.success("删除物模型检查项配置成功");
        } catch (Exception e) {
            log.error("删除物模型检查项配置失败", e);
            return Response.failError("删除物模型检查项配置失败：" + e.getMessage());
        }
    }
}
