package com.rc.admin.groupconfig.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 分组配置DTO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "分组配置DTO", description = "分组配置请求参数")
public class GroupConfigDTO {

    @ApiModelProperty(value = "分组标识", notes = "编辑时必填，新增时不需要")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    @NotBlank(message = "分组名称不能为空")
    @Size(max = 20, message = "分组名称不能超过20个字符")
    private String doubleRateName;

    @ApiModelProperty("大区代码列表")
    @NotEmpty(message = "请至少选择一个大区")
    private List<String> regionCodes;

    @ApiModelProperty("国家代码列表")
    @NotEmpty(message = "请至少选择一个国家")
    private List<String> countryCodes;

    @ApiModelProperty("物模型检查项配置列表")
    @NotEmpty(message = "请至少配置一组物模型检查项")
    private List<ModelCheckItemDTO> modelCheckItems;

    @Data
    @ApiModel(value = "物模型检查项配置")
    public static class ModelCheckItemDTO {
        @ApiModelProperty("物模型ID")
        @NotBlank(message = "物模型ID不能为空")
        private String modelId;

        @ApiModelProperty("检查项编码")
        @NotBlank(message = "检查项编码不能为空")
        private String paramCode;
    }
}
