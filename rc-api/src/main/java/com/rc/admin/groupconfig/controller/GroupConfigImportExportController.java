package com.rc.admin.groupconfig.controller;

import com.alibaba.fastjson.JSON;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.dto.ImportGroupConfigDTO;
import com.rc.admin.groupconfig.service.GroupConfigImportExportService;
import com.rc.admin.groupconfig.vo.ImportResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 分组配置导入导出控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "分组配置导入导出")
@RestController
@RequestMapping("/api/groupConfig/importExport")
public class GroupConfigImportExportController {

    @Autowired
    private GroupConfigImportExportService importExportService;

    @ApiOperation("导出分组配置")
    @GetMapping("/export")
    public void exportGroupConfig(HttpServletResponse response) {
        try {
            importExportService.exportGroupConfig(response);
        } catch (Exception e) {
            log.error("导出分组配置失败", e);
            // 导出失败时设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("下载导入模板")
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            importExportService.downloadTemplate(response);
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            // 下载失败时设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    
    @ApiOperation("导入分组配置")
    @PostMapping("/import")
    public Response importGroupConfig(@ApiParam("导入参数") @RequestPart("dto") String dtoJson,
                                      @ApiParam("Excel文件") @RequestPart("file") MultipartFile file) {
        try {
            log.info("接收到分组配置导入请求，dtoJson长度：{}，文件名：{}，文件大小：{}", 
                dtoJson != null ? dtoJson.length() : 0, 
                file != null ? file.getOriginalFilename() : "null",
                file != null ? file.getSize() : 0);
            
            // 验证参数
            if (dtoJson == null || dtoJson.trim().isEmpty()) {
                return Response.failError("导入参数不能为空");
            }
            
            if (file == null || file.isEmpty()) {
                return Response.failError("上传的Excel文件不能为空");
            }
            
            // 解析JSON字符串为DTO对象
            ImportGroupConfigDTO dto;
            try {
                dto = JSON.parseObject(dtoJson, ImportGroupConfigDTO.class);
                log.info("解析导入参数成功：{}", dto);
            } catch (Exception e) {
                log.error("解析导入参数JSON失败：{}", dtoJson, e);
                return Response.failError("导入参数格式错误：" + e.getMessage());
            }
            
            // 验证DTO参数
            if (dto.getDoubleRateName() == null || dto.getDoubleRateName().trim().isEmpty()) {
                return Response.failError("分组名称不能为空");
            }
            
            if (dto.getRegionCodes() == null || dto.getRegionCodes().isEmpty()) {
                return Response.failError("请至少选择一个大区");
            }
            
            if (dto.getCountryCodes() == null || dto.getCountryCodes().isEmpty()) {
                return Response.failError("请至少选择一个国家");
            }

            ImportResultVO result = importExportService.importGroupConfig(dto, file);
            log.info("分组配置导入完成，成功：{}，失败：{}", result.getSuccessCount(), result.getErrorCount());
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("导入分组配置失败", e);
            String errorMessage = e.getMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = "未知错误";
            }
            return Response.failError("导入分组配置失败：" + errorMessage);
        }
    }
}
