package com.rc.admin.groupconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分组配置VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "分组配置VO", description = "分组配置返回参数")
public class GroupConfigVO {

    @ApiModelProperty("分组标识")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    private String doubleRateName;

    @ApiModelProperty("大区数量")
    private Integer regionCount;

    @ApiModelProperty("国家数量")
    private Integer countryCount;

    @ApiModelProperty("模型数量")
    private Integer modelCount;

    @ApiModelProperty("检查项数量")
    private Integer checkItemCount;

}
