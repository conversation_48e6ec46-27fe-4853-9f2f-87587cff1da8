package com.rc.admin.groupconfig.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.groupconfig.dto.GroupConfigDTO;
import com.rc.admin.groupconfig.vo.GroupConfigVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分组配置服务接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public interface GroupConfigService {

    /**
     * 新增分组配置
     *
     * @param dto 分组配置DTO
     * @return 新增的分组标识
     */
    String add(GroupConfigDTO dto);

    /**
     * 编辑分组配置
     *
     * @param dto 分组配置DTO
     */
    void edit(GroupConfigDTO dto);

    /**
     * 删除分组配置
     *
     * @param ids 分组标识列表
     */
    void delete(List<String> ids);

    /**
     * 分页查询分组配置
     *
     * @param dto 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<GroupConfigVO> pageList(GroupConfigDTO dto, int pageNum, int pageSize);

    /**
     * 根据分组标识查询分组配置
     *
     * @param doubleRateSign 分组标识
     * @return 分组配置VO
     */
    GroupConfigVO getByDoubleRateSign(String doubleRateSign);

    /**
     * 导出分组配置明细
     *
     * @param doubleRateSign 分组标识
     * @param response HTTP响应
     */
    void exportGroupConfigDetail(String doubleRateSign, HttpServletResponse response);
}
