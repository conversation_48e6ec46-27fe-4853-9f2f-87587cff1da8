package com.rc.admin.groupconfig.service;

import com.rc.admin.groupconfig.dto.ModelCheckItemConfigDTO;

import java.util.List;

/**
 * 物模型检查项配置服务接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public interface ModelCheckItemConfigService {

    /**
     * 保存物模型检查项配置
     *
     * @param dto 配置DTO
     */
    void saveConfig(ModelCheckItemConfigDTO dto);

    /**
     * 获取物模型检查项配置
     *
     * @param doubleRateSign 分组标识
     * @return 配置列表
     */
    List<ModelCheckItemConfigDTO.ConfigItemDTO> getConfig(String doubleRateSign);

    /**
     * 删除物模型检查项配置
     *
     * @param doubleRateSign 分组标识
     */
    void deleteConfig(String doubleRateSign);
}
