package com.rc.admin.groupconfig.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.dto.GroupConfigDTO;
import com.rc.admin.groupconfig.service.GroupConfigService;
import com.rc.admin.groupconfig.vo.GroupConfigVO;
import com.rc.admin.groupconfig.vo.AddGroupConfigResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分组配置控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "分组配置管理")
@RestController
@RequestMapping("/api/groupConfig")
public class GroupConfigController {

    @Autowired
    private GroupConfigService groupConfigService;

    @ApiOperation("新增分组配置")
    @PostMapping("/add")
    public Response add(@ApiParam("分组配置信息") @RequestBody GroupConfigDTO dto) {
        try {
            String doubleRateSign = groupConfigService.add(dto);
            AddGroupConfigResultVO result = new AddGroupConfigResultVO(doubleRateSign, dto.getDoubleRateName());
            return Response.success(result);
        } catch (Exception e) {
            log.error("新增分组配置失败", e);
            return Response.failError("新增分组配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("编辑分组配置")
    @PostMapping("/edit")
    public Response edit(@ApiParam("分组配置信息") @RequestBody GroupConfigDTO dto) {
        try {
            groupConfigService.edit(dto);
            return Response.success("编辑分组配置成功");
        } catch (Exception e) {
            log.error("编辑分组配置失败", e);
            return Response.failError("编辑分组配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除分组配置")
    @PostMapping("/delete")
    public Response delete(@ApiParam("分组标识列表") @RequestBody List<String> ids) {
        try {
            groupConfigService.delete(ids);
            return Response.success("删除分组配置成功");
        } catch (Exception e) {
            log.error("删除分组配置失败", e);
            return Response.failError("删除分组配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("分页查询分组配置")
    @PostMapping("/list")
    public Response list(@ApiParam("查询条件") @RequestBody GroupConfigDTO dto,
                                   @ApiParam("页码") @RequestParam(defaultValue = "1") int pageNum,
                                   @ApiParam("页大小") @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Page<GroupConfigVO> result = groupConfigService.pageList(dto, pageNum, pageSize);
            return Response.success(result);
        } catch (Exception e) {
            log.error("查询分组配置失败", e);
            return Response.failError("查询分组配置失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据分组标识查询分组配置")
    @GetMapping("/get/{doubleRateSign}")
    public Response getDetail(@ApiParam("分组标识") @PathVariable String doubleRateSign) {
        try {
            GroupConfigVO result = groupConfigService.getByDoubleRateSign(doubleRateSign);
            if (result == null) {
                return Response.failError("分组配置不存在");
            }
            return Response.success(result);
        } catch (Exception e) {
            log.error("查询分组配置详情失败", e);
            return Response.failError("查询分组配置详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("导出分组配置明细")
    @PostMapping("/export")
    public void exportGroupConfigDetail(@ApiParam("查询条件") @RequestBody GroupConfigDTO dto,
                                        HttpServletResponse response) {
        try {
            log.info("开始导出分组配置明细，分组标识：{}", dto.getDoubleRateSign());
            groupConfigService.exportGroupConfigDetail(dto.getDoubleRateSign(), response);
            log.info("导出分组配置明细完成，分组标识：{}", dto.getDoubleRateSign());
        } catch (Exception e) {
            log.error("导出分组配置明细失败，分组标识：{}", dto.getDoubleRateSign(), e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }
}
