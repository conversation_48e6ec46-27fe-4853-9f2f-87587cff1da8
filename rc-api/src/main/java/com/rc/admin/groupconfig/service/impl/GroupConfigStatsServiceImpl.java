package com.rc.admin.groupconfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.groupconfig.dao.OrsCountryDoubleRateConfigMapper;
import com.rc.admin.groupconfig.entity.OrsCountryDoubleRateConfig;
import com.rc.admin.groupconfig.service.GroupConfigStatsService;
import com.rc.admin.groupconfig.vo.GroupConfigVO;
import com.rc.admin.groupconfig.vo.GroupStatsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 分组配置统计服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
public class GroupConfigStatsServiceImpl implements GroupConfigStatsService {

    @Autowired
    private OrsCountryDoubleRateConfigMapper countryDoubleRateConfigMapper;

    @Override
    public List<GroupConfigVO> getAvailableGroups() {
        // 查询所有分组，按创建时间倒序
        QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT double_rate_sign, double_rate_name, create_time");
        wrapper.orderByDesc("double_rate_sign");
        
        List<OrsCountryDoubleRateConfig> entities = countryDoubleRateConfigMapper.selectList(wrapper);
        
        return entities.stream()
                .map(this::convertToGroupConfigVO)
                .collect(Collectors.toList());
    }

    @Override
    public GroupStatsVO getGroupStats(String doubleRateSign) {
        // 这里应该根据实际业务逻辑计算统计数据
        // 简化处理，返回模拟数据
        GroupStatsVO statsVO = new GroupStatsVO();
        statsVO.setDoubleRateSign(doubleRateSign);
        
        // 查询分组名称
        QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_sign", doubleRateSign);
        wrapper.select("double_rate_name");
        wrapper.last("LIMIT 1");
        
        OrsCountryDoubleRateConfig entity = countryDoubleRateConfigMapper.selectOne(wrapper);
        if (entity != null) {
            statsVO.setDoubleRateName(entity.getDoubleRateName());
        }
        
        // 模拟统计数据
        statsVO.setAccuracyRate(98.5);
        statsVO.setCompletenessRate(99.2);
        statsVO.setAbnormalRate(1.5);
        statsVO.setTotalCount(10000L);
        statsVO.setAbnormalCount(150L);
        
        return statsVO;
    }

    /**
     * 转换为GroupConfigVO
     */
    private GroupConfigVO convertToGroupConfigVO(OrsCountryDoubleRateConfig entity) {
        GroupConfigVO vo = new GroupConfigVO();
        vo.setDoubleRateSign(entity.getDoubleRateSign());
        vo.setDoubleRateName(entity.getDoubleRateName());
        
        // 其他字段设置为默认值，实际应该查询统计
        vo.setRegionCount(0);
        vo.setCountryCount(0);
        vo.setModelCount(0);
        vo.setCheckItemCount(0);
        
        return vo;
    }
}
