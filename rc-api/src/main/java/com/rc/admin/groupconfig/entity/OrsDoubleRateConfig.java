package com.rc.admin.groupconfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 分组配置明细表实体
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@TableName("ors_double_rate_config")
@ApiModel(value = "分组配置明细表", description = "分组配置明细表实体")
public class OrsDoubleRateConfig {

    @ApiModelProperty("分组标志")
    private String doubleRateSign;

    @ApiModelProperty("物模型ID")
    private String modelId;

    @ApiModelProperty("属性编码")
    private Integer paramCode;

}
