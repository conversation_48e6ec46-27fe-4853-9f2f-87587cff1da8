package com.rc.admin.groupconfig.util;

import com.rc.admin.groupconfig.dto.GroupConfigDTO;
import org.springframework.util.StringUtils;

/**
 * 分组配置校验工具类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public class GroupConfigValidator {

    /**
     * 校验分组配置DTO
     *
     * @param dto 分组配置DTO
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateGroupConfigDTO(GroupConfigDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("分组配置不能为空");
        }

        // 校验分组名称
        if (!StringUtils.hasText(dto.getDoubleRateName())) {
            throw new IllegalArgumentException("分组名称不能为空");
        }
        if (dto.getDoubleRateName().length() > 20) {
            throw new IllegalArgumentException("分组名称不能超过20个字符");
        }

        // 校验大区选择
        if (dto.getRegionCodes() == null || dto.getRegionCodes().isEmpty()) {
            throw new IllegalArgumentException("请至少选择一个大区");
        }

        // 校验国家选择
        if (dto.getCountryCodes() == null || dto.getCountryCodes().isEmpty()) {
            throw new IllegalArgumentException("请至少选择一个国家");
        }

        // 校验物模型检查项配置
        if (dto.getModelCheckItems() == null || dto.getModelCheckItems().isEmpty()) {
            throw new IllegalArgumentException("请至少配置一组物模型检查项");
        }

        // 校验每个配置项
        for (GroupConfigDTO.ModelCheckItemDTO item : dto.getModelCheckItems()) {
            if (!StringUtils.hasText(item.getModelId())) {
                throw new IllegalArgumentException("物模型ID不能为空");
            }
            if (!StringUtils.hasText(item.getParamCode())) {
                throw new IllegalArgumentException("检查项编码不能为空");
            }
        }
    }
}
