package com.rc.admin.groupconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分组统计VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "分组统计VO", description = "分组统计返回参数")
public class GroupStatsVO {

    @ApiModelProperty("分组标识")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    private String doubleRateName;

    @ApiModelProperty("准确率")
    private Double accuracyRate;

    @ApiModelProperty("完整率")
    private Double completenessRate;

    @ApiModelProperty("异常数据占比")
    private Double abnormalRate;

    @ApiModelProperty("总数据量")
    private Long totalCount;

    @ApiModelProperty("异常数据量")
    private Long abnormalCount;
}
