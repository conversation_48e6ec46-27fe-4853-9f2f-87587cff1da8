package com.rc.admin.groupconfig.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.groupconfig.entity.OrsCountryDoubleRateConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 分组配置主表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Mapper
public interface OrsCountryDoubleRateConfigMapper extends BaseMapper<OrsCountryDoubleRateConfig> {

    /**
     * 查询最大的分组标识
     *
     * @return 最大的double_rate_sign
     */
    @Select("SELECT MAX(CAST(double_rate_sign AS INTEGER)) FROM ors_country_double_rate_config")
    String selectMaxDoubleRateSign();

    /**
     * 根据分组标识查询分组信息
     *
     * @param doubleRateSign 分组标识
     * @return 分组信息
     */
    @Select("SELECT * FROM ors_country_double_rate_config WHERE double_rate_sign = #{doubleRateSign} LIMIT 1")
    OrsCountryDoubleRateConfig selectByDoubleRateSign(@Param("doubleRateSign") String doubleRateSign);
}
