package com.rc.admin.groupconfig.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.groupconfig.entity.OrsDoubleRateConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 分组配置明细表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Mapper
public interface OrsDoubleRateConfigMapper extends BaseMapper<OrsDoubleRateConfig> {

    /**
     * 根据分组标识查询配置明细
     *
     * @param doubleRateSign 分组标识
     * @return 配置明细列表
     */
    @Select("SELECT * FROM ors_double_rate_config WHERE double_rate_sign = #{doubleRateSign}")
    List<OrsDoubleRateConfig> selectByDoubleRateSign(@Param("doubleRateSign") String doubleRateSign);

    /**
     * 根据分组标识删除配置明细
     *
     * @param doubleRateSign 分组标识
     * @return 删除数量
     */
    @Delete("DELETE FROM ors_double_rate_config WHERE double_rate_sign = #{doubleRateSign}")
    int deleteByDoubleRateSign(@Param("doubleRateSign") String doubleRateSign);
}
