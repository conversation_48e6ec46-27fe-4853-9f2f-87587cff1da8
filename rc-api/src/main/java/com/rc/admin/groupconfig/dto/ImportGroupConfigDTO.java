package com.rc.admin.groupconfig.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 导入分组配置DTO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "导入分组配置DTO", description = "导入分组配置请求参数")
public class ImportGroupConfigDTO {

    @ApiModelProperty("分组名称")
    @NotBlank(message = "分组名称不能为空")
    private String doubleRateName;

    @ApiModelProperty("大区代码列表")
    @NotEmpty(message = "请至少选择一个大区")
    private List<String> regionCodes;

    @ApiModelProperty("国家代码列表")
    @NotEmpty(message = "请至少选择一个国家")
    private List<String> countryCodes;

    @ApiModelProperty("物模型检查项配置列表")
    private List<ModelCheckItemDTO> modelCheckItems = new ArrayList<>();

    @Data
    public static class ModelCheckItemDTO {
        @ApiModelProperty("物模型ID")
        @NotBlank(message = "物模型ID不能为空")
        private String modelId;

        @ApiModelProperty("检查项编码")
        @NotBlank(message = "检查项编码不能为空")
        private String paramCode;
    }

    @Data
    public static class ImportRowData {
        @com.alibaba.excel.annotation.ExcelProperty("物模型ID")
        private String modelId;
        
        @com.alibaba.excel.annotation.ExcelProperty("检查项编码")
        private String paramCode;
        
        // 添加无参构造函数
        public ImportRowData() {}
        
        // 添加带参构造函数
        public ImportRowData(String modelId, String paramCode) {
            this.modelId = modelId;
            this.paramCode = paramCode;
        }
        
        // 添加setter方法（虽然@Data注解会生成，但为了明确性）
        public void setModelId(String modelId) {
            this.modelId = modelId;
        }
        
        public void setParamCode(String paramCode) {
            this.paramCode = paramCode;
        }
        
        // 添加getter方法
        public String getModelId() {
            return modelId;
        }
        
        public String getParamCode() {
            return paramCode;
        }
        
        @Override
        public String toString() {
            return "ImportRowData{modelId='" + modelId + "', paramCode='" + paramCode + "'}";
        }
    }
}
