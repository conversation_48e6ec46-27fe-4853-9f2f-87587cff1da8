package com.rc.admin.groupconfig.service.impl;

import com.rc.admin.groupconfig.dao.OrsCountryDoubleRateConfigMapper;
import com.rc.admin.groupconfig.service.GroupConfigIdGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.ReentrantLock;

/**
 * 分组标识生成服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
public class GroupConfigIdGeneratorServiceImpl implements GroupConfigIdGeneratorService {

    @Autowired
    private OrsCountryDoubleRateConfigMapper groupConfigMapper;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public String generateNextGroupId() {
        lock.lock();
        try {
            // 查询当前最大标识
            String maxId = groupConfigMapper.selectMaxDoubleRateSign();
            if (maxId == null || maxId.trim().isEmpty()) {
                return "1"; // 第一个分组
            }

            // 转换为数字并加1
            int nextId = Integer.parseInt(maxId.trim()) + 1;
            return String.valueOf(nextId);
        } catch (Exception e) {
            log.error("生成分组标识失败", e);
            throw new RuntimeException("生成分组标识失败", e);
        } finally {
            lock.unlock();
        }
    }
}
