package com.rc.admin.groupconfig.dao;

import com.rc.admin.groupconfig.vo.OptionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分组配置数据字典 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Mapper
public interface GroupConfigDictMapper {

    /**
     * 获取大区列表
     *
     * @return 大区列表
     */
    @Select("SELECT DISTINCT region_code as code, region as name FROM ors_country_region_sync ORDER BY region_code")
    List<OptionVO> selectRegionList();

    /**
     * 获取国家列表
     *
     * @param regionCode 大区代码
     * @return 国家列表
     */
    @Select("SELECT DISTINCT country_code as code, country_name as name FROM ors_country_region_sync WHERE region_code = #{regionCode} ORDER BY country_code")
    List<OptionVO> selectCountryListByRegion(@Param("regionCode") String regionCode);

    /**
     * 获取所有国家列表
     *
     * @return 国家列表
     */
    @Select("SELECT DISTINCT country_code as code, country_name as name FROM ors_country_region_sync where country_code is not null ORDER BY country_code")
    List<OptionVO> selectAllCountryList();

    /**
     * 获取物模型列表（支持关键字搜索）
     *
     * @param keyword 搜索关键字
     * @return 物模型列表
     */
    @Select("SELECT model_id as code, model_name_cn as name FROM ors_model_division WHERE model_id LIKE CONCAT('%', #{keyword}, '%') OR model_name_cn LIKE CONCAT('%', #{keyword}, '%') ORDER BY model_id ")
    List<OptionVO> selectModelListByKeyword(@Param("keyword") String keyword);

    /**
     * 获取物模型列表（默认）
     *
     * @return 物模型列表
     */
    @Select("SELECT model_id as code, model_name_cn as name FROM ors_model_division where model_name_cn is not  null   ORDER BY model_id ")
    List<OptionVO> selectModelList();

    /**
     * 获取检查项列表
     *
     * @param modelId 物模型ID
     * @return 检查项列表
     */
    @Select("SELECT param_code as code, param_name as name FROM ors_model_properties_config WHERE model_id = #{modelId} ORDER BY param_code")
    List<OptionVO> selectCheckItemListByModel(@Param("modelId") String modelId);
}
