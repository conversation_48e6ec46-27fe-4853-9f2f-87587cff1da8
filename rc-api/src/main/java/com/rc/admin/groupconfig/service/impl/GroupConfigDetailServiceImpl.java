package com.rc.admin.groupconfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.groupconfig.dao.GroupConfigDictMapper;
import com.rc.admin.groupconfig.dao.OrsCountryDoubleRateConfigMapper;
import com.rc.admin.groupconfig.dao.OrsDoubleRateConfigMapper;
import com.rc.admin.groupconfig.entity.OrsCountryDoubleRateConfig;
import com.rc.admin.groupconfig.entity.OrsDoubleRateConfig;
import com.rc.admin.groupconfig.service.GroupConfigDetailService;
import com.rc.admin.groupconfig.vo.GroupConfigDetailVO;
import com.rc.admin.groupconfig.vo.OptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import cn.hutool.core.util.StrUtil;

/**
 * 分组详情服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
public class GroupConfigDetailServiceImpl implements GroupConfigDetailService {

    @Autowired
    private OrsCountryDoubleRateConfigMapper countryDoubleRateConfigMapper;

    @Autowired
    private OrsDoubleRateConfigMapper doubleRateConfigMapper;

    @Autowired
    private GroupConfigDictMapper groupConfigDictMapper;

    @Override
    public GroupConfigDetailVO getGroupConfigDetail(String doubleRateSign) {
        // 1. 查询分组基础信息
        QueryWrapper<OrsCountryDoubleRateConfig> mainWrapper = new QueryWrapper<>();
        mainWrapper.eq("double_rate_sign", doubleRateSign);
        List<OrsCountryDoubleRateConfig> mainEntities = countryDoubleRateConfigMapper.selectList(mainWrapper);

        if (mainEntities.isEmpty()) {
            return null;
        }

        // 2. 构建详情VO
        GroupConfigDetailVO detailVO = new GroupConfigDetailVO();
        OrsCountryDoubleRateConfig firstEntity = mainEntities.get(0);
        detailVO.setDoubleRateSign(firstEntity.getDoubleRateSign());
        detailVO.setDoubleRateName(firstEntity.getDoubleRateName());

        // 3. 提取大区和国家信息（处理逗号分隔的字符串）
        Set<String> regionCodeSet = new HashSet<>();
        Set<String> countryCodeSet = new HashSet<>();
        
        for (OrsCountryDoubleRateConfig entity : mainEntities) {
            // 处理大区代码（逗号分隔）
            if (StrUtil.isNotBlank(entity.getRegionCode())) {
                String[] regions = entity.getRegionCode().split(",");
                for (String regionCode : regions) {
                    if (StrUtil.isNotBlank(regionCode.trim())) {
                        regionCodeSet.add(regionCode.trim());
                    }
                }
            }
            
            // 处理国家代码（逗号分隔）
            if (StrUtil.isNotBlank(entity.getCountryCode())) {
                String[] countries = entity.getCountryCode().split(",");
                for (String countryCode : countries) {
                    if (StrUtil.isNotBlank(countryCode.trim())) {
                        countryCodeSet.add(countryCode.trim());
                    }
                }
            }
        }
        
        List<String> regionCodes = new ArrayList<>(regionCodeSet);
        List<String> countryCodes = new ArrayList<>(countryCodeSet);
        detailVO.setRegionCodes(regionCodes);
        detailVO.setCountryCodes(countryCodes);

        // 4. 获取大区和国家名称
        List<OptionVO> regionOptions = groupConfigDictMapper.selectRegionList();
        Map<String, String> regionMap = regionOptions.stream()
                .collect(Collectors.toMap(OptionVO::getCode, OptionVO::getName));

        List<OptionVO> countryOptions = groupConfigDictMapper.selectAllCountryList();
        Map<String, String> countryMap = countryOptions.stream()
                .collect(Collectors.toMap(OptionVO::getCode, OptionVO::getName));

        List<String> regionNames = regionCodes.stream()
                .map(regionMap::get)
                .collect(Collectors.toList());
        detailVO.setRegionNames(regionNames);

        List<String> countryNames = countryCodes.stream()
                .map(countryMap::get)
                .collect(Collectors.toList());
        detailVO.setCountryNames(countryNames);

        // 5. 查询配置明细
        QueryWrapper<OrsDoubleRateConfig> detailWrapper = new QueryWrapper<>();
        detailWrapper.eq("double_rate_sign", doubleRateSign);
        List<OrsDoubleRateConfig> detailEntities = doubleRateConfigMapper.selectList(detailWrapper);

        // 6. 获取物模型和检查项名称
        List<OptionVO> modelOptions = groupConfigDictMapper.selectModelList();
        Map<String, String> modelMap = modelOptions.stream()
                .collect(Collectors.toMap(OptionVO::getCode, OptionVO::getName));

        List<GroupConfigDetailVO.ConfigDetailItemVO> configItems = detailEntities.stream()
                .map(entity -> {
                    GroupConfigDetailVO.ConfigDetailItemVO item = new GroupConfigDetailVO.ConfigDetailItemVO();
                    item.setModelId(entity.getModelId());
                    item.setModelName(modelMap.get(entity.getModelId()));
                    item.setParamCode(String.valueOf(entity.getParamCode()));
                    // 这里需要根据物模型ID查询检查项名称，简化处理
                    item.setParamName("检查项" + entity.getParamCode());
                    return item;
                })
                .collect(Collectors.toList());

        detailVO.setConfigItems(configItems);

        return detailVO;
    }
}
