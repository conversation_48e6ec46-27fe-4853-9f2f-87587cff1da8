package com.rc.admin.groupconfig.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.rc.admin.groupconfig.dto.ImportGroupConfigDTO;
import com.rc.admin.groupconfig.service.GroupConfigImportExportService;
import com.rc.admin.groupconfig.service.GroupConfigService;
import com.rc.admin.groupconfig.vo.ExportGroupConfigVO;
import com.rc.admin.groupconfig.vo.ImportResultVO;
import com.rc.admin.groupconfig.vo.TemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 分组配置导入导出服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
public class GroupConfigImportExportServiceImpl implements GroupConfigImportExportService {

    @Autowired
    private GroupConfigService groupConfigService;

    @Override
    public void exportGroupConfig(HttpServletResponse response) {
        try {
            // 这里应该查询实际数据，这里简化处理
            List<ExportGroupConfigVO> dataList = new ArrayList<>();
            
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", 
                "attachment;filename=" + URLEncoder.encode("分组配置.xlsx", "UTF-8"));

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), ExportGroupConfigVO.class)
                .sheet("分组配置")
                .doWrite(dataList);
        } catch (IOException e) {
            log.error("导出分组配置失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", 
                "attachment;filename=" + URLEncoder.encode("分组配置导入模板.xlsx", "UTF-8"));

            // 创建模板数据
            List<TemplateVO> templateList = new ArrayList<>();
            
            TemplateVO template1 = new TemplateVO();
            template1.setModelId("示例物模型ID1");
            template1.setParamCode("1001");
            templateList.add(template1);
            
            TemplateVO template2 = new TemplateVO();
            template2.setModelId("示例物模型ID2");
            template2.setParamCode("1002");
            templateList.add(template2);
            
            TemplateVO template3 = new TemplateVO();
            template3.setModelId("示例物模型ID3");
            template3.setParamCode("1003");
            templateList.add(template3);

            log.info("生成导入模板，包含{}行示例数据", templateList.size());

            // 使用EasyExcel导出模板
            EasyExcel.write(response.getOutputStream(), TemplateVO.class)
                .sheet("模板")
                .doWrite(templateList);
        } catch (Exception e) {
            log.error("下载模板失败", e);
            throw new RuntimeException("下载模板失败", e);
        }
    }

    @Override
    public ImportResultVO importGroupConfig(ImportGroupConfigDTO dto, MultipartFile file) {
        ImportResultVO result = new ImportResultVO();
        List<ImportResultVO.ImportErrorDTO> errorList = new ArrayList<>();
        final int[] successCount = {0};
        final int[] errorCount = {0};

        try {
            // 验证文件
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("上传的文件不能为空");
            }
            
            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                throw new RuntimeException("只支持Excel文件格式(.xlsx或.xls)");
            }
            
            log.info("开始导入分组配置，文件名：{}，文件大小：{} bytes", fileName, file.getSize());
            log.info("导入参数：doubleRateName={}, regionCodes={}, countryCodes={}", 
                dto.getDoubleRateName(), dto.getRegionCodes(), dto.getCountryCodes());

            // 使用EasyExcel解析Excel文件
            EasyExcel.read(file.getInputStream(), ImportGroupConfigDTO.ImportRowData.class, new AnalysisEventListener<ImportGroupConfigDTO.ImportRowData>() {
                private int rowNum = 1;

                @Override
                public void invoke(ImportGroupConfigDTO.ImportRowData data, AnalysisContext context) {
                    rowNum++;
                    try {
                        log.debug("解析第{}行数据：{}", rowNum, data);
                        
                        // 校验数据
                        validateImportRow(data);
                        
                        // 添加到配置列表
                        ImportGroupConfigDTO.ModelCheckItemDTO item = new ImportGroupConfigDTO.ModelCheckItemDTO();
                        item.setModelId(data.getModelId());
                        item.setParamCode(data.getParamCode());
                        dto.getModelCheckItems().add(item);
                        
                        successCount[0]++;
                        log.debug("第{}行数据解析成功", rowNum);
                    } catch (Exception e) {
                        errorCount[0]++;
                        ImportResultVO.ImportErrorDTO error = new ImportResultVO.ImportErrorDTO();
                        error.setRowNum(rowNum);
                        error.setErrorMessage(e.getMessage());
                        error.setOriginalData(data != null ? data.toString() : "null");
                        errorList.add(error);
                        log.warn("第{}行数据解析失败：{}", rowNum, e.getMessage());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel文件解析完成，总行数：{}", rowNum);
                }
            }).sheet().doRead();

            log.info("Excel解析完成，成功：{}行，失败：{}行", successCount[0], errorCount[0]);

            // 如果有成功数据，保存分组配置
            if (successCount[0] > 0) {
                log.info("开始保存分组配置，成功数据：{}条", successCount[0]);
                
                // 转换为GroupConfigDTO
                com.rc.admin.groupconfig.dto.GroupConfigDTO groupConfigDTO = new com.rc.admin.groupconfig.dto.GroupConfigDTO();
                groupConfigDTO.setDoubleRateName(dto.getDoubleRateName());
                groupConfigDTO.setRegionCodes(dto.getRegionCodes());
                groupConfigDTO.setCountryCodes(dto.getCountryCodes());
                groupConfigDTO.setModelCheckItems(new ArrayList<>());
                
                // 转换物模型检查项
                for (ImportGroupConfigDTO.ModelCheckItemDTO item : dto.getModelCheckItems()) {
                    com.rc.admin.groupconfig.dto.GroupConfigDTO.ModelCheckItemDTO newItem = 
                        new com.rc.admin.groupconfig.dto.GroupConfigDTO.ModelCheckItemDTO();
                    newItem.setModelId(item.getModelId());
                    newItem.setParamCode(item.getParamCode());
                    groupConfigDTO.getModelCheckItems().add(newItem);
                }
                
                try {
                    groupConfigService.add(groupConfigDTO);
                    log.info("分组配置保存成功");
                } catch (Exception e) {
                    log.error("保存分组配置失败", e);
                    throw new RuntimeException("保存分组配置失败：" + e.getMessage(), e);
                }
            } else {
                log.warn("没有成功解析的数据，跳过保存步骤");
            }

            // 设置结果
            result.setSuccessCount(successCount[0]);
            result.setErrorCount(errorCount[0]);
            result.setErrorList(errorList);

        } catch (Exception e) {
            log.error("导入分组配置失败", e);
            // 返回更详细的错误信息
            throw new RuntimeException("导入失败：" + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 校验导入行数据
     */
    private void validateImportRow(ImportGroupConfigDTO.ImportRowData data) {
        if (data.getModelId() == null || data.getModelId().trim().isEmpty()) {
            throw new RuntimeException("物模型ID不能为空");
        }
        if (data.getParamCode() == null || data.getParamCode().trim().isEmpty()) {
            throw new RuntimeException("检查项编码不能为空");
        }
    }
}
