package com.rc.admin.groupconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分组详情VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "分组详情VO", description = "分组详情返回参数")
public class GroupConfigDetailVO {

    @ApiModelProperty("分组标识")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    private String doubleRateName;

    @ApiModelProperty("大区代码列表")
    private List<String> regionCodes;

    @ApiModelProperty("大区名称列表")
    private List<String> regionNames;

    @ApiModelProperty("国家代码列表")
    private List<String> countryCodes;

    @ApiModelProperty("国家名称列表")
    private List<String> countryNames;

    @ApiModelProperty("配置明细列表")
    private List<ConfigDetailItemVO> configItems;

    @Data
    @ApiModel(value = "配置明细项")
    public static class ConfigDetailItemVO {
        @ApiModelProperty("物模型ID")
        private String modelId;

        @ApiModelProperty("物模型名称")
        private String modelName;

        @ApiModelProperty("检查项编码")
        private String paramCode;

        @ApiModelProperty("检查项名称")
        private String paramName;
    }
}
