package com.rc.admin.groupconfig.service;

import com.rc.admin.groupconfig.dto.ImportGroupConfigDTO;
import com.rc.admin.groupconfig.vo.ExportGroupConfigVO;
import com.rc.admin.groupconfig.vo.ImportResultVO;
import com.rc.admin.groupconfig.vo.TemplateVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分组配置导入导出服务接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public interface GroupConfigImportExportService {

    /**
     * 导出分组配置
     *
     * @param response HTTP响应
     */
    void exportGroupConfig(HttpServletResponse response);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入分组配置
     *
     * @param dto 导入参数
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResultVO importGroupConfig(ImportGroupConfigDTO dto, MultipartFile file);
}
