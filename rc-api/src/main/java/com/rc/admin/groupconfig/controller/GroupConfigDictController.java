package com.rc.admin.groupconfig.controller;

import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.service.GroupConfigDictService;
import com.rc.admin.groupconfig.vo.OptionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分组配置数据字典控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "分组配置数据字典")
@RestController
@RequestMapping("/api/groupConfig/dict")
public class GroupConfigDictController {

    @Autowired
    private GroupConfigDictService groupConfigDictService;

    @ApiOperation("获取大区列表")
    @GetMapping("/regionList")
    public Response getRegionList() {
        try {
            List<OptionVO> result = groupConfigDictService.getRegionList();
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取大区列表失败", e);
            return Response.failError("获取大区列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据大区获取国家列表")
    @GetMapping("/countryList")
    public Response getCountryListByRegion(@ApiParam("大区代码") @RequestParam(required = false) String regionCode) {
        try {
            List<OptionVO> result = groupConfigDictService.getCountryListByRegion(regionCode);
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取国家列表失败", e);
            return Response.failError("获取国家列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取所有国家列表")
    @GetMapping("/allCountryList")
    public Response getAllCountryList() {
        try {
            List<OptionVO> result = groupConfigDictService.getAllCountryList();
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取所有国家列表失败", e);
            return Response.failError("获取所有国家列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据关键字搜索物模型列表")
    @GetMapping("/modelList")
    public Response getModelListByKeyword(@ApiParam("搜索关键字") @RequestParam(required = false) String keyword) {
        try {
            List<OptionVO> result = groupConfigDictService.getModelListByKeyword(keyword);
            return Response.success(result);
        } catch (Exception e) {
            log.error("搜索物模型列表失败", e);
            return Response.failError("搜索物模型列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取物模型列表")
    @GetMapping("/modelListAll")
    public Response getModelList() {
        try {
            List<OptionVO> result = groupConfigDictService.getModelList();
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取物模型列表失败", e);
            return Response.failError("获取物模型列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据物模型获取检查项列表")
    @GetMapping("/checkItemList")
    public Response getCheckItemListByModel(@ApiParam("物模型ID") @RequestParam String modelId) {
        try {
            List<OptionVO> result = groupConfigDictService.getCheckItemListByModel(modelId);
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取检查项列表失败", e);
            return Response.failError("获取检查项列表失败：" + e.getMessage());
        }
    }
}
