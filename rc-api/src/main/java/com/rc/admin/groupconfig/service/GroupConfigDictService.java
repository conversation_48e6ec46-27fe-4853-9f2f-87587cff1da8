package com.rc.admin.groupconfig.service;

import com.rc.admin.groupconfig.vo.OptionVO;

import java.util.List;

/**
 * 分组配置数据字典服务接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public interface GroupConfigDictService {

    /**
     * 获取大区列表
     *
     * @return 大区列表
     */
    List<OptionVO> getRegionList();

    /**
     * 根据大区获取国家列表
     *
     * @param regionCode 大区代码
     * @return 国家列表
     */
    List<OptionVO> getCountryListByRegion(String regionCode);

    /**
     * 获取所有国家列表
     *
     * @return 国家列表
     */
    List<OptionVO> getAllCountryList();

    /**
     * 根据关键字搜索物模型列表
     *
     * @param keyword 搜索关键字
     * @return 物模型列表
     */
    List<OptionVO> getModelListByKeyword(String keyword);

    /**
     * 获取物模型列表
     *
     * @return 物模型列表
     */
    List<OptionVO> getModelList();

    /**
     * 根据物模型获取检查项列表
     *
     * @param modelId 物模型ID
     * @return 检查项列表
     */
    List<OptionVO> getCheckItemListByModel(String modelId);
}
