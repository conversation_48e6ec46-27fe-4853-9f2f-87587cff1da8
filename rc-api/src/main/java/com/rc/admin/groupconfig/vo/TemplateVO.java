package com.rc.admin.groupconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 导入模板VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "导入模板VO", description = "导入模板返回参数")
public class TemplateVO {

    @ExcelProperty("物模型ID")
    @ApiModelProperty("物模型ID")
    private String modelId;

    @ExcelProperty("检查项编码")
    @ApiModelProperty("检查项编码")
    private String paramCode;
}
