package com.rc.admin.groupconfig.service.impl;

import com.rc.admin.groupconfig.dao.GroupConfigDictMapper;
import com.rc.admin.groupconfig.service.GroupConfigDictService;
import com.rc.admin.groupconfig.vo.OptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.ArrayList;

/**
 * 分组配置数据字典服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
public class GroupConfigDictServiceImpl implements GroupConfigDictService {

    @Autowired
    private GroupConfigDictMapper groupConfigDictMapper;

    @Override
    public List<OptionVO> getRegionList() {
        return groupConfigDictMapper.selectRegionList();
    }

    @Override
    public List<OptionVO> getCountryListByRegion(String regionCode) {
        if (!StringUtils.hasText(regionCode)) {
            return getAllCountryList();
        }
        return groupConfigDictMapper.selectCountryListByRegion(regionCode);
    }

    @Override
    public List<OptionVO> getAllCountryList() {
        return groupConfigDictMapper.selectAllCountryList();
    }

    @Override
    public List<OptionVO> getModelListByKeyword(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return getModelList();
        }
        return groupConfigDictMapper.selectModelListByKeyword(keyword);
    }

    @Override
    public List<OptionVO> getModelList() {
        return groupConfigDictMapper.selectModelList();
    }

    @Override
    public List<OptionVO> getCheckItemListByModel(String modelId) {
        if (!StringUtils.hasText(modelId)) {
            return new ArrayList<>();
        }
        return groupConfigDictMapper.selectCheckItemListByModel(modelId);
    }
}
