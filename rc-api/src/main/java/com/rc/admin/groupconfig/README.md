# 分组配置模块

## 模块概述

分组配置模块是WDCL RC Tool API系统的核心业务模块，用于管理设备数据质量统计的分组配置。支持按大区、国家、物模型和检查项进行灵活分组配置，为首页双率（准确率/完整率）统计展示提供数据基础。

## 功能特性

### ✅ 已完成功能

#### TASK001 - 基础架构搭建
- [x] 创建模块目录结构
- [x] 适配现有数据表
- [x] 创建基础实体类
- [x] 创建基础接口定义
- [x] 创建分组标识生成服务

#### TASK002 - 基础CRUD实现
- [x] 实现Service业务逻辑层
- [x] 实现Controller控制器
- [x] 创建DTO和VO类
- [x] 实现基础数据校验
- [x] 集成分组标识生成服务

#### TASK003 - 数据字典集成
- [x] 集成数据字典服务
- [x] 实现基础API接口

#### TASK004 - 物模型检查项配置
- [x] 实现物模型下拉接口
- [x] 实现检查项联动接口
- [x] 实现动态配置表单
- [x] 实现组合唯一性校验

#### TASK005 - 导入导出功能
- [x] 实现Excel导出功能
- [x] 实现Excel导入功能
- [x] 实现数据校验机制
- [x] 实现错误处理机制

#### TASK006 - 分组详情接口
- [x] 实现分组详情查询接口
- [x] 实现详情数据接口
- [x] 实现明细数据筛选
- [x] 实现详情页导出

#### TASK007 - 首页统计集成
- [x] 实现分组统计数据接口
- [x] 实现统计数据接口
- [x] 实现统计查询优化
- [x] 实现统计监控告警

### 🔄 待完成功能

#### TASK008 - 性能优化与监控
- [ ] 实现数据库查询优化
- [ ] 添加必要索引
- [ ] 优化复杂查询SQL

#### TASK009 - 系统集成测试
- [ ] 执行完整功能测试
- [ ] 执行性能压力测试
- [ ] 执行安全性测试
- [ ] 准备生产环境部署

## 技术架构

### 后端架构
```
rc-api/src/main/java/com/rc/admin/groupconfig/
├── controller/           # REST API控制器
│   ├── GroupConfigController.java              # 分组配置管理
│   ├── GroupConfigDictController.java          # 数据字典
│   ├── GroupConfigImportExportController.java  # 导入导出
│   ├── GroupConfigDetailController.java        # 分组详情
│   └── GroupConfigStatsController.java         # 统计接口
├── service/             # 业务逻辑层
│   ├── GroupConfigService.java                 # 分组配置服务
│   ├── GroupConfigDictService.java             # 数据字典服务
│   ├── GroupConfigImportExportService.java     # 导入导出服务
│   ├── GroupConfigDetailService.java           # 分组详情服务
│   ├── GroupConfigStatsService.java            # 统计服务
│   ├── ModelCheckItemConfigService.java        # 物模型检查项配置服务
│   ├── GroupConfigIdGeneratorService.java      # 分组标识生成服务
│   └── impl/            # 服务实现类
├── dao/                 # 数据访问层
│   ├── OrsCountryDoubleRateConfigMapper.java   # 主表Mapper
│   ├── OrsDoubleRateConfigMapper.java          # 明细表Mapper
│   └── GroupConfigDictMapper.java              # 数据字典Mapper
├── entity/              # 实体类
│   ├── OrsCountryDoubleRateConfig.java         # 主表实体
│   └── OrsDoubleRateConfig.java                # 明细表实体
├── dto/                 # 数据传输对象
│   ├── GroupConfigDTO.java                     # 分组配置DTO
│   ├── ImportGroupConfigDTO.java               # 导入DTO
│   └── ModelCheckItemConfigDTO.java            # 物模型检查项配置DTO
├── vo/                  # 视图对象
│   ├── GroupConfigVO.java                      # 分组配置VO
│   ├── GroupConfigDetailVO.java                # 分组详情VO
│   ├── GroupConfigStatsVO.java                 # 分组统计VO
│   ├── OptionVO.java                           # 通用选项VO
│   ├── ImportResultVO.java                     # 导入结果VO
│   ├── ExportGroupConfigVO.java                # 导出VO
│   └── TemplateVO.java                         # 模板VO
└── util/                # 工具类
    └── GroupConfigValidator.java               # 校验工具类
```

### 数据库设计

基于现有系统表结构，使用以下数据表：

```sql
-- 分组配置主表（现有表）
CREATE TABLE "dqm"."ors_country_double_rate_config" (
  "country_code" varchar(255) COMMENT '国家code（多个逗号分隔）',
  "region_code" varchar(255) COMMENT '大区code（多个逗号分隔）',
  "double_rate_sign" varchar(10) COMMENT '分组标志',
  "double_rate_name" varchar(50) COMMENT '分组名称',
  CONSTRAINT "ors_country_double_rate_config_pkey" PRIMARY KEY ("double_rate_sign","country_code","region_code")
);

-- 分组配置明细表（现有表）
CREATE TABLE "dqm"."ors_double_rate_config" (
  "model_id" varchar(30) COMMENT '物模型ID',
  "param_code" int4 COMMENT '属性编码',
  "double_rate_sign" varchar(10) COMMENT '分组标志',
  CONSTRAINT "ors_double_rate_config_pkey" PRIMARY KEY ("double_rate_sign","model_id","param_code")
);
```

## API接口

### 分组管理接口
- `POST /api/groupConfig/add` - 新增分组（返回新增结果）
- `POST /api/groupConfig/edit` - 编辑分组  
- `POST /api/groupConfig/delete` - 删除分组
- `POST /api/groupConfig/list` - 分页查询分组
- `GET /api/groupConfig/get/{id}` - 查看分组配置（基本信息）
- `GET /api/groupConfig/detail/detail/{id}` - 查看分组详情（包含大区、国家、物模型检查项等详细信息）

### 导入导出接口
- `GET /api/groupConfig/importExport/export` - 导出分组配置
- `POST /api/groupConfig/importExport/import` - 导入分组配置
- `GET /api/groupConfig/importExport/template` - 下载导入模板

### 下拉数据接口  
- `GET /api/groupConfig/dict/regionList` - 大区下拉列表
- `GET /api/groupConfig/dict/countryList` - 国家下拉列表
- `GET /api/groupConfig/dict/modelList` - 物模型下拉列表
- `GET /api/groupConfig/dict/checkItemList` - 检查项下拉列表

### 首页统计接口
- `GET /api/groupConfig/stats/groups` - 可用分组列表
- `GET /api/groupConfig/stats/data` - 分组统计数据

## 核心功能

### 1. 分组标识自动生成
- 使用`ReentrantLock`确保并发安全
- 从数字1开始递增
- 支持数据库事务和错误重试机制

### 2. 物模型检查项配置
- 支持动态添加/删除配置行
- 物模型+检查项组合唯一性校验
- 联动查询优化

### 3. 导入导出功能
- 使用EasyExcel进行Excel操作
- 支持标准模板下载
- 完整的错误处理和结果反馈

### 4. 数据字典集成
- 大区、国家、物模型、检查项数据源集成
- 支持关键字搜索和联动查询
- 统一的OptionVO格式返回

## 业务规则

### 数据验证规则
- 分组名称：必填，≤20字，全局唯一
- 大区选择：至少选择1个大区
- 国家选择：至少选择1个国家
- 物模型检查项：至少配置1组，组合不可重复

### 性能要求
- 分组列表查询响应时间 < 500ms
- 分组保存响应时间 ≤ 2秒
- 首页双率数据加载 ≤ 1秒
- 支持100个并发用户操作

## 部署说明

### 环境要求
- JDK 8+
- Spring Boot 2.7.3
- MyBatis Plus 3.5.2
- MySQL 8.0+

### 配置说明
- 数据库连接配置在`application.yml`中
- 分组标识生成使用分布式锁机制
- Excel导入导出使用EasyExcel库

## 开发说明

### 代码规范
- 遵循项目现有的代码规范和注解风格
- 使用MyBatis Plus的`@TableName`、`@TableId`等注解
- 实体类包含完整的字段注释和Swagger注解
- 包含必要的审计字段(create_by, create_time等)

### 测试要求
- 单元测试覆盖率 ≥ 80%
- 接口响应时间满足性能要求
- 支持完整的端到端测试流程

## 更新日志

### v1.0.0 (2023-10-01)
- 完成基础架构搭建
- 实现基础CRUD功能
- 集成数据字典服务
- 实现物模型检查项配置
- 完成导入导出功能
- 实现分组详情接口
- 集成首页统计功能

## 联系方式

如有问题，请联系开发团队或查看项目文档。
