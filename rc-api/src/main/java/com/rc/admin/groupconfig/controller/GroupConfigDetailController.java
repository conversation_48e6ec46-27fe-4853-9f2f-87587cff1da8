package com.rc.admin.groupconfig.controller;

import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.service.GroupConfigDetailService;
import com.rc.admin.groupconfig.vo.GroupConfigDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分组详情控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "分组详情管理")
@RestController
@RequestMapping("/api/groupConfig/detail")
public class GroupConfigDetailController {

    @Autowired
    private GroupConfigDetailService groupConfigDetailService;

    @ApiOperation("获取分组详情")
    @GetMapping("/detail/{doubleRateSign}")
    public Response getGroupConfigDetail(@ApiParam("分组标识") @PathVariable String doubleRateSign) {
        try {
            GroupConfigDetailVO result = groupConfigDetailService.getGroupConfigDetail(doubleRateSign);
            if (result == null) {
                return Response.failError("分组详情不存在");
            }
            
            // 转换为API文档期望的格式
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("doubleRateSign", result.getDoubleRateSign());
            responseData.put("doubleRateName", result.getDoubleRateName());
            
            // 转换大区格式：从分离的code和name数组转换为对象数组
            List<Map<String, String>> regions = new ArrayList<>();
            if (result.getRegionCodes() != null && result.getRegionNames() != null) {
                for (int i = 0; i < result.getRegionCodes().size(); i++) {
                    Map<String, String> region = new HashMap<>();
                    region.put("code", result.getRegionCodes().get(i));
                    region.put("name", i < result.getRegionNames().size() ? result.getRegionNames().get(i) : "");
                    regions.add(region);
                }
            }
            responseData.put("regions", regions);
            
            // 转换国家格式：从分离的code和name数组转换为对象数组
            List<Map<String, String>> countries = new ArrayList<>();
            if (result.getCountryCodes() != null && result.getCountryNames() != null) {
                for (int i = 0; i < result.getCountryCodes().size(); i++) {
                    Map<String, String> country = new HashMap<>();
                    country.put("code", result.getCountryCodes().get(i));
                    country.put("name", i < result.getCountryNames().size() ? result.getCountryNames().get(i) : "");
                    countries.add(country);
                }
            }
            responseData.put("countries", countries);
            
            // 转换配置明细格式：从configItems转换为configDetails
            List<Map<String, String>> configDetails = new ArrayList<>();
            if (result.getConfigItems() != null) {
                for (GroupConfigDetailVO.ConfigDetailItemVO item : result.getConfigItems()) {
                    Map<String, String> detail = new HashMap<>();
                    detail.put("modelId", item.getModelId());
                    detail.put("modelName", item.getModelName());
                    detail.put("paramCode", item.getParamCode());
                    detail.put("paramName", item.getParamName());
                    configDetails.add(detail);
                }
            }
            responseData.put("configDetails", configDetails);
            
            return Response.success(responseData);
        } catch (Exception e) {
            log.error("获取分组详情失败", e);
            return Response.failError("获取分组详情失败：" + e.getMessage());
        }
    }
}
