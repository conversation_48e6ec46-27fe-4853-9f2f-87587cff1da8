package com.rc.admin.groupconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增分组配置结果VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "新增分组配置结果VO", description = "新增分组配置返回结果")
public class AddGroupConfigResultVO {

    @ApiModelProperty("分组标识")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    private String doubleRateName;

    @ApiModelProperty("操作结果")
    private String message;

    public AddGroupConfigResultVO(String doubleRateSign, String doubleRateName) {
        this.doubleRateSign = doubleRateSign;
        this.doubleRateName = doubleRateName;
        this.message = "新增分组配置成功";
    }
}
