package com.rc.admin.groupconfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.groupconfig.dao.OrsCountryDoubleRateConfigMapper;
import com.rc.admin.groupconfig.dao.OrsDoubleRateConfigMapper;
import com.rc.admin.groupconfig.dto.GroupConfigDTO;
import com.rc.admin.groupconfig.entity.OrsCountryDoubleRateConfig;
import com.rc.admin.groupconfig.entity.OrsDoubleRateConfig;
import com.rc.admin.groupconfig.service.GroupConfigService;
import com.rc.admin.groupconfig.service.GroupConfigIdGeneratorService;
import com.rc.admin.groupconfig.service.GroupConfigDetailService;
import com.rc.admin.groupconfig.vo.GroupConfigVO;
import com.rc.admin.groupconfig.vo.GroupConfigDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import cn.hutool.core.util.StrUtil;
import com.rc.admin.divisionalproductgroup.util.ExcelImportExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分组配置服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
@Transactional
public class GroupConfigServiceImpl implements GroupConfigService {

    @Autowired
    private OrsCountryDoubleRateConfigMapper countryDoubleRateConfigMapper;

    @Autowired
    private OrsDoubleRateConfigMapper doubleRateConfigMapper;

    @Autowired
    private GroupConfigIdGeneratorService idGeneratorService;

    @Autowired
    private GroupConfigDetailService groupConfigDetailService;

    @Override
    public String add(GroupConfigDTO dto) {
        // 1. 校验分组名称唯一性
        validateGroupNameUnique(dto.getDoubleRateName(), null);

        // 2. 生成分组标识
        String doubleRateSign = idGeneratorService.generateNextGroupId();

        // 3. 保存主表数据
        saveMainConfig(dto, doubleRateSign);

        // 4. 保存明细表数据
        saveDetailConfig(dto.getModelCheckItems(), doubleRateSign);

        log.info("新增分组配置成功，分组标识：{}", doubleRateSign);
        
        // 返回新增的分组标识
        return doubleRateSign;
    }

    @Override
    public void edit(GroupConfigDTO dto) {
        // 1. 校验分组标识
        if (StrUtil.isBlank(dto.getDoubleRateSign())) {
            throw new IllegalArgumentException("编辑时分组标识不能为空");
        }
        
        // 2. 校验分组是否存在
        QueryWrapper<OrsCountryDoubleRateConfig> existWrapper = new QueryWrapper<>();
        existWrapper.eq("double_rate_sign", dto.getDoubleRateSign());
        OrsCountryDoubleRateConfig existEntity = countryDoubleRateConfigMapper.selectOne(existWrapper);
        if (existEntity == null) {
            throw new IllegalArgumentException("分组不存在，分组标识：" + dto.getDoubleRateSign());
        }
        
        // 3. 校验分组名称唯一性（排除自身）
        validateGroupNameUnique(dto.getDoubleRateName(), dto.getDoubleRateSign());
        
        // 4. 删除原有的主表数据
        QueryWrapper<OrsCountryDoubleRateConfig> mainDeleteWrapper = new QueryWrapper<>();
        mainDeleteWrapper.eq("double_rate_sign", dto.getDoubleRateSign());
        countryDoubleRateConfigMapper.delete(mainDeleteWrapper);
        
        // 5. 删除原有的明细表数据
        QueryWrapper<OrsDoubleRateConfig> detailDeleteWrapper = new QueryWrapper<>();
        detailDeleteWrapper.eq("double_rate_sign", dto.getDoubleRateSign());
        doubleRateConfigMapper.delete(detailDeleteWrapper);
        
        // 6. 保存新的主表数据
        saveMainConfig(dto, dto.getDoubleRateSign());
        
        // 7. 保存新的明细表数据
        saveDetailConfig(dto.getModelCheckItems(), dto.getDoubleRateSign());
        
        log.info("编辑分组配置成功，分组标识：{}", dto.getDoubleRateSign());
    }

    @Override
    public void delete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("删除的分组标识不能为空");
        }

        for (String id : ids) {
            // 删除主表数据
            QueryWrapper<OrsCountryDoubleRateConfig> mainWrapper = new QueryWrapper<>();
            mainWrapper.eq("double_rate_sign", id);
            countryDoubleRateConfigMapper.delete(mainWrapper);

            // 删除明细表数据
            QueryWrapper<OrsDoubleRateConfig> detailWrapper = new QueryWrapper<>();
            detailWrapper.eq("double_rate_sign", id);
            doubleRateConfigMapper.delete(detailWrapper);
        }

        log.info("删除分组配置成功，分组标识：{}", ids);
    }

    @Override
    public Page<GroupConfigVO> pageList(GroupConfigDTO dto, int pageNum, int pageSize) {
        Page<OrsCountryDoubleRateConfig> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
        if (StringUtils.hasText(dto.getDoubleRateName())) {
            wrapper.like("double_rate_name", dto.getDoubleRateName());
        }
        wrapper.orderByDesc("double_rate_sign");

        // 执行分页查询
        Page<OrsCountryDoubleRateConfig> resultPage = countryDoubleRateConfigMapper.selectPage(page, wrapper);

        // 转换为VO
        Page<GroupConfigVO> voPage = new Page<>();
        BeanUtils.copyProperties(resultPage, voPage);

        List<GroupConfigVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public GroupConfigVO getByDoubleRateSign(String doubleRateSign) {
        QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_sign", doubleRateSign);
        OrsCountryDoubleRateConfig entity = countryDoubleRateConfigMapper.selectOne(wrapper);

        if (entity == null) {
            return null;
        }

        return convertToVO(entity);
    }

    /**
     * 校验分组名称唯一性
     */
    private void validateGroupNameUnique(String groupName, String excludeId) {
        QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_name", groupName);
        if (excludeId != null) {
            wrapper.ne("double_rate_sign", excludeId);
        }

        Long count = countryDoubleRateConfigMapper.selectCount(wrapper);
        if (count > 0) {
            throw new RuntimeException("分组名称已存在");
        }
    }

    /**
     * 保存主表配置
     */
    private void saveMainConfig(GroupConfigDTO dto, String doubleRateSign) {
        // 创建一条记录，大区和国家用逗号分隔存储
        OrsCountryDoubleRateConfig entity = new OrsCountryDoubleRateConfig();
        entity.setDoubleRateSign(doubleRateSign);
        entity.setDoubleRateName(dto.getDoubleRateName());
        
        // 将大区列表转换为逗号分隔的字符串
        String regionCodesStr = String.join(",", dto.getRegionCodes());
        entity.setRegionCode(regionCodesStr);
        
        // 将国家列表转换为逗号分隔的字符串
        String countryCodesStr = String.join(",", dto.getCountryCodes());
        entity.setCountryCode(countryCodesStr);
        

        countryDoubleRateConfigMapper.insert(entity);
    }

    /**
     * 保存明细表配置
     */
    private void saveDetailConfig(List<GroupConfigDTO.ModelCheckItemDTO> items, String doubleRateSign) {
        for (GroupConfigDTO.ModelCheckItemDTO item : items) {
            OrsDoubleRateConfig entity = new OrsDoubleRateConfig();
            entity.setDoubleRateSign(doubleRateSign);
            entity.setModelId(item.getModelId());
            entity.setParamCode(Integer.parseInt(item.getParamCode()));

            doubleRateConfigMapper.insert(entity);
        }
    }

    /**
     * 转换为VO
     */
    private GroupConfigVO convertToVO(OrsCountryDoubleRateConfig entity) {
        GroupConfigVO vo = new GroupConfigVO();
        BeanUtils.copyProperties(entity, vo);

        // 查询统计数量
        String doubleRateSign = entity.getDoubleRateSign();
        
        // 计算大区数量 - 解析逗号分隔的字符串
        int regionCount = 0;
        if (StringUtils.hasText(entity.getRegionCode())) {
            regionCount = entity.getRegionCode().split(",").length;
        }
        vo.setRegionCount(regionCount);

        // 计算国家数量 - 解析逗号分隔的字符串
        int countryCount = 0;
        if (StringUtils.hasText(entity.getCountryCode())) {
            countryCount = entity.getCountryCode().split(",").length;
        }
        vo.setCountryCount(countryCount);

        // 查询模型数量
        QueryWrapper<OrsDoubleRateConfig> modelWrapper = new QueryWrapper<>();
        modelWrapper.eq("double_rate_sign", doubleRateSign);
        modelWrapper.select("DISTINCT model_id");
        Long modelCount = doubleRateConfigMapper.selectCount(modelWrapper);
        vo.setModelCount(modelCount.intValue());

        // 查询检查项数量
        QueryWrapper<OrsDoubleRateConfig> checkItemWrapper = new QueryWrapper<>();
        checkItemWrapper.eq("double_rate_sign", doubleRateSign);
        Long checkItemCount = doubleRateConfigMapper.selectCount(checkItemWrapper);
        vo.setCheckItemCount(checkItemCount.intValue());
        return vo;
    }

    @Override
    public void exportGroupConfigDetail(String doubleRateSign, HttpServletResponse response) {
        try {
            // 1. 校验分组标识
            if (StrUtil.isBlank(doubleRateSign)) {
                throw new IllegalArgumentException("分组标识不能为空");
            }
            
            // 2. 获取分组详情数据
            GroupConfigDetailVO detailVO = groupConfigDetailService.getGroupConfigDetail(doubleRateSign);
            if (detailVO == null) {
                throw new IllegalArgumentException("分组不存在，分组标识：" + doubleRateSign);
            }
            
            // 3. 转换为扁平化的导出数据
            List<GroupConfigDetailExportVO> exportList = new ArrayList<>();
            
            // 为每个配置明细项创建一条导出记录
            if (detailVO.getConfigItems() != null && !detailVO.getConfigItems().isEmpty()) {
                for (GroupConfigDetailVO.ConfigDetailItemVO configItem : detailVO.getConfigItems()) {
                    GroupConfigDetailExportVO exportVO = new GroupConfigDetailExportVO();
                    exportVO.setDoubleRateSign(detailVO.getDoubleRateSign());
                    exportVO.setDoubleRateName(detailVO.getDoubleRateName());
                    
                    // 大区信息（逗号分隔）
                    if (detailVO.getRegionCodes() != null) {
                        exportVO.setRegionCodes(String.join(",", detailVO.getRegionCodes()));
                    }
                    if (detailVO.getRegionNames() != null) {
                        exportVO.setRegionNames(String.join(",", detailVO.getRegionNames()));
                    }
                    
                    // 国家信息（逗号分隔）
                    if (detailVO.getCountryCodes() != null) {
                        exportVO.setCountryCodes(String.join(",", detailVO.getCountryCodes()));
                    }
                    if (detailVO.getCountryNames() != null) {
                        exportVO.setCountryNames(String.join(",", detailVO.getCountryNames()));
                    }
                    
                    // 配置明细信息
                    exportVO.setModelId(configItem.getModelId());
                    exportVO.setModelName(configItem.getModelName());
                    exportVO.setParamCode(configItem.getParamCode());
                    exportVO.setParamName(configItem.getParamName());
                    
                    exportList.add(exportVO);
                }
            } else {
                // 如果没有配置明细，至少导出基本信息
                GroupConfigDetailExportVO exportVO = new GroupConfigDetailExportVO();
                exportVO.setDoubleRateSign(detailVO.getDoubleRateSign());
                exportVO.setDoubleRateName(detailVO.getDoubleRateName());
                
                if (detailVO.getRegionCodes() != null) {
                    exportVO.setRegionCodes(String.join(",", detailVO.getRegionCodes()));
                }
                if (detailVO.getRegionNames() != null) {
                    exportVO.setRegionNames(String.join(",", detailVO.getRegionNames()));
                }
                if (detailVO.getCountryCodes() != null) {
                    exportVO.setCountryCodes(String.join(",", detailVO.getCountryCodes()));
                }
                if (detailVO.getCountryNames() != null) {
                    exportVO.setCountryNames(String.join(",", detailVO.getCountryNames()));
                }
                
                exportList.add(exportVO);
            }
            
            // 4. 定义导出的表头和字段
            String[] headers = {
                "分组标识",
                "分组名称", 
                "大区代码",
                "大区名称",
                "国家代码",
                "国家名称",
                "物模型ID",
                "物模型名称",
                "检查项编码",
                "检查项名称"
            };
            
            String[] fields = {
                "doubleRateSign",
                "doubleRateName",
                "regionCodes",
                "regionNames", 
                "countryCodes",
                "countryNames",
                "modelId",
                "modelName",
                "paramCode",
                "paramName"
            };
            
            // 5. 设置文件名
            String fileName = "分组配置明细_" + doubleRateSign + "_" + System.currentTimeMillis() + ".xlsx";
            
            // 6. 使用工具类导出Excel
            ExcelImportExportUtil<GroupConfigDetailExportVO> excelUtil = new ExcelImportExportUtil<>();
            excelUtil.exportExcel(exportList, headers, fields, fileName, response);
            
            log.info("导出分组配置明细成功，分组标识：{}，导出记录数：{}", doubleRateSign, exportList.size());
            
        } catch (Exception e) {
            log.error("导出分组配置明细失败，分组标识：{}", doubleRateSign, e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 分组配置明细导出VO（内部类）
     */
    public static class GroupConfigDetailExportVO {
        private String doubleRateSign;     // 分组标识
        private String doubleRateName;     // 分组名称
        private String regionCodes;        // 大区代码（逗号分隔）
        private String regionNames;        // 大区名称（逗号分隔）
        private String countryCodes;       // 国家代码（逗号分隔）
        private String countryNames;       // 国家名称（逗号分隔）
        private String modelId;            // 物模型ID
        private String modelName;          // 物模型名称
        private String paramCode;          // 检查项编码
        private String paramName;          // 检查项名称

        // Getters and Setters
        public String getDoubleRateSign() { return doubleRateSign; }
        public void setDoubleRateSign(String doubleRateSign) { this.doubleRateSign = doubleRateSign; }
        
        public String getDoubleRateName() { return doubleRateName; }
        public void setDoubleRateName(String doubleRateName) { this.doubleRateName = doubleRateName; }
        
        public String getRegionCodes() { return regionCodes; }
        public void setRegionCodes(String regionCodes) { this.regionCodes = regionCodes; }
        
        public String getRegionNames() { return regionNames; }
        public void setRegionNames(String regionNames) { this.regionNames = regionNames; }
        
        public String getCountryCodes() { return countryCodes; }
        public void setCountryCodes(String countryCodes) { this.countryCodes = countryCodes; }
        
        public String getCountryNames() { return countryNames; }
        public void setCountryNames(String countryNames) { this.countryNames = countryNames; }
        
        public String getModelId() { return modelId; }
        public void setModelId(String modelId) { this.modelId = modelId; }
        
        public String getModelName() { return modelName; }
        public void setModelName(String modelName) { this.modelName = modelName; }
        
        public String getParamCode() { return paramCode; }
        public void setParamCode(String paramCode) { this.paramCode = paramCode; }
        
        public String getParamName() { return paramName; }
        public void setParamName(String paramName) { this.paramName = paramName; }
    }
}
