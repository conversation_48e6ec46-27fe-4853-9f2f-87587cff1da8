package com.rc.admin.groupconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 导入结果VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "导入结果VO", description = "导入结果返回参数")
public class ImportResultVO {

    @ApiModelProperty("成功数量")
    private Integer successCount;

    @ApiModelProperty("失败数量")
    private Integer errorCount;

    @ApiModelProperty("错误列表")
    private List<ImportErrorDTO> errorList;

    @Data
    @ApiModel(value = "导入错误信息")
    public static class ImportErrorDTO {
        @ApiModelProperty("行号")
        private Integer rowNum;

        @ApiModelProperty("错误信息")
        private String errorMessage;

        @ApiModelProperty("原始数据")
        private String originalData;
    }
}
