package com.rc.admin.groupconfig.controller;

import com.rc.admin.common.core.util.Response;
import com.rc.admin.groupconfig.service.GroupConfigStatsService;
import com.rc.admin.groupconfig.vo.GroupConfigVO;
import com.rc.admin.groupconfig.vo.GroupStatsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分组配置统计控制器
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Api(tags = "分组配置统计")
@RestController
@RequestMapping("/api/groupConfig/stats")
public class GroupConfigStatsController {

    @Autowired
    private GroupConfigStatsService groupConfigStatsService;

    @ApiOperation("获取可用分组列表")
    @GetMapping("/groups")
    public Response getAvailableGroups() {
        try {
            List<GroupConfigVO> result = groupConfigStatsService.getAvailableGroups();
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取可用分组列表失败", e);
            return Response.failError("获取可用分组列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取分组统计数据")
    @GetMapping("/data")
    public Response getGroupStats(@ApiParam("分组标识") @RequestParam String groupId) {
        try {
            GroupStatsVO result = groupConfigStatsService.getGroupStats(groupId);
            if (result == null) {
                return Response.failError("分组统计数据不存在");
            }
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取分组统计数据失败", e);
            return Response.failError("获取分组统计数据失败：" + e.getMessage());
        }
    }
}
