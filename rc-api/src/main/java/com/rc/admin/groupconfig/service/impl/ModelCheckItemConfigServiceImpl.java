package com.rc.admin.groupconfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.groupconfig.dao.OrsDoubleRateConfigMapper;
import com.rc.admin.groupconfig.dto.ModelCheckItemConfigDTO;
import com.rc.admin.groupconfig.entity.OrsDoubleRateConfig;
import com.rc.admin.groupconfig.service.ModelCheckItemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物模型检查项配置服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Slf4j
@Service
@Transactional
public class ModelCheckItemConfigServiceImpl implements ModelCheckItemConfigService {

    @Autowired
    private OrsDoubleRateConfigMapper doubleRateConfigMapper;

    @Override
    public void saveConfig(ModelCheckItemConfigDTO dto) {
        // 1. 删除原有配置
        deleteConfig(dto.getDoubleRateSign());

        // 2. 保存新配置
        for (ModelCheckItemConfigDTO.ConfigItemDTO item : dto.getConfigItems()) {
            // 校验配置项
            validateConfigItem(dto.getDoubleRateSign(), item);

            // 创建配置实体
            OrsDoubleRateConfig entity = new OrsDoubleRateConfig();
            entity.setDoubleRateSign(dto.getDoubleRateSign());
            entity.setModelId(item.getModelId());
            entity.setParamCode(Integer.parseInt(item.getParamCode()));

            doubleRateConfigMapper.insert(entity);
        }

        log.info("保存物模型检查项配置成功，分组标识：{}", dto.getDoubleRateSign());
    }

    @Override
    public List<ModelCheckItemConfigDTO.ConfigItemDTO> getConfig(String doubleRateSign) {
        QueryWrapper<OrsDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_sign", doubleRateSign);
        List<OrsDoubleRateConfig> entities = doubleRateConfigMapper.selectList(wrapper);

        return entities.stream()
                .map(this::convertToConfigItem)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteConfig(String doubleRateSign) {
        QueryWrapper<OrsDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_sign", doubleRateSign);
        doubleRateConfigMapper.delete(wrapper);
    }

    /**
     * 校验配置项
     */
    private void validateConfigItem(String doubleRateSign, ModelCheckItemConfigDTO.ConfigItemDTO item) {
        // 检查物模型+检查项组合是否重复
        QueryWrapper<OrsDoubleRateConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("double_rate_sign", doubleRateSign);
        wrapper.eq("model_id", item.getModelId());
        wrapper.eq("param_code", Integer.parseInt(item.getParamCode()));

        Long count = doubleRateConfigMapper.selectCount(wrapper);
        if (count > 0) {
            throw new RuntimeException("物模型" + item.getModelId() + "与检查项" + item.getParamCode() + "的组合已存在");
        }
    }

    /**
     * 转换为配置项DTO
     */
    private ModelCheckItemConfigDTO.ConfigItemDTO convertToConfigItem(OrsDoubleRateConfig entity) {
        ModelCheckItemConfigDTO.ConfigItemDTO dto = new ModelCheckItemConfigDTO.ConfigItemDTO();
        dto.setModelId(entity.getModelId());
        dto.setParamCode(String.valueOf(entity.getParamCode()));
        return dto;
    }
}
