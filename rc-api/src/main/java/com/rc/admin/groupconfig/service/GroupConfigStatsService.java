package com.rc.admin.groupconfig.service;

import com.rc.admin.groupconfig.vo.GroupConfigVO;
import com.rc.admin.groupconfig.vo.GroupStatsVO;

import java.util.List;

/**
 * 分组配置统计服务接口
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
public interface GroupConfigStatsService {

    /**
     * 获取可用分组列表
     *
     * @return 分组列表
     */
    List<GroupConfigVO> getAvailableGroups();

    /**
     * 获取分组统计数据
     *
     * @param doubleRateSign 分组标识
     * @return 统计数据
     */
    GroupStatsVO getGroupStats(String doubleRateSign);
}
