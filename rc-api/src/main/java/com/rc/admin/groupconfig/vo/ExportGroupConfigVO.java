package com.rc.admin.groupconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 导出分组配置VO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "导出分组配置VO", description = "导出分组配置返回参数")
public class ExportGroupConfigVO {

    @ExcelProperty("分组名称")
    @ApiModelProperty("分组名称")
    private String doubleRateName;

    @ExcelProperty("大区名称列表")
    @ApiModelProperty("大区名称列表")
    private String regionNames;

    @ExcelProperty("国家名称列表")
    @ApiModelProperty("国家名称列表")
    private String countryNames;

    @ExcelProperty("物模型ID")
    @ApiModelProperty("物模型ID")
    private String modelId;

    @ExcelProperty("物模型名称")
    @ApiModelProperty("物模型名称")
    private String modelName;

    @ExcelProperty("检查项编码")
    @ApiModelProperty("检查项编码")
    private String paramCode;

    @ExcelProperty("检查项名称")
    @ApiModelProperty("检查项名称")
    private String paramName;
}
