package com.rc.admin.groupconfig.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物模型检查项配置DTO
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@ApiModel(value = "物模型检查项配置DTO", description = "物模型检查项配置请求参数")
public class ModelCheckItemConfigDTO {

    @ApiModelProperty("分组标识")
    @NotBlank(message = "分组标识不能为空")
    private String doubleRateSign;

    @ApiModelProperty("物模型检查项配置列表")
    @NotEmpty(message = "配置列表不能为空")
    private List<ConfigItemDTO> configItems;

    @Data
    @ApiModel(value = "配置项")
    public static class ConfigItemDTO {
        @ApiModelProperty("物模型ID")
        @NotBlank(message = "物模型ID不能为空")
        private String modelId;

        @ApiModelProperty("检查项编码")
        @NotBlank(message = "检查项编码不能为空")
        private String paramCode;
    }
}
