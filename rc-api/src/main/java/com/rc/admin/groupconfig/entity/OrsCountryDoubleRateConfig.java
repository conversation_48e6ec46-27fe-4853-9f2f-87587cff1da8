package com.rc.admin.groupconfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 分组配置主表实体
 *
 * <AUTHOR>
 * @since 2023-10-01
 */
@Data
@TableName("ors_country_double_rate_config")
@ApiModel(value = "分组配置主表", description = "分组配置主表实体")
public class OrsCountryDoubleRateConfig {

    @ApiModelProperty("国家code（多个逗号分隔）")
    private String countryCode;

    @ApiModelProperty("大区code（多个逗号分隔）")
    private String regionCode;

    @ApiModelProperty("分组标志")
    private String doubleRateSign;

    @ApiModelProperty("分组名称")
    private String doubleRateName;

}
