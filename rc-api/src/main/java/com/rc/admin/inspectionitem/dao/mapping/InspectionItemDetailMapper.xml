<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.inspectionitem.dao.InspectionItemDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.inspectionitem.entity.InspectionItemDetail">
        <id column="id" property="id" />
        <result column="inspection_item_id" property="inspectionItemId" />
        <result column="model_id" property="modelId" />
        <result column="model_name" property="modelName" />
        <result column="property_id" property="propertyId" />
        <result column="property_name" property="propertyName" />
        <result column="property_display_name" property="propertyDisplayName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, inspection_item_id, model_id, model_name, property_id, property_name, 
        property_display_name, create_time, update_time, create_by, update_by, is_deleted
    </sql>

    <!-- 批量插入明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO inspection_item_details (
            inspection_item_id, model_id, model_name, property_id, property_name, 
            property_display_name, create_time, update_time, create_by, update_by, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.inspectionItemId}, #{item.modelId}, #{item.modelName}, 
                #{item.propertyId}, #{item.propertyName}, #{item.propertyDisplayName}, 
                NOW(), NOW(), #{item.createBy}, #{item.updateBy}, 0
            )
        </foreach>
    </insert>

    <!-- 根据检查项ID查询明细列表 -->
    <select id="selectByInspectionItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM inspection_item_details 
        WHERE inspection_item_id = #{inspectionItemId} 
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据检查项ID物理删除明细 -->
    <delete id="deleteByInspectionItemId">
        DELETE FROM inspection_item_details 
        WHERE inspection_item_id = #{inspectionItemId}
    </delete>

</mapper>
