package com.rc.admin.inspectionitem.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import cn.hutool.core.util.StrUtil;

/**
 * 物模型属性下拉选项VO
 * 用于前端下拉选择组件
 *
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "ModelPropertyOptionVO", description = "物模型属性下拉选项")
public class ModelPropertyOptionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("属性名称")
    private String name;

    @ApiModelProperty("属性显示名称")
    private String displayName;

    /**
     * 获取显示名称，优先使用displayName，如果没有则使用name
     */
    public String getDisplayText() {
        return StrUtil.isNotBlank(this.displayName) ? this.displayName : this.name;
    }
}
