<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.inspectionitem.dao.InspectionItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.inspectionitem.entity.InspectionItem">
        <id column="id" property="id" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="english_name" property="englishName" />
        <result column="description" property="description" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, item_code, item_name, english_name, description, creator, data_type,create_time, update_time, is_deleted
    </sql>

    <!-- 检查检查项名称唯一性 -->
    <select id="countByItemName" resultType="int">
        SELECT COUNT(1)
        FROM inspection_items
        WHERE item_name = #{itemName}
          AND is_deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查英文名称唯一性 -->
    <select id="countByEnglishName" resultType="int">
        SELECT COUNT(1)
        FROM inspection_items
        WHERE english_name = #{englishName}
          AND is_deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查编码唯一性 -->
    <select id="countByItemCode" resultType="int">
        SELECT COUNT(1)
        FROM inspection_items
        WHERE item_code = #{itemCode}
          AND is_deleted = 0
    </select>

    <!-- 分页查询检查项（复杂条件） -->
    <select id="selectPageWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM inspection_items
        WHERE is_deleted = 0
        <if test="query.itemName != null and query.itemName != ''">
            AND item_name LIKE CONCAT('%', #{query.itemName}, '%')
        </if>
        <if test="query.itemCode != null and query.itemCode != ''">
            AND item_code = #{query.itemCode}
        </if>
        <if test="query.englishName != null and query.englishName != ''">
            AND english_name LIKE CONCAT('%', #{query.englishName}, '%')
        </if>
        <if test="query.creator != null and query.creator != ''">
            AND creator = #{query.creator}
        </if>
        <if test="query.startTime != null">
            AND create_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND create_time &lt;= #{query.endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询检查项详情 -->
    <select id="selectDetailById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM inspection_items
        WHERE id = #{id}
          AND is_deleted = 0
    </select>

    <!-- 软删除检查项 -->
    <update id="softDeleteById">
        UPDATE inspection_items
        SET is_deleted = 1,
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
          AND is_deleted = 0
    </update>

    <!-- 更新检查项 -->
    <update id="updateInspectionItem">
        UPDATE inspection_items
        SET item_name = #{itemName},
            english_name = #{englishName},
            description = #{description},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
          AND is_deleted = 0
    </update>

</mapper>
