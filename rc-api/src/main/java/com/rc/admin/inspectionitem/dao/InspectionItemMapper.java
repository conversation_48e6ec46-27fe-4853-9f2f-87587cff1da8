package com.rc.admin.inspectionitem.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.inspectionitem.dto.InspectionItemQueryDTO;
import com.rc.admin.inspectionitem.entity.InspectionItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 检查项配置数据访问层
 * 
 * <AUTHOR>
 * @since 2024
 */
@Mapper
public interface InspectionItemMapper extends BaseMapper<InspectionItem> {

    /**
     * 检查检查项名称唯一性
     * 
     * @param itemName 检查项名称
     * @param excludeId 排除的记录ID（修改时使用）
     * @return 重复记录数量
     */
    int countByItemName(@Param("itemName") String itemName, 
                       @Param("excludeId") Long excludeId);

    /**
     * 检查英文名称唯一性
     * 
     * @param englishName 英文名称
     * @param excludeId 排除的记录ID（修改时使用）
     * @return 重复记录数量
     */
    int countByEnglishName(@Param("englishName") String englishName,
                          @Param("excludeId") Long excludeId);

    /**
     * 检查编码唯一性
     * 
     * @param itemCode 检查项编码
     * @return 重复记录数量
     */
    int countByItemCode(@Param("itemCode") String itemCode);

    /**
     * 分页查询检查项（复杂条件）
     * 
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<InspectionItem> selectPageWithConditions(Page<InspectionItem> page,
                                                 @Param("query") InspectionItemQueryDTO query);

    /**
     * 根据ID查询检查项详情
     * 
     * @param id 检查项ID
     * @return 检查项详情
     */
    InspectionItem selectDetailById(@Param("id") Long id);

    /**
     * 软删除检查项
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 更新检查项
     */
    int updateInspectionItem(InspectionItem inspectionItem);
}
