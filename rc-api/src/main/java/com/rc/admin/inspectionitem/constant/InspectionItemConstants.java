package com.rc.admin.inspectionitem.constant;

/**
 * 检查项配置常量
 * 
 * <AUTHOR>
 * @since 2024
 */
public class InspectionItemConstants {
    
    /**
     * 操作类型常量
     */
    public static class OperationType {
        /** 创建 */
        public static final String CREATE = "CREATE";
        /** 更新 */
        public static final String UPDATE = "UPDATE";
        /** 删除 */
        public static final String DELETE = "DELETE";
    }
    
    /**
     * 数据类型常量
     */
    public static class DataType {
        /** 工况 */
        public static final Integer WORKING_CONDITION = 1;
        /** 定位 */
        public static final Integer LOCATION = 2;
    }
    
    /**
     * 删除状态常量
     */
    public static class DeleteStatus {
        /** 正常 */
        public static final Integer NORMAL = 0;
        /** 已删除 */
        public static final Integer DELETED = 1;
    }
    
    /**
     * 校验规则常量
     */
    public static class Validation {
        /** 检查项名称最大长度 */
        public static final int ITEM_NAME_MAX_LENGTH = 100;
        /** 英文名称最大长度 */
        public static final int ENGLISH_NAME_MAX_LENGTH = 50;
        /** 描述最大长度 */
        public static final int DESCRIPTION_MAX_LENGTH = 255;
        /** 明细列表最小数量 */
        public static final int DETAILS_MIN_SIZE = 1;
        /** 明细列表最大数量 */
        public static final int DETAILS_MAX_SIZE = 100;
    }
    
    /**
     * 英文名称格式正则表达式
     */
    public static final String ENGLISH_NAME_PATTERN = "^[a-zA-Z_]+$";
    
    /**
     * 默认分页参数
     */
    public static class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE_NUM = 1;
        /** 默认页面大小 */
        public static final int DEFAULT_PAGE_SIZE = 10;
        /** 最大页面大小 */
        public static final int MAX_PAGE_SIZE = 100;
    }
}
