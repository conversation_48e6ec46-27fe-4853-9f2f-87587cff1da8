package com.rc.admin.inspectionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 检查项明细实体类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inspection_item_details")
@ApiModel(value = "InspectionItemDetail对象", description = "检查项明细表")
public class InspectionItemDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("检查项ID")
    @TableField("inspection_item_id")
    private Long inspectionItemId;

    @ApiModelProperty("物模型ID")
    @TableField("model_id")
    private String modelId;

    @ApiModelProperty("物模型名称")
    @TableField("model_name")
    private String modelName;

    @ApiModelProperty("检查属性ID")
    @TableField("property_id")
    private String propertyId;

    @ApiModelProperty("检查属性名称")
    @TableField("property_name")
    private String propertyName;

    @ApiModelProperty("检查属性显示名称")
    @TableField("property_display_name")
    private String propertyDisplayName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty("软删除标记：0-正常，1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
