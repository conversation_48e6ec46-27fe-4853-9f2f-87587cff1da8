package com.rc.admin.inspectionitem.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 导入结果VO
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("导入结果VO")
public class ImportResultVO {
    
    @ApiModelProperty("成功数量")
    private Integer successCount;
    
    @ApiModelProperty("失败数量")
    private Integer errorCount;
    
    @ApiModelProperty("错误文件路径")
    private String errorFilePath;
    
    @ApiModelProperty("错误详情列表")
    private List<ImportErrorDetail> errorDetails;
    
    @ApiModelProperty("总数量")
    private Integer totalCount;
    
    /**
     * 导入错误详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportErrorDetail {
        @ApiModelProperty("行号")
        private Integer rowNum;
        
        @ApiModelProperty("错误信息")
        private String errorMessage;
        
        @ApiModelProperty("原始数据")
        private String originalData;
    }
    
    /**
     * 创建成功结果
     */
    public static ImportResultVO success(int count) {
        return ImportResultVO.builder()
                .successCount(count)
                .errorCount(0)
                .totalCount(count)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static ImportResultVO error(int errorCount, String errorFilePath) {
        return ImportResultVO.builder()
                .successCount(0)
                .errorCount(errorCount)
                .errorFilePath(errorFilePath)
                .totalCount(errorCount)
                .build();
    }
}
