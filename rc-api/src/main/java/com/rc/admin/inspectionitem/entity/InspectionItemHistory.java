package com.rc.admin.inspectionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 检查项历史记录实体类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inspection_item_history")
@ApiModel(value = "InspectionItemHistory对象", description = "检查项历史记录表")
public class InspectionItemHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("检查项ID")
    @TableField("inspection_item_id")
    private Long inspectionItemId;

    @ApiModelProperty("操作类型：CREATE-创建，UPDATE-修改，DELETE-删除")
    @TableField("operation_type")
    private String operationType;

    @ApiModelProperty("操作描述")
    @TableField("operation_desc")
    private String operationDesc;

    @ApiModelProperty("操作人")
    @TableField("operator")
    private String operator;

    @ApiModelProperty("操作时间")
    @TableField("operation_time")
    private LocalDateTime operationTime;
}
