package com.rc.admin.inspectionitem.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 检查项导入模板行数据
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateRow {
    
    @ExcelProperty(value = "物模型ID", index = 0)
    private String modelId;
    
    @ExcelProperty(value = "检查属性ID", index = 1)
    private String propertyIds;
}
