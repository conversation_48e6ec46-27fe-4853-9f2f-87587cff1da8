<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.inspectionitem.dao.InspectionItemHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.inspectionitem.entity.InspectionItemHistory">
        <id column="id" property="id" />
        <result column="inspection_item_id" property="inspectionItemId" />
        <result column="operation_type" property="operationType" />
        <result column="operation_desc" property="operationDesc" />
        <result column="operator" property="operator" />
        <result column="operation_time" property="operationTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, inspection_item_id, operation_type, operation_desc, operator, operation_time
    </sql>

    <!-- 根据检查项ID查询历史记录 -->
    <select id="selectByInspectionItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM inspection_item_history 
        WHERE inspection_item_id = #{inspectionItemId}
        ORDER BY operation_time DESC
    </select>

    <!-- 查询最近的历史记录 -->
    <select id="selectRecentHistory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM inspection_item_history 
        WHERE inspection_item_id = #{inspectionItemId}
        ORDER BY operation_time DESC
        LIMIT #{limit}
    </select>

</mapper>
