package com.rc.admin.inspectionitem.util;

import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.inspectionitem.dto.InspectionItemDetailDTO;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 检查项配置校验器
 * 
 * <AUTHOR>
 * @since 2024
 */
@Component
@Validated
public class InspectionItemValidator {

    /**
     * 校验数据类型
     * 
     * @param dataType 数据类型
     */
    public void validateDataType(Integer dataType) {
        if (dataType == null) {
            throw new EasyException("数据类型不能为空");
        }
        if (dataType != 1 && dataType != 2) {
            throw new EasyException("数据类型值无效，只能是1-工况或2-定位");
        }
    }

    /**
     * 校验明细列表
     * 
     * @param details 明细列表
     */
    public void validateDetails(List<InspectionItemDetailDTO> details) {
        if (details == null || details.isEmpty()) {
            throw new EasyException("至少需要配置一个物模型和检查属性");
        }
        
        // 检查物模型和属性的唯一性
        Set<String> modelPropertyPairs = new HashSet<>();
        for (InspectionItemDetailDTO detail : details) {
            // 校验必填字段
            if (detail.getModelId() == null || detail.getModelId().trim().isEmpty()) {
                throw new EasyException("物模型ID不能为空");
            }
            if (detail.getPropertyId() == null || detail.getPropertyId().trim().isEmpty()) {
                throw new EasyException("检查属性ID不能为空");
            }
            
            // 校验唯一性
            String pair = detail.getModelId() + ":" + detail.getPropertyId();
            if (modelPropertyPairs.contains(pair)) {
                throw new EasyException("物模型和检查属性组合不能重复");
            }
            modelPropertyPairs.add(pair);
        }
    }

    /**
     * 校验英文名称格式
     * 
     * @param englishName 英文名称
     */
    public void validateEnglishNameFormat(String englishName) {
        if (englishName == null || englishName.trim().isEmpty()) {
            throw new EasyException("英文名称不能为空");
        }
        
        // 只能包含字母和下划线
        if (!englishName.matches("^[a-zA-Z_]+$")) {
            throw new EasyException("英文名称只能包含字母和下划线");
        }
        
        // 长度校验
        if (englishName.length() > 50) {
            throw new EasyException("英文名称长度不能超过50字符");
        }
    }

    /**
     * 校验检查项名称
     * 
     * @param itemName 检查项名称
     */
    public void validateItemName(String itemName) {
        if (itemName == null || itemName.trim().isEmpty()) {
            throw new EasyException("检查项名称不能为空");
        }
        
        if (itemName.length() > 100) {
            throw new EasyException("检查项名称长度不能超过100字符");
        }
    }

    /**
     * 校验描述
     * 
     * @param description 描述
     */
    public void validateDescription(String description) {
        if (description != null && description.length() > 255) {
            throw new EasyException("描述长度不能超过255字符");
        }
    }
}
