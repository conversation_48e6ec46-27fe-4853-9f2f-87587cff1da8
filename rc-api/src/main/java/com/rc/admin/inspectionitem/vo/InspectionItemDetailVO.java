package com.rc.admin.inspectionitem.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import com.rc.admin.inspectionitem.entity.InspectionItemDetail;
import com.rc.admin.inspectionitem.entity.InspectionItemHistory;

/**
 * 检查项配置详情视图对象
 * 用于详情查询返回
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "InspectionItemDetailVO", description = "检查项配置详情视图对象")
public class InspectionItemDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("检查项编码")
    private String itemCode;

    @ApiModelProperty("检查项名称")
    private String itemName;

    @ApiModelProperty("英文名称")
    private String englishName;

    @ApiModelProperty("检查项描述")
    private String description;

    @ApiModelProperty("数据类型：1-工况，2-定位")
    private Integer dataType;

    @ApiModelProperty("数据类型名称")
    private String dataTypeName;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("明细列表")
    private List<InspectionItemDetail> details;

    @ApiModelProperty("历史记录列表")
    private List<InspectionItemHistory> historyList;
}

