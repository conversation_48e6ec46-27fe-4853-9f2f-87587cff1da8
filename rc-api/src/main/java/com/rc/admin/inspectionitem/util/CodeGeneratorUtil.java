package com.rc.admin.inspectionitem.util;

import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.inspectionitem.dao.InspectionItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Random;

/**
 * 检查项编码生成工具类
 * 负责生成检查项的唯一编码：英文名称+2位随机数
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Component
public class CodeGeneratorUtil {

    @Autowired
    private InspectionItemMapper inspectionItemMapper;

    /**
     * 生成检查项编码
     * 格式：英文名称_XX（XX为2位随机数）
     * 示例：engine_temperature_check_01
     * 
     * @param englishName 英文名称
     * @return 生成的唯一编码
     */
    public String generateItemCode(String englishName) {
        log.info("开始生成检查项编码，英文名称：{}", englishName);
        
        // 最多重试3次
        for (int i = 0; i < 3; i++) {
            String randomSuffix = generateRandomNum();
            String itemCode = englishName + "_" + randomSuffix;
            
            log.debug("第{}次生成编码：{}", i + 1, itemCode);
            
            // 检查编码是否已存在
            if (inspectionItemMapper.countByItemCode(itemCode) == 0) {
                log.info("成功生成检查项编码：{}", itemCode);
                return itemCode;
            }
            
            log.warn("编码{}已存在，进行第{}次重试", itemCode, i + 2);
        }
        
        log.error("检查项编码生成失败，英文名称：{}，已重试3次", englishName);
        throw new EasyException("编码生成失败，请重试");
    }

    /**
     * 生成2位随机数字
     * 范围：00-99
     * 
     * @return 2位随机数字字符串
     */
    private String generateRandomNum() {
        Random random = new Random();
        int num = random.nextInt(100); // 0-99
        return String.format("%02d", num);
    }

    /**
     * 验证编码格式是否正确
     * 
     * @param itemCode 检查项编码
     * @return true-格式正确，false-格式错误
     */
    public boolean validateCodeFormat(String itemCode) {
        if (itemCode == null || itemCode.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否以下划线+2位数字结尾
        return itemCode.matches("^[a-zA-Z_]+_\\d{2}$");
    }

    /**
     * 从编码中提取英文名称
     * 
     * @param itemCode 检查项编码
     * @return 英文名称
     */
    public String extractEnglishName(String itemCode) {
        if (!validateCodeFormat(itemCode)) {
            throw new IllegalArgumentException("编码格式不正确：" + itemCode);
        }
        
        // 找到最后一个下划线的位置
        int lastUnderscoreIndex = itemCode.lastIndexOf("_");
        if (lastUnderscoreIndex > 0) {
            return itemCode.substring(0, lastUnderscoreIndex);
        }
        
        throw new IllegalArgumentException("无法从编码中提取英文名称：" + itemCode);
    }
}

