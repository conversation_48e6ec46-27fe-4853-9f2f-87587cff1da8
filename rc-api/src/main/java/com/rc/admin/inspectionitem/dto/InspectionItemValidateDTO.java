package com.rc.admin.inspectionitem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 检查项配置唯一性校验DTO
 * 用于校验检查项名称和英文名称的唯一性
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "InspectionItemValidateDTO", description = "检查项配置唯一性校验")
public class InspectionItemValidateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字段名称", required = true, example = "itemName", 
                     notes = "可选值：itemName(检查项名称)、englishName(英文名称)")
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;

    @ApiModelProperty(value = "字段值", required = true, example = "发动机温度检查")
    @NotBlank(message = "字段值不能为空")
    private String fieldValue;

    @ApiModelProperty(value = "排除的记录ID", example = "1", notes = "修改时使用，排除自身记录")
    private Long excludeId;
}

