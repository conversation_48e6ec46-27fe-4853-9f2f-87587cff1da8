package com.rc.admin.inspectionitem.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查项数据类型枚举
 * 
 * <AUTHOR>
 * @since 2024
 */
@Getter
@AllArgsConstructor
public enum DataTypeEnum {
    
    /**
     * 工况数据
     */
    WORKING_CONDITION(1, "工况"),
    
    /**
     * 定位数据
     */
    LOCATION(2, "定位");
    
    private final Integer code;
    private final String name;
    
    /**
     * 根据代码获取名称
     * 
     * @param code 代码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DataTypeEnum dataType : values()) {
            if (dataType.getCode().equals(code)) {
                return dataType.getName();
            }
        }
        
        return null;
    }
    
    /**
     * 校验代码是否有效
     * 
     * @param code 代码
     * @return true-有效，false-无效
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        
        for (DataTypeEnum dataType : values()) {
            if (dataType.getCode().equals(code)) {
                return true;
            }
        }
        
        return false;
    }
}
