package com.rc.admin.inspectionitem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 检查项明细数据传输对象
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "InspectionItemDetailDTO", description = "检查项明细数据传输对象")
public class InspectionItemDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物模型ID", required = true)
    @NotBlank(message = "物模型ID不能为空")
    private String modelId;

    @ApiModelProperty(value = "物模型名称")
    private String modelName;

    @ApiModelProperty(value = "检查属性ID", required = true)
    @NotBlank(message = "检查属性ID不能为空")
    private String propertyId;

    @ApiModelProperty(value = "检查属性名称")
    private String propertyName;

    @ApiModelProperty(value = "检查属性显示名称")
    private String propertyDisplayName;
}
