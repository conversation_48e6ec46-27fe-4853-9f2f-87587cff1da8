package com.rc.admin.inspectionitem.exception;

import com.rc.admin.common.core.exception.EasyException;

/**
 * 检查项配置相关异常
 * 
 * <AUTHOR>
 * @since 2024
 */
public class InspectionItemException extends EasyException {
    
    private static final long serialVersionUID = 1L;
    
    public InspectionItemException(String message) {
        super(message);
    }
    
    public InspectionItemException(String message, Throwable cause) {
        super(message);
        this.initCause(cause);
    }
    
    /**
     * 明细必填异常
     */
    public static InspectionItemException detailRequired() {
        return new InspectionItemException("至少需要配置一个物模型和检查属性");
    }
    
    /**
     * 物模型属性组合重复异常
     */
    public static InspectionItemException duplicateModelProperty() {
        return new InspectionItemException("物模型和检查属性组合不能重复");
    }
    
    /**
     * 物模型不存在异常
     */
    public static InspectionItemException modelNotFound() {
        return new InspectionItemException("物模型不存在");
    }
    
    /**
     * 检查属性不存在异常
     */
    public static InspectionItemException propertyNotFound() {
        return new InspectionItemException("检查属性不存在");
    }
    
    /**
     * 检查项编码生成失败异常
     */
    public static InspectionItemException codeGenerationFailed() {
        return new InspectionItemException("检查项编码生成失败");
    }
    
    /**
     * 检查项名称重复异常
     */
    public static InspectionItemException itemNameExists() {
        return new InspectionItemException("检查项名称已存在");
    }
    
    /**
     * 英文名称重复异常
     */
    public static InspectionItemException englishNameExists() {
        return new InspectionItemException("英文名称已存在");
    }
    
    /**
     * 数据类型无效异常
     */
    public static InspectionItemException invalidDataType() {
        return new InspectionItemException("数据类型值无效，只能是1-工况或2-定位");
    }
    
    /**
     * 检查项不存在异常
     */
    public static InspectionItemException itemNotFound() {
        return new InspectionItemException("检查项不存在");
    }
    
    /**
     * 检查项已被使用异常
     */
    public static InspectionItemException itemInUse() {
        return new InspectionItemException("该检查项已被使用，无法删除");
    }
}
