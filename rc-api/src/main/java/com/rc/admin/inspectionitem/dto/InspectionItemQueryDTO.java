package com.rc.admin.inspectionitem.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 检查项配置查询条件DTO
 * 用于分页查询和条件筛选
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "InspectionItemQueryDTO", description = "检查项配置查询条件")
public class InspectionItemQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "检查项名称", example = "发动机")
    private String itemName;

    @ApiModelProperty(value = "检查项编码", example = "engine_temperature_check_01")
    private String itemCode;

    @ApiModelProperty(value = "英文名称", example = "engine")
    private String englishName;

    @ApiModelProperty(value = "创建人", example = "admin")
    private String creator;

    @ApiModelProperty(value = "开始时间", example = "2024-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}

