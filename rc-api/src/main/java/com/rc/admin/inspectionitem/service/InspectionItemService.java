package com.rc.admin.inspectionitem.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.inspectionitem.dto.InspectionItemDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemQueryDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemValidateDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemImportDTO;
import com.rc.admin.inspectionitem.vo.InspectionItemDetailVO;
import com.rc.admin.inspectionitem.vo.InspectionItemVO;
import com.rc.admin.inspectionitem.vo.ModelPropertyOptionVO;
import com.rc.admin.inspectionitem.vo.ImportResultVO;
import com.rc.admin.divisionalproductgroup.entity.DivisionalProductGroup;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 检查项配置服务接口
 * 
 * <AUTHOR>
 * @since 2024
 */
public interface InspectionItemService {

    /**
     * 新增检查项
     * 
     * @param dto 检查项信息
     */
    void addInspectionItem(InspectionItemDTO dto);

    /**
     * 修改检查项
     * 
     * @param id 检查项ID
     * @param dto 检查项信息
     */
    void updateInspectionItem(Long id, InspectionItemDTO dto);

    /**
     * 删除检查项
     * 
     * @param id 检查项ID
     */
    void deleteInspectionItem(Long id);

    /**
     * 分页查询检查项
     * 
     * @param query 查询条件
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    Page<InspectionItemVO> pageQuery(InspectionItemQueryDTO query, int pageNum, int pageSize);

    /**
     * 查询检查项详情
     * 
     * @param id 检查项ID
     * @return 检查项详情
     */
    InspectionItemDetailVO getDetail(Long id);

    /**
     * 唯一性校验
     * 
     * @param dto 校验信息
     * @return true-唯一，false-重复
     */
    boolean validateUniqueness(InspectionItemValidateDTO dto);

    /**
     * 生成检查项编码
     * 
     * @param englishName 英文名称
     * @return 生成的编码
     */
    String generateItemCode(String englishName);

    /**
     * 获取物模型列表（从事业部产品组配置）
     * 
     * @param divisionCode 事业部编码
     * @return 物模型列表
     */
    List<DivisionalProductGroup> getModelList(String divisionCode);

    /**
     * 获取物模型属性列表（直接调用外部接口）
     * 
     * @param modelId 物模型ID
     * @return 属性列表
     */
    List<Object> getModelProperties(String modelId);

    /**
     * 同步指定物模型的属性
     * 
     * @param modelId 物模型ID
     */
    void syncModelProperties(String modelId);

    /**
     * 获取物模型属性下拉选项列表
     * 
     * @param modelId 物模型ID
     * @param searchKeyword 搜索关键词（可选，支持name和displayName模糊搜索）
     * @return 下拉选项列表
     */
    List<ModelPropertyOptionVO> getModelPropertyOptions(String modelId, String searchKeyword);
    
    /**
     * 下载检查项导入模板
     * 
     * @param response HTTP响应对象
     */
    void downloadImportTemplate(HttpServletResponse response);
    
    /**
     * 导入检查项数据
     * 
     * @param importDTO 导入数据（包含基本信息和Excel文件）
     * @return 导入结果
     */
    ImportResultVO importData(InspectionItemImportDTO importDTO);
}

