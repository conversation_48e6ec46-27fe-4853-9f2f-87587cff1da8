package com.rc.admin.inspectionitem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 检查项配置数据传输对象
 * 用于新增和修改检查项的请求参数
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel(value = "InspectionItemDTO", description = "检查项配置数据传输对象")
public class InspectionItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "检查项名称", required = true, example = "发动机温度检查")
    @NotBlank(message = "检查项名称不能为空")
    @Size(min = 1, max = 100, message = "检查项名称长度必须在1-100字符之间")
    private String itemName;

    @ApiModelProperty(value = "英文名称", required = true, example = "engine_temperature_check")
    @NotBlank(message = "英文名称不能为空")
    @Size(min = 1, max = 50, message = "英文名称长度必须在1-50字符之间")
    @Pattern(regexp = "^[a-zA-Z_]+$", message = "英文名称只能包含字母和下划线")
    private String englishName;

    @ApiModelProperty(value = "检查项描述", example = "监控发动机实时温度")
    @Size(max = 255, message = "描述长度不能超过255字符")
    private String description;

    @ApiModelProperty(value = "数据类型：1-工况，2-定位", required = true, example = "1")
    @NotNull(message = "数据类型不能为空")
    @Min(value = 1, message = "数据类型值无效")
    @Max(value = 2, message = "数据类型值无效")
    private Integer dataType;

    @ApiModelProperty(value = "明细列表")
    @Valid
    @Size(min = 1, message = "至少需要配置一个物模型和检查属性")
    private List<InspectionItemDetailDTO> details;
}

