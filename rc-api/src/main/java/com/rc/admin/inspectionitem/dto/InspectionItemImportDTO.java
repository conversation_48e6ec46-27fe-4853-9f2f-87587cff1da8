package com.rc.admin.inspectionitem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 检查项导入DTO
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@ApiModel("检查项导入DTO")
public class InspectionItemImportDTO {
    
    @ApiModelProperty("检查项名称")
    @NotEmpty(message = "检查项名称不能为空")
    private String itemName;
    
    @ApiModelProperty("英文名称")
    @NotEmpty(message = "英文名称不能为空")
    private String englishName;
    
    @ApiModelProperty("数据类型：1-工况 2-定位")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;
    
    @ApiModelProperty("描述")
    private String description;
    
    @ApiModelProperty("导入文件")
    @NotNull(message = "导入文件不能为空")
    private MultipartFile importFile;
}
