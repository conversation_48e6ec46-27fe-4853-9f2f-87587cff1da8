package com.rc.admin.inspectionitem.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.inspectionitem.entity.InspectionItemDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查项明细Mapper接口
 * 
 * <AUTHOR>
 * @since 2024
 */
@Mapper
public interface InspectionItemDetailMapper extends BaseMapper<InspectionItemDetail> {

    /**
     * 批量插入明细
     * 
     * @param details 明细列表
     * @return 插入数量
     */
    int insertBatch(@Param("list") List<InspectionItemDetail> details);

    /**
     * 根据检查项ID查询明细列表
     * 
     * @param inspectionItemId 检查项ID
     * @return 明细列表
     */
    List<InspectionItemDetail> selectByInspectionItemId(@Param("inspectionItemId") Long inspectionItemId);

    /**
     * 根据检查项ID物理删除明细
     * 
     * @param inspectionItemId 检查项ID
     * @return 删除数量
     */
    int deleteByInspectionItemId(@Param("inspectionItemId") Long inspectionItemId);
}
