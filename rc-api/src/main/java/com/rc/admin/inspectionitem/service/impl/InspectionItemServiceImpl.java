package com.rc.admin.inspectionitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.divisionalproductgroup.dao.DivisionalProductGroupMapper;
import com.rc.admin.divisionalproductgroup.entity.DivisionalProductGroup;
import com.rc.admin.inspectionitem.dao.InspectionItemDetailMapper;
import com.rc.admin.inspectionitem.dao.InspectionItemHistoryMapper;
import com.rc.admin.inspectionitem.dao.InspectionItemMapper;
import com.rc.admin.inspectionitem.dto.InspectionItemDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemDetailDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemQueryDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemValidateDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemImportDTO;
import com.rc.admin.inspectionitem.dto.TemplateRow;
import com.rc.admin.inspectionitem.entity.InspectionItem;
import com.rc.admin.inspectionitem.entity.InspectionItemDetail;
import com.rc.admin.inspectionitem.entity.InspectionItemHistory;
import com.rc.admin.inspectionitem.service.InspectionItemService;
import com.rc.admin.inspectionitem.util.CodeGeneratorUtil;
import com.rc.admin.inspectionitem.vo.InspectionItemDetailVO;
import com.rc.admin.inspectionitem.vo.InspectionItemVO;
import com.rc.admin.inspectionitem.vo.ModelPropertyOptionVO;
import com.rc.admin.ors.quality.entity.ModelPropertySync;
import com.rc.admin.ors.quality.model.OrsRootCloudResp;
import com.rc.admin.ors.quality.service.ModelPropertySyncService;
import com.rc.admin.ors.quality.service.OrsSyncDeviceService;
import com.rc.admin.util.ShiroUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.rc.admin.inspectionitem.vo.ImportResultVO;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import com.rc.admin.inspectionitem.vo.ImportResultVO.ImportErrorDetail;
import java.io.IOException;

/**
 * 检查项配置服务实现类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectionItemServiceImpl implements InspectionItemService {

    @Autowired
    private InspectionItemMapper inspectionItemMapper;

    @Autowired
    private InspectionItemDetailMapper detailMapper;

    @Autowired
    private InspectionItemHistoryMapper historyMapper;

    @Autowired
    private DivisionalProductGroupMapper modelMapper;

    @Autowired
    private OrsSyncDeviceService orsSyncDeviceService;

    @Autowired
    private ModelPropertySyncService modelPropertySyncService;

    @Autowired
    private CodeGeneratorUtil codeGeneratorUtil;

    @Value("${ors.rootCloud.urlPre:http://federation-openapi-gateway-zone-china.ngc.sanygroup.com}")
    private String urlPre;

    @Value("${ors.root-cloud.page-size:100}")
    private int pageSize;

    @Override
    public void addInspectionItem(InspectionItemDTO dto) {
        log.info("开始新增检查项，名称：{}", dto.getItemName());

        // 1. 参数校验
        validateInput(dto);

        // 2. 业务规则校验
        checkBusinessRules(dto);

        // 3. 生成编码
        String itemCode = codeGeneratorUtil.generateItemCode(dto.getEnglishName());

        // 4. 保存主表
        InspectionItem entity = convertToEntity(dto);
        entity.setItemCode(itemCode);
        entity.setCreator(ShiroUtil.getCurrentUser().getUsername());
        entity.setCreateTime(LocalDateTime.now());

        int result = inspectionItemMapper.insert(entity);
        if (result <= 0) {
            throw new EasyException("新增检查项失败");
        }

        // 5. 保存明细表
        saveDetails(entity.getId(), dto.getDetails());

        // 6. 记录历史
        recordHistory(entity.getId(), "CREATE", "创建检查项", getCurrentUser());

        log.info("新增检查项成功，ID：{}，名称：{}", entity.getId(), entity.getItemName());
    }

    @Override
    public void updateInspectionItem(Long id, InspectionItemDTO dto) {
        log.info("开始修改检查项，ID：{}，名称：{}", id, dto.getItemName());

        // 1. 检查检查项是否存在
        InspectionItem existing = inspectionItemMapper.selectById(id);
        if (existing == null || existing.getIsDeleted() == 1) {
            throw new EasyException("检查项不存在");
        }

        // 2. 参数校验
        validateInput(dto);

        // 3. 业务规则校验（排除自身）
        checkBusinessRulesForUpdate(id, dto);

        // 4. 更新主表
        InspectionItem entity = convertToEntity(dto);
        entity.setId(id);
        entity.setItemCode(existing.getItemCode()); // 编码不可修改

        int result = inspectionItemMapper.updateById(entity);
        if (result <= 0) {
            throw new EasyException("修改检查项失败");
        }

        // 5. 更新明细表（先删除后新增）
        detailMapper.delete(new QueryWrapper<InspectionItemDetail>()
            .eq("inspection_item_id", id));
        saveDetails(id, dto.getDetails());

        // 6. 记录历史
        recordHistory(id, "UPDATE", "修改检查项", getCurrentUser());

        log.info("修改检查项成功，ID：{}", id);
    }

    @Override
    public void deleteInspectionItem(Long id) {
        log.info("开始删除检查项，ID：{}", id);

        // 1. 参数校验
        if (id == null || id <= 0) {
            throw new EasyException("检查项ID不能为空");
        }

        // 2. 检查记录是否存在
        InspectionItem existingItem = inspectionItemMapper.selectById(id);
        if (existingItem == null) {
            throw new EasyException("检查项不存在");
        }

        // 3. TODO: 检查是否被规则配置引用（后续完善）
        // checkRuleReference(id);

        // 4. 执行软删除
        int result = inspectionItemMapper.softDeleteById(id);
        if (result <= 0) {
            throw new EasyException("删除检查项失败");
        }

        log.info("成功删除检查项，ID：{}", id);
    }

    @Override
    public Page<InspectionItemVO> pageQuery(InspectionItemQueryDTO query, int pageNum, int pageSize) {
        log.info("开始分页查询检查项，页码：{}，页面大小：{}", pageNum, pageSize);

        // 1. 参数校验
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0 || pageSize > 100) {
            pageSize = 10;
        }

        // 2. 构建分页参数
        Page<InspectionItem> page = new Page<>(pageNum, pageSize);

        // 3. 执行查询
        Page<InspectionItem> resultPage = inspectionItemMapper.selectPageWithConditions(page, query);

        // 4. 转换结果
        Page<InspectionItemVO> voPage = new Page<>();
        BeanUtil.copyProperties(resultPage, voPage);

        List<InspectionItemVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);

        log.info("分页查询完成，总记录数：{}，当前页记录数：{}", voPage.getTotal(), voList.size());
        return voPage;
    }

    @Override
    public InspectionItemDetailVO getDetail(Long id) {
        log.info("开始查询检查项详情，ID：{}", id);

        // 1. 查询主表
        InspectionItem item = inspectionItemMapper.selectById(id);
        if (item == null || item.getIsDeleted() == 1) {
            return null;
        }

        // 2. 查询明细表
        List<InspectionItemDetail> details = detailMapper.selectList(
            new QueryWrapper<InspectionItemDetail>()
                .eq("inspection_item_id", id)
                .eq("is_deleted", 0)
        );

        // 3. 查询历史记录
        List<InspectionItemHistory> historyList = historyMapper.selectList(
            new QueryWrapper<InspectionItemHistory>()
                .eq("inspection_item_id", id)
                .orderByDesc("operation_time")
        );

        // 4. 转换为VO
        InspectionItemDetailVO vo = convertToDetailVO(item);
        vo.setDetails(details);
        vo.setHistoryList(historyList);

        log.info("查询检查项详情完成，ID：{}", id);
        return vo;
    }

    @Override
    public boolean validateUniqueness(InspectionItemValidateDTO dto) {
        log.info("开始唯一性校验，字段：{}，值：{}", dto.getFieldName(), dto.getFieldValue());

        String fieldName = dto.getFieldName();
        String fieldValue = dto.getFieldValue();
        Long excludeId = dto.getExcludeId();

        int count = 0;
        switch (fieldName) {
            case "itemName":
                count = inspectionItemMapper.countByItemName(fieldValue, excludeId);
                break;
            case "englishName":
                count = inspectionItemMapper.countByEnglishName(fieldValue, excludeId);
                break;
            default:
                throw new EasyException("不支持的校验字段：" + fieldName);
        }

        boolean isUnique = count == 0;
        log.info("唯一性校验完成，字段：{}，值：{}，结果：{}", fieldName, fieldValue, isUnique ? "唯一" : "重复");
        return isUnique;
    }

    @Override
    public String generateItemCode(String englishName) {
        log.info("开始生成检查项编码，英文名称：{}", englishName);

        if (StrUtil.isBlank(englishName)) {
            throw new EasyException("英文名称不能为空");
        }

        return codeGeneratorUtil.generateItemCode(englishName);
    }

    /**
     * 参数校验
     */
    private void validateInput(InspectionItemDTO dto) {
        if (StrUtil.isBlank(dto.getItemName())) {
            throw new EasyException("检查项名称不能为空");
        }
        if (StrUtil.isBlank(dto.getEnglishName())) {
            throw new EasyException("英文名称不能为空");
        }
        if (!dto.getEnglishName().matches("^[a-zA-Z_]+$")) {
            throw new EasyException("英文名称只能包含字母和下划线");
        }
    }

    /**
     * 新增时的唯一性校验
     */
    private void validateUniquenessForAdd(InspectionItemDTO dto) {
        // 检查项名称唯一性
        if (inspectionItemMapper.countByItemName(dto.getItemName(), null) > 0) {
            throw new EasyException("检查项名称已存在");
        }

        // 英文名称唯一性
        if (inspectionItemMapper.countByEnglishName(dto.getEnglishName(), null) > 0) {
            throw new EasyException("英文名称已存在");
        }
    }

    /**
     * 修改时的唯一性校验
     */
    private void validateUniquenessForUpdate(InspectionItemDTO dto, Long excludeId) {
        // 检查项名称唯一性
        if (inspectionItemMapper.countByItemName(dto.getItemName(), excludeId) > 0) {
            throw new EasyException("检查项名称已存在");
        }

        // 英文名称唯一性
        if (inspectionItemMapper.countByEnglishName(dto.getEnglishName(), excludeId) > 0) {
            throw new EasyException("英文名称已存在");
        }
    }

    /**
     * 转换为VO对象
     */
    private InspectionItemVO convertToVO(InspectionItem item) {
        InspectionItemVO vo = new InspectionItemVO();
        BeanUtil.copyProperties(item, vo);
        return vo;
    }

    @Override
    public List<DivisionalProductGroup> getModelList(String divisionCode) {
        QueryWrapper<DivisionalProductGroup> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(divisionCode)) {
            wrapper.eq("division_code", divisionCode);
        }
        wrapper.orderByAsc("model_name_cn");
        
        return modelMapper.selectList(wrapper);
    }

    @Override
    public List<Object> getModelProperties(String modelId) {
        log.info("开始获取物模型属性，modelId：{}", modelId);
        
        try {
            // 获取Token
            String token = orsSyncDeviceService.getRootCloudToken();
            if (StrUtil.isBlank(token)) {
                throw new EasyException("获取认证Token失败");
            }
            
            List<Object> allProperties = new ArrayList<>();
            int page = 0;
            
            while (true) {
                // 请求物模型对应的属性信息
                String url = String.format("%s/thing-model/v1/thing/thing-classes/%s/properties", urlPre, modelId);
                HttpRequest request = HttpRequest.get(url + "?_limit=" + pageSize + "&_skip=" + (page * pageSize));
                request.bearerAuth(token);
                HttpResponse response = request.execute();

                if (StrUtil.isBlank(response.body())) {
                    break;
                }
                
                // 先尝试解析为OrsRootCloudResp格式
                try {
                    OrsRootCloudResp resp = JSONObject.parseObject(response.body(), OrsRootCloudResp.class);
                    if (resp != null && StrUtil.isNotBlank(resp.getPayload())) {
                        List<Object> properties = JSONObject.parseArray(resp.getPayload(), Object.class);
                        if (properties != null && !properties.isEmpty()) {
                            allProperties.addAll(properties);
                            
                            // 如果返回的数据量小于pageSize，说明已经是最后一页
                            if (properties.size() < pageSize) {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析OrsRootCloudResp格式失败，尝试直接解析为数组，modelId：{}，错误：{}", modelId, e.getMessage());
                    
                    // 如果解析失败，尝试直接解析为数组
                    try {
                        List<Object> properties = JSONObject.parseArray(response.body(), Object.class);
                        if (properties != null && !properties.isEmpty()) {
                            allProperties.addAll(properties);
                            
                            // 如果返回的数据量小于pageSize，说明已经是最后一页
                            if (properties.size() < pageSize) {
                                break;
                            }
                        }
                    } catch (Exception e2) {
                        log.error("直接解析为数组也失败，modelId：{}，响应内容：{}", modelId, response.body());
                        throw new EasyException("解析物模型属性数据失败：" + e2.getMessage());
                    }
                }
                 
                page++;
            }
            
            log.info("获取物模型属性完成，modelId：{}，属性数量：{}", modelId, allProperties.size());
            return allProperties;
            
        } catch (Exception e) {
            log.error("获取物模型属性失败，modelId：{}", modelId, e);
            throw new EasyException("获取物模型属性失败：" + e.getMessage());
        }
    }

    @Override
    public void syncModelProperties(String modelId) {
        try {
            // 调用ORS同步服务（如果需要持久化到数据库时使用）
            orsSyncDeviceService.syncProperty(null, modelId);
            log.info("同步物模型属性成功，modelId：{}", modelId);
        } catch (Exception e) {
            log.error("同步物模型属性失败，modelId：{}", modelId, e);
            throw new EasyException("同步物模型属性失败：" + e.getMessage());
        }
    }

        @Override
    public List<ModelPropertyOptionVO> getModelPropertyOptions(String modelId, String searchKeyword) {
        log.info("开始获取物模型属性下拉选项，modelId：{}，搜索关键词：{}", modelId, searchKeyword);
        
        try {
            // 构建查询条件
            QueryWrapper<ModelPropertySync> wrapper = new QueryWrapper<>();
            wrapper.eq("model_id", modelId);
            
            // 如果有关键词，进行模糊搜索
            if (StrUtil.isNotBlank(searchKeyword)) {
                wrapper.and(w -> w.like("name", searchKeyword)
                    .or()
                    .like("display_name", searchKeyword));
            }
            
            // 查询属性列表
            List<ModelPropertySync> properties = modelPropertySyncService.list(wrapper);
            
            // 转换为VO对象
            List<ModelPropertyOptionVO> options = new ArrayList<>();
            for (ModelPropertySync property : properties) {
                ModelPropertyOptionVO option = new ModelPropertyOptionVO();
                option.setName(property.getName());
                option.setDisplayName(property.getDisplayName());
                options.add(option);
            }
            
            log.info("获取物模型属性下拉选项完成，modelId：{}，选项数量：{}", modelId, options.size());
            return options;
            
        } catch (Exception e) {
            log.error("获取物模型属性下拉选项失败，modelId：{}", modelId, e);
            throw new EasyException("获取物模型属性下拉选项失败：" + e.getMessage());
        }
    }

    /**
     * 业务规则校验
     */
    private void checkBusinessRules(InspectionItemDTO dto) {
        // 唯一性校验
        validateUniquenessForAdd(dto);
        
        // 明细校验
        validateDetails(dto.getDetails());
    }

    /**
     * 业务规则校验（更新时）
     */
    private void checkBusinessRulesForUpdate(Long id, InspectionItemDTO dto) {
        // 唯一性校验（排除自身）
        validateUniquenessForUpdate(dto, id);
        
        // 明细校验
        validateDetails(dto.getDetails());
    }

    /**
     * 校验明细列表
     */
    private void validateDetails(List<InspectionItemDetailDTO> details) {
        if (details == null || details.isEmpty()) {
            throw new EasyException("至少需要配置一个物模型和检查属性");
        }
        
        // 检查物模型和属性的唯一性
        java.util.Set<String> modelPropertyPairs = new java.util.HashSet<>();
        for (InspectionItemDetailDTO detail : details) {
            String pair = detail.getModelId() + ":" + detail.getPropertyId();
            if (modelPropertyPairs.contains(pair)) {
                throw new EasyException("物模型和检查属性组合不能重复");
            }
            modelPropertyPairs.add(pair);
        }
    }

    /**
     * 转换为实体对象
     */
    private InspectionItem convertToEntity(InspectionItemDTO dto) {
        InspectionItem entity = new InspectionItem();
        BeanUtil.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 转换为详情VO对象
     */
    private InspectionItemDetailVO convertToDetailVO(InspectionItem item) {
        InspectionItemDetailVO vo = new InspectionItemDetailVO();
        BeanUtil.copyProperties(item, vo);
        
        // 设置数据类型名称
        if (item.getDataType() != null) {
            vo.setDataTypeName(item.getDataType() == 1 ? "工况" : "定位");
        }
        
        return vo;
    }

    /**
     * 保存明细数据
     */
    private void saveDetails(Long itemId, List<InspectionItemDetailDTO> detailDTOs) {
        if (detailDTOs == null || detailDTOs.isEmpty()) {
            return;
        }
        
        List<InspectionItemDetail> details = detailDTOs.stream()
            .map(dto -> convertToDetailEntity(dto, itemId))
            .collect(Collectors.toList());
        
        detailMapper.insertBatch(details);
    }

    /**
     * 转换为明细实体对象
     */
    private InspectionItemDetail convertToDetailEntity(InspectionItemDetailDTO dto, Long itemId) {
        InspectionItemDetail detail = new InspectionItemDetail();
        BeanUtil.copyProperties(dto, detail);
        detail.setInspectionItemId(itemId);
        detail.setCreateBy(getCurrentUser());
        detail.setUpdateBy(getCurrentUser());
        return detail;
    }

    /**
     * 记录历史操作
     */
    private void recordHistory(Long itemId, String operationType, String operationDesc, String operator) {
        InspectionItemHistory history = new InspectionItemHistory();
        history.setInspectionItemId(itemId);
        history.setOperationType(operationType);
        history.setOperationDesc(operationDesc);
        history.setOperator(operator);
        history.setOperationTime(LocalDateTime.now());
        
        historyMapper.insert(history);
    }

    /**
     * 获取当前登录用户
     * TODO: 实现从安全上下文获取用户信息
     */
    private String getCurrentUser() {
        // 临时返回固定值，后续从安全上下文获取
        return "admin";
    }

    /**
     * 将属性数据转换为下拉选项VO
     */
    private List<ModelPropertyOptionVO> convertToPropertyOptions(List<Object> properties) {
        List<ModelPropertyOptionVO> options = new ArrayList<>();
        
        for (Object property : properties) {
            try {
                JSONObject propJson = (JSONObject) property;
                ModelPropertyOptionVO option = new ModelPropertyOptionVO();
                
                // 只设置必要的两个字段
                option.setName(propJson.getString("name"));
                option.setDisplayName(propJson.getString("displayName"));
                
                options.add(option);
            } catch (Exception e) {
                log.warn("转换属性数据失败：{}", property, e);
                // 继续处理下一个属性
            }
        }
        
        return options;
    }
    
    @Override
    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 设置正确的响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            
            // 处理文件名编码，避免中文乱码
            String fileName = "检查项导入模板.xlsx";
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=UTF-8''" + encodedFileName);
            
            // 创建模板数据 - 只包含物模型ID和检查属性ID两个字段
            List<TemplateRow> templateData = new ArrayList<>();
            templateData.add(new TemplateRow("model_001", "prop_001")); // 示例数据
            templateData.add(new TemplateRow("model_002", "prop_001,prop_002")); // 示例数据
            templateData.add(new TemplateRow("model_003", "prop_003,prop_004,prop_005")); // 示例数据
            
            try {
                // 使用EasyExcel生成Excel文件
                EasyExcel.write(response.getOutputStream(), TemplateRow.class)
                    .sheet("检查项导入模板")
                    .doWrite(templateData);
                    
                log.info("成功生成检查项导入模板，数据行数：{}", templateData.size());
                
            } catch (Exception e) {
                log.warn("EasyExcel生成失败，回退到CSV格式：{}", e.getMessage());
                // 回退到CSV格式
                generateCSVFallback(response, templateData);
            }
            
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            throw new EasyException("下载导入模板失败：" + e.getMessage());
        }
    }
    
    /**
     * CSV格式回退方案
     */
    private void generateCSVFallback(HttpServletResponse response, List<TemplateRow> templateData) throws Exception {
        // 重新设置CSV响应头
        response.setContentType("text/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        
        // 使用.csv扩展名
        String fileName = "检查项导入模板.csv";
        String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=UTF-8''" + encodedFileName);
        
        // 添加BOM标记，确保Excel能正确识别UTF-8编码
        response.getOutputStream().write(0xEF);
        response.getOutputStream().write(0xBB);
        response.getOutputStream().write(0xBF);
        
        // 输出CSV内容
        java.io.PrintWriter writer = response.getWriter();
        for (TemplateRow row : templateData) {
            // 处理字段中的逗号和引号
            String modelId = row.getModelId() != null ? row.getModelId() : "";
            String propertyIds = row.getPropertyIds() != null ? row.getPropertyIds() : "";
            
            if (modelId.contains(",") || modelId.contains("\"") || modelId.contains("\n")) {
                modelId = "\"" + modelId.replace("\"", "\"\"") + "\"";
            }
            if (propertyIds.contains(",") || propertyIds.contains("\"") || propertyIds.contains("\n")) {
                propertyIds = "\"" + propertyIds.replace("\"", "\"\"") + "\"";
            }
            
            writer.write(modelId + "," + propertyIds + "\n");
        }
        writer.flush();
        
        log.info("成功生成CSV格式模板，数据行数：{}", templateData.size());
    }
    
    @Override
    public ImportResultVO importData(InspectionItemImportDTO importDTO) {
        try {
            // 1. 验证导入文件
            validateImportFile(importDTO.getImportFile());
            
            // 2. 解析Excel文件（从第二行开始，跳过表头）
            List<Object[]> excelData = parseExcelFile(importDTO.getImportFile());
            
            // 3. 收集所有明细数据
            List<InspectionItemDetailDTO> allDetails = new ArrayList<>();
            List<ImportErrorDetail> errorDetails = new ArrayList<>();
            
            for (int i = 1; i < excelData.size(); i++) { // 从索引1开始，跳过表头
                Object[] rowData = excelData.get(i);
                try {
                    // 验证行数据
                    validateRowData(rowData);
                    
                    // 解析行数据，添加到明细列表
                    InspectionItemDetailDTO rowDetail = parseRowToDetails(rowData);
                    allDetails.add(rowDetail);
                    
                } catch (Exception e) {
                    log.error("处理第{}行数据失败：{}", i + 1, e.getMessage());
                    ImportErrorDetail error = ImportErrorDetail.builder()
                            .rowNum(i + 1)
                            .errorMessage(e.getMessage())
                            .originalData(Arrays.toString(rowData))
                            .build();
                    errorDetails.add(error);
                }
            }
            
            // 4. 如果有错误，返回错误信息
            if (!errorDetails.isEmpty()) {
                String errorFilePath = generateErrorReport(errorDetails);
                return ImportResultVO.error(errorDetails.size(), errorFilePath);
            }
            
            // 5. 创建检查项（包含所有明细）
            if (!allDetails.isEmpty()) {
                InspectionItemDTO itemDto = new InspectionItemDTO();
                itemDto.setItemName(importDTO.getItemName());
                itemDto.setEnglishName(importDTO.getEnglishName());
                itemDto.setDescription(importDTO.getDescription());
                itemDto.setDataType(importDTO.getDataType());
                itemDto.setDetails(allDetails);
                
                // 添加检查项
                addInspectionItem(itemDto);
                log.info("成功创建检查项，包含{}个明细", allDetails.size());
                
                return ImportResultVO.success(1); // 成功创建1个检查项
            } else {
                throw new EasyException("Excel文件中没有有效数据");
            }
            
        } catch (Exception e) {
            log.error("导入检查项数据失败", e);
            throw new EasyException("导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 校验导入文件
     */
    private void validateImportFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new EasyException("导入文件不能为空");
        }
        
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new EasyException("只支持Excel文件格式(.xlsx/.xls)");
        }
    }
    
    /**
     * 解析Excel文件
     */
    private List<Object[]> parseExcelFile(MultipartFile file) {
        try {
            log.info("开始解析Excel文件：{}", file.getOriginalFilename());
            
            // 使用EasyExcel读取Excel文件
            List<TemplateRow> templateRows = EasyExcel.read(file.getInputStream())
                    .head(TemplateRow.class)
                    .sheet()
                    .doReadSync();
            
            if (templateRows == null || templateRows.isEmpty()) {
                throw new EasyException("Excel文件内容为空");
            }
            
            log.info("成功解析Excel文件，共{}行数据", templateRows.size());
            
            // 转换为Object[]格式，保持与原有代码的兼容性
            List<Object[]> result = new ArrayList<>();
            
            // 添加表头
            result.add(new Object[]{"物模型ID", "检查属性ID"});
            
            // 添加数据行，并进行数据验证
            int validRowCount = 0;
            int invalidRowCount = 0;
            
            for (int i = 0; i < templateRows.size(); i++) {
                TemplateRow row = templateRows.get(i);
                
                // 验证行数据
                if (row != null && StrUtil.isNotBlank(row.getModelId()) && StrUtil.isNotBlank(row.getPropertyIds())) {
                    // 验证物模型ID格式
                    String modelId = row.getModelId().trim();
                    if (!modelId.matches("^[a-zA-Z0-9_-]+$")) {
                        log.warn("第{}行物模型ID格式不正确：{}", i + 1, modelId);
                        invalidRowCount++;
                        continue;
                    }
                    
                    // 验证检查属性ID格式
                    String propertyIds = row.getPropertyIds().trim();
                    if (!propertyIds.matches("^[a-zA-Z0-9_]+(,[a-zA-Z0-9_]+)*$")) {
                        log.warn("第{}行检查属性ID格式不正确：{}", i + 1, propertyIds);
                        invalidRowCount++;
                        continue;
                    }
                    
                    result.add(new Object[]{modelId, propertyIds});
                    validRowCount++;
                } else {
                    log.warn("第{}行数据无效：modelId={}, propertyIds={}", 
                            i + 1,
                            row != null ? row.getModelId() : "null", 
                            row != null ? row.getPropertyIds() : "null");
                    invalidRowCount++;
                }
            }
            
            if (validRowCount == 0) {
                throw new EasyException("Excel文件中没有有效的数据行");
            }
            
            log.info("Excel文件解析完成，有效数据行数：{}，无效行数：{}", validRowCount, invalidRowCount);
            return result;
            
        } catch (IOException e) {
            log.error("读取Excel文件流失败：{}", e.getMessage(), e);
            throw new EasyException("读取Excel文件失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("解析Excel文件失败：{}", e.getMessage(), e);
            if (e instanceof EasyException) {
                throw e;
            } else {
                throw new EasyException("Excel文件解析失败：" + e.getMessage());
            }
        }
    }
    
    /**
     * 校验行数据
     */
    private void validateRowData(Object[] rowData) {
        if (rowData.length < 2) {
            throw new EasyException("数据列数不足，需要2列数据（物模型ID和检查属性ID）");
        }
        
        // 检查必填字段
        if (rowData[0] == null || rowData[0].toString().trim().isEmpty()) {
            throw new EasyException("物模型ID不能为空");
        }
        if (rowData[1] == null || rowData[1].toString().trim().isEmpty()) {
            throw new EasyException("检查属性ID不能为空");
        }
        
        // 验证检查属性ID格式（支持逗号分隔的多个ID）
        String propertyIds = rowData[1].toString().trim();
        if (!propertyIds.matches("^[a-zA-Z0-9_]+(,[a-zA-Z0-9_]+)*$")) {
            throw new EasyException("检查属性ID格式不正确，多个ID请用逗号分隔");
        }
    }
    
    /**
     * 解析行数据为明细列表
     */
    private InspectionItemDetailDTO parseRowToDetails(Object[] rowData) {
        InspectionItemDetailDTO detail = new InspectionItemDetailDTO();
        detail.setModelId(rowData[0].toString()); // 物模型ID在第1列
        detail.setPropertyId(rowData[1].toString()); // 检查属性ID在第2列，保持逗号分隔格式
        
        if (StrUtil.isBlank(detail.getPropertyId())) {
            throw new EasyException("检查属性ID不能为空");
        }
        
        return detail;
    }
    
    /**
     * 生成错误报告文件
     */
    private String generateErrorReport(List<ImportErrorDetail> errorList) {
        // 这里应该生成Excel错误报告文件
        // 临时返回文件路径
        return "/temp/import_errors_" + System.currentTimeMillis() + ".xlsx";
    }
}

