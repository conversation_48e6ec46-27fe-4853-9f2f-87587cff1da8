package com.rc.admin.inspectionitem.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.inspectionitem.dto.InspectionItemDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemQueryDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemValidateDTO;
import com.rc.admin.inspectionitem.dto.InspectionItemImportDTO;
import com.rc.admin.inspectionitem.service.InspectionItemService;
import com.rc.admin.inspectionitem.vo.InspectionItemDetailVO;
import com.rc.admin.inspectionitem.vo.InspectionItemVO;
import com.rc.admin.inspectionitem.vo.ModelPropertyOptionVO;
import com.rc.admin.inspectionitem.vo.ImportResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检查项配置控制器
 * 
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@Api(tags = "检查项管理")
@RestController
@RequestMapping("/api/inspection-items")
public class InspectionItemController {

    @Autowired
    private InspectionItemService inspectionItemService;

    /**
     * 新增检查项
     */
    @ApiOperation("新增检查项")
    @PostMapping
    public Response add(@ApiParam("检查项信息") @RequestBody @Valid InspectionItemDTO dto) {
        log.info("接收新增检查项请求，参数：{}", dto);
        
        try {
            inspectionItemService.addInspectionItem(dto);
            log.info("新增检查项成功，名称：{}", dto.getItemName());
            return Response.success("新增检查项成功");
        } catch (Exception e) {
            log.error("新增检查项失败", e);
            return Response.failError("新增检查项失败：" + e.getMessage());
        }
    }

    /**
     * 修改检查项
     */
    @ApiOperation("修改检查项")
    @PutMapping("/{id}")
    public Response update(@ApiParam("检查项ID") @PathVariable Long id,
                        @ApiParam("检查项信息") @RequestBody @Valid InspectionItemDTO dto) {
        log.info("接收修改检查项请求，ID：{}，参数：{}", id, dto);
        
        try {
            inspectionItemService.updateInspectionItem(id, dto);
            log.info("修改检查项成功，ID：{}", id);
            return Response.success("修改检查项成功");
        } catch (Exception e) {
            log.error("修改检查项失败", e);
            return Response.failError("修改检查项失败：" + e.getMessage());
        }
    }

    /**
     * 删除检查项
     */
    @ApiOperation("删除检查项")
    @DeleteMapping("/{id}")
    public Response delete(@ApiParam("检查项ID") @PathVariable Long id) {
        log.info("接收删除检查项请求，ID：{}", id);
        
        try {
            inspectionItemService.deleteInspectionItem(id);
            log.info("删除检查项成功，ID：{}", id);
            return Response.success("删除检查项成功");
        } catch (Exception e) {
            log.error("删除检查项失败", e);
            return Response.failError("删除检查项失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询检查项
     */
    @ApiOperation("分页查询检查项")
    @GetMapping
    public Response pageQuery(@ApiParam("查询条件") InspectionItemQueryDTO query,
                           @ApiParam("页码") @RequestParam(defaultValue = "1") int pageNum,
                           @ApiParam("页面大小") @RequestParam(defaultValue = "10") int pageSize) {
        log.info("接收分页查询检查项请求，页码：{}，页面大小：{}，查询条件：{}", pageNum, pageSize, query);
        
        try {
            Page<InspectionItemVO> page = inspectionItemService.pageQuery(query, pageNum, pageSize);
            log.info("分页查询检查项完成，总记录数：{}", page.getTotal());
            return Response.success(page);
        } catch (Exception e) {
            log.error("查询检查项失败", e);
            return Response.failError("查询检查项失败：" + e.getMessage());
        }
    }

    /**
     * 查询检查项详情
     */
    @ApiOperation("查询检查项详情")
    @GetMapping("/{id}")
    public Response getDetail(@ApiParam("检查项ID") @PathVariable Long id) {
        log.info("接收查询检查项详情请求，ID：{}", id);
        
        try {
            InspectionItemDetailVO detail = inspectionItemService.getDetail(id);
            if (detail == null) {
                return Response.failError("检查项不存在");
            }
            log.info("查询检查项详情完成，ID：{}", id);
            return Response.success(detail);
        } catch (Exception e) {
            log.error("查询检查项详情失败", e);
            return Response.failError("查询检查项详情失败：" + e.getMessage());
        }
    }

    /**
     * 唯一性校验
     */
    @ApiOperation("唯一性校验")
    @PostMapping("/validate")
    public Response validate(@ApiParam("校验信息") @RequestBody @Valid InspectionItemValidateDTO dto) {
        log.info("接收唯一性校验请求，字段：{}，值：{}", dto.getFieldName(), dto.getFieldValue());
        
        try {
            boolean isUnique = inspectionItemService.validateUniqueness(dto);
            
            Map<String, Object> result = new HashMap<>();
            result.put("isUnique", isUnique);
            result.put("message", isUnique ? "该值可以使用" : "该值已存在");
            
            log.info("唯一性校验完成，字段：{}，结果：{}", dto.getFieldName(), isUnique ? "唯一" : "重复");
            return Response.success(result);
        } catch (Exception e) {
            log.error("唯一性校验失败", e);
            return Response.failError("唯一性校验失败：" + e.getMessage());
        }
    }

    /**
     * 生成检查项编码
     */
    @ApiOperation("生成检查项编码")
    @PostMapping("/generate-code")
    public Response generateCode(@ApiParam("英文名称") @RequestParam String englishName) {
        log.info("接收生成编码请求，英文名称：{}", englishName);
        
        try {
            String itemCode = inspectionItemService.generateItemCode(englishName);
            
            Map<String, Object> result = new HashMap<>();
            result.put("itemCode", itemCode);
            result.put("message", "编码生成成功");
            
            log.info("生成编码完成，英文名称：{}，编码：{}", englishName, itemCode);
            return Response.success(result);
        } catch (Exception e) {
            log.error("生成编码失败", e);
            return Response.failError("生成编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取物模型列表
     */
    @ApiOperation("获取物模型列表")
    @GetMapping("/models")
    public Response getModelList(@ApiParam("事业部编码") @RequestParam(required = false) String divisionCode) {
        log.info("接收获取物模型列表请求，事业部编码：{}", divisionCode);
        
        try {
            List modelList = inspectionItemService.getModelList(divisionCode);
            log.info("获取物模型列表完成，数量：{}", modelList.size());
            return Response.success(modelList);
        } catch (Exception e) {
            log.error("获取物模型列表失败", e);
            return Response.failError("获取物模型列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取物模型属性列表
     */
    @ApiOperation("获取物模型属性列表")
    @GetMapping("/models/{modelId}/properties")
    public Response getModelProperties(@ApiParam("物模型ID") @PathVariable String modelId) {
        log.info("接收获取物模型属性列表请求，物模型ID：{}", modelId);
        
        try {
            List<Object> propertyList = inspectionItemService.getModelProperties(modelId);
            log.info("获取物模型属性列表完成，数量：{}", propertyList != null ? propertyList.size() : 0);
            return Response.success(propertyList);
        } catch (Exception e) {
            log.error("获取物模型属性列表失败", e);
            return Response.failError("获取物模型属性列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取物模型属性下拉选项列表
     */
    @ApiOperation("获取物模型属性下拉选项列表")
    @GetMapping("/models/{modelId}/properties/options")
    public Response getModelPropertyOptions(
            @ApiParam("物模型ID") @PathVariable String modelId,
            @ApiParam("搜索关键词（可选，支持name和displayName模糊搜索）") @RequestParam(required = false) String searchKeyword) {
        log.info("接收获取物模型属性下拉选项请求，物模型ID：{}，搜索关键词：{}", modelId, searchKeyword);
        
        try {
            List<ModelPropertyOptionVO> optionList = inspectionItemService.getModelPropertyOptions(modelId, searchKeyword);
            log.info("获取物模型属性下拉选项完成，数量：{}", optionList != null ? optionList.size() : 0);
            return Response.success(optionList);
        } catch (Exception e) {
            log.error("获取物模型属性下拉选项失败", e);
            return Response.failError("获取物模型属性下拉选项失败：" + e.getMessage());
        }
    }

    /**
     * 同步指定物模型的属性
     */
    @ApiOperation("同步指定物模型的属性")
    @PostMapping("/models/{modelId}/properties/sync")
    public Response syncModelProperties(@ApiParam("物模型ID") @PathVariable String modelId) {
        log.info("接收同步物模型属性请求，物模型ID：{}", modelId);
        
        try {
            inspectionItemService.syncModelProperties(modelId);
            log.info("同步物模型属性完成，物模型ID：{}", modelId);
            return Response.success("同步物模型属性成功");
        } catch (Exception e) {
            log.error("同步物模型属性失败", e);
            return Response.failError("同步物模型属性失败：" + e.getMessage());
        }
    }
    
    /**
     * 下载检查项导入模板
     */
    @ApiOperation("下载检查项导入模板")
    @GetMapping("/download-template")
    public void downloadImportTemplate(HttpServletResponse response) {
        log.info("接收下载导入模板请求");
        
        try {
            inspectionItemService.downloadImportTemplate(response);
            log.info("下载导入模板成功");
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 导入检查项数据
     */
    @ApiOperation("导入检查项数据")
    @PostMapping("/import")
    public Response importData(
            @RequestParam("itemName") @NotEmpty @ApiParam("检查项名称") String itemName,
            @RequestParam("englishName") @NotEmpty @ApiParam("英文名称") String englishName,
            @RequestParam("dataType") @NotNull @ApiParam("数据类型") Integer dataType,
            @RequestParam("description") @ApiParam("描述") String description,
            @RequestParam("importFile") @NotNull @ApiParam("导入文件") MultipartFile file) {
        log.info("接收导入检查项数据请求，名称：{}，英文名称：{}，数据类型：{}", itemName, englishName, dataType);
        
        try {
            // 构建导入DTO
            InspectionItemImportDTO importDTO = new InspectionItemImportDTO();
            importDTO.setItemName(itemName);
            importDTO.setEnglishName(englishName);
            importDTO.setDataType(dataType);
            importDTO.setDescription(description);
            importDTO.setImportFile(file);
            
            ImportResultVO result = inspectionItemService.importData(importDTO);
            log.info("导入检查项数据成功，成功数量：{}，失败数量：{}", result.getSuccessCount(), result.getErrorCount());
            return Response.success(result);
        } catch (Exception e) {
            log.error("导入检查项数据失败", e);
            return Response.failError("导入失败：" + e.getMessage());
        }
    }
}
