package com.rc.admin.inspectionitem.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.inspectionitem.entity.InspectionItemHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查项历史记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2024
 */
@Mapper
public interface InspectionItemHistoryMapper extends BaseMapper<InspectionItemHistory> {

    /**
     * 根据检查项ID查询历史记录
     * 
     * @param inspectionItemId 检查项ID
     * @return 历史记录列表
     */
    List<InspectionItemHistory> selectByInspectionItemId(@Param("inspectionItemId") Long inspectionItemId);

    /**
     * 查询最近的历史记录
     * 
     * @param inspectionItemId 检查项ID
     * @param limit 数量限制
     * @return 历史记录列表
     */
    List<InspectionItemHistory> selectRecentHistory(@Param("inspectionItemId") Long inspectionItemId, 
                                                   @Param("limit") int limit);
}
