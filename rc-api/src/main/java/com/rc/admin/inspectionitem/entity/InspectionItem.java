package com.rc.admin.inspectionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 检查项配置实体类
 * 
 * <AUTHOR>
 * @since 2024
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inspection_items")
@ApiModel(value = "InspectionItem对象", description = "检查项配置表")
public class InspectionItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("检查项编码，英文名称+2位随机数，业务主键，全局唯一")
    @TableField("item_code")
    private String itemCode;

    @ApiModelProperty("检查项名称，业务标识，全局唯一")
    @TableField("item_name")
    private String itemName;

    @ApiModelProperty("英文名称，仅允许字母和下划线，全局唯一")
    @TableField("english_name")
    private String englishName;

    @ApiModelProperty("检查项描述，可为空")
    @TableField("description")
    private String description;

    @ApiModelProperty("数据类型：1-工况，2-定位")
    @TableField("data_type")
    private Integer dataType;

    @ApiModelProperty("创建人")
    @TableField("creator")
    private String creator;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("软删除标记：0-正常，1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    // 关联字段（非数据库字段）
    @TableField(exist = false)
    private List<InspectionItemDetail> details;

    @TableField(exist = false)
    private List<InspectionItemHistory> historyList;
}

