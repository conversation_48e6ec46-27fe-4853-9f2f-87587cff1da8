<%include("../common/head.html"){}%>
<style>
    .map {
        width: 100%;
        height: 660px;
    }
</style>
<div class="case_banner CaseDetai_banner ContactBanner"
     style="background:url(${themeUrl}/assets/images/bg27.jpg) no-repeat 50%/cover;height: 460px">
    <div class="case_banner_content ContactBanner_content clear">
        <div class="ContactBanner_content_left fl">
            <dd>如您有合作意向，欢迎致电我们</dd>
            <dt>15900708016</dt>
        </div>
        <div class="ContactBanner_content_right"><img src="${themeUrl}/assets/images/bg28.jpg"></div>
    </div>
</div>

<div class="IndexService contact">
    <div class="IndexService_content all">

        <div class="recruit">
            <% articles = ArticleUtil.selectArticle(site.id, "recruit", 4, true); %>
            <% if(articles.~size > 0){ %>
            <div class="IndexService_content_title">
                <dd>招聘信息</dd>
            </div>
            <div class="contact_content clear carousel">
                <%for(article in articles){%>
                <div class="contact_content_list fl">
                    <div class="content_list_title">
                        <dt>${article.title}</dt>
                    </div>
                    <div class="content_list_txt">
                        ${article.content}
                    </div>
                </div>
                <%}%>
            </div>
            <div class="column-more">
                <a style="background-color: #f5f5f5" href="/column/recruit.html"><span>Read More</span></a>
            </div>
            <% } %>
        </div>

        <div class="contact_motto clear">
            <div class="contact_motto_left fl">
                <dd>
                    <p>XXX科技有一批专业的开发团队，我们是8090后，我们敢闯、我们敢拼。 永不放弃是我们的口号，永不言败是我们的座右铭。</p>
                    <p>我们是一群平凡的人，凝聚在一起做不平凡的事。</p>
                </dd>
                <dd>
                    <p>梦想：我们也有梦想，我们渴望有一天我们成为业内佼佼者；</p>
                    <p>追求：我们追求完美，希望通过我们的努力为客户提供最完美的产品；</p>
                    <p>执着：我们很执着，我们很直溜，我们为达目的誓不罢休。</p>
                </dd>
                <dd>
                    <p>如果你有热情与兴趣加入XXX团队，你就也可以成为这样的一群人之一。</p>
                    <p>请将简历发送到 <EMAIL>，我们将与你取得联系！</p>
                </dd>
            </div>
            <div class="contact_motto_right fr"><img src="${themeUrl}/assets/images/bn31.png"></div>
        </div>
    </div>
    <div class="contact_motto_map">
        <div class="motto_map_top">
            <div id="map" class="map"></div>
        </div>
        <div class="motto_map_bottom">
            <div class="map_bottom_content">
                <div class="map_bottom_title">
                    <dd>联系我们<span>CONTACT US</span></dd>
                    <dt>
                        <p>电话：15900708016</p>
                        <p>邮箱：<EMAIL></p>
                        <p>地址：上海市闵行区紫星路588号2号楼324室</p>
                    </dt>
                </div>
                <div class="map_bottom_img"><img src="${themeUrl}/assets/images/bg30.jpg"></div>
            </div>
        </div>
    </div>
</div>

<%include("../common/footer.html"){}%>

<script src="${themeUrl}/assets/js/carousel.js"></script>
<script src="https://webapi.amap.com/maps?v=1.4.15&key=3eb487d6495bb284a4cd584f875daf3e"></script>

<script>
    $(document).ready(function ($) {
        $('.carousel').owlCarousel({
            loop: false,
            margin: 20,
            nav: true,
            lazyLoad: true,
            autoplay: false,//是否开启自动播放
            autoplayTimeout: 1000,//控制自动播放的速度
            merge: true,
            video: true,
            responsive: {
                120: {items: 1},//当屏幕大小缩小到480的时候变2个
                480: {items: 1},//当屏幕大小缩小到480的时候变2个
                678: {items: 2},//当屏幕大小缩小到678的时候变3个
                960: {items: 3},//当屏幕大小缩小到960的时候变5个
                1200: {items: 4, margin: 10},
                1440: {items: 4, margin: 20},
            }
        });

        var isMobileDevice = $(window).width() > 520;

        var map = new AMap.Map('map', {
            resizeEnable: true,
            zoom: isMobileDevice ? 16 : 14,
            center: isMobileDevice ? [121.458969, 31.02115] : [121.463969, 31.02115]
        });

        var marker = new AMap.Marker({
            position: [121.463969, 31.02115]
        });

        map.add(marker);
    });
</script>
</body>
</html>