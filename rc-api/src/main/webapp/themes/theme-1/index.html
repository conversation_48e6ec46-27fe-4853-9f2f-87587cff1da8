<%include("./common/head.html"){}%>

<div class="banner">
    <div class="slick-hero-slider-wrapper">
        <div class="slider slick-hero-slider slick-slider-center-mode slick-animation slick-inner-dot alt-dot-position with-main-search-wrapper-2">
            <div class="slick-item">
                <div class="image-bg"
                     style=" background:url(${themeUrl}/assets/images/index_banner.jpg) no-repeat 50%/cover">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12 col-sm-10 col-sm-offset-1 col-md-10">
                                <div class="slick-hero-slider-caption">
                                    <dd class="animation fromBottom transitionDelay2">
                                        <img src="${themeUrl}/assets/images/bn1.png">
                                    </dd>
                                    <div class="animation fromBottom transitionDelay4">
                                        十余载致力于互联网技术服务、开发及应用，为企事业单位提供一站式、完善的整合网络品牌服务
                                    </div>
                                    <dd class="animation fromBottom transitionDelay6">
                                        <p>More than ten years dedicated to Internet technology services, development
                                            and application,</p>
                                        <p>providing a one-stop, integrated network brand service for enterprises and
                                            institutions.</p>
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="slick-item">
                <div class="image-bg Network_bg"
                     style=" background:url(${themeUrl}/assets/images/index_banner4.jpg) no-repeat 50%/cover">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12 col-sm-10 col-sm-offset-1 col-md-10">
                                <div class="slick-hero-slider-caption Network">
                                    <dd class="animation fromBottom transitionDelay2">Jinjin Tech</dd>
                                    <div class="animation fromBottom transitionDelay4">满足全行业各场景的直播解决方案</div>
                                    <div class="animation fromBottom transitionDelay4">已服务客户2000+</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="slick-item">
                <div class="image-bg"
                     style=" background:url(${themeUrl}/assets/images/index_banner5.jpg) no-repeat 50%/cover">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12 col-sm-10 col-sm-offset-1 col-md-10">
                                <div class="slick-hero-slider-caption year">
                                    <b class="bg2"><img src="${themeUrl}/assets/images/banner_yuan.png"></b>
                                    <dd><img style="width: 40%;height: auto" src="${themeUrl}/assets/images/banner_title.png"></dd>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="IndexService">
    <div class="IndexService_content all wow fadeIn">
        <div class="IndexService_content_title">
            <dd>产品与服务 / Products and Services</dd>
            <dt>专业团队打造精品网站</dt>
        </div>
        <div class="IndexService_content_body clear carousel">
            <a href="/column/product.html" class="content_body_list fl" target="_blank">
                <div class="body_list_title">高端网站定制</div>
                <div class="body_list_content">需求分析-创意设计-程序制作-资料添加-测试交付每一步我们都追求精致</div>
                <div class="body_list_classification clear">
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe70b;</dd>
                        <dt>企业官网</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe6d2;</dd>
                        <dt>外贸网站</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe659;</dd>
                        <dt>综合门户</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe606;</dd>
                        <dt>电商网站</dt>
                    </div>
                </div>
            </a>
            <a href="/column/product.html" class="content_body_list fl" target="_blank">
                <div class="body_list_title">微信开发</div>
                <div class="body_list_content">基于微信为企业提供开发、运营、培训、推广一体化解决方案，帮助企业实现线，上线下互通</div>
                <div class="body_list_classification clear">
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe62e;</dd>
                        <dt>吸粉系统</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe61e;</dd>
                        <dt>分销系统</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe600;</dd>
                        <dt>互动系统</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe601;</dd>
                        <dt>交易系统</dt>
                    </div>
                </div>
            </a>
            <a href="/column/product.html" class="content_body_list fl" target="_blank">
                <div class="body_list_title">APP开发</div>
                <div class="body_list_content">完全个性化设计，专属定制研发应用软件，安全稳定的技术运维服务，为您提供多方位解决方案</div>
                <div class="body_list_classification clear">
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe633;</dd>
                        <dt>HTML5开发</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe60b;</dd>
                        <dt>安卓APP开发</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe6c0;</dd>
                        <dt>IOS APP开发</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe748;</dd>
                        <dt>运维管理</dt>
                    </div>
                </div>
            </a>
            <a href="/column/product.html" class="content_body_list fl" target="_blank">
                <div class="body_list_title">网络营销</div>
                <div class="body_list_content">根据企业行业特点和自身需求整合企业网络资源提供长期的网络营销服务支持</div>
                <div class="body_list_classification clear">
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe634;</dd>
                        <dt>口碑营销</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe62d;</dd>
                        <dt>Seo优化</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe6af;</dd>
                        <dt>SEM托管</dt>
                    </div>
                    <div class="list_classification_list fl">
                        <dd class="iconfont">&#xe62b;</dd>
                        <dt>舆情公关</dt>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>


<div class="IndexService IndexCase" id="digital">
    <div class="IndexService_content all wow fadeIn">
        <div class="IndexService_content_title">
            <dd>案例展示 / case show</dd>
            <dt>
                <p>累计为超过2000家客户提供互联网技术解决或咨询服务，包含国际知名企业，如：Polylink，海尔集团，普天集团；国有事业单位：如上海应用物理研究所、</p>
                <p>上海海洋大学等；涉及：金融、地产、物业、制造、家居、营销、传媒文化、餐饮、电子科技数码等多个行业</p>
            </dt>
        </div>
        <div class="IndexCase_content clear">

            <%for(article in ArticleUtil.selectArticle(site.id, "case", 9)){%>
            <div class="IndexCase_content_list fl wow fadeInUp" data-wow-delay="0.1s">
                <div class="content_list_img">
                    <div><img src="${baseUrl}${article.coverPath}" class="tra" alt="${article.title}">
                    </div>
                    <div class="content_list_hover iconfont">
                        <dd></dd>
                        <dt>&#xe657;</dt>
                        <div class="hr"><a href="${article.url}" title="${article.title}" target="_blank">
                            <img src="${themeUrl}/assets/images/bn7.png"></a>
                        </div>
                    </div>
                </div>
                <div class="content_list_title clear">
                    <div class="list_title_left fl">
                        <dd><a href="${article.url}" target="_blank">${article.title}</a><i>|</i><span>${article.subtitle}</span>
                        </dd>
                        <dt>${ArticleUtil.formatTags(article.tags)}</dt>
                    </div>
                    <div class="list_title_icon fr iconfont">&#xe614;</div>
                </div>
            </div>
            <%}%>
        </div>
        <div class="column-more">
            <a href="/column/case.html"><span>Read More</span></a>
        </div>
    </div>
</div>


<div class="advantage clear">
    <div class="advantage_content all">
        <div class="advantage_content_list fl">
            <div class="content_list_title">
                <span class="counter-demo-4 box" data-lem-counter='{"value_from": 0, "value_to": 9, "animate_duration": 2}'>9</span>
            </div>
            <div class="content_list_body">
                <p>XXX科技9年专注于网站建设</p>
                <p>因为专注，所以专业</p>
            </div>
        </div>
        <div class="advantage_content_list fl">
            <div class="content_list_title">
                <span class="counter-demo-4 box" data-lem-counter='{"value_from": 0, "value_to": 1000, "animate_duration": 2}'>1000</span>
            </div>
            <div class="content_list_body">
                <p>迄今为止我们服务的客户超过</p>
                <p>1000家，遍及各大行业</p>
            </div>
        </div>
        <div class="advantage_content_list fl">
            <div class="content_list_title">
                <span class="counter-demo-4 box" data-lem-counter='{"value_from": 0, "value_to": 100, "animate_duration": 2}'>100</span>%
            </div>
            <div class="content_list_body">
                <p>百分百的独特创意设计，纯定制</p>
                <p>开发，让您的网站与众不同</p>
            </div>
        </div>
        <div class="advantage_content_list fl">
            <div class="content_list_title">
                <span class="counter-demo-4 box" data-lem-counter='{"value_from": 0, "value_to": 99, "animate_duration": 2}'>99</span>%
            </div>
            <div class="content_list_body">
                <p>良好的口碑是我们立足业界</p>
                <p>的根本，客户满意度超99%</p>
            </div>
        </div>
    </div>
</div>

<div class="IndexService IndexNews">
    <div class="IndexService_content all wow fadeIn">
        <div class="IndexService_content_title clear">
            <dd class="fl">新闻动态 / news information</dd>
        </div>
        <div class="IndexNews_content clear">
            <div class="IndexNews_content_video fl">
                <%for(article in ArticleUtil.selectArticle(site.id, "home-video", 1, true)){%>
                <div class="content_video_title look_video">
                    <div class="videobox">
                        <div class="videobox_nei">
                            ${article.content}
                        </div>
                    </div>
                </div>
                <div class="content_video_body">
                    <dd><span class="iconfont">&#xe61b;</span>${article.title}</dd>
                    <dt>${article.excerpt}</dt>
                </div>
                <%}%>
            </div>
            <div class="IndexNews_content_right fr clear">

                <%for(article in ArticleUtil.selectArticle(site.id, "news", 3)){%>
                <div class="content_right_list clear fl">
                    <div class="right_list_img fl">
                        <a href="${article.url}">
                            <img src="${baseUrl}${article.coverPath}" class="tra" alt="${article.title}">
                        </a>
                    </div>
                    <div class="right_list_content fr clear">
                        <div class="list_content_title clear">
                            <dd class="fl"><a href="${article.url}">${article.title}</a></dd>
                            <dt class="fr">${article.releaseDate,dateFormat='MM-dd'}</dt>
                        </div>
                        <p>${article.excerpt}</p>
                    </div>
                </div>
                <%}%>
            </div>
        </div>
    </div>
</div>


<div class="IndexService partner">
    <div class="IndexService_content all wow fadeIn">
        <div class="IndexService_content_title">
            <dd>我们与您携手共进</dd>
        </div>
        <div class="partner_content clear">
            <%for(article in ArticleUtil.selectArticle(site.id, "partner", 18)){%>
            <div class="partner_content_list fl"><img src="${baseUrl}${article.coverPath}" class="gray"></div>
            <%}%>
        </div>
    </div>
</div>


<%include("./common/footer.html"){}%>
<script src="${themeUrl}/assets/js/banner.js"></script>
<script src="${themeUrl}/assets/js/digital.js"></script>
<script src="${themeUrl}/assets/js/wow.js"></script>
<script src="${themeUrl}/assets/js/carousel.js"></script>


<!--视频播放-->
<script type="text/javascript">
    <!--服务项目-->
    $(document).ready(function ($) {
        $('.carousel').owlCarousel({
            loop: false,
            margin: 0,
            nav: true,
            lazyLoad: true,
            autoplay: false,//是否开启自动播放
            autoplayTimeout: 1000,//控制自动播放的速度
            merge: true,
            video: true,
            responsive: {
                120: {items: 1},//当屏幕大小缩小到480的时候变2个
                480: {items: 1},//当屏幕大小缩小到480的时候变2个
                678: {items: 2},//当屏幕大小缩小到678的时候变3个
                960: {items: 3},//当屏幕大小缩小到960的时候变5个
                1200: {items: 4},
            }
        });
    });


    <!--动态特效-->
    wow = new WOW(
        {
            animateClass: 'animated',
            offset: 200
        }
    );
    wow.init();


    //判断浏览器大小加上事件
    var width = $(window).width();
    if (width <= 700) {
        wow = new WOW(
            {
                animateClass: 'animated',
                offset: 10
            }
        );
        wow.init();

        function fixed_gun(obj) {
            var m_top = $(obj).offset().top;
            $(window).scroll(function () {
                if ($(window).scrollTop() >= m_top - 300) {
                    onlyOne();
                }
            });
        };
    }

    //数字滚动
    fixed_gun("#digital");
    var flag = 1;

    function onlyOne() {
        if (flag == 1) {
            $('.counter-demo-4').lemCounter();
        }
        flag = 0;
    }

    function fixed_gun(obj) {
        var m_top = $(obj).offset().top;
        $(window).scroll(function () {
            if ($(window).scrollTop() >= m_top + 300) {
                onlyOne();
            }
        });
    }


    <!--首页banner图-->
    $('.slick-hero-slider').slick({
        dots: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        centerMode: true,
        infinite: true,
        centerPadding: '0',
        focusOnSelect: true,
        adaptiveHeight: false,
        autoplay: true,
        autoplaySpeed: 6000,
        pauseOnHover: false,
    });
</script>
</body>
</html>