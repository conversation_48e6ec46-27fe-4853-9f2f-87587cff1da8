
.owl-carousel .owl-stage:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}

.owl-carousel{
	display: none;
	width: 100%;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	/* position relative and z-index fix webkit rendering fonts issue */
	position: relative;
	z-index:1;
}


.owl-carousel .owl-stage{
	position: relative;
	-ms-touch-action: pan-Y;
}

.owl-carousel .owl-stage-outer{
	position:relative;
	overflow: hidden;
	/* temporary fix for flashing background */
	-webkit-transform: translate3d(0px, 0px, 0px);
}

.owl-carousel .owl-stage-outer.owl-height{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}

.owl-carousel .owl-video-wrapper{
	position: relative;
	height: 100%;
	background: #000;
}

.owl-controls .owl-nav div,
.owl-controls .owl-dot{
	cursor: pointer;
	cursor: hand;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.owl-carousel .owl-video-play-icon{
	position:absolute;
	height: 80px;
	width: 80px;
	left: 50%;
	top: 50%;
	margin-left: -40px;
	margin-top: -40px;
	background: url('owl-video-play.png') no-repeat;
	cursor: pointer;
	z-index: 1;
	-webkit-backface-visibility: hidden;
	transition-property: scale;
	transition-duration: 100ms;
	transition-timing-function: ease;
}

.owl-carousel .owl-video-play-icon:hover{
	-moz-transform: scale(1.3, 1.3);
	-ms-transform: scale(1.3, 1.3);
	-o-transform: scale(1.3, 1.3);
	-webkit-transform: scale(1.3, 1.3);
	transform: scale(1.3, 1.3);
}

.owl-carousel .owl-video-playing .owl-video-tn,
.owl-carousel .owl-video-playing .owl-video-play-icon{
	display: none;
}

.owl-carousel .owl-video-tn {
	opacity: 0;
	height: 100%;
	background-position: center center;
	background-repeat: no-repeat;

	-webkit-background-size: contain;
	-moz-background-size: contain;
	-o-background-size: contain;
	background-size: contain;

	transition-property: opacity;
  	transition-duration: 400ms;
  	transition-timing-function: ease;
}

.owl-carousel .owl-video-frame{
	position: relative;
	z-index: 1;
}

.owl-loaded{
	display: block;
}
.owl-loading{
	opacity: 0;
	display: block;
}
.owl-hidden{
	opacity:0;
}
.owl-carousel .owl-refresh .owl-item{
	display: none;
}

.owl-carousel .owl-item{
	position:relative;
	min-height: 1px;
	float: left;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: rgba(0,0,0,0); 
	-webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
	text-align:center;
}
/* If mouseDrag:false then you are able to select text */
.owl-carousel.owl-text-select-on .owl-item{
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
}

.owl-carousel .owl-grab {
	cursor: move;
	cursor: -webkit-grab;
	cursor: -o-grab;
	cursor: -ms-grab;
	cursor: grab;
}

.owl-carousel .owl-item img.owl-lazy{
	opacity: 0;
}


/* to do */
.owl-carousel .owl-stage.backfacefix .owl-item{
	-webkit-backface-visibility: hidden;
}

.owl-rtl{
	direction: rtl;
}
.owl-rtl .owl-item {
	float:right;
}

/* No Js */
.no-js .owl-carousel{
	display: block;
}

/* animate */

.animated {
  -webkit-animation-duration: 600ms;
  animation-duration: 600ms;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.owl-animated-in{
	z-index: 0
}
.owl-animated-out{
	z-index: 1
}

.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}



.owl-theme .owl-controls{
	text-align: center;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	position: absolute;
	left: 0; right: 0; margin: 0 auto;
}
/* Styling Next and Prev buttons */
.owl-theme .owl-controls .owl-nav div{ width:21px; height:40px; position:absolute; top:-135px;}
.owl-theme .owl-controls .owl-dots{ margin-top:30px; display: none !important;}

.owl-theme .owl-controls .owl-nav .owl-prev{ left:-55px; background:url(../images/news_prev.png) no-repeat 100%}
.owl-theme .owl-controls .owl-nav .owl-next{ right:-55px; background:url(../images/news_next.png) no-repeat 100%}


@media screen and (max-width:1200px){
.owl-theme .owl-controls .owl-nav .owl-prev,.team_content_wen .owl-theme .owl-controls .owl-nav .owl-prev{ left:0 !important;}
.owl-theme .owl-controls .owl-nav .owl-next,.team_content_wen .owl-theme .owl-controls .owl-nav .owl-next{ right:0!important;}

}

/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls .owl-nav div:hover{
	text-decoration: none;
}

/* Styling dots*/
.owl-theme .owl-dots .owl-dot{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
}

.owl-theme .owl-dots .owl-dot span{
	-webkit-backface-visibility: visible;
	display: block;
	width: 13px;
	height: 13px;
	margin: 5px 7px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	background: #bfbfbf;
	transition-property: opacity;
  	transition-duration: 200ms;
  	transition-timing-function: ease;
}

.owl-theme .owl-dots .owl-dot.active span{
	background:#00a0ae;
}
