@charset "utf-8"; /* CSS Document */
body {
    margin: 0;
    padding: 0;
    border: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Liberation Sans", "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", SimH<PERSON>, SimSun, "WenQuanYi Zen Hei Sharp", sans-serif;
    color: #555555;
    font-size: 14px;
    width: 100%;
    background: #000;
}

a {
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    color: #555555;
    font-size: 13px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

a:hover {
    text-decoration: none;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

* {
    padding: 0;
    margin: 0;
    border: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

/* background:rgba(0,0,0,0);filter:progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7F000000,endcolorstr=#7F000000);*/
li {
    list-style: none;
}

.clear:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

/* 触发 hasLayout */
img {
    vertical-align: middle;
}

input:focus {
    outline: none;
}

/*去除按钮选中的高亮标签*/
textarea:focus {
    outline: none;
}

/*去除文本框选中的高亮标签*/
button:focus {
    outline: none;
}

/*去除按钮选中的高亮标签*/
select:focus {
    outline: none;
}

/*去除下拉框选中的高亮标签*/
img {
    vertical-align: middle;
}

div, p, table, td, ul, li, span, a, ol, input, label {
    padding: 0;
    margin: 0;
    border: 0;
}

input::-ms-input-placeholder { /* Internet Explorer 10+ */
    color: #999;
    font-size: 14px;
}

input::-webkit-input-placeholder { /* WebKit browsers */
    color: #999;
    font-size: 14px;
}

input::-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #999;
    font-size: 14px;
}

input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #999;
    font-size: 14px;
}

input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #999;
    font-size: 14px;
}

textarea::-webkit-input-placeholder {
    color: #999;
    font-size: 13px;
    font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei"
}

.search input::-ms-input-placeholder { /* Internet Explorer 10+ */
    color: #89a4d5;
    font-size: 13px;
}

.search input::-webkit-input-placeholder { /* WebKit browsers */
    color: #89a4d5;
    font-size: 13px;
}

.search input::-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #89a4d5;
    font-size: 13px;
}

.search input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #89a4d5;
    font-size: 13px;
}

.search input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #89a4d5;
    font-size: 13px;
}

video::-internal-media-controls-download-button {
    display: none;
}

video::-webkit-media-controls-enclosure {
    overflow: hidden;
}

video::-webkit-media-controls-panel {
    width: calc(100% + 30px);
}

.mo_down {
    height: 0;
    overflow: hidden
}

.mo_down.on {
    height: auto;
}

input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px white inset;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

/*渐变从左向右*/
.jianbian2 {
    height: 200px;
    background: -webkit-linear-gradient(to right, blue, yellow); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(to right, blue, yellow); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(to right, blue, yellow); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, white, yellow); /* 标准的语法（必须放在最后） */
}

/*黑白照片*/
.gray {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: gray;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
}

/*圆角5px*/
.radius5 {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
}

/*圆角10px*/
.radius10 {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px;
}

/*图片变大*/
.tra {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    filter: scale(1);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.tra:hover {
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    -ms-transform: scale(1.15);
    -o-transform: scale(1.15);
    filter: scale(1.15);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

/*透明度50%*/
.opacity5 {
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
}

.shadow1 {
    -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px;
    -moz-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px;
    -ms-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px;
    -o-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px;
}

.shadow2 {
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow2:hover {
    -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 15px;
    -moz-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 15px;
    -ms-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 15px;
    -o-box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 15px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 15px;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

/*投影*/
.shadow5 {
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow7 {
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow7:hover {
    -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px;
    -moz-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px;
    -ms-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px;
    -o-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow1_1 {
    -webkit-box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 5px;
    -moz-box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 5px;
    -ms-box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 5px;
    -o-box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 5px;
    box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 5px;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow5:hover {
    -webkit-box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    -moz-box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    -ms-box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    -o-box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
}

.shadow8 {
    -webkit-box-shadow: rgba(0, 0, 0, 0.05) 5px 5px 5px;
    -moz-box-shadow: rgba(0, 0, 0, 0.05) 5px 5px 5px;
    -ms-box-shadow: rgba(0, 0, 0, 0.05) 5px 5px 5px;
    -o-box-shadow: rgba(0, 0, 0, 0.05) 5px 5px 5px;
    box-shadow: rgba(0, 0, 0, 0.05) 5px 5px 5px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.shadow6 {
    -webkit-box-shadow: rgba(0, 0, 0, 0.3) 5px 5px 60px;
    -moz-box-shadow: rgba(0, 0, 0, 0.3) 5px 5px 60px;
    -ms-box-shadow: rgba(0, 0, 0, 0.3) 5px 5px 60px;
    -o-box-shadow: rgba(0, 0, 0, 0.3) 5px 5px 60px;
    box-shadow: rgba(0, 0, 0, 0.3) 5px 5px 60px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

/*投影*/
.shadow10 {
    -webkit-box-shadow: rgba(0, 0, 0, 0.13) 0px 0px 10px;
    -moz-box-shadow: rgba(0, 0, 0, 0.13) 0px 0px 10px;
    -ms-box-shadow: rgba(0, 0, 0, 0.13) 0px 0px 10px;
    -o-box-shadow: rgba(0, 0, 0, 0.13) 0px 0px 10px;
    box-shadow: rgba(0, 0, 0, 0.13) 0px 0px 10px;
}

/*图片居中*/
.center {
    -webkit-align-items: center;
    display: -webkit-flex;
    -webkit-justify-content: star;
}

/*图片居中2*/
.vertical {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex; /* 水平居中*/
    -webkit-box-align: center;
    -moz-box-align: center;
    -ms-flex-pack: center; /* IE 10 */
    -webkit-justify-content: center;
    -moz-justify-content: center;
    justify-content: center; /* IE 11+,Firefox 22+,Chrome 29+,Opera 12.1*//* 垂直居中 */
    -webkit-box-pack: center;
    -ms-flex-align: center;
    -moz-box-pack: center; /* IE 10 */
    -webkit-align-items: center;
    -moz-align-items: center;
    align-items: center;
}

/*图片翻转*/
.flip {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.flip:hover {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
    cursor: pointer;
}

.pc_header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.25);
    height: 88px;
    z-index: 50;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header.pc_header_fixed {
    background: #222;
    border: 0;
}

.pc_header .pc_header_content {
    max-width: 1500px;
    margin: 0 auto;
}

.pc_header .pc_header_content .header_content_logo .site-name {
    display: inline-block;
    color: #fff;
    font-size: 32px;
    line-height: 88px;
}

.pc_header .pc_header_content .header_content_logo img {
    height: 42px;
    margin-top: -10px;
}

.pc_header .pc_header_content .header_content_right .menu ul li {
    margin-right: 47px;
}

.pc_header .pc_header_content .header_content_right .menu ul li .menu_title {
    font-size: 16px;
    color: #fff;
    display: inline-block;
    padding: 0 10px;
    line-height: 88px;
    position: relative;
    text-align: center;
}

.pc_header .pc_header_content .header_content_right .menu ul li:hover .DropDown .DropDown_close {
    color: #fff;
    font-size: 28px;
    position: absolute;
    right: 10px;
    top: 10px;
    display: none;
}

.pc_header .pc_header_content .header_content_right .menu ul li:hover .DropDown {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    filter: scale(1);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
    opacity: 1;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown {
    position: absolute;
    width: 100%;
    left: 0;
    background: rgba(0, 0, 0, .5);
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    filter: scale(0);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
    opacity: 0;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown.on {
    -webkit-transform: scale(1) !important;
    -moz-transform: scale(1) !important;
    -ms-transform: scale(1) !important;
    -o-transform: scale(1) !important;
    filter: scale(1) !important;
    filter: transform;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
    cursor: pointer;
    opacity: 1;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown.in {
    -webkit-transform: scale(0) !important;
    -moz-transform: scale(0) !important;
    -ms-transform: scale(0) !important;
    -o-transform: scale(0) !important;
    filter: scale(0) !important;
    filter: transform;
    transition: all 0.3s ease 0s;
    -webkit-transform: all 0.3s ease 0s;
    cursor: pointer;
    opacity: 0;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown .DropDown_content {
    max-width: 1500px;
    margin: 0 auto;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown a:last-child {
    border: 0;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown a {
    display: inline-block;
    width: 25%;
    color: #fff;
    font-size: 14px;
    text-align: center;
    padding: 25px 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown a.on dd {
    color: #ff7019;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown a dd {
    font-size: 35px;
    margin-bottom: 15px;
}

.pc_header .pc_header_content .header_content_right .menu ul li .DropDown a:hover dd {
    color: #ff7019;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header .pc_header_content .header_content_right .menu ul li .menu_title:before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: inline-block;
    height: 1px;
    background: #ff7019;
    opacity: 0;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header .pc_header_content .header_content_right .menu ul li.on .menu_title:before {
    opacity: 1;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header .pc_header_content .header_content_right .menu ul li:hover .menu_title:before {
    opacity: 1;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.pc_header .pc_header_content .header_content_right .menu ul li:hover .menu_title {
    color: #ff7019;
}

.pc_header .pc_header_content .header_content_right .menu ul li.on .menu_title {
    color: #ff7019;
}

.pc_header .pc_header_content .header_content_right .header_content_phone {
    font-size: 24px;
    color: #fff;
    line-height: 88px;
}

.pc_header .pc_header_content .header_content_right .header_content_phone span {
    display: inline-block;
    width: 34px;
    height: 34px;
    border-radius: 100%;
    background: #5e5e5c;
    line-height: 34px;
    text-align: center;
    margin-right: 11px;
    position: relative;
    top: -3px;
    color: #fff;
}

.case_banner {
    height: 460px;
}

.details_banner {
    height: 460px;
}

.WeChatBanner {
    height: 460px;
}

.IndexService {
    background: #f6f6f6;
}

.IndexService .IndexService_content {
    max-width: 1500px;
    margin: 0 auto;
    padding: 75px 0;
}

.IndexService .IndexService_content .IndexService_content_title {
    text-align: center;
    padding-bottom: 55px;
}

.IndexService .IndexService_content .IndexService_content_title dd {
    font-size: 30px;
    color: #333;
    text-transform: uppercase;
}

.IndexService .IndexService_content .IndexService_content_title dt {
    font-size: 16px;
    color: #999;
    margin-top: 12px;
    line-height: 28px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list {
    width: 100%;
    background: url(../images/bn2.png) no-repeat 100% 50%;
    padding: 0px 50px;
}

.IndexService .IndexService_content .IndexService_content_body .owl-item:first-child .content_body_list {
    padding-left: 0;
}

.IndexService .IndexService_content .IndexService_content_body .owl-item:last-child .content_body_list {
    padding-right: 0;
    background: none;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_title {
    font-size: 20px;
    color: #333;
    text-align: center;
    margin-bottom: 15px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_content {
    font-size: 13px;
    color: #666;
    text-align: center;
    line-height: 26px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification {
    padding-top: 50px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list {
    display: inline-block;
    width: 50%;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    padding: 20px 0;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:hover dd {
    color: #ff7019;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:hover dt {
    color: #ff7019;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:nth-child(2n) {
    border-right: 0;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:nth-child(1) {
    padding-top: 10px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:nth-child(2) {
    padding-top: 10px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:nth-child(3) {
    border-bottom: 0;
    padding-bottom: 10px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list:nth-child(4) {
    border-bottom: 0;
    padding-bottom: 10px;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list dd {
    color: #222222;
    font-size: 28px;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification .list_classification_list dt {
    color: #333;
    font-size: 14px;
    margin-top: 10px;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase {
    background: #fff;
}

.IndexCase .IndexCase_content .IndexCase_content_list {
    display: inline-block;
    width: 32%;
    margin-right: 2%;
    margin-bottom: 2%;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 25px;
    position: relative;
}

.IndexCase .IndexCase_content .IndexCase_content_list:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 0%;
    left: 0;
    background: #ff7019;
    bottom: -1px;
    height: 1px;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list:nth-child(3n) {
    margin-right: 0;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_title .list_title_icon {
    color: #ff7019;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover:before {;
    width: 100%;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_img .tra {
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    -ms-transform: scale(1.15);
    -o-transform: scale(1.15);
    filter: scale(1.15);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_img {
    overflow: hidden;
    position: relative;
    background: #000;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_img a {
    display: inline-block;
    width: 100%;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_img img {
    width: 100%;
    height: 330px;
    object-fit: cover;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title {
    margin-top: 25px;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left {
    width: 88%;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd {
    color: #333;
    font-size: 20px;
    line-height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd a {
    color: #333;
    font-size: 20px;
    line-height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd span {
    font-size: 16px;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd i {
    font-style: inherit;
    padding: 0px 15px;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dt {
    color: #999;
    font-size: 13px;
    margin-top: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_icon {
    color: #bbbbbb;
    font-size: 30px;
    line-height: 52px;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_hover dt {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 5;
    color: #fff;
    font-size: 35px;
    opacity: 0;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_hover dd {
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    opacity: 0;
    display: none;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_hover .hr {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 5;
}

.IndexCase .IndexCase_content .IndexCase_content_list .content_list_hover .hr img {
    width: auto;
    height: auto;
    opacity: 0;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_img img {
    opacity: .6;
    transition: all 0.5s ease;
    -webkit-transform: all 0.5s ease;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_hover dd {
    opacity: 1;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_hover .hr img {
    opacity: 1;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_hover dt {
    opacity: 1;
}

.IndexCase .IndexCase_content .IndexCase_content_list:hover .content_list_hover .hr img {
    -webkit-transform: rotate(360deg);
    animation: rotation 10s linear infinite;
    -moz-animation: rotation 10s linear infinite;
    -webkit-animation: rotation 10s linear infinite;
    -o-animation: rotation 10s linear infinite;
}

@-webkit-keyframes rotation {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}

.column-more {
    text-align: center;
    margin-top: 15px;
}

.column-more a {
    background: #eeeeee;
    display: inline-block;
    width: 185px;
    line-height: 64px;
    text-transform: capitalize;
    font-size: 16px;
    color: #999;
    font-weight: bolder;
    position: relative;
}

.column-more a:hover {
    color: #fff;
}

.column-more a:hover:before {
    opacity: 1;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: rgba(255, 112, 25, 1);
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.column-more a:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 50px;
    height: 50px;
    background: rgba(255, 112, 25, 0);
    border-radius: 100%;
    left: 50%;
    opacity: 0;
    top: 50%;
    -webkit-transform: all 0.5s ease 0s;
    transform: translateX(-50%) translateY(-50%);
    transition: all 0.5s ease 0s;
}

.column-more a span {
    position: relative;
    z-index: 1;
}

.advantage {
    background: url(../images/bg10.jpg) no-repeatd 50%/cover;
    height: 532px;
}

.advantage .advantage_content {
    max-width: 1500px;
    margin: 0 auto;
}

.advantage .advantage_content .advantage_content_list {
    padding-top: 80px;
    padding-left: 47px;
    height: 532px;
    width: 20%;
    margin-left: 5%;
}

.advantage .advantage_content .advantage_content_list:nth-child(1) {
    background: url(../images/bn3.png) no-repeat 0% 0%;
    margin-left: 0;
}

.advantage .advantage_content .advantage_content_list:nth-child(2) {
    background: url(../images/bn4.png) no-repeat 0% 0%;
}

.advantage .advantage_content .advantage_content_list:nth-child(3) {
    background: url(../images/bn5.png) no-repeat 0% 0%;
}

.advantage .advantage_content .advantage_content_list:nth-child(4) {
    background: url(../images/bn6.png) no-repeat 0% 0%;
}

.advantage .advantage_content .advantage_content_list .content_list_title {
    font-size: 70px;
    color: #fff;
    font-family: "Impact";
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 25px;
}

.advantage .advantage_content .advantage_content_list .content_list_title:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 40px;
    height: 1px;
    background: #ff7019;
    left: 0;
    bottom: 0;
}

.advantage .advantage_content .advantage_content_list .content_list_body p {
    font-size: 16px;
    color: #fff;
    margin-bottom: 5px;
}

.advantage .advantage_content .advantage_content_list .content_list_body p:last-child {
    margin-bottom: 0;
}

.video_title_item.on {
    display: none;
}

.video_title_item {
    z-index: 2111;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.video_title_item dd {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    color: #fff;
    font-size: 50px;
}

.video_title_item dt {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

@keyframes scale {
    0% {
        transform: scale(1);
    }
    50%, 75% {
        transform: scale(2.5);
    }
    78%, 100% {
        opacity: 0;
    }
}

.IndexNews {
    background: #fff;
}

.IndexNews .IndexService_content .IndexService_content_title dd {
    line-height: 50px;
}

.IndexNews .IndexNews_title a {
    width: 120px;
    line-height: 50px;
    display: inline-block;
    background: #eeeeee;
    font-size: 16px;
    color: #333;
    margin-right: 13px;
}

.IndexNews .IndexNews_title a:hover {
    color: #fff;
    background: #ff7019;
}

.IndexNews .IndexNews_title a.on {
    color: #fff;
    background: #ff7019;
}

.IndexNews .IndexNews_title a:last-child {
    margin-right: 0;
}

.IndexNews .IndexNews_content .IndexNews_content_video {
    width: 38%;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_title {
    cursor: pointer;
    position: relative;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_title img {
    width: 100%;
    height: 370px;
    object-fit: cover;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_body {
    border: 1px solid #e5e5e5;
    border-top: 0;
    padding: 25px 25px;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_body dd {
    font-size: 18px;
    color: #333;
    line-height: 30px;
    margin-bottom: 15px;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_body dd span {
    font-size: 30px;
    position: relative;
    top: 3px;
    margin-right: 12px;
}

.IndexNews .IndexNews_content .IndexNews_content_video .content_video_body dt {
    color: #666;
    font-size: 14px;
    line-height: 22px;
    display: -webkit-box;
    height: 42px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.IndexNews .IndexNews_content .IndexNews_content_right {
    width: 57%;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list {
    display: inline-block;
    width: 100%;
    margin-bottom: 31px;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list:last-child {
    margin-bottom: 0;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img a {
    display: inline-block;
    width: 100%;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img {
    width: 25%;
    overflow: hidden;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list:hover .tra {
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    -ms-transform: scale(1.15);
    -o-transform: scale(1.15);
    filter: scale(1.15);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list:hover .right_list_content .list_content_title dd {
    color: #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: inline-block;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content {
    width: 72%;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 15px;
    line-height: 40px;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dd {
    width: 85%;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dd a {
    font-size: 18px;
    color: #333;
    width: 100%;
    transition: all 0.5s ease 0s;
    display: inline-block;
    -webkit-transform: all 0.5s ease 0s;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dt {
    font-size: 18px;
    color: #999;
}

.IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content p {
    color: #666;
    line-height: 24px;
    font-size: 14px;
    display: -webkit-box;
    height: 69px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}

.partner .partner_content .partner_content_list {
    width: 16.6%;
    text-align: center;
    overflow: hidden;
    border: 1px solid #dcdcdc;
    line-height: 130px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    border-right: 0;
    margin-bottom: -1px;
    cursor: pointer;
    padding: 0px 20px;
}

.partner .partner_content .partner_content_list:hover img {
    -webkit-filter: grayscale(0%);
    -moz-filter: grayscale(0%);
    -ms-filter: grayscale(0%);
    -o-filter: grayscale(0%);
    filter: grayscale(0%);
    filter: gray;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
}

.partner .partner_content .partner_content_list:nth-child(6n) {
    border-right: 1px solid #dcdcdc;
}

.partner .partner_content .partner_content_list img {
    max-height: 40px;
    max-width: 80%;
}

.partner .partner_content .partner_content_list:hover {
    background: #eeeeee;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.footer {
    background: #222222;
    position: relative;
}

.footer #mydiv {
    position: absolute;
    z-index: 5;
    opacity: .2;
}

.footer .footer_content {
    max-width: 1500px;
    margin: 0 auto;
    position: relative;
    z-index: 6;
}

.footer .footer_content .footer_content_top {
    padding: 60px 0;
    padding-bottom: 40px;
}

.footer .footer_content .footer_content_top .content_top_left {
    width: 55%;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list {
    width: 25%;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list dd {
    margin-bottom: 20px;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list dd a {
    font-size: 14px;
    color: #fff;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list dt a {
    font-size: 14px;
    color: #777777;
    display: inline-block;
    width: 100%;
    margin-bottom: 12px;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list dt a:hover {
    color: #fff;
}

.footer .footer_content .footer_content_top .content_top_left .top_left_list dt a:last-child {
    margin-bottom: 0;
}

.footer .footer_content .footer_content_top .content_top_right {
    width: 23%;
}

.footer .footer_content .footer_content_top .content_top_right .top_right_title {
    font-size: 16px;
    color: #fff;
    margin-bottom: 20px;
}

.footer .footer_content .footer_content_top .content_top_right .top_right_content .right_content_list dd {
    position: relative;
    line-height: 20px;
    margin-right: 15px;
}

.footer .footer_content .footer_content_top .content_top_share {
    width: 21%;
}

.footer .footer_content .footer_content_top .content_top_share .site-name {
    color: #fff;
    font-size: 32px;
}

.footer .footer_content .footer_content_top .content_top_share .top_share_content {
    margin-top: 40px;
}

.footer .footer_content .footer_content_top .content_top_share .top_share_content dd {
    color: #777777;
    font-size: 14px;
    margin-bottom: 10px;
}

.footer .footer_content .footer_content_top .content_top_share .top_share_content dt a {
    width: 50px !important;
    height: 50px !important;
    margin: 0px 0px !important;
    padding-left: 0px !important;
    text-align: center;
    color: #fff;
    font-size: 20px;
    line-height: 50px;
    border: 1px solid #363636;
    margin-right: -1px !important;
}

.footer .footer_content .footer_content_top .content_top_right .top_right_content .right_content_list {
    color: #777777;
    font-size: 14px;
    margin-bottom: 15px;
}

.footer .footer_content .footer_content_top .content_top_right .top_right_content .right_content_list dt {
    width: 80%;
}

.bds_tsina {
    background: #2a2a2a !important;
}

.bds_sqq {
    background: #2a2a2a !important;
}

.bds_weixin {
    background: #2a2a2a !important;
}

.footer .footer_content .footer_content_top .content_top_share .top_share_content dt a:hover {
    background: #252525 !important;
    opacity: 1 !important;
    color: #fb6a19;
}

.footer .footer_content .footer_content_bottom {
    margin-bottom: 20px;
}

.footer .footer_content .footer_content_bottom .content_bottom_left {
    font-size: 14px;
    color: #555555;
    width: 5%
}

.footer .footer_content .footer_content_bottom .content_bottom_right {
    width: 95%;
}

.footer .footer_content .footer_content_bottom .content_bottom_right a {
    font-size: 12px;
    color: #555555;
    display: inline-block;
    width: 7%;
    margin-left: 2%;
    margin-bottom: .8%;
}

.footer .footer_content .footer_content_bottom .content_bottom_right a:hover {
    color: #fff;
}

.footer_content_copyright {
    padding: 24px 0;
    text-align: center;
    border-top: 1px solid #313131;
    z-index: 521;
    position: relative;
}

.video_window {
    background: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: none;
    z-index: 55;
}

.video_window .videobox {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.video_window .video_window_close {
    color: #fff;
    width: 35px;
    height: 35px;
    border-radius: 100%;
    text-align: center;
    line-height: 35px;
    display: inline-block;
    font-size: 35px;
    position: relative;
    top: -6px;
    right: -26px;
}

.case .IndexService_content {
    position: relative;
    padding: 55px 0;
}

.case .IndexService_content .case_content_title {
    height: 153px;
    position: absolute;
    top: -153px;
    background: #fff;
    width: 100%;
    padding: 43px 40px;
    border-bottom: 1px solid #e5e5e5;
}

.case .IndexService_content .case_content_title a {
    color: #999;
    font-size: 14px;
    display: inline-block;
    width: 6%;
    margin-right: 2.5%;
    margin-bottom: 2%;
    position: relative;
    text-align: center;
}

.case .IndexService_content .case_content_title a:nth-child(12n) {
    margin-right: 0;
}

.case .IndexService_content .case_content_title a:hover {
    color: #333;
}

.case .IndexService_content .case_content_title a:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 0;
    height: 2px;
    background: #ff7019;
    bottom: -10px;
    left: 0;
    margin: 0 auto;
    right: 0;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.case .IndexService_content .case_content_title a:hover:before {
    width: 56px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.case .IndexService_content .case_content_title a.on:before {
    width: 56px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.case .IndexService_content .case_content_title a.on {
    color: #333;
}

.case .IndexCase_content {
    margin-bottom: 40px;
}

.pagination {

}

.pagination {

}

.pagination .pagination-link-disabled,
.pagination .pagination-link-disabled:hover {
    cursor: not-allowed;
    color: rgba(0, 0, 0, .25);
    border-color: #d9d9d9;
    background: #eeeeee;
}

.page li {
    display: inline-block;
}

.page li a {
    width: 48px;
    height: 48px;
    line-height: 48px;
    display: inline-block;
    text-align: center;
    background: #eeeeee;
    color: #333;
    margin-right: 15px;
    cursor: pointer;
}

.page li.active a, .pagination .pagination-link-active {
    background: #ff7019;
    color: #fff;
    cursor: auto;
}

.page li a:hover {
    background: #ff7019;
    color: #fff;
}

.page li a span {
    font-size: 18px;
}

.page li .next span {
    display: inline-block;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.news .IndexService_content {
    padding: 45px 0;
}

.news .IndexService_content .IndexService_content_title {
    border-bottom: 1px solid #e5e5e5;
}

.news .IndexService_content .IndexService_content_title dd {
    font-size: 24px;
    color: #333;
    font-family: "宋体";
    position: relative;
    padding-left: 35px;
}

.news .IndexService_content .IndexService_content_title dd:before {
    content: "“";
    font-size: 56px;
    color: #bbbbbb;
    font-family: "Arial";
    position: absolute;
    left: 0;
    top: 5px;
}

.news .news_content {
    margin-bottom: 45px;
}

.news .news_content .news_content_list {
    display: inline-block;
    width: 100%;
    padding: 30px 0;
    border-bottom: 1px solid #e5e5e5;
}

.news .news_content .news_content_list:hover .content_list_time dt span:before {
    color: #fff;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    width: 100%;
}

.news .news_content .news_content_list:hover .tra {
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    -ms-transform: scale(1.15);
    -o-transform: scale(1.15);
    filter: scale(1.15);
    filter: transform;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    cursor: pointer;
}

.news .news_content .news_content_list .content_list_time {
    width: 10%;
}

.news .news_content .news_content_list .content_list_txt {
    width: 60%;
    margin-right: 12.5%;
}

.news .news_content .news_content_list .content_list_img {
    width: 17.5%;
    overflow: hidden;
}

.news .news_content .news_content_list .btn {
    width: 100%;
    color: #333;
}

.news .news_content .news_content_list .btn a {
    display: inline-block;
    margin-left: 13px;
}

.news .news_content .news_content_list .content_list_img a {
    display: inline-block;
    width: 100%;
}

.news .news_content .news_content_list .content_list_time dd {
    font-size: 26px;
    color: #333;
    margin-bottom: 25px;
}

.news .news_content .news_content_list .content_list_time dt span {
    color: #cccccc;
    display: inline-block;
    font-size: 22px;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    position: relative;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.news .news_content .news_content_list .content_list_time dt span:before {
    content: "\e602";
    position: absolute;
    display: inline-block;
    width: 0%;
    left: 0;
    height: 100%;
    top: 0;
    background: #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.news .news_content .news_content_list .content_list_txt dd a {
    font-size: 18px;
    color: #333;
    margin-bottom: 32px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.news .news_content .news_content_list .content_list_txt p a {
    font-size: 14px;
    color: #666;
    line-height: 24px;
    display: inline-block;
    width: 100%;
    display: -webkit-box;
    max-height: 117px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    overflow: hidden;
}

.news .news_content .news_content_list .content_list_img img {
    width: 100%;
    height: 186px;
    object-fit: cover;
    display: inline-block;
    margin: 0 auto;
}

.NewsDetails {
    background: #fff;
}

.NewsDetails .IndexService_content {
    padding: 55px 0;
}

.NewsDetails .NewsDetails_title {
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 35px;
}

.NewsDetails .NewsDetails_title h1 {
    font-size: 26px;
    color: #333;
    font-weight: inherit;
    text-align: center;
    margin-bottom: 20px;
}

.NewsDetails .NewsDetails_title .NewsDetails_title_sort span {
    font-size: 13px;
    color: #999;
    margin-right: 25px;
}

.NewsDetails .NewsDetails_title .NewsDetails_title_sort span:last-child {
    margin-right: 0;
}

.NewsDetails .NewsDetails_content {
    padding: 45px 0;
    border-bottom: 1px solid #e5e5e5;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_top {
    font-size: 14px;
    color: #333;
    line-height: 24px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_top p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 24px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_top p img {
    max-width: 100%;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_top p.on {
    text-align: center;
}
.NewsDetails .NewsDetails_content .NewsDetails_content_bottom {
    margin-top: 55px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left {
    padding: 17px 0;
    width: 34%;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left .bottom_left_copyright {
    font-size: 13px;
    color: #999;
    margin-bottom: 25px;
    line-height: 26px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left .bottom_left_TAG dd {
    font-size: 13px;
    color: #999;
    line-height: 33px;
    margin-right: 20px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left .bottom_left_TAG dt a {
    display: inline-block;
    font-size: 13px;
    color: #333;
    background: #f4f4f4 url(../images/bg14.jpg) no-repeat 100% 50%;
    line-height: 32px;
    padding: 0px 20px;
    margin-right: 15px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left .bottom_left_TAG dt a:last-child {
    margin-right: 0;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help {
    margin-left: 170px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help a {
    display: inline-block;
    width: 140px;
    height: 82px;
    background: #ff7019;
    text-align: center;
    color: #fff;
    border-radius: 40px;
    padding: 15px 0;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help a dd {
    font-size: 32px;
}

.NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help .bottom_help_content {
    font-size: 12px;
    color: #999;
    margin-top: 12px;
    text-align: center;
}

.NewsDetails .NewsDetails_page {
    padding: 30px 0;
    padding-bottom: 0;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left {
    margin: 6px 0;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list {
    margin-bottom: 8px;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list:last-child {
    margin-bottom: 0;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list dd {
    font-size: 14px;
    color: #333;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list a {
    font-size: 14px;
    color: #333;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list a:hover {
    color: #ff7019;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_back {
    display: inline-block;
    border: 1px dashed #e5e5e5;
    width: 160px;
    line-height: 55px;
    text-align: center;
    font-size: 16px;
    color: #333;
}

.NewsDetails .NewsDetails_page .NewsDetails_page_back span {
    color: #ff7019;
    font-size: 25px;
    margin-right: 8px;
    position: relative;
    top: 3px;
}

.column-banner {
    position: relative;
}

.column-banner .column-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 40px;
    color: #fff;
    text-align: center;
}

.column-banner .column-info > .column-name {
    margin-bottom: 20px;
    font-size: 40px;
    font-weight: bold;
}

.column-banner .column-info > .column-description {
    font-size: 18px;
}


.case_banner .case_banner_content {
    max-width: 1500px;
    margin: 0 auto;
    position: relative;
    height: 460px;
}

.case_banner .case_banner_content .banner_content_left {
    position: absolute;
    bottom: 70px;
    width: 55%;
}

.case_banner .case_banner_content .banner_content_left .content_left_title {
    font-size: 32px;
    color: #fff;
    position: relative;
    padding-bottom: 30px;
    margin-bottom: 45px;
}

.case_banner .case_banner_content .banner_content_left .content_left_title:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 38px;
    height: 2px;
    background: #fff;
    bottom: 0;
}

.case_banner .case_banner_content .banner_content_left .content_left_txt {
    font-size: 14px;
    color: #fff;
    line-height: 26px;
    margin-bottom: 80px;
}

.case_banner .case_banner_content .banner_content_left .content_left_button a {
    display: inline-block;
    width: 150px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.13);
    border-radius: 50px;
}

.case_banner .case_banner_content .banner_content_left .content_left_button a:hover {
    background: #ff7019;
}

.case_banner .case_banner_content .banner_content_left .content_left_button a:last-child {
    margin-left: 35px;
}

.case_banner .case_banner_content .banner_content_left .content_left_button a span {
    font-size: 18px;
    margin-right: 10px;
    display: inline-block;
    position: relative;
    top: 2px;
}

.case_banner .case_banner_content .banner_content_left .content_left_button a:last-child span {
    margin-right: 0;
    margin-left: 10px;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.case_banner .case_banner_content .banner_content_right {
    position: absolute;
    right: 0;
    bottom: -135px;
    width: 400px;
    height: 544px;
    background: #ff7019;
    z-index: 50;
    padding: 0px 70px;
}

.case_banner .case_banner_content .banner_content_right .content_right_img {
    border-radius: 100%;
    background: #fff;
    text-align: center;
    width: 144px;
    height: 144px;
    line-height: 144px;
    padding: 0px 20px;
    margin: 65px auto;
}

.case_banner .case_banner_content .banner_content_right .content_right_img img {
    max-width: 100%;
}

.case_banner .case_banner_content .banner_content_right .content_right_more {
    color: #fff;
    font-size: 16px;
    display: inline-block;
    background: rgba(0, 0, 0, .27);
    width: 100%;
    line-height: 55px;
    text-align: center;
}

.case_banner .case_banner_content .banner_content_right .content_right_more span {
    color: #fff;
    font-size: 18px;
    margin-right: 15px;
    display: inline-block;
}

.case_banner .case_banner_content .banner_content_right .content_right_share {
    margin-top: 100px;
}

.case_banner .case_banner_content .banner_content_right .content_right_share dd {
    color: rgba(255, 255, 255, .43);
    font-size: 14px;
    position: relative;
    text-align: center;
    margin-bottom: 16px;
}

.case_banner .case_banner_content .banner_content_right .content_right_share dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 84px;
    height: 1px;
    background: rgba(255, 255, 255, .43);
    left: 16%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 5;
}

.case_banner .case_banner_content .banner_content_right .content_right_share dd:after {
    content: "";
    position: absolute;
    display: inline-block;
    width: 84px;
    height: 1px;
    background: rgba(255, 255, 255, .43);
    right: -16%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 5;
}

.case_banner .case_banner_content .banner_content_right .content_right_share dt {
    text-align: center;
}

.case_banner .case_banner_content .banner_content_right .content_right_share dt a {
    background: #ff7019 !important;
    color: #fff;
    font-size: 20px;
}

.CaseDetail .IndexService_content {
    padding-top: 45px;
}

.CaseDetail .CaseDetail_content {
    background: #fff;
    padding: 65px 75px;
}

.CaseDetail .CaseDetail_content .CaseDetail_content_top p {
    margin-bottom: 45px;
    text-align: center;
}

.CaseDetail .CaseDetail_content .CaseDetail_content_top p:last-child {
    margin-bottom: 0;
}

.CaseDetail .CaseDetail_content .CaseDetail_content_top p img {
    max-width: 100%;
}

.CaseDetail .CaseDetail_content .CaseDetail_content_contact {
    text-align: center;
    margin-top: 50px;
}

.CaseDetail .CaseDetail_content .CaseDetail_content_contact a {
    font-size: 18px;
    color: #fff;
    text-align: center;
    width: 259px;
    height: 60px;
    line-height: 60px;
    display: inline-block;
    background: #ff7019;
    border-radius: 50px;
}

.CaseDetail .IndexService_content .IndexService_content_title {
    padding-bottom: 50px;
}

.CaseDetail .CaseDetail_content .case_body_tag {
    margin-top: 15px;
    line-height: 35px;
}

.CaseDetail .CaseDetail_content .case_body_tag img {
    float: none !important;
    margin-right: 10px;
}

.CaseDetail .CaseDetail_content .case_body_tag a {
    color: #333;
    font-size: 16px;
    display: inline-block;
    line-height: 35px;
}

.owl-theme .owl-controls .owl-nav {
    display: none !important;
}

.about_banner {
    position: relative;
}

.about_banner .about_banner_img img {
    max-width: 100%;
}

.about_banner .about_banner_content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    text-align: left;
    width: 57%;
    text-align: center;
}

.about_banner .about_banner_content dd {
    font-size: 34px;
    color: #fff;
}

.about_banner .about_banner_content .banner_content_logo {
    margin: 50px 0;
}

.about_banner .about_banner_content dt p {
    font-size: 16px;
    color: #fff;
    line-height: 35px;
}

.AboutDetails, .profit, .App {
    background: #fff;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_left {
    position: relative;
    top: -90px;
    width: 50%;
    margin-bottom: -90px;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_left img {
    width: 100%;
    height: 690px;
    object-fit: cover;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right {
    width: 50%;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_img {
    width: 50%;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content {
    width: 50%;
    position: relative;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    text-align: left;
    width: 52%;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dd {
    color: #333;
    font-size: 32px;
    margin-bottom: 45px;
    padding-bottom: 20px;
    position: relative;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 34px;
    height: 2px;
    background: #000000;
    bottom: 0;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dd span {
    color: #999;
    font-size: 24px;
    text-transform: uppercase;
    margin-left: 15px;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dt {
    color: #666;
    font-size: 20px;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dd:before {
    background: #fff;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dd {
    color: #fff;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dd span {
    color: #fff;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dt {
    color: #fff;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left {
    width: 50%;
    position: relative;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 83px;
    height: 170px;
    background: url(../images/bn28.png) no-repeat 50%;
    top: 50%;
    left: 100%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 50;
}

.AboutDetails .AboutDetails_innovate .AboutDetails_innovate_left, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_img, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content, .AboutDetails .AboutDetails_honor .AboutDetails_culture_left {
    overflow: hidden;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 70%;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dd {
    font-size: 32px;
    color: #333;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: 45px;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 34px;
    height: 2px;
    background: #000000;
    bottom: 0;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dd span {
    font-size: 24px;
    color: #999;
    margin-left: 15px;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dt p {
    color: #666;
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 30px;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dt p:last-child {
    margin-bottom: 0;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_right {
    width: 50%;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_right img {
    width: 100%;
    height: 540px;
    object-fit: cover;
}

.AboutDetails .AboutDetails_provider .AboutDetails_culture_right img {
    max-width: 100%;
    height: auto;
    width: auto;
}

.AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_img img {
    width: 100%;
    height: 540px;
    object-fit: cover;
}

.AboutDetails .AboutDetails_provider {
    max-width: 1500px;
    margin: 0 auto;
}

.AboutDetails .AboutDetails_provider {
    padding: 80px 0;
}

.AboutDetails .AboutDetails_provider .AboutDetails_culture_left .culture_left_title {
    width: 100%;
}

.AboutDetails .AboutDetails_provider .AboutDetails_culture_left:before {
    display: none;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_left:before {
    display: none;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dd {
    color: #fff;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dd:before {
    background: #fff;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dt p {
    color: #fff;
}

.AboutDetails .AboutDetails_honor .AboutDetails_honor_content {
    margin-top: 45px;
}

.AboutDetails .AboutDetails_honor .AboutDetails_honor_content div {
    display: inline-block;
    margin-right: 2%;
    width: 32%;
}

.AboutDetails .AboutDetails_honor .AboutDetails_honor_content div:last-child {
    margin-right: 0;
}

.AboutDetails .AboutDetails_honor .AboutDetails_honor_content div img {
    max-width: 100%;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_img img {
    width: 100%;
    height: 660px;
    object-fit: cover;
}

.AboutDetails .AboutDetails_honor .AboutDetails_culture_right img {
    width: 100%;
    height: 660px;
    object-fit: cover;
}

.partner_detail .IndexService_content .IndexService_content_title dd {
    position: relative;
    padding-bottom: 20px;
}

.partner_detail .IndexService_content .IndexService_content_title dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 34px;
    height: 2px;
    background: #000000;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: 0;
}

.AboutNews {
    background: #fff;
}

.AboutNews .AboutNews_content .AboutNews_content_left {
    width: 49%;
}

.AboutNews .AboutNews_content .AboutNews_content_left .content_left_list {
    width: 48%;
    margin-right: 4%;
    margin-bottom: 4%;
    overflow: hidden;
}

.AboutNews .AboutNews_content .AboutNews_content_left .content_left_list:nth-child(2n) {
    margin-right: 0;
}

.AboutNews .AboutNews_content .AboutNews_content_left .content_left_list img {
    width: 100%;
    height: 230px;
    object-fit: cover;
}

.AboutNews .AboutNews_content .AboutNews_content_right {
    width: 49%;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list {
    display: inline-block;
    width: 100%;
    background: #f6f6f6;
    padding: 55px 60px;
    margin-bottom: 4%;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list:last-child {
    margin-bottom: 0;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list:hover {
    background: #fb6a19;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list:hover dd {
    color: #fff;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list:hover dt {
    color: #fff;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list:hover p {
    color: #fff;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list dt {
    font-size: 14px;
    color: #999;
}

.AboutNews .AboutNews_content .AboutNews_content_right .content_right_list p {
    font-size: 14px;
    color: #666;
    line-height: 26px;
    margin-top: 20px;
    display: -webkit-box;
    height: 48px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.ContactBanner .ContactBanner_content {
    position: relative;
}

.ContactBanner .ContactBanner_content .ContactBanner_content_left {
    position: absolute;
    bottom: 145px;
}

.ContactBanner .ContactBanner_content .ContactBanner_content_left dd {
    font-size: 30px;
    color: #fff;
    letter-spacing: 8px;
    margin-bottom: 14px;
}

.ContactBanner .ContactBanner_content .ContactBanner_content_left dt {
    font-size: 60px;
    color: #fff;
    font-family: "Impact";
    letter-spacing: 7px;
}

.ContactBanner .ContactBanner_content .ContactBanner_content_right {
    position: absolute;
    bottom: 60px;
    right: 0;
}

.ContactBanner .ContactBanner_content .ContactBanner_content_right img {
    width: 220px;
    height: auto;
}

.contact .contact_content .contact_content_list {
    background: #fff;
    width: 100%;
    margin-right: 0%;
    padding: 50px 30px;
    padding-bottom: 30px;
    position: relative;
    height: 550px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
    text-align: left;
}

.contact .contact_content .contact_content_list:last-child {
    margin-right: 0;
}

.contact .contact_content .contact_content_list .content_list_title {
    text-align: center;
    margin-bottom: 30px;
}

.contact .contact_content .contact_content_list .content_list_title dd {
    font-size: 58px;
    color: #333;
    margin-bottom: 15px;
}

.contact .contact_content .contact_content_list .content_list_title dt {
    font-size: 20px;
    color: #333;
}

.contact .contact_content .contact_content_list .content_list_txt p {
    font-size: 13px;
    color: #666;
    line-height: 26px;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.contact .contact_content .contact_content_list .content_list_icon {
    font-size: 29px;
    color: #bbbbbb;
    text-align: center;
    margin-top: 20px;
    -webkit-animation: pulse 1s infinite;
    animation: pulse 1s infinite;
    cursor: pointer;
    left: 0;
    right: 0;
    bottom: 8%;
    position: absolute;
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    50% {
        -webkit-transform: translate(0, 10px);
        transform: translate(0, 10px);
    }
    100% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
}

@keyframes pulse {
    0% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    50% {
        -webkit-transform: translate(0, 10px);
        transform: translate(0, 10px);
    }
    100% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
}

.contact {
    background: #ebebeb;
}

.contact .IndexService_content {
    padding-bottom: 0;
}

.contact .contact_content .contact_content_list .content_list_hover {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background: #fb6a19;
    padding: 30px 25px;
    bottom: -550px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.contact .contact_content .contact_content_list .content_list_hover p {
    color: #fff;
    font-size: 14px;
    line-height: 26px;
    margin-bottom: 10px;
}

.contact .contact_content .contact_content_list .content_list_hover p:last-child {
    margin-bottom: 0;
}

.contact .contact_content .contact_content_list:hover .content_list_hover {
    bottom: 0;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.contact .contact_motto {
    padding: 30px 0;
}

.contact .contact_motto .contact_motto_left dd p {
    font-size: 14px;
    color: #333;
    line-height: 26px;
}

.contact .contact_motto .contact_motto_left dd {
    margin-bottom: 20px;
}

.contact .contact_motto .contact_motto_left dd:last-child {
    margin-bottom: 0;
}

.contact .contact_motto .contact_motto_left {
    width: 52%;
    padding: 90px 0;
}

.contact .contact_motto .contact_motto_right {
    width: 48%;
}

.contact .contact_motto .contact_motto_right img {
    max-width: 100%;
}

.contact .contact_motto_map {
    position: relative;
}

.contact .contact_motto_map .motto_map_top img {
    width: 100%;
    height: 651px;
    object-fit: cover;
}

.contact .contact_motto_map .motto_map_bottom {
    position: absolute;
    top: 50%;
    left: 19%;
    transform: translateX(-50%) translateY(-50%);
}

.contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title {
    background: #313131;
    width: 427px;
    padding: 40px 64px;
}

.contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title dd {
    font-size: 32px;
    color: #fff;
    margin-bottom: 30px;
}

.contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title dd span {
    font-size: 24px;
    margin-left: 15px;
}

.contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title dt p {
    color: #fff;
    font-size: 14px;
    line-height: 35px;
}

.FloatingWindow {
    position: fixed;
    right: 0;
    z-index: 550;
    bottom: 15%;
    width: 72px;
}

.FloatingWindow .FloatingWindow_list {
    width: 100%;
    display: inline-block;
    position: relative;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.5);
}

.FloatingWindow .FloatingWindow_list:hover {
    background: #ec6d1f;
}

.FloatingWindow .FloatingWindow_list .FloatingWindow_list_down {
    position: absolute;
    background: rgba(255, 112, 25, 1);
    width: 258px;
    line-height: 72px;
    color: #fff;
    font-size: 16px;
    text-align: center;
    left: -187px;
    top: 0;
    display: none;
}

.FloatingWindow .FloatingWindow_list .FloatingWindow_list_title {
    text-align: center;
    border: 1px solid #ec6d1f;
    padding: 10px;
}

.FloatingWindow .FloatingWindow_list .FloatingWindow_list_title dd {
    color: #fff;
    font-size: 28px;
}

.FloatingWindow .FloatingWindow_list .FloatingWindow_list_title dt {
    color: #fff;
    font-size: 12px;
    margin-top: 3px;
}

.FloatingWindow .FloatingWindow_list:nth-child(3):hover .FloatingWindow_list_down {
    display: block;
}

.ServiceBanner {
    height: 760px;
    background: -webkit-linear-gradient(to right, #828fff, #191c32); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(to right, #828fff, #191c32); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(to right, #828fff, #191c32); /* Firefox 3.6 - 15 */
    background: linear-gradient(to top, #828fff, #191c32); /* 标准的语法（必须放在最后） */
}

.ServiceBanner .ServiceBanner_content {
    width: 1500px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 45%;
    transform: translateX(-50%) translateY(-50%);
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left {
    width: 50%;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_right {
    text-align: right;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_right img {
    max-width: 100%;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_title dd {
    font-size: 48px;
    color: #fff;
    margin-bottom: 45px;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_title dt {
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    margin-bottom: 70px;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list {
    margin-right: 25px;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list:last-child {
    margin-right: 0;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list dd {
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.32);
    text-align: center;
    width: 80px;
    line-height: 78px;
    font-size: 34px;
}

.ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list dt {
    font-size: 14px;
    color: #fff;
    text-align: center;
    margin-top: 10px;
}

.ServiceApp_title {
    background: #fff;
}

.ServiceApp_title .IndexService_content {
    padding: 80px 100px;
}

.ServiceApp_title .ServiceApp_title_list .title_list_top {
    width: 109px;
    height: 109px;
    border-radius: 100%;
    border: 1px solid #e5e5e5;
    text-align: center;
    padding: 18px;
    margin: 0 auto;
}

.ServiceApp_title .ServiceApp_title_list .title_list_top dd {
    width: 70px;
    height: 70px;
    border-radius: 100%;
    margin: 0 auto;
    background: #edf9e8;
    padding: 5px;
    position: relative;
}

.ServiceApp_title .ServiceApp_title_list .title_list_top span {
    width: 60px;
    height: 60px;
    border-radius: 100%;
    background: #99dd81;
    margin: 0 auto;
    line-height: 60px;
    display: inline-block;
    color: #fff;
    font-size: 30px;
    position: relative;
    z-index: 20;
}

.ServiceApp_title .ServiceApp_title_list .title_list_content {
    margin-top: 25px;
}

.ServiceApp_title .ServiceApp_title_list .title_list_content dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 25px;
}

.ServiceApp_title .ServiceApp_title_list .title_list_content dt {
    font-size: 14px;
    color: #666;
    line-height: 24px;
}

.ServiceApp_title .owl-item:nth-child(2) .ServiceApp_title_list .title_list_top dd {
    background: #eaf3fe;
}

.ServiceApp_title .owl-item:nth-child(2) .ServiceApp_title_list .title_list_top dd span {
    background: #8bbbfc;
}

.ServiceApp_title .owl-item:nth-child(3) .ServiceApp_title_list .title_list_top dd {
    background: #fae3e9;
}

.ServiceApp_title .owl-item:nth-child(3) .ServiceApp_title_list .title_list_top dd span {
    background: #e66284;
}

.ServiceApp_title .owl-item:nth-child(4) .ServiceApp_title_list .title_list_top dd {
    background: #d5ebf2;
}

.ServiceApp_title .owl-item:nth-child(4) .ServiceApp_title_list .title_list_top dd span {
    background: #1592b6;
}

@keyframes ServiceAppScale {
    0% {
        transform: scale(1);
    }
    50%, 75% {
        transform: scale(1.3);
    }
    78%, 100% {
        opacity: 0;
    }
}

.ServiceApp_title .ServiceApp_title_list .title_list_top dd:before {
    content: '';
    width: 70px;
    height: 70px;
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 100%;
    background: #edf9e8;
    opacity: 1;
    animation: ServiceAppScale 2s infinite cubic-bezier(0, 0, 0.49, 1.02);
    animation-delay: 100ms;
    transition: 0.5s all ease;
    transform: scale(1);
}

.ServiceApp_title .owl-item:nth-child(2) .ServiceApp_title_list .title_list_top dd:before {
    background: #eaf3fe;
}

.ServiceApp_title .owl-item:nth-child(3) .ServiceApp_title_list .title_list_top dd:before {
    background: #fae3e9;
}

.ServiceApp_title .owl-item:nth-child(4) .ServiceApp_title_list .title_list_top dd:before {
    background: #d5ebf2;
}

.ServiceApp_application .IndexService_content {
    max-width: 100%;
    background: #f1f1f1;
    padding: 0;
}

.ServiceApp_application .ServiceApp_application_left {
    background: #f6f6f6;
    padding: 75px 0;
    width: 62%;
    padding-left: 11%;
}

.ServiceApp_application .ServiceApp_application_left .application_left_title {
    margin-bottom: 85px;
}

.ServiceApp_application .ServiceApp_application_left .application_left_title dd {
    font-size: 32px;
    color: #333;
    margin-bottom: 15px;
}

.ServiceApp_application .ServiceApp_application_left .application_left_title dt {
    font-size: 16px;
    color: #666;
}

.ServiceApp_application .ServiceApp_application_right {
    width: 33%;
    padding-right: 11%;
    padding-top: 70px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_top {
    border-bottom: 1px solid #dcdcdc;
    padding-bottom: 26px;
    margin-bottom: 70px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_top dd {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_top dt {
    font-size: 14px;
    color: #666;
    line-height: 26px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_bottom .right_bottom_list {
    margin-bottom: 15px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_bottom .right_bottom_list:last-child {
    margin-bottom: 0;
}

.ServiceApp_application .ServiceApp_application_right .application_right_bottom .right_bottom_list dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 5px;
}

.ServiceApp_application .ServiceApp_application_right .application_right_bottom .right_bottom_list dt {
    font-size: 14px;
    color: #666;
    line-height: 24px;
}

.App .App_content {
    max-width: 1500px;
    margin: 0 auto;
    padding: 80px 0;
}

.App .App_content .App_content_title {
    text-align: center;
}

.App .App_content .App_content_title dd {
    font-size: 32px;
    color: #333;
}

.App .App_content .App_content_title dt {
    font-size: 16px;
    color: #999;
    margin-top: 10px;
}

.App .App_content .App_content_nav {
    margin-top: 40px;
}

.App .App_content .App_content_nav a {
    width: 140px;
    line-height: 52px;
    text-align: center;
    background: #f6f6f6;
    margin-right: 24px;
}

.App .App_content .App_content_nav a:hover {
    background: #ff7019;
    color: #fff;
}

.App .App_content .App_content_nav a:last-child {
    margin-right: 0;
}

.App .App_content .App_content_nav a.on {
    background: #ff7019;
    color: #fff;
}

.App .App_content .App_content_body {
    margin-top: 70px;
}

.App .App_content .App_content_body .content_body_left {
    position: relative;
    width: 49%;
}

.App .App_content .App_content_body .content_body_left:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 1px;
    height: 589px;
    background: #e5e5e5;
    right: -84px;
}

.App .App_content .App_content_body .content_body_left .body_left_img {
    margin-bottom: 40px;
}

.App .App_content .App_content_body .content_body_left .body_left_img img {
    max-width: 100%;
}

.App .App_content .App_content_body .content_body_left .body_left_txt dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 25px;
}

.App .App_content .App_content_body .content_body_left .body_left_txt dt p {
    font-size: 14px;
    color: #666;
    line-height: 30px;
}

.App .App_content .App_content_body .content_body_right {
    border: 5px solid #e5e5e5;
    width: 37%;
    padding: 50px;
}

.App .App_content .App_content_body .content_body_right .body_right_top {
    margin-bottom: 25px;
}

.App .App_content .App_content_body .content_body_right .body_right_top dd {
    font-size: 22px;
    color: #333;
}

.App .App_content .App_content_body .content_body_right .body_right_top dt {
    font-size: 14px;
    color: #666;
    line-height: 24px;
    margin-top: 20px;
}

.App .App_content .App_content_body .content_body_right .body_right_bottom dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.App .App_content .App_content_body .content_body_right .body_right_bottom dt p {
    font-size: 14px;
    color: #333;
    margin-bottom: 15px;
}

.app_case {
    background: #f6f6f6;
    padding-top: 15px;
}

.app_case .app_case_title {
    font-size: 32px;
    color: #333;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 40px;
}

.app_case .IndexCase_content .IndexCase_content_list {
    width: 23.5%;
    margin-right: 2%;
}

.app_case .IndexCase_content .IndexCase_content_list:nth-child(3) {
    margin-right: 2%;
}

.app_case .IndexCase_content .IndexCase_content_list:nth-child(4) {
    margin-right: 0%;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .list_img_title img {
    height: 550px;
}

.app_case .IndexCase_content {
    margin-bottom: 0;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt {
    position: absolute;
    width: 80%;
    opacity: 0;
    transition: 0.5s all ease;
    left: 50%;
    top: 50%;
    height: 60%;
    transform: translateX(-50%) translateY(-50%);
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_logo {
    text-align: center;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_logo img {
    height: auto;
    width: 30%;
    opacity: 1;
    border-radius: 15px;
    overflow: hidden;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_title {
    text-align: center;
    font-size: 24px;
    text-align: center;
    color: #fff;
    margin: 17px 0;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_content {
    text-align: center;
    font-size: 14px;
    color: #fff;
    line-height: 23px;
    border-top: 1px solid rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.6);
    padding: 15px 0px
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list {
    border: 2px solid #fff;
    border-radius: 8px;
    width: 100%;
    margin-top: 25px;
    text-align: center;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list .download_list_qrcode {
    width: 100%;
    color: #fff;
    font-size: 28px;
    padding: 8px 0;
    cursor: pointer;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list .download_list_qrcode:hover {
    background: rgba(255, 255, 255, .5)
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list .download_list_qrcode .list_qrcode_content {
    display: none;
    position: absolute;
    bottom: 48px;
    width: 100%;
    cursor: pointer;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list:hover .download_list_qrcode .list_qrcode_content {
    display: block;
    cursor: pointer;
}

.app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_download .txt_download_list .download_list_qrcode .list_qrcode_content img {
    width: auto;
    height: auto;
    opacity: 1;
}

.app_case .IndexCase_content .IndexCase_content_list:hover .content_list_img .content_list_txt {
    opacity: 1;
    transition: 0.5s all ease;
}

.app_footer {
    height: 150px;
    width: 100%;
    background: #828fff url(../images/bn48.png) no-repeat 50% 50%;
    text-align: center;
    line-height: 150px;
}

.app_footer a {
    display: inline-block;
    width: 280px;
    line-height: 50px;
    text-align: center;
    color: #828fff;
    font-size: 16px;
    background: #fff;
}

.app_footer a:hover {
    width: 295px;
    color: #828fff;
}

.ServiceBanner_website {
    background: -webkit-linear-gradient(to right, #1a85c3, #192025); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(to right, #1a85c3, #192025); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(to right, #1a85c3, #192025); /* Firefox 3.6 - 15 */
    background: linear-gradient(to top, #1a85c3, #192025); /* 标准的语法（必须放在最后） */
}

.ServiceWebsite {
    background: #fff;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left {
    width: 74%
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list {
    width: 32%;
    margin-right: 2%;
    border: 1px solid #e5e5e5;
    padding: 30px 0;
    transition: all 0.5s ease 0s;
    cursor: pointer;
    padding-right: 2%;
    margin-bottom: 2%;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(3n) {
    margin-right: 0;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_icon {
    font-size: 45px;
    color: #999;
    width: 40%;
    text-align: center;
    line-height: 76px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet {
    width: 58%;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet dd {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet dt p {
    font-size: 13px;
    color: #666;
    line-height: 22px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:hover .left_list_icon {
    color: #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:hover {
    border: 1px solid #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(1) .left_list_icon {
    font-size: 70px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(2) .left_list_icon {
    font-size: 60px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(4) .left_list_icon {
    font-size: 75px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(5) .left_list_icon {
    font-size: 55px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(6) .left_list_icon {
    font-size: 60px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(7) .left_list_icon {
    font-size: 50px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(8) .left_list_icon {
    font-size: 70px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(9) .left_list_icon {
    font-size: 55px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(7) {
    margin-bottom: 0;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(8) {
    margin-bottom: 0;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(9) {
    margin-bottom: 0;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right {
    border: 1px solid #e5e5e5;
    width: 23%;
    text-align: center;
    padding: 47px 0;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title {
    margin-bottom: 53px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title dd {
    font-size: 22px;
    color: #333;
    margin-bottom: 10px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title dt {
    font-size: 16px;
    color: #999;
    margin-bottom: 30px;
}

.ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title p {
    font-size: 14px;
    color: #1a81bd;
}

.ServiceCooperation {
    background: #f5f5f5;
    height: 793px;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list {
    position: relative;
    background: url(../images/bn38.png) no-repeat 50% 100%;
    padding-bottom: 45px;
    width: 159px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 1px;
    height: 67px;
    background: #1a83bf;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: 23px;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_title {
    background: #fff;
    width: 220px;
    height: 150px;
    text-align: center;
    padding-top: 30px;
    position: relative;
    left: -30px;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_title dd {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_title dt {
    font-size: 14px;
    color: #666;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_icon {
    width: 85px;
    height: 85px;
    border-radius: 100%;
    text-align: center;;
    line-height: 85px;
    background: #1a83bf;
    position: relative;
    top: -42px;
    margin-bottom: -42px;
    margin: 0 auto;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list.on {
    background: url(../images/bn38_on.png) no-repeat 50% 100%;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list.on .content_list_icon {
    background: #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list.on:before {
    background: #ff7019;
    transition: all 0.5s ease 0s;
    -webkit-transform: all 0.5s ease 0s;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list:nth-child(2n) .content_list_title {
    position: relative;
    bottom: -380px;
    padding-top: 66px;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list:nth-child(2n) .content_list_icon {
    position: relative;
    bottom: -180px;
    top: auto;
}

.ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list:nth-child(2n):before {
    bottom: -66px;
}

.ServiceCooperation .ServiceCooperation_content_mo {
    display: none;
}

.ServiceCase .IndexCase_content {
    margin-bottom: 0;
}

.WeChat .IndexService_content {
    padding-bottom: 0;
}

.WeChat .IndexService_content .IndexService_content_title dd {
    font-size: 32px;
}

.WeChat .WeChat_content {
    padding: 0px 185px;
}

.WeChat .WeChat_content .WeChat_content_list dt {
    font-size: 16px;
    color: #666;
    margin-top: 40px;
}

.WeChat .IndexService_content .IndexService_content_title {
    padding-bottom: 64px;
}

.WeChat .WeChat_fan dd {
    font-size: 28px;
    color: #333;
    text-align: center;
    margin-top: 90px;
    margin-bottom: 60px;
}

.WeChat .WeChat_fan dt {
    text-align: center;
}

.profit .profit_title {
    padding: 85px 0;
    text-align: center;
    font-size: 32px;
    color: #333;
}

.profit .profit_content .profit_content_list {
    background: #f2fdfa;
    width: 25%;
    text-align: center;
    padding: 55px 60px;
    height: 450px;
    cursor: pointer;
}

.profit .profit_content .profit_content_list .content_list_title {
    font-size: 22px;
    color: #45a993;
    margin-bottom: 55px;
}

.profit .profit_content .profit_content_list .content_list_icon {
    height: 110px;
    text-align: center;
    padding-bottom: 30px;
    background: url(../images/bn55.png) no-repeat 50% 100%;
    margin-bottom: 54px;
}

.profit .profit_content .profit_content_list .content_list_txt {
    color: #666;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
}

.profit .profit_content .profit_content_list:nth-child(2), .profit .profit_content .profit_content_list:nth-child(4), .profit .profit_content .profit_content_list:nth-child(5), .profit .profit_content .profit_content_list:nth-child(7) {
    background: #fef8f0;
}

.profit .profit_content .profit_content_list:hover .flip {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
    cursor: pointer;
}

.program {
    background: #45a993;
    height: 718px;
}

.program .program_title {
    font-size: 32px;
    color: #fff;
    text-align: center;
    margin-bottom: 45px;
}

.program .program_content .program_content_left {
    width: 32%;
}

.program .program_content .program_content_center {
    width: 36%;
    text-align: center;
}

.program .program_content .program_content_left .content_left_list {
    margin-top: 185px;
    margin-right: 50px;
    width: 32%;
    margin-right: 2%;
}

.program .program_content .program_content_left .content_left_list:last-child {
    margin-right: 0;
}

.program .program_content .program_content_left .content_left_list dd {
    width: 122px;
    height: 122px;
    border-radius: 100%;
    overflow: hidden;
    text-align: center;
    line-height: 122px;
    background: #fff;
}

.program .program_content .program_content_left .content_left_list dd img {
    max-width: 100%;
}

.program .program_content .program_content_left .content_left_list dt {
    font-size: 18px;
    color: #fff;
    text-align: center;
    margin-top: 20px;
}

.program .program_service .program_service_title dd {
    font-size: 28px;
    color: #333;
}

.program .program_service .program_service_title dt {
    font-size: 16px;
    color: #666;
}

.program_service {
    padding-top: 195px;
    background: #f8f8f8;
}

.program_service .program_service_title {
    text-align: center;
    margin-bottom: 55px;
}

.program_service .program_service_title dd {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
}

.program_service .program_service_title dt {
    font-size: 16px;
    color: #666;
    line-height: 28px;
}

.program_service .program_service_content .service_content_list {
    text-align: center;
    width: 10%;
    margin-bottom: 40px;
    padding: 10px 0;
    cursor: pointer;
}

.program_service .program_service_content .service_content_list dd {
    height: 45px;
    line-height: 45px;
    margin-bottom: 20px;
}

.program_service .program_service_content .service_content_list dt {
    font-size: 14px;
    color: #333;
}

.program_service .IndexService_content {
    padding-bottom: 30px;
}

.Mall {
    background: #fff;
    padding-bottom: 75px;
}

.Mall .IndexService_content .IndexService_content_title dd {
    font-size: 32px;
    color: #45a993;
    padding-bottom: 15px;
}

.Mall .Mall_content .WeChat_content_list dt {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    margin-top: 30px;
}

.Mall .Mall_content .WeChat_content_list p {
    font-size: 14px;
    color: #999;
    line-height: 22px;
}

.WeChat_footer {
    background: #45a993 url(../images/bn48.png) no-repeat 50% 50%;
}

.WeChat_footer a {
    color: #45a993;
}

.website_footer {
    background: #1a82be url(../images/bn48.png) no-repeat 50% 50%;
}

.website_footer a {
    color: #1a82be;
}

.MarketingBanner {
    height: 760px;
    background: -webkit-linear-gradient(to right, #494f86, #2a3062); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(to right, #494f86, #2a3062); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(to right, #494f86, #2a3062); /* Firefox 3.6 - 15 */
    background: linear-gradient(to top, #494f86, #2a3062); /* 标准的语法（必须放在最后） */
}

.Marketing_footer {
    background: #484f86 url(../images/bn48.png) no-repeat 50% 50%;
}

.Marketing_footer a {
    color: #484f86;
}

.Marketing_idea {
    background: #fff;
}

.Marketing_idea .IndexService_content .IndexService_content_title dd {
    margin-bottom: 25px;
    font-size: 32px;
}

.Marketing_idea .IndexService_content .IndexService_content_title dt {
    margin-top:;
}

.Marketing_idea .IndexService_content .IndexService_content_title dt p {
    line-height: 24px;
    font-size: 14px;
}

.Marketing_idea .Marketing_idea_content {
    background: url(../images/bn90.png) no-repeat 50% 50%;
    padding: 0px 115px;
}

.Marketing_idea .Marketing_idea_content .idea_content_list {
    text-align: center;
    width: 100%;
    cursor: pointer;
}

.Marketing_idea .Marketing_idea_content .idea_content_list .content_list_title dd {
    font-size: 18px;
    color: #495087;
    margin-bottom: 15px;
}

.Marketing_idea .Marketing_idea_content .idea_content_list .content_list_title dt {
    font-size: 14px;
    color: #333;
}

.Marketing_idea .Marketing_idea_content .idea_content_list .content_list_icon {
    font-size: 53px;
    color: #333;
    width: 99px;
    height: 99px;
    border-radius: 100%;
    text-align: center;
    line-height: 99px;
    background: url(../images/bn89.png) no-repeat 50% 50%;
    margin: 40px auto;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_idea .Marketing_idea_content .idea_content_list .content_list_txt p {
    color: #999;
    font-size: 14px;
    line-height: 22px;
}

.Marketing_idea .Marketing_idea_content .idea_content_list:hover .content_list_icon {
    background: url(../images/bn89_on.png) no-repeat 50% 50%;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
    color: #484f86;
}

.Marketing_idea .Marketing_idea_button {
    text-align: center;
    margin-top: 55px;
}

.Marketing_idea .Marketing_idea_button a {
    background: #484f86;
    display: inline-block;
    width: 172px;
    border-radius: 50px;
    text-align: center;
    line-height: 48px;
    color: #fff;
}

.Marketing_seo {
    background: #f6f6f6;
}

.Marketing_seo .Marketing_seo_content {
    padding: 0px 110px;
}

.Marketing_seo .Marketing_seo_content .seo_content_list {
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_icon {
    width: 109px;
    height: 109px;
    border-radius: 100%;
    text-align: center;;
    line-height: 109px;
    font-size: 43px;
    color: #333;
    background: #fff;
    margin: 0 auto;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_txt {
    margin-top: 30px;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_txt dd {
    font-size: 18px;
    color: #333;
    padding-bottom: 10px;
    position: relative;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
    text-align: center;
    margin-bottom: 20px;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_txt dt {
    text-align: center;
    margin-bottom: 40px;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_txt dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 15px;
    height: 2px;
    background: #434343;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: 0;
}

.Marketing_seo .Marketing_seo_content .seo_content_list .content_list_txt dt p {
    font-size: 14px;
    color: #999;
    line-height: 24px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_seo_content .seo_content_list:hover .content_list_icon {
    background: #484f86;
    color: #fff;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_seo_content .seo_content_list:hover .content_list_txt dd {
    color: #484f86;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_seo_content .seo_content_list:hover .content_list_txt dt p {
    color: #666;
    uppercase;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_seo_content .seo_content_list:hover .content_list_txt dd:before {
    background: #484f86;
    transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;
}

.Marketing_seo .Marketing_idea_button {
    margin-top: 0;
}

.Marketing_seo .owl-theme .owl-controls .owl-nav div {
    top: -205px !important;
}

.Marketing_partner {
    background: #fff;
}

.Marketing_partner .Marketing_partner_content .partner_content_list {
    width: 25%;
    text-align: center;
    border-right: 1px solid #dcdcdc;
    height: 120px;
    line-height: 120px;
}

.Marketing_partner .Marketing_partner_content .partner_content_list:nth-child(4n) {
    border-right: 0;
}

.Marketing_team {
    background: url(../images/bg38.jpg) no-repeat 50%/cover;
}

.Marketing_team .IndexService_content .IndexService_content_title dd {
    color: #fff;
}

.Marketing_team .Marketing_team_content .team_content_left {
    width: 30%;
    padding: 28px 0;
}

.Marketing_team .Marketing_team_content .team_content_center {
    width: 40%;
    text-align: center;
    position: relative;
    height: 357px;
}

.Marketing_team .Marketing_team_content .team_content_center dd {
    animation: animtran 10s linear infinite;
    -webkit-animation: animtran 10s linear infinite;
}

.Marketing_team .Marketing_team_content .team_content_center dt {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.Marketing_team .Marketing_team_content .team_content_left .content_left_list {
    width: 100%;
    text-align: right;
    margin-bottom: 35px;
}

.Marketing_team .Marketing_team_content .team_content_left .content_left_list:last-child {
    margin-bottom: 0;
}

.Marketing_team .Marketing_team_content .team_content_left .content_left_list dd {
    font-size: 18px;
    color: #fff;
    margin-bottom: 5px;
    position: relative;
}

.Marketing_team .Marketing_team_content .team_content_left .content_left_list dd:before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 100%;
    background: #fff;
    right: -25px;
    top: 40%;
}

.Marketing_team .Marketing_team_content .team_content_left .content_left_list dt {
    font-size: 14px;
    color: #62626d;
    text-transform: uppercase;
}

.Marketing_team .Marketing_team_content .team_content_left:last-child .content_left_list dd:before {
    left: -25px;
    right: auto;
}

.Marketing_team .Marketing_team_content .team_content_left:last-child .content_left_list {
    text-align: left;
}

.videobox video {
    width: 100%;
}

@keyframes animtran {
    from {
        transform: rotateZ(0deg);
    }
    to {
        transform: rotateZ(360deg);
    }
}

.MarketingCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_icon {
    background: #484f86;
}

.MarketingCooperation .ServiceCooperation_content .ServiceCooperation_content_list {
    background: url(../images/bn101.png) no-repeat 50% 100%;
}

.videobox {
    width: 100%;
    margin: 0 auto;
}

.videobox .videobox_nei {
    width: 100%;
    position: relative
}

#video_button {
    width: 100%;
    position: absolute;
    cursor: pointer;
    z-index: 10;
    background: url(../images/bg13.jpg) no-repeat 50%/cover;
}

#video_button2 {
    width: 100%;
}

@media screen and (max-width: 1480px) {
    .IndexService .all, .advantage .all, .footer .footer_content, .pc_header .pc_header_content, .case_banner .case_banner_content, .contact .contact_motto_map .motto_map_bottom .map_bottom_content, .ServiceBanner .ServiceBanner_content, .App .App_content {
        max-width: 1200px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list {
        padding: 0px 25px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_content {
        height: 78px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification {
        padding-top: 35px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 264px;
    }

    .case .IndexService_content .case_content_title a {
        margin-right: 2.5%;
        width: 6%;
    }

    .contact .contact_content .contact_content_list {
        padding: 50px 18px;
    }

    .contact .contact_content .contact_content_list .content_list_hover p {
        line-height: 22px;
    }

    .contact .contact_content .contact_content_list .content_list_hover {
        padding: 20px;
    }

    .contact .contact_content .contact_content_list {
        width: 100%;
    }

    .about_banner .about_banner_content {
        width: 65%;
    }

    .about_banner .about_banner_content .banner_content_logo {
        margin: 30px 0;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_content {
        padding-right: 5%;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_content img {
        max-width: 100%;
    }

    .ServiceApp_application .ServiceApp_application_left {
        padding-left: 5%;
    }

    .ServiceApp_application .ServiceApp_application_right {
        padding-right: 5%;
        padding-top: 50px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 550px;
    }

    .App .App_content .App_content_body .content_body_right {
        padding: 20px;
    }

    .ServiceCooperation .ServiceCooperation_content {
        padding-left: 18px;
    }

    .ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list {
        width: 130px;
    }

    .ServiceCooperation .ServiceCooperation_content .ServiceCooperation_content_list .content_list_title {
        left: -45px;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left {
        width: 75%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_icon {
        width: 34%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet {
        width: 66%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list {
        padding-right: 1%;
    }

    .IndexService .WeChat_content {
        padding: 0px 85px;
    }

    .profit .profit_content .profit_content_list {
        padding: 55px 20px;
        height: 480px;
    }

    .Marketing_seo .Marketing_seo_content, .Marketing_idea .Marketing_idea_content {
        padding: 0px 20px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .list_img_title img {
        height: 443px;
    }

    .contact .contact_content .contact_content_list .content_list_icon {
        bottom: 2%;
    }
}

@media screen and (max-width: 1200px) {
    /*服务-高端网站定制*/
    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left {
        width: 100%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right {
        width: 100%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list {
        width: 49%;
        margin-right: 2%;
        padding: 25px 0;
        margin-bottom: 2% !important;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(3) {
        margin-right: 2%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list:nth-child(2n) {
        margin-right: 0;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet dt {
        height: 66px;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_icon, .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet {
        float: none;
        width: 100%;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_icon {
        margin-bottom: 15px;
        line-height: 30px;
        font-size: 33px !important;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_left .content_left_list .left_list_contnet {
        padding: 0px 10px;
        text-align: center;
    }

    .ServiceCooperation {
        height: auto;
    }

    .ServiceCooperation .ServiceCooperation_content {
        display: none;
    }

    .ServiceCooperation .ServiceCooperation_content_mo {
        display: block;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right {
        padding: 15px 0;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title dd {
        font-size: 18px;
        margin-bottom: 5px;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title dt {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .ServiceWebsite .ServiceWebsite_content .ServiceWebsite_content_right .content_right_title {
        margin-bottom: 30px;
    }

    .FloatingWindow {
        top: 85%;
        z-index: 3
    }

    .FloatingWindow .FloatingWindow_list {
        display: none;
    }

    .FloatingWindow .FloatingWindow_list:last-child {
        display: block;
    }

    /*底部*/
    .footer .footer_content {
        display: none;
    }

    .footer {
        height: auto;
    }

    .footer #mydiv {
        display: none;
    }

    .footer_content_copyright {
        padding: 20px 2%;
    }

    /*案例展示*/
    .case .IndexService_content .case_content_title {
        height: auto;
        position: inherit;
        padding: 0 0;
        top: 0;
        margin-bottom: 30px;
        border-bottom: 0;
    }

    img {
        max-width: 100%;
    }

    .index_ourys .index_ourys_nei .ourys_nei_wen {
        display: none;
    }

    .all {
        width: 96%;
        margin: 0 auto;
        height: auto;
        overflow: hidden;
    }

    .slick-next, .slick-prev {
        display: none !important;
    }

    .mo_height {
        height: 50px;
    }

    .slick-dots li {
        width: 15px !important;
        height: 1px !important;
        margin: 0px 4px !important;
    }

    /*服务项目*/
    .ServiceBanner .ServiceBanner_content {
        width: 96%;
    }

    .Marketing_team .Marketing_team_content .team_content_left {
        width: 100%;
        padding-bottom: 0;
    }

    .Marketing_team .Marketing_team_content .team_content_left:nth-child(3) {
        padding: 0;
    }

    .Marketing_team .Marketing_team_content .team_content_center {
        display: none;
    }

    .Marketing_team .Marketing_team_content .team_content_left .content_left_list {
        text-align: left;
        width: 49%;
        margin-right: 2%;
        text-align: center;
    }

    .Marketing_team .Marketing_team_content .team_content_left .content_left_list:nth-child(2n) {
        margin-right: 0;
    }

    .Marketing_team .Marketing_team_content .team_content_left .content_left_list dd:before {
        display: none;
    }

    .Marketing_team .Marketing_team_content .team_content_left:last-child .content_left_list {
        text-align: center;
    }

    .Marketing_partner .Marketing_partner_content .partner_content_list {
        width: 49%;
        margin-right: 2%;
    }

    .Marketing_partner .Marketing_partner_content .partner_content_list:nth-child(2n) {
        margin-right: 0;
    }

    .Marketing_partner .Marketing_partner_content .partner_content_list:nth-child(2n) {
        border-right: 0;
    }
}

@media screen and (max-width: 1065px) {
    .pc_header .pc_header_content .header_content_right .menu ul li .DropDown {
        opacity: 1;
        top: 0;
        position: fixed;
        height: 100%;
        -webkit-transform: scale(0) !important;
        -moz-transform: scale(0) !important;
        -ms-transform: scale(0) !important;
        -o-transform: scale(0) !important;
        filter: scale(0) !important;
        filter: transform;
        transition: all 0.3s ease 0s;
        -webkit-transform: all 0.3s ease 0s;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li .DropDown .DropDown_content {
        width: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
    }

    .pc_header .pc_header_content .header_content_right .menu ul li .DropDown a {
        width: 100%;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li:hover .DropDown .DropDown_close {
        display: block;
    }

    .owl-theme .owl-controls .owl-nav {
        display: block !important;
    }

    .page li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
    }

    div.burger {
        z-index: 50;
        height: 36px;
        width: 26px;
        position: relative;
        cursor: pointer
    }

    div.x, div.y, div.z {
        position: absolute;
        margin: auto;
        top: 0;
        bottom: 0;
        background: #ff7019;
        border-radius: 2px;
        -webkit-transition: all 200ms ease-out;
        -moz-transition: all 200ms ease-out;
        -ms-transition: all 200ms ease-out;
        -o-transition: all 200ms ease-out;
        transition: all 200ms ease-out
    }

    div.x, div.y, div.z {
        height: 2px;
        width: 20px
    }

    div.y {
        top: 14px;
        width: 16px;
    }

    div.z {
        top: 28px
    }

    div.collapse {
        top: 20px;
        background: #ff7019;
        -webkit-transition: all 70ms ease-out;
        -moz-transition: all 70ms ease-out;
        -ms-transition: all 70ms ease-out;
        -o-transition: all 70ms ease-out;
        transition: all 70ms ease-out
    }

    div.rotate30 {
        -ms-transform: rotate(30deg);
        -webkit-transform: rotate(30deg);
        transform: rotate(30deg);
        -webkit-transition: all 50ms ease-out;
        -moz-transition: all 50ms ease-out;
        -ms-transition: all 50ms ease-out;
        -o-transition: all 50ms ease-out;
        transition: all 50ms ease-out
    }

    div.rotate150 {
        -ms-transform: rotate(150deg);
        -webkit-transform: rotate(150deg);
        transform: rotate(150deg);
        -webkit-transition: all 50ms ease-out;
        -moz-transition: all 50ms ease-out;
        -ms-transition: all 50ms ease-out;
        -o-transition: all 50ms ease-out;
        transition: all 50ms ease-out
    }

    div.rotate45 {
        -ms-transform: rotate(45deg);
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
        -webkit-transition: all 100ms ease-out;
        -moz-transition: all 100ms ease-out;
        -ms-transition: all 100ms ease-out;
        -o-transition: all 100ms ease-out;
        transition: all 100ms ease-out
    }

    div.rotate135 {
        -ms-transform: rotate(135deg);
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg);
        -webkit-transition: all 100ms ease-out;
        -moz-transition: all 100ms ease-out;
        -ms-transition: all 100ms ease-out;
        -o-transition: all 100ms ease-out;
        transition: all 100ms ease-out
    }

    div.navbar {
        height: 73px;
        background: #385e97
    }

    div.circle {
        z-index: 50;
        border-radius: 50%;
        width: 0;
        height: 0;
        position: fixed;
        top: 10px;
        right: 16px;
        background: url(../images/bg15.jpg) no-repeat 50%/cover;
        background-size: 100%;
        opacity: 1;
        -webkit-transition: all 300ms cubic-bezier(0, .995, .99, 1);
        -moz-transition: all 300ms cubic-bezier(0, .995, .99, 1);
        -ms-transition: all 300ms cubic-bezier(0, .995, .99, 1);
        -o-transition: all 300ms cubic-bezier(0, .995, .99, 1);
        transition: all 300ms cubic-bezier(0, .995, .99, 1)
    }

    div.circle.expand {
        width: 100%;
        height: 100%;
        border-radius: 0%;
        top: 0;
        right: 0;
        -webkit-transition: all 400ms cubic-bezier(0, .995, .99, 1);
        -moz-transition: all 400ms cubic-bezier(0, .995, .99, 1);
        -ms-transition: all 400ms cubic-bezier(0, .995, .99, 1);
        -o-transition: all 400ms cubic-bezier(0, .995, .99, 1);
        transition: all 400ms cubic-bezier(0, .995, .99, 1)
    }

    .pc_header .pc_header_content .header_content_right .menu ul li {
        height: 0;
    }

    .pc_header .pc_header_content .header_content_right .menu.on ul li {
        height: auto;
    }

    div.open {
        position: fixed;
        right: 2%;
    }

    div.menu ul li {
        width: 100%;
        list-style: none;
        position: absolute;
        top: 50px;
        left: 0;
        height: 0;
        overflow: hidden;
        opacity: 0;
        z-index: 1;
        text-align: center;
        font-size: 0;
        -webkit-transition: all 70ms cubic-bezier(0, .995, .99, 1);
        -moz-transition: all 70ms cubic-bezier(0, .995, .99, 1);
        -ms-transition: all 70ms cubic-bezier(0, .995, .99, 1);
        -o-transition: all 70ms cubic-bezier(0, .995, .99, 1);
        transition: all 70ms cubic-bezier(0, .995, .99, 1)
    }

    div.menu ul li a {
        color: #ff7019;
        width: 100%;
        font-size: 16px;
        display: inline-block;
        text-transform: uppercase;
        text-decoration: none;
        letter-spacing: 3px
    }

    div.menu li.animate {
        font-size: 21px;
        opacity: 1;
        overflow: inherit;
        position: fixed;
        z-index: 6000;
        -webkit-transition: all 150ms cubic-bezier(0, .995, .99, 1);
        -moz-transition: all 150ms cubic-bezier(0, .995, .99, 1);
        -ms-transition: all 150ms cubic-bezier(0, .995, .99, 1);
        -o-transition: all 150ms cubic-bezier(0, .995, .99, 1);
        transition: all 150ms cubic-bezier(0, .995, .99, 1)
    }

    div.menu li.animate:nth-of-type(1) {
        top: 80px;
        transition-delay: .06s
    }

    div.menu li.animate:nth-of-type(2) {
        top: 150px;
        transition-delay: .09s
    }

    div.menu li.animate:nth-of-type(3) {
        top: 220px;
        transition-delay: .12s
    }

    div.menu li.animate:nth-of-type(4) {
        top: 290px;
        transition-delay: .15s
    }

    div.menu li.animate:nth-of-type(5) {
        top: 360px;
        transition-delay: .17s
    }

    div.menu li.animate:nth-of-type(6) {
        top: 430px;
        transition-delay: .20s
    }

    .slick-slider .slick-list, .banner, .slick-hero-slider .image-bg {
        height: 230px !important;
    }

    .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption div {
        font-size: 14px;
    }

    .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption dd p {
        display: none;
    }

    .pc_header {
        background: #fff;
        height: 50px;
        position: inherit;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .pc_header.pc_header_fixed {
        background: #fff;
    }

    .pc_header .pc_header_content {
        padding: 0 15px;
        margin: 0 auto;
        position: relative;
    }

    .pc_header .pc_header_content .header_content_logo img {
        margin-top: -5px;
        height: 32px;
    }

    .pc_header .pc_header_content .header_content_logo .site-name {
        color: #555;
        font-size: 22px;
        line-height: 50px;
    }

    .pc_header .pc_header_content .header_content_right {
        position: absolute;
        top: 50px;
        left: 0;
        z-index: 210;
        width: 100%;
    }

    .pc_header .pc_header_content .header_content_right .menu {
        width: 100%;
        /*margin-top: 50px;*/
        padding: 0 20px;
        background: #fff;
    }

    .pc_header .pc_header_content .header_content_right .menu.on {
        height: 380px;
    }

    .pc_header .pc_header_content .header_content_right .header_content_phone {
        display: none;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li {
        margin-right: 0;
        width: 100%;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li:last-child a {
        border-bottom: 0;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li .menu_title {
        width: 100%;
        line-height: 44px;
        color: #ff7019;
        font-weight: bolder;
    }

    .pc_header .pc_header_content .header_content_right .menu ul li .menu_title:before {
        display: none;
    }

    .recruit {
        display: none;
    }

    .map {
        height: 260px !important;
    }

    .column-banner .column-info {
        margin: 0;
    }

    .column-banner .column-info > .column-name {
        font-size: 24px;
    }

    .column-banner .column-info > .column-description {
        font-size: 14px;
    }

    /*案例展示*/
    .case .IndexService_content .case_content_title {
        display: none;
    }

    /*新闻动态*/
    .news .news_content .news_content_list .content_list_time {
        margin-right: 1%;
        width: 18%;
    }

    .news .news_content .news_content_list .content_list_time dd {
        font-size: 18px;
        margin-bottom: 1px;
    }

    .news .news_content .news_content_list .content_list_txt {
        width: 81%;
        margin-right: 0;
    }

    .news .news_content .news_content_list .content_list_txt p a {
        height: 45px;
        line-height: 22px;
        -webkit-line-clamp: 2;
    }

    .news .news_content .news_content_list .content_list_txt dd a {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .news .news_content .news_content_list .content_list_img {
        display: none;
    }

    .news .news_content .news_content_list {
        padding: 20px 0;
    }

    .news .news_content .news_content_list .btn {
        margin-top: 15px;
    }

    /*新闻内页*/
    .NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_left, .NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help {
        float: none;
        width: 100%;
        margin: 0;
    }

    .NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help {
        text-align: center;
    }

    .NewsDetails .NewsDetails_page .NewsDetails_page_left, .NewsDetails .NewsDetails_page .NewsDetails_page_back {
        width: 100%;
        float: none;
    }

    .NewsDetails .NewsDetails_page .NewsDetails_page_back {
        margin-top: 20px;
    }

    .NewsDetails .NewsDetails_title h1 {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .NewsDetails .NewsDetails_title {
        padding-bottom: 25px;
    }

    .NewsDetails .NewsDetails_content {
        padding: 20px 0;
    }

    .NewsDetails .NewsDetails_content .NewsDetails_content_bottom .content_bottom_help a {
        width: 82px;
        border-radius: 100%;
    }

    .NewsDetails .NewsDetails_page .NewsDetails_page_left .page_left_list {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /*关于我们*/
    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_left, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_left, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_left, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_culture .AboutDetails_culture_left, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_img, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content {
        width: 100%;
        margin: 0;
        top: 0;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_left img, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list img, .AboutDetails .AboutDetails_culture .AboutDetails_culture_right img, .AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_img img, .AboutDetails .AboutDetails_honor .AboutDetails_culture_right img {
        height: auto;
    }

    .AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title, .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt {
        position: inherit;
        top: 0;
        left: 0;
        width: 100%;
        transform: inherit;
        margin: 10px 0;
    }

    .AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_img {
        display: none;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_img {
        display: none;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dd {
        color: #333;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dt {
        color: #666;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list:last-child .right_list_content .list_content_txt dd:before {
        background: #000;
    }

    .AboutDetails .AboutDetails_provider {
        padding: 25px 0;
    }

    .AboutDetails .AboutDetails_culture .AboutDetails_culture_left .culture_left_title dd {
        font-size: 18px;
        margin-bottom: 20px;
        padding-bottom: 10px;
    }

    .AboutDetails .AboutDetails_culture .AboutDetails_culture_left:before {
        display: none;
    }

    .AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dt p {
        color: #666;
    }

    .AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dd {
        color: #333;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .AboutDetails .AboutDetails_honor .AboutDetails_culture_left .culture_left_title dd:before {
        background: #000;
    }

    .AboutNews .AboutNews_content .AboutNews_content_left, .AboutNews .AboutNews_content .AboutNews_content_right {
        width: 100%;
        float: none;
    }

    .AboutNews .AboutNews_content .AboutNews_content_right .content_right_list {
        width: 100%;
        padding: 20px 10px;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dd {
        margin-bottom: 20px;
        padding-bottom: 10px;
        font-size: 18px;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dd span {
        font-size: 14px;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt dt {
        font-size: 14px;
    }

    .AboutDetails .AboutDetails_innovate .AboutDetails_innovate_right .innovate_right_list .right_list_content .list_content_txt {
        padding: 0px 2%;
    }

    /*联系我们*/
    .contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title {
        width: 100%;
    }

    .contact .owl-theme .owl-controls .owl-nav div {
        top: -235px;
    }

    .contact .contact_motto .contact_motto_left, .contact .contact_motto .contact_motto_right {
        width: 100%;
        float: none;
    }

    .contact .contact_motto .contact_motto_left {
        padding: 20px 0;
    }

    .contact .contact_motto {
        padding: 0px 0;
    }

    .contact .contact_motto_map .motto_map_bottom {
        position: inherit;
        transform: inherit;
        top: 0;
        left: 0;
    }

    .contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title {
        padding: 20px 10px;
    }

    .contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title dd {
        font-size: 18px;
    }

    .contact .contact_motto_map .motto_map_bottom .map_bottom_content .map_bottom_title dd span {
        font-size: 14px;
    }

    .contact .contact_motto_map .motto_map_top img {
        height: auto;
    }

    .ContactBanner .ContactBanner_content .ContactBanner_content_right {
        display: none;
    }

    .ContactBanner .ContactBanner_content .ContactBanner_content_left {
        padding: 0px 2%;
        text-align: center;
    }

    .ContactBanner .ContactBanner_content .ContactBanner_content_left dd {
        font-size: 24px;
    }

    .ContactBanner .ContactBanner_content .ContactBanner_content_left dt {
        font-size: 24px;
    }

    .ContactBanner .ContactBanner_content:before {
        display: none;
    }

    .ContactBanner .ContactBanner_content .ContactBanner_content_left {
        position: inherit;
        width: 100%;
        left: inherit;
        top: inherit;
        bottom: auto;
        transform: inherit;
    }

    .about_banner .about_banner_content {
        width: 90%;
    }

    .about_banner .about_banner_content dd {
        font-size: 18px;
        color: #fff;
    }

    .about_banner .about_banner_content .banner_content_logo {
        margin: 20px 0;
    }

    .about_banner .about_banner_content .banner_content_logo img {
        width: 50%
    }

    .about_banner .about_banner_content dt p {
        font-size: 14px;
        color: #fff;
        line-height: 24px;
    }

    /*服务项目*/
    .ServiceBanner .ServiceBanner_content {
        width: 96%;
        position: relative;
        top: 0;
        left: 0;
        transform: inherit;
    }

    .ServiceBanner {
        height: auto;
        padding: 25px 0;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_right {
        text-align: center;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_right img {
        width: 60%;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_title dd {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_title dt {
        font-size: 14px;
        margin-bottom: 20px;
        line-height: 22px;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left {
        width: 100%;
        margin-top: 20px;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list {
        width: 18%;
        margin-right: 2%;
        margin-bottom: 3%;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list dd {
        width: 100%;
        height: 71px;
        line-height: 71px;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list:nth-child(5n) {
        margin-right: 0;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_title {
        margin-bottom: 20px;
    }

    .ServiceApp_application .ServiceApp_application_left, .ServiceApp_application .ServiceApp_application_right {
        padding: 0;
        width: 100%;
    }

    .ServiceApp_application .IndexService_content {
        padding: 30px 2%;
        background: #f6f6f6;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_title dt {
        font-size: 14px;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_title dd {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .ServiceApp_application .ServiceApp_application_right .application_right_top dd {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .ServiceApp_application .ServiceApp_application_left .application_left_content {
        margin: 20px 0;
        padding: 0;
    }

    .ServiceApp_application .ServiceApp_application_right .application_right_top {
        margin-bottom: 30px;
    }

    .ServiceApp_application .ServiceApp_application_right .application_right_bottom .right_bottom_list dd {
        font-size: 16px;
    }

    .App .App_content .App_content_title dd, .App .App_content .App_content_body .content_body_right .body_right_top dd {
        font-size: 18px;
    }

    .App .App_content .App_content_title dt {
        font-size: 14px;
    }

    .App .App_content {
        padding: 30px 0;
    }

    .App .App_content .App_content_nav, .App .App_content .App_content_body .content_body_right, .App .App_content .App_content_body .content_body_right .body_right_top dt {
        margin-top: 15px;
    }

    .App .App_content .App_content_nav a {
        width: 23%;
        margin-right: 2%;
        border-radius: 5px;
    }

    .App .App_content .App_content_body {
        margin-top: 35px;
    }

    .App .App_content .App_content_body .content_body_left, .App .App_content .App_content_body .content_body_right {
        width: 100%;
    }

    .App .App_content .App_content_body .content_body_left .body_left_img {
        margin-bottom: 20px;
    }

    .App .App_content .App_content_body .content_body_left:before {
        display: none;
    }

    .App .App_content .App_content_body .content_body_left .body_left_txt dd {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .app_case .app_case_title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .IndexService .WeChat_content {
        padding: 0px 0;
    }

    .WeChat .WeChat_fan dd {
        font-size: 22px;
        margin: 45px 0;
        margin-top: 65px;
    }

    .WeChat .WeChat_content .WeChat_content_list dt {
        margin-top: 20px;
    }

    .WeChat .WeChat_content .WeChat_content_list dd img {
        width: 10%;
    }

    .WeChat .owl-theme .owl-controls .owl-nav div {
        top: -75px;
    }

    .profit .profit_title {
        font-size: 22px;
        padding: 40px 0;
    }

    .profit .profit_content .profit_content_list {
        width: 100%;
        height: auto;
    }

    .profit .profit_content .profit_content_list:nth-child(2n) {
        background: #fef8f0;
    }

    .profit .profit_content .profit_content_list:nth-child(5), .profit .profit_content .profit_content_list:nth-child(7) {
        background: #f2fdfa;
    }

    .profit .profit_content .profit_content_list .content_list_title {
        font-size: 18px;
        margin-bottom: 25px;
    }

    .profit .profit_content .profit_content_list .content_list_icon {
        margin-bottom: 35px;
    }

    .program .program_content .program_content_center {
        display: none;
    }

    .program .program_content .program_content_left {
        width: 100%;
    }

    .program .program_content .program_content_left .content_left_list {
        width: 32%;
        margin-right: 2%;
        margin-top: 0;
        margin-bottom: 4%;
    }

    .program .program_content .program_content_left .content_left_list:nth-child(3n) {
        margin-right: 0%;
    }

    .program .program_content .program_content_left .content_left_list dd {
        width: 80px;
        height: 80px;
        line-height: 80px;
        margin: 0 auto;
        padding: 0px 8px;
    }

    .program .program_title {
        font-size: 22px;
    }

    .program .program_content .program_content_left .content_left_list dt {
        font-size: 14px;
    }

    .program {
        height: auto;
    }

    .program_service .program_service_title dd {
        font-size: 22px;
    }

    .program_service .program_service_title dt {
        font-size: 14px;
    }

    .program_service {
        padding-top: 0;
    }

    .program_service .program_service_content .service_content_list {
        width: 32%;
        margin-right: 2%;
    }

    .program_service .program_service_content .service_content_list:nth-child(3n) {
        margin-right: 0;
    }

    .Mall .Mall_content .WeChat_content_list dt {
        font-size: 16px;
    }

    .Mall .Mall_content .WeChat_content_list dd img {
        width: 20%
    }

    .Mall {
        padding-bottom: 15px;
    }

    .case_banner .case_banner_content .banner_content_right {
        display: none;
    }

    .case_banner .case_banner_content .banner_content_left {
        position: inherit;
        width: 95%;
        height: auto !important;
        margin: 0 auto;
        bottom: 0;
        left: inherit;
        top: inherit;
        transform: inherit;
    }

    .CaseDetai_banner, .case_banner .case_banner_content {
        height: auto !important;
        padding: 20px 0;
    }
}

@media screen and (max-width: 768px) {
    /*首页*/
    .IndexService .IndexService_content {
        padding: 30px 0;
    }

    .IndexService .IndexService_content .IndexService_content_title dd {
        font-size: 18px;
    }

    .IndexService .IndexService_content .IndexService_content_title dt {
        line-height: 22px;
        font-size: 14px;
        margin-top: 8px;
    }

    .IndexService .IndexService_content .IndexService_content_title {
        padding-bottom: 25px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list {
        width: 100%;
        margin-right: 0%;
        padding: 0px 5% !important;
        background: none;
        margin-bottom: 25px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_classification {
        padding-top: 20px;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_content {
        height: auto;
    }

    .IndexService .IndexService_content .IndexService_content_body .content_body_list .body_list_title {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list {
        width: 49%;
        margin-right: 2%;
        margin-bottom: 4%;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list:nth-child(2n) {
        margin-right: 0;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list:nth-child(3) {
        margin-right: 2%;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_icon {
        display: none;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd {
        font-size: 16px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd a {
        font-size: 16px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dd span {
        font-size: 13px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left {
        width: 100%;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title {
        margin-top: 10px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list {
        padding-bottom: 15px;
    }

    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_title .list_title_left dt {
        margin-top: 0;
    }

    .column-more a {
        font-size: 14px;
        width: 120px;
        line-height: 44px;
    }

    .advantage .advantage_content .advantage_content_list {
        width: 100%;
        height: auto;
        padding: 0;
        margin-right: 0%;
        margin-left: 0;
        background: none !important;
        margin-bottom: 25px;
    }

    .advantage .advantage_content .advantage_content_list:last-child {
        margin-bottom: 0;
    }

    .advantage .advantage_content .advantage_content_list .content_list_title {
        font-size: 40px;
        padding-bottom: 5px;
        margin-bottom: 10px;
        text-align: center;
    }

    .advantage .advantage_content .advantage_content_list .content_list_body p {
        font-size: 13px;
        text-align: center;
    }

    .advantage .advantage_content .advantage_content_list .content_list_title:before {
        left: 0;
        right: 0;
        margin: 0 auto;
    }

    .advantage .advantage_content {
        padding: 4% 2%;
    }

    .partner .partner_content .partner_content_list {
        margin-right: -1px;
        border-right: 1px solid #dcdcdc;
    }

    .IndexNews .IndexService_content .IndexService_content_title dd, .IndexNews .IndexService_content .IndexService_content_title .IndexNews_title {
        width: 100%;
        float: none;
    }

    .IndexNews .IndexNews_title a {
        width: 32%;
        margin-right: 2%;
    }

    .IndexNews .IndexNews_title a:nth-child(3) {
        margin-right: 0;
    }

    .IndexNews .IndexNews_content .IndexNews_content_video, .IndexNews .IndexNews_content .IndexNews_content_right, .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img, .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content {
        width: 100%;
        float: none;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right {
        margin-top: 25px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img {
        margin-bottom: 15px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_video .content_video_body dd {
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .IndexNews .IndexNews_content .IndexNews_content_video .content_video_body dd span {
        font-size: 25px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_video .content_video_body {
        padding: 20px 10px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dd {
        font-size: 16px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dd a {
        font-size: 16px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title dt {
        font-size: 16px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_content .list_content_title a {
        line-height: 35px;
    }

    .IndexNews .IndexNews_content .IndexNews_content_video .content_video_title img {
        height: auto;
    }

    .CaseDetail .CaseDetail_content {
        padding: 20px;
    }

    .CaseDetail .CaseDetail_content .CaseDetail_content_contact {
        margin-top: 25px;
    }

    .CaseDetail .CaseDetail_content .CaseDetail_content_contact a {
        width: auto;
        font-size: 16px;
        line-height: 45px;
        padding: 0px 20px;
        height: auto;
    }

    .case_banner .case_banner_content .banner_content_left .content_left_title {
        font-size: 18px;
        margin-bottom: 25px;
        padding-bottom: 10px;
    }

    .case_banner .case_banner_content .banner_content_left .content_left_txt {
        margin-bottom: 30px;
    }

    .video_window .videobox {
        width: 95%;
    }

    .video_window .videobox video {
        width: 100%;
    }

    .video_window .video_window_close {
        right: 0;
    }

    .contact .contact_motto_map .motto_map_bottom .map_bottom_content {
        padding-left: 0;
        margin-top: 25px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt {
        height: 92%;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .content_list_txt .list_txt_title {
        font-size: 18px;
        margin: 9px 0;
    }
}

@media screen and (max-width: 414px) {
    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 134px;
    }

    .partner .partner_content .partner_content_list {
        width: 50%;
    }

    .IndexNews .IndexNews_content .IndexNews_content_right .content_right_list .right_list_img img {
        height: 215px;
    }

    .details_banner, .WeChatBanner {
        height: 120px !important;
    }

    .case_banner {
        height: 200px !important;
    }

    .ContactBanner {
        height: auto !important;
    }

    .case_banner .case_banner_content .banner_content_left .content_left_button a {
        width: 48%;
        margin-right: 4%;
    }

    .case_banner .case_banner_content .banner_content_left .content_left_button a:last-child {
        margin-left: 0;
        margin-right: 0;
    }

    .AboutNews .AboutNews_content .AboutNews_content_left .content_left_list img {
        height: 125px;
    }

    .about_banner .about_banner_img img {
        height: 450px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 306px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .list_img_title img {
        height: 306px
    }
}

@media screen and (max-width: 375px) {
    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 121px;
    }

    .AboutNews .AboutNews_content .AboutNews_content_left .content_left_list img {
        height: 113px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 277px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .list_img_title img {
        height: 277px;
    }
}

@media screen and (max-width: 320px) {
    .IndexCase .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 103px;
    }

    .AboutNews .AboutNews_content .AboutNews_content_left .content_left_list img {
        height: 96px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img img {
        height: 236px;
    }

    .app_case .IndexCase_content .IndexCase_content_list .content_list_img .list_img_title img {
        height: 236px;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list {
        width: 23%;
        margin-right: 2%;
        margin-bottom: 3%;
    }

    .ServiceBanner .ServiceBanner_content .ServiceBanner_content_left .content_left_bottom .left_bottom_list:nth-child(4n) {
        margin-right: 0;
    }
}