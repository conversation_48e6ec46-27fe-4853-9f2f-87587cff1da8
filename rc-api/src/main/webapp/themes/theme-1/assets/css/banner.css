
.slick-hero-slider-caption h1{
    font-size: 46px;
}

.slick-hero-slider .image-bg {
  background-position: center center;
  background-size: cover;
  position: relative;
  height: 520px;
}

.banner{ overflow: hidden; width: 100%;margin: 0 auto; height: 520px;}
.slick-hero-slider-caption {
  padding-top: 120px;
  padding-bottom: 100px;
  color: rgba(255, 255, 255, 0.8)
}

.slick-hero-slider-caption .hero-caption-label {
  color: #FFF;
  font-size: 15px;
}

.slick-hero-slider-caption h1 {
  color: #FFF;
  margin-top: 0;
  line-height: 1.2;
  margin-bottom: 20px;
}

.slick-hero-slider-caption p {
  color: #FFF;
}

.slick-hero-slider-caption a span {
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.8);
  color: #FFF;
  display: inline-block;
  padding: 7px 25px;
  display: inline-block;
  margin-top: 10px;
  transition: all .3s ease;
  -webkit-transition: all .3s ease;
  -moz-transition: all .3s ease
}

.slick-hero-slider-caption a span.bg-primary {
  border: #ff9800;
  padding: 10px 20px 10px;
}

.slick-hero-slider-caption a span.bg-primary:after {
  content: "";
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: -1px;
  height: 4px;
  border-radius: 0 0 4px 4px;
  background: rgba(0, 0, 0, 0.15);
}
.slick-hero-slider-caption a span {
    background-color: #ff9800;
}
.slick-hero-slider-caption a:hover span {
  text-decoration: none;
  position: relative;
  color: #212121;
  background-color: #ffffff !important;
}

.slick-hero-slider.slick-inner-dot .slick-dots {
  bottom: 0
}

.alt-dot-position.slick-hero-slider.slick-inner-dot .slick-dots {
  bottom: 0
}

.alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption {
  width:100%;
  margin:0 auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.alt-dot-position.slick-hero-slider.slick-inner-dot .index_banner5 dd{ text-align:center;}

.alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption dd{ width:100%; text-align:center;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption div{ font-size:24px; color:#fff;letter-spacing:7px; text-align:center; width:100%; margin:20px 0px;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption dd p{ font-size:20px; color:#fff; text-transform:uppercase; line-height:28px;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .clear dd{ width:40%; text-align:right;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .clear dd:nth-child(1){ text-align:right; margin-right:80px;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .clear dd:nth-child(2){ text-align:left; padding:32px 0px;}

.alt-dot-position.slick-hero-slider.slick-inner-dot .Network{ width:80%;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .Network dd{ text-align:left;
	font-size: 48px;
    display: block;
	font-weight:bolder;
    margin-bottom: 15px;
}
.alt-dot-position.slick-hero-slider.slick-inner-dot .Network div{ text-align:left;letter-spacing:0px;
	font-size: 36px;
    line-height: 60px;
	margin:0px;
}


.slick-slider-center-mode .slick-item.slick-center .year dd{ position:absolute;
left: 53%; top: 50%; transform: translateX(-50%) translateY(-50%);
}

.slick-slider-center-mode .slick-item.slick-center .Network_bg{

    animation: animate-cloud 12s ;	
}

@-webkit-keyframes animate-cloud {
  from {
    background-position: 100% 0px;
  }
  to {
    background-position: 100% -500px;
  }
}

.bg2{
	position: absolute;
	width: 705px;
	height: 705px;
	background:url(../images/yuan2.png) no-repeat 100%;
	animation: animtrans 32s linear infinite;
	-webkit-animation: animtrans 32s linear infinite;
	left: 32%;
    top: -100%;
    transform: translateX(-50%) translateY(-50%);
}

@keyframes animtrans {
	from{
		transform: rotateZ(360deg);
	}
	to{
		transform: rotateZ(0deg);
	}
}



@media only screen and (max-width: 1500px) {
 .slick-slider-center-mode .slick-item.slick-center .Network_bg{  animation: animate-cloud 20s ;	}
@-webkit-keyframes animate-cloud {
  from {
    background-position: 100% 0px;
  }
  to {
    background-position: 100% -300px;
  }
}
}










.slider.slick-hero-slider.with-main-search-wrapper-2 .slick-dots {
  bottom: 45px;
}

@media only screen and (max-width: 1199px) {
  .hero {
    background-size: auto;
  }

}

@media only screen and (max-width: 991px) {
  .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption {
    padding-bottom: 100px
  }
  .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-dots {
    bottom: 20px
  }
  .slick-hero-slider-caption h1 {
    font-size: 30px;
  }
  .home-single-service .content h4{
    padding-bottom: 5px;
  }
  .banner-area {
    padding-top: 20px;
    margin-bottom: 20px;
  }
  .banner-area h2 {
    margin-top: 10px;
  }
  .bottom-area .instragram ul li {
    margin-right: 5px;
  }
  .search-box{
    display: none;
  }
.bg2{ width: 150px; height:auto}
.slick-slider-center-mode .slick-item.slick-center .year dd{ width:60%; left:50%;}
.slick-slider-center-mode .slick-item.slick-center .Network_bg{  animation: animate-cloud 62s ;	}
@-webkit-keyframes animate-cloud {
  from {
    background-position: 100% 0px;
  }
  to {
    background-position: 100% 0px;
  }
}
.alt-dot-position.slick-hero-slider.slick-inner-dot .Network dd{ font-size:18px;}
.alt-dot-position.slick-hero-slider.slick-inner-dot .Network div{ line-height:24px;}
}


@media only screen and (max-width: 1200px) and (min-width: 992px) {
  .home-single-service .content h4 {
    padding-top: 10px;
    padding-bottom: 5px;
  }
  .home-single-service .content a{
    margin-top: 5px;
  }
  .home-chose-us-area .inner .home-chose-us-area-left{
    width: 100% !important;
  }
  .home-project-detail .content ul {
    padding-bottom: 26px;
  }
  .home-project-area #myCarousel .nav{
    overflow: hidden;
  }
  .menu-area .menuzord-brand {
    margin: 14px 15px 0 0;
  }
  .home-call-action a{
    margin-top: 36px;
  }
  .slick-hero-slider-caption h1{
    font-size: 30px;
  }
  .about-experience-area .content {
    margin-top: 30px;
    padding-left: 15px;
  }
  .hidden-tab-land{
    display: none;
  }
  .partner-heading.about {
    margin-bottom: 10px;
  }
  .quick-contact {
    padding: 15px;
  }
  .project-detail-overview .project-logo-area{
    width: 330px;
  }
  .single-project-detail .content {
    padding-top: 0;
  }
  .single-project-detail .content h3 {
    font-size: 18px;
    line-height: 26px;
    margin-top: 0;
    margin-bottom: 10px;
  }

}


@media only screen and (min-width: 768px) and (max-width: 991px) {

  .home-work-carousel .mix {
    width: 100%;
  }
  .home-single-work img {
    width: 100%;
  }
  .home-project-area #myCarousel .nav{
    overflow: hidden;
  }
  .home-call-action a{
    margin-top: 52px;
  }
  .single-news .content {
    padding: 25px 15px 30px 15px;
  }
  .about-tab-content .feedback {
    padding-top: 20px;
  }
  .quick-contact {
    padding: 10px;
  }
  .quick-contact p{
    min-height: 83px;
  }

}

@media only screen and (max-width: 767px) {
  .hero {
    padding: 40px 0 40px;
  }
  .hero h1 {
    font-size: 36px;
    line-height: 1.35;
  }
    .slick-hero-slider-caption h1{
        font-size: 16px;
    }
  .slick-hero-slider-caption h2 {
    font-size: 35px;
    margin-bottom: 20px;
    line-height: 1.2;
    text-align: left
  }
  .slick-hero-slider-caption p {
    font-size: 12px;
    line-height: 1.5;
    text-align: left
  }
  .slick-hero-slider-caption .hero-caption-label{
    font-size: 12px;
  }
  .slick-hero-slider-caption {
    padding: 50px 30px 70px
  }
  .slick-hero-slider .slick-prev {
    left: 10px
  }
  .slick-hero-slider .slick-next {
    right: 10px
  }
  .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption dd a{ margin-right: 3px;}
  .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-hero-slider-caption {
    padding: 20px;
  }
  .alt-dot-position.slick-hero-slider.slick-inner-dot .slick-dots {
    bottom: 10px;
  }
  .who-we-are .content h1{
    font-size: 28px;
  }
  .who-we-are {
    padding-top: 0;
    padding-bottom: 20px;
  }
  .partner-heading {
    margin-bottom: 40px;
    margin-top: 40px;
  }
  .home-one-service {
    padding-bottom: 0;
  }
  .home-bussiness-area {
    padding-bottom: 20px;
  }
  .home-call-action a{
    margin-top: 5px;
  }
  .banner-area h2 {
    margin-top: 0;
    font-size: 18px;
  }
  .banner-area ul {
    padding-top: 10px;
    padding-bottom: 0;
  }
  .banner-area .touch-button {
    padding-top: 30px;
  }

  .footer-bar .usefull-link ul li {
    margin-left: 5px;
  }
  .partner-heading h2{
    font-size: 18px;
  }
  .single-complete-project .content h2 {
    font-size: 18px;
  }
  .news-detail-social-one .categories ul li {
    margin-left: 5px;
  }
  .service-body .single > h2, .mission-body .single-box h2, .partner-content .detail h3, .career-overview h2 {
    line-height: 26px;
    font-size: 18px;
  }
  .project-detail-content .first h2, .project-detail-content .second h3, .single-project-detail .content h3{
    font-size: 18px;
    line-height: 26px;
  }


}

@media (max-width: 479px) {
  .slick-hero-slider-caption h2 {
    font-size: 30px;
    line-height: 1.2
  }
  .slick-hero-slider-caption p {
    font-size: 12px;
    line-height: 1.5;
  }
}



.slick-slider {
    position: relative;
    display: block;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent
}

.slick-list {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0
}

.slick-list:focus {
    outline: none
}

.slick-list.dragging {
    cursor: pointer;
    cursor: hand
}

.slick-slider .slick-track,
.slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.slick-track {
    position: relative;
    top: 0;
    left: 0;
    display: block
}

.slick-track:before,
.slick-track:after {
    display: table;
    content: ''
}

.slick-track:after {
    clear: both
}

.slick-loading .slick-track {
    visibility: hidden
}

.slick-slide {
    display: none;
    float: left;
    height: 100%;
    min-height: 1px
}

[dir='rtl'] .slick-slide {
    float: right
}


.slick-slide.slick-loading img {
    display: none
}

.slick-slide.dragging img {
    pointer-events: none
}

.slick-initialized .slick-slide {
    display: block
}

.slick-loading .slick-slide {
    visibility: hidden
}

.slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent
}





/* Arrows */

.slick-prev,
.slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 22px;
    height: 20px;
    margin-top: -10px;
    padding: 0;
    cursor: pointer;
    color: transparent;
    border: none;
    outline: none;
    background: transparent;
    line-height: 20px;
    text-align: center
}

.slick-prev:hover,
.slick-prev:focus,
.slick-next:hover,
.slick-next:focus {
    color: transparent;
    outline: none;
    background: transparent
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
    opacity: 1
}

.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
    opacity: 0
}

.slick-prev:before,
.slick-next:before {
    content: "";
    line-height: 1;
    opacity: .3;
    color: #333;
    width: 44px;
    height: 44px;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.slick-prev {
    left: 0px
}

[dir='rtl'] .slick-prev {
    right: 0px;
    left: auto
}

.slick-prev:before {
    content: "\f060";
}

[dir='rtl'] .slick-prev:before {
    content: '\e610'
}

.slick-next {
    right: -65px
}

[dir='rtl'] .slick-next {
    right: auto;
    left: -65px
}

.slick-next:before {
    content: "";
    line-height: 1;
    opacity: .3;
    color: #333;
    width: 44px;
    height: 44px;
    display: inline-block;
}

[dir='rtl'] .slick-next:before {
    content: '\e60e'
}


/* Dots */

.slick-slider {
    margin: 0
}

.slick-dots {
    position: absolute;
    bottom: -25px;
    display: block;
    width: 100%;
    padding: 0;
    list-style: none;
    text-align: center
}

.slick-dots li{
    position: relative;
    display: inline-block;
    width: 41px;
    height: 2px;
    margin: 0;
    padding: 0;
    cursor: pointer;
	background:#848484;
	margin:0px 8px;
	overflow: hidden;
           transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;  
}
.slick-dots li span{ display:inline-block; position:absolute; width:0px; left:0; top:0; height:100%; opacity:0;background:#ff7019;
transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;  
}

.slick-dots li.slick-active span{ opacity:1;animation:mymove 7s ease-in-out 0s 1 alternate forwards;
           transition: all 0.3s ease;
    -webkit-transform: all 0.3s ease;  }



@keyframes mymove
	{
		from {width:0px;}
		to {width:100%;}
	}







.slick-dots li button {
    font-size: 0;
    line-height: 0;
    display: block;
    width: 18px;
    height: 18px;
    padding: 5px;
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: none;
    background: transparent;
	display:none;
}

.slick-dots li button:hover,
.slick-dots li button:focus {
    outline: none
}

.slick-dots li button:hover:before,
.slick-dots li button:focus:before {
    opacity: 1
}

.slick-dots li button:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    content: '';
    background: #000;
    border-radius: 50%;
	margin:0px 10px
}

.slick-dots li.slick-active button:before {
    opacity: .75;
    background: #000
}

.slick-slide .image {
    padding: 2px
}


.slick-slide img.slick-loading {
    border: 0
}

.slider h3 {
    color: #fff;
    font-size: 36px;
    line-height: 100px;
    margin: 10px;
    padding: 2%;
    position: relative;
    text-align: center
}

.variable-width .slick-slide p {
    height: 100px;
    color: #FFF;
    margin: 5px;
    line-height: 100px
}

.variable-width .image {
    height: 100%
}

.variable-width .image img {
    display: block;
    height: 100%;
    width: 100%
}

.slick-center-mode .slick-center h3 {
    -moz-transform: scale(1.08);
    -ms-transform: scale(1.08);
    -o-transform: scale(1.08);
    -webkit-transform: scale(1.08);
    color: #e67e22;
    opacity: 1;
    transform: scale(1.08)
}

.slick-center h3 {
    opacity: .8;
    transition: all 300ms ease
}

.slick-content {
    margin: auto;
    padding: 20px;
    width: 600px
}

.slick-content:after,
.buttons::after {
    clear: both;
    content: "";
    display: table
}

.slick-center-mode .slick-center .image {
    -moz-transform: scale(1.08);
    -ms-transform: scale(1.08);
    -o-transform: scale(1.08);
    -webkit-transform: scale(1.08);
    color: #e67e22;
    opacity: 1;
    transform: scale(1.08)
}

.slick-center-mode .image {
    opacity: .3;
    transition: all 300ms ease;
    padding: 10px
}

.slick-center-mode .image:hover {
    cursor: pointer
}

.slick-content {
    margin: auto;
    padding: 20px;
    width: 600px
}

.slick-content:after,
.buttons::after {
    clear: both;
    content: "";
    display: table
}

.slick-center-mode img {
    border: 2px solid #FFF;
    display: block;
    width: 100%
}


/* Slick Override */

.slick-slide .image {
    padding: 0
}


/* Slick Carousel Gap */

.slick-carousel.gap-2 {
    margin-left: -1px;
    margin-right: -1px
}

.slick-carousel.gap-2 .slick-carousel-inner {
    padding-left: 1px;
    padding-right: 1px
}

.slick-carousel.gap-5 {
    margin-left: -2px;
    margin-right: -3px
}

.slick-carousel.gap-5 .slick-carousel-inner {
    padding-left: 2px;
    padding-right: 3px
}

.slick-carousel.gap-10 {
    margin-left: -5px;
    margin-right: -5px
}

.slick-carousel.gap-10 .slick-carousel-inner {
    padding-left: 5px;
    padding-right: 5px
}

.slick-carousel.gap-15 {
    margin-left: -7px;
    margin-right: -8px
}

.slick-carousel.gap-15 .slick-carousel-inner {
    padding-left: 7px;
    padding-right: 8px
}

.slick-carousel.gap-20 {
    margin-left: -10px;
    margin-right: -10px
}

.slick-carousel.gap-20 .slick-carousel-inner {
    padding-left: 10px;
    padding-right: 10px
}

.slick-carousel.gap-25 {
    margin-left: -12px;
    margin-right: -13px
}

.slick-carousel.gap-25 .slick-carousel-inner {
    padding-left: 12px;
    padding-right: 13px
}

.slick-carousel.gap-30 {
    margin-left: -15px;
    margin-right: -15px
}

.slick-carousel.gap-30 .slick-carousel-inner {
    padding-left: 15px;
    padding-right: 15px
}

.slick-carousel.gap-40 {
    margin-left: -20px;
    margin-right: -20px
}

.slick-carousel.gap-40 .slick-carousel-inner {
    padding-left: 20px;
    padding-right: 20px
}

.slick-carousel.gap-50 {
    margin-left: -25px;
    margin-right: -25px
}

.slick-carousel.gap-50 .slick-carousel-inner {
    padding-left: 25px;
    padding-right: 25px
}


/* Slick Carousel Center Mode */

.slick-carousel-center-mode {
    margin-left: -40px;
    margin-right: -40px
}

.slick-carousel-center-mode .slick-carousel-inner {
    margin: 40px;
    -webkit-transition: all .3s ease-in-out 0;
    -moz-transition: all .3s ease-in-out 0;
    -ms-transition: all .3s ease-in-out 0;
    -o-transition: all .3s ease-in-out 0;
    transition: all .3s ease-in-out 0
}

.slick-carousel-center-mode .slick-center .slick-carousel-inner {
    margin: 0;
    margin-left: -10px;
    margin-right: -10px
}

.slick-carousel-center-mode .slick-dots {
    bottom: -50px
}


/* Slick Carousel Gallery */

.slick-gallery-slideshow .slick-prev {
    left: 10px;
    padding-left: 1px
}

.slick-gallery-slideshow .slick-next {
    right: 10px;
    padding-left: 1px
}

.slick-gallery-slideshow .slick-prev:before,
.slick-gallery-slideshow .slick-next:before {
    color: #FFF;
    opacity: .7
}

.slick-gallery-slideshow .slick-prev:hover:before,
.slick-gallery-slideshow .slick-prev:focus:before,
.slick-gallery-slideshow .slick-next:hover:before,
.slick-gallery-slideshow .slick-next:focus:before {
    opacity: 1
}

.gallery-slideshow {
    margin-bottom: 1px
}

.gallery-slideshow .image {
    padding: 0
}

.gallery-nav .image {
    padding: 0;
    border: 1px solid transparent;
    opacity: 1;
    transition: all 300ms ease;
    position: relative
}

.gallery-nav .image:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .5);
    -webkit-transition: all .3s ease-in-out 0;
    -moz-transition: all .3s ease-in-out 0;
    -ms-transition: all .3s ease-in-out 0;
    -o-transition: all .3s ease-in-out 0;
    transition: all .3s ease-in-out 0
}

.gallery-nav .slick-center .image {
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    -webkit-transform: none
}

.gallery-nav .slick-center .image:after {
    background: rgba(0, 0, 0, 0)
}

.gallery-nav .image:hover {
    cursor: pointer
}

.slick-slider-center-mode .slick-item {
    position: relative
}

.slick-slider-center-mode .slick-item .image {
    position: relative;
    padding: 0
}

.slick-slider-center-mode .slick-item .image:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .7);
    -webkit-transition: all .3s ease-in-out 0;
    -moz-transition: all .3s ease-in-out 0;
    -ms-transition: all .3s ease-in-out 0;
    -o-transition: all .3s ease-in-out 0;
    transition: all .3s ease-in-out 0
}

.slick-slider-center-mode .slick-center.slick-item .image:after {
    background: rgba(0, 0, 0, 0)
}

.slick-slider-center-mode .slick-item .image:hover {
    cursor: pointer
}

.slick-slider-center-mode.slick-slider-full .slick-item .image:after {
    display: none
}

.slick-slider-center-mode.gap-2 .slick-item {
    padding: 0 1px
}

.slick-slider-center-mode .slick-prev {
    left: 33px;
}

.slick-slider-center-mode .slick-next {
    right: 33px;
}

.slick-slider-center-mode .slick-prev:before,
.slick-slider-center-mode .slick-next:before {
    color: #FFF;
    opacity: 1;
}

.slick-slider-center-mode .slick-prev:hover:before,
.slick-slider-center-mode .slick-prev:focus:before,
.slick-slider-center-mode .slick-next:hover:before,
.slick-slider-center-mode .slick-next:focus:before {
    opacity: 1
}

.slick-slider-center-mode.slick-slider-full .slick-prev {
    left: 15px
}

.slick-slider-center-mode.slick-slider-full .slick-next {
    right: 15px
}

.slick-slider-center-mode.slick-slider-full .image.image-bg {
    height: 660px;
    background-size: cover!important;
    background-position: center center
}

.slick-slider-center-mode .slick-caption {
    position: absolute;
    top: 20px;
    left: 20px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.3)
}

.slick-slider-center-mode .slick-center .slick-caption {
    color: rgba(255, 255, 255, 1)
}


/* Slick Carousel Wariable width */

.variable-width .image {
    padding: 0;
    border: 1px solid transparent;
    opacity: .5;
    transition: all 300ms ease
}

.variable-width .slick-center .image {
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    -webkit-transform: none;
    opacity: 1;
    border-color: red
}

.variable-width .image:hover {
    cursor: pointer
}


/* Slick Carousel Animation */

.slick-animation .animation {
    display: inline-block;
    opacity: 0;
    -webkit-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
    display: inline-block;
    opacity: 0;
    -webkit-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out
}

.slick-animation .transitionDelay1 {
    -webkit-transition-delay: .1s;
    transition-delay: .1s
}

.slick-animation .transitionDelay2 {
    -webkit-transition-delay: .2s;
    transition-delay: .2s
}

.slick-animation .transitionDelay3 {
    -webkit-transition-delay: .3s;
    transition-delay: .3s
}

.slick-animation .transitionDelay4 {
    -webkit-transition-delay: .4s;
    transition-delay: .4s
}

.slick-animation .transitionDelay5 {
    -webkit-transition-delay: .5s;
    transition-delay: .5s
}

.slick-animation .transitionDelay6 {
    -webkit-transition-delay: .6s;
    transition-delay: .6s
}

.slick-animation .transitionDelay7 {
    -webkit-transition-delay: .7s;
    transition-delay: .7s
}

.slick-animation .transitionDelay8 {
    -webkit-transition-delay: .8s;
    transition-delay: .8s
}

.slick-animation .transitionDelay9 {
    -webkit-transition-delay: .9s;
    transition-delay: .9s
}

.slick-animation .transitionDelay10 {
    -webkit-transition-delay: 1s;
    transition-delay: 1s
}

.slick-animation .transitionDelay12 {
    -webkit-transition-delay: 1.2s;
    transition-delay: 1.2s
}

.slick-animation .transitionDelay14 {
    -webkit-transition-delay: 1.4s;
    transition-delay: 1.4s
}

.slick-animation .transitionDuration2 {
    -webkit-transition-duration: .2s;
    transition-duration: .2s
}

.slick-animation .transitionDuration4 {
    -webkit-transition-duration: .4s;
    transition-duration: .4s
}

.slick-animation .transitionDuration6 {
    -webkit-transition-duration: .6s;
    transition-duration: .6s
}

.slick-animation .transitionDuration8 {
    -webkit-transition-duration: .8s;
    transition-duration: .8s
}

.slick-animation .transitionDuration10 {
    -webkit-transition-duration: 1s;
    transition-duration: 1s
}

.slick-animation .transitionDuration12 {
    -webkit-transition-duration: 1.2s;
    transition-duration: 1.2s
}

.slick-animation .transitionDuration14 {
    -webkit-transition-duration: 1.4s;
    transition-duration: 1.4s
}

.slick-animation .transitionDuration16 {
    -webkit-transition-duration: 1.6s;
    transition-duration: 1.6s
}

.slick-animation .transitionDuration18 {
    -webkit-transition-duration: 1.8s;
    transition-duration: 1.8s
}

.slick-animation .transitionDuration20 {
    -webkit-transition-duration: 2s;
    transition-duration: 2s
}

.slick-animation .transitionDuration22 {
    -webkit-transition-duration: 2.2s;
    transition-duration: 2.2s
}

.slick-animation .transitionDuration24 {
    -webkit-transition-duration: 2.4s;
    transition-duration: 2.4s
}

.slick-animation .transitionDuration26 {
    -webkit-transition-duration: 2.6s;
    transition-duration: 2.6s
}

.slick-animation .fromTop {
    -webkit-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%)
}

.slick-animation .fromBottom {
    -webkit-transform: translateY(100%);
    -ms-transform: translateY(100%);
    -o-transform: translateY(100%);
    transform: translateY(100%)
}

.slick-animation .fromLeft {
    -webkit-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    -o-transform: translateX(-100%);
    transform: translateX(-100%)
}

.slick-animation .fromRight {
    -webkit-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%)
}

.slick-animation .slick-center .animation {
    opacity: 1
}

.slick-animation .slick-center .fromTop {
    -webkit-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%)
}

.slick-animation .slick-center .fromBottom {
    -webkit-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%)
}

.slick-animation .slick-center .fromLeft {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%)
}

.slick-animation .slick-center .fromRight {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%)
}


/* Slick Carousel Banner Slider */

.slick-banner-slider-wrapper {
    overflow: hidden
}

.slick-banner-slider-wrapper .section-title h2 {
    color: #FFF
}

.slick-banner-slider-wrapper .section-title p {
    color: rgba(255, 255, 255, 0.7)
}

.slick-banner-slider-wrapper .image-bg {
    width: 100%;
    background-position: left top;
    background-size: cover;
    position: relative
}

.slick-banner-slider-wrapper .image-bg:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.85)
}

.slick-banner-slider-wrapper.relative .absolute {
    color: rgba(255, 255, 255, 0.85);
    width: 100%;
    z-index: 99;
    top: 50px
}

.slick-banner-slider-wrapper.relative .absolute .section-title-2 h2 {
    color: #FFF
}


/* Customized */

.slick-inner-dot .slick-dots {
    bottom: 50px
}

.slick-inner-dot .slick-dots li button:before {
    background:url(../images/bn1.png) no-repeat 50%;
}

.slick-inner-dot .slick-dots li:hover button:before {
    background:url(../images/bn1.png) no-repeat 50%;
}

.slick-inner-dot .slick-dots li.slick-active button:before {
    background:url(../images/bn1.png) no-repeat 50%;
}

@media only screen and (max-width: 991px) {
    .slick-banner-slider-wrapper.relative .absolute {
        top: 70px
    }
}

@media (max-width: 767px) {
    .slick-banner-slider-wrapper.relative .absolute {
        top: 50px
    }

}

@media (max-width: 479px) {
    .slick-banner-slider-wrapper.relative .absolute {
        top: 40px
    }
}



@media (min-width: 768px) {}

@media only screen and (max-width: 1199px) {}

@media only screen and (max-width: 991px) {}

@media only screen and (max-width: 767px) {


}

@media (max-width: 479px) {

}
