---
description:
globs:
alwaysApply: false
---
# JAVA SPRING BOOT + VUE/REACT AGENTIC CODING GUIDE

**CRITICAL INSTRUCTION**: When implementing any software solution:

1. **PROJECT UNDERSTANDING FIRST** - Always analyze the existing project structure, technology stack, and patterns before suggesting any implementation
2. **ADAPT TO PROJECT CONTEXT** - Align your approach with the project's established conventions, frameworks, and best practices
3. **HUMAN DESIGN, AI IMPLEMENTATION** - Follow the collaborative model where humans provide design direction and AI handles implementation details

## Phase 1: Project Analysis & Understanding

### 1.1 Backend Project Context Discovery (Java Spring Boot)

**Technology Stack Analysis:**
- Java version (8, 11, 17, 21)
- Spring Boot version and starters in use
- Build tool (Maven vs Gradle)
- Database (MySQL, PostgreSQL, MongoDB, etc.)
- ORM/ODM (JPA/Hibernate, MyBatis, etc.)
- Security framework (Spring Security, JWT, OAuth2)
- Testing frameworks (JUnit, Mockito, TestContainers)
- Documentation tools (<PERSON>wagger/OpenAPI, Spring REST Docs)

**Architecture Patterns:**
- Layered architecture (Controller → Service → Repository)
- Domain-driven design patterns
- Microservices vs Monolithic
- API design patterns (REST, GraphQL)
- Event-driven architecture (Spring Events, Message Queues)
- Caching strategies (Redis, Caffeine, Spring Cache)

**Code Organization:**
```
src/main/java/com/company/project/
├── config/           # Configuration classes
├── controller/       # REST controllers
├── service/          # Business logic
├── repository/       # Data access layer
├── entity/           # JPA entities
├── dto/              # Data transfer objects
├── exception/        # Custom exceptions
├── security/         # Security configurations
└── util/             # Utility classes
```

### 1.2 Frontend Project Context Discovery (Vue/React)

**Vue.js Projects:**
- Vue version (2.x vs 3.x)
- State management (Vuex, Pinia)
- Router (Vue Router)
- UI frameworks (Element UI, Vuetify, Ant Design Vue)
- Build tools (Vite, Webpack, Vue CLI)
- Testing (Vue Test Utils, Jest, Cypress)
- TypeScript integration

**React Projects:**
- React version and features used (Hooks, Context API)
- State management (Redux, Zustand, React Query)
- Router (React Router)
- UI frameworks (Material-UI, Ant Design, Chakra UI)
- Build tools (Create React App, Vite, Next.js)
- Testing (React Testing Library, Jest, Cypress)
- TypeScript integration

**Frontend Architecture:**
```
src/
├── components/       # Reusable components
├── views/pages/      # Page components
├── store/           # State management
├── services/        # API services
├── utils/           # Utility functions
├── assets/          # Static assets
├── styles/          # CSS/SCSS files
└── types/           # TypeScript definitions
```

### 1.3 Project Assessment Questions

**Backend Assessment:**
1. What's the current Spring Boot version and configuration style?
2. How is database access implemented? (JPA repositories, custom queries)
3. What's the authentication/authorization strategy?
4. How are exceptions handled globally?
5. What's the API documentation approach?
6. How is testing structured? (unit, integration, e2e)

**Frontend Assessment:**
1. What's the component architecture pattern?
2. How is state managed across the application?
3. What's the API communication strategy?
4. How are routes and navigation handled?
5. What's the styling approach? (CSS modules, styled-components, etc.)
6. How is error handling implemented?

## Phase 2: Technology-Specific Best Practices

### 2.1 Java Spring Boot Best Practices

**Project Structure:**
```java
// Configuration
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    // Security configuration
}

// Controller Layer
@RestController
@RequestMapping("/api/v1/users")
@Validated
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/{id}")
    public ResponseEntity<UserDTO> getUser(@PathVariable Long id) {
        return ResponseEntity.ok(userService.findById(id));
    }
}

// Service Layer
@Service
@Transactional
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    public UserDTO findById(Long id) {
        return userRepository.findById(id)
            .map(this::convertToDTO)
            .orElseThrow(() -> new UserNotFoundException(id));
    }
}

// Repository Layer
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);
}
```

**Key Patterns:**
- Use `@RestController` for REST APIs
- Implement proper exception handling with `@ControllerAdvice`
- Use DTOs for API responses to avoid exposing entities
- Implement validation with `@Valid` and custom validators
- Use `@Transactional` for service methods
- Implement proper logging with SLF4J
- Use Spring Profiles for environment-specific configuration

### 2.2 Vue.js Best Practices

**Component Structure:**
```vue
<template>
  <div class="user-profile">
    <el-card v-loading="loading">
      <template #header>
        <span>{{ $t('user.profile.title') }}</span>
      </template>
      <user-form 
        :user="user" 
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import UserForm from '@/components/UserForm.vue'

const userStore = useUserStore()
const loading = ref(false)
const user = ref<User | null>(null)

onMounted(async () => {
  await loadUser()
})

const loadUser = async () => {
  loading.value = true
  try {
    user.value = await userStore.fetchCurrentUser()
  } catch (error) {
    console.error('Failed to load user:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

**Key Patterns:**
- Use Composition API with `<script setup>`
- Implement proper state management with Pinia
- Use TypeScript for type safety
- Implement proper error handling and loading states
- Use internationalization (i18n) for text content
- Follow Vue 3 best practices and reactivity patterns

### 2.3 React Best Practices

**Component Structure:**
```tsx
import React, { useState, useEffect } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Card, Button, Form, message } from 'antd'
import { userService } from '@/services/userService'
import { User } from '@/types/user'

interface UserProfileProps {
  userId: string
}

const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const [form] = Form.useForm()
  
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => userService.getUser(userId)
  })
  
  const updateMutation = useMutation({
    mutationFn: userService.updateUser,
    onSuccess: () => {
      message.success('User updated successfully')
    },
    onError: (error) => {
      message.error(`Failed to update user: ${error.message}`)
    }
  })
  
  const handleSubmit = (values: Partial<User>) => {
    updateMutation.mutate({ id: userId, ...values })
  }
  
  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>
  
  return (
    <Card title="User Profile">
      <Form
        form={form}
        initialValues={user}
        onFinish={handleSubmit}
        layout="vertical"
      >
        {/* Form fields */}
      </Form>
    </Card>
  )
}

export default UserProfile
```

**Key Patterns:**
- Use functional components with hooks
- Implement proper data fetching with React Query
- Use TypeScript for props and state typing
- Implement proper error boundaries
- Use proper state management (Context API, Zustand, Redux)
- Follow React best practices for performance optimization

## Phase 3: Human-AI Collaborative Development

### 3.1 Collaboration Model

| Phase | Human Role | AI Role | Backend Focus | Frontend Focus |
|-------|------------|---------|---------------|----------------|
| **API Design** | ★★★ Lead | ★☆☆ Support | Define REST endpoints, DTOs | Define API client interfaces |
| **Database Design** | ★★★ Lead | ★☆☆ Support | Entity relationships, constraints | N/A |
| **Business Logic** | ★★★ Lead | ★★☆ Support | Service layer design | State management design |
| **Implementation** | ★☆☆ Review | ★★★ Execute | Controllers, Services, Repos | Components, Services, Stores |
| **Testing** | ★★☆ Strategy | ★★★ Execute | Unit, Integration tests | Component, E2E tests |
| **UI/UX** | ★★★ Lead | ★☆☆ Support | N/A | Component design, user flows |

### 3.2 Backend Development Workflow

**Human Design Responsibilities:**
- Define API endpoints and request/response structures
- Design database schema and entity relationships
- Specify business rules and validation logic
- Define security requirements and access controls
- Establish error handling strategies

**AI Implementation Responsibilities:**
- Generate Spring Boot controllers following REST conventions
- Implement service layer with proper transaction management
- Create JPA entities and repository interfaces
- Generate comprehensive unit and integration tests
- Implement proper exception handling and logging

### 3.3 Frontend Development Workflow

**Human Design Responsibilities:**
- Define user interface mockups and user flows
- Specify component hierarchy and data flow
- Define state management structure
- Establish routing and navigation patterns
- Define user interaction patterns

**AI Implementation Responsibilities:**
- Generate Vue/React components following project patterns
- Implement state management (Pinia/Vuex or Redux/Context)
- Create API service layers with proper error handling
- Generate comprehensive component tests
- Implement responsive design and accessibility features

## Phase 4: Full-Stack Integration Patterns

### 4.1 API Integration

**Backend API Design:**
```java
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "http://localhost:3000")
public class UserController {
    
    @GetMapping
    public ResponseEntity<PageResponse<UserDTO>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        PageRequest pageRequest = PageRequest.of(page, size);
        Page<UserDTO> users = userService.findUsers(search, pageRequest);
        
        return ResponseEntity.ok(new PageResponse<>(users));
    }
    
    @PostMapping
    public ResponseEntity<UserDTO> createUser(@Valid @RequestBody CreateUserRequest request) {
        UserDTO user = userService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(user);
    }
}
```

**Frontend API Service:**
```typescript
// Vue/React API Service
import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:8080/api/v1'

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

export const userService = {
  async getUsers(params: { page: number; size: number; search?: string }) {
    const response = await apiClient.get('/users', { params })
    return response.data
  },
  
  async createUser(userData: CreateUserRequest) {
    const response = await apiClient.post('/users', userData)
    return response.data
  }
}
```

### 4.2 Error Handling Integration

**Backend Global Exception Handler:**
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidation(ValidationException ex) {
        ErrorResponse error = new ErrorResponse(
            "VALIDATION_ERROR",
            ex.getMessage(),
            System.currentTimeMillis()
        );
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFound(ResourceNotFoundException ex) {
        ErrorResponse error = new ErrorResponse(
            "RESOURCE_NOT_FOUND",
            ex.getMessage(),
            System.currentTimeMillis()
        );
        return ResponseEntity.notFound().build();
    }
}
```

**Frontend Error Handling:**
```typescript
// Vue Error Handler
export const handleApiError = (error: any) => {
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        ElMessage.error(data.message || 'Validation error')
        break
      case 401:
        ElMessage.error('Unauthorized access')
        router.push('/login')
        break
      case 404:
        ElMessage.error('Resource not found')
        break
      default:
        ElMessage.error('An unexpected error occurred')
    }
  } else {
    ElMessage.error('Network error')
  }
}
```

## Phase 5: Quality Assurance Framework

### 5.1 Backend Testing Strategy

**Unit Testing:**
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void shouldFindUserById() {
        // Given
        User user = new User("John", "<EMAIL>");
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        
        // When
        UserDTO result = userService.findById(1L);
        
        // Then
        assertThat(result.getName()).isEqualTo("John");
        assertThat(result.getEmail()).isEqualTo("<EMAIL>");
    }
}
```

**Integration Testing:**
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
class UserControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldCreateUser() {
        CreateUserRequest request = new CreateUserRequest("John", "<EMAIL>");
        
        ResponseEntity<UserDTO> response = restTemplate.postForEntity(
            "/api/v1/users", request, UserDTO.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getName()).isEqualTo("John");
    }
}
```

### 5.2 Frontend Testing Strategy

**Vue Component Testing:**
```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('should render user information', async () => {
    const mockUser = { id: 1, name: 'John', email: '<EMAIL>' }
    
    const wrapper = mount(UserProfile, {
      props: { user: mockUser }
    })
    
    expect(wrapper.text()).toContain('John')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('should emit update event on form submit', async () => {
    const wrapper = mount(UserProfile)
    
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.emitted('update')).toBeTruthy()
  })
})
```

**React Component Testing:**
```tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import UserProfile from '@/components/UserProfile'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('UserProfile', () => {
  it('should render user information', async () => {
    render(<UserProfile userId="1" />, { wrapper: createWrapper() })
    
    await waitFor(() => {
      expect(screen.getByText('John')).toBeInTheDocument()
    })
  })
})
```

## Implementation Guidelines

### Backend Development:
1. Follow Spring Boot conventions and best practices
2. Use proper layered architecture (Controller → Service → Repository)
3. Implement comprehensive validation and error handling
4. Use Spring Security for authentication and authorization
5. Write both unit and integration tests
6. Document APIs with OpenAPI/Swagger

### Frontend Development:
1. Follow Vue 3 Composition API or React Hooks patterns
2. Use TypeScript for type safety
3. Implement proper state management
4. Create reusable and testable components
5. Handle loading states and errors gracefully
6. Write comprehensive component tests

### Full-Stack Integration:
1. Design consistent API contracts between backend and frontend
2. Implement proper error handling on both sides
3. Use environment-specific configurations
4. Implement proper authentication flow
5. Ensure responsive design and accessibility
6. Set up proper CI/CD pipelines

## Success Metrics

- **Code Quality:** Follows established patterns and conventions
- **Test Coverage:** Comprehensive testing on both backend and frontend
- **Performance:** Optimized API responses and frontend rendering
- **Security:** Proper authentication, authorization, and data validation
- **Maintainability:** Clean, readable, and well-documented code
- **User Experience:** Responsive, accessible, and intuitive interfaces

Remember: Always understand the existing project structure and patterns before implementing new features. Adapt your approach to match the project's established conventions while maintaining high code quality and following best practices.
