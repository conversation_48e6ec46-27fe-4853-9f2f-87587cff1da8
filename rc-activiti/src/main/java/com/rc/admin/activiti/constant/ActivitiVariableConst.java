package com.rc.admin.activiti.constant;

/**
 * 流程参数key
 *
 * <AUTHOR>
 * @date 2020/4/27
 */
public class ActivitiVariableConst {
    /**
     * 业务数据ID
     */
    public static final String BUSINESS_KEY = "businessKey";
    /**
     * 业务标题
     */
    public static final String BUSINESS_TITLE = "businessTitle";
    /**
     * 业务数据详情url
     */
    public static final String BUSINESS_DETAILS_URL = "businessDetailsPath";
    /**
     * 流程发起人id
     */
    public static final String APPLY_USER_ID = "applyUserId";
    /**
     * 流程发起人昵称
     */
    public static final String APPLY_USER_NICKNAME = "applyUserNickname";
    /**
     * 流程发起人所在部门ID
     */
    public static final String DEPT_ID = "deptId";
    /**
     * 流程发起人所在部门type code
     */
    public static final String DEPT_TYPE_CODE = "deptTypeCode";
    /**
     * 流程发起人所在部门type name
     */
    public static final String DEPT_TYPE_NAME = "deptTypeName";
    /**
     * 流程发起人所在部门名称
     */
    public static final String DEPT_NAME = "deptName";
    /**
     * 流程发起人所属角色 eg: role1,role2,role3
     */
    public static final String USER_ROLE_CODES = "userRoleCodes";
}
