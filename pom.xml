<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.rc.admin</groupId>
    <artifactId>rc-admin</artifactId>
    <packaging>pom</packaging>
    <version>0.1.0</version>
    <!-- <name>${project.artifactId}</name> -->
    <name>rc-api</name>


    <modules>
        <module>rc-activiti</module>
        <module>rc-api</module>
        <module>rc-common</module>
        <module>rc-file</module>
        <module>rc-generator</module>
        <module>rc-scheduler</module>
        <module>rc-sys</module>
        <module>rc-pst</module>
        <module>rc-sany</module>
        <module>rc-rule</module>
        <module>rc-rule-ors</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot.version>2.7.0</spring-boot.version>
        <hutool.version>5.8.3</hutool.version>
        <tiny-pin-yin.version>2.0.3.RELEASE</tiny-pin-yin.version>
        <javax.mail.version>1.4.7</javax.mail.version>
        <poi.version>5.2.2</poi.version>
        <easypoi.version>4.4.0</easypoi.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <druid.version>1.2.11</druid.version>
        <mysql-connector-java.version>8.0.29</mysql-connector-java.version>
        <dynamic-datasource.version>3.5.1</dynamic-datasource.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <jjwt.version>0.9.1</jjwt.version>
        <knife4j.version>4.3.0</knife4j.version>
        <shiro.version>1.11.0</shiro.version>
        <beetl.version>3.10.0.RELEASE</beetl.version>
        <jsoup.version>1.15.1</jsoup.version>
        <log4j2.version>2.17.1</log4j2.version>
        <captcha.version>1.3.0</captcha.version>
        <lombok.version>1.18.28</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-activiti</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-file</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.rc.admin</groupId>-->
<!--                <artifactId>rc-sample</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-scheduler</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-sys</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-pst</artifactId>
                <version>${project.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.rc.admin</groupId>-->
<!--                <artifactId>rc-elasticsearch</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-sany</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-rule</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rc.admin</groupId>
                <artifactId>rc-rule-ors</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
