<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.scheduler.dao.SchedulerJobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.scheduler.model.SchedulerJob">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="cron" property="cron" />
        <result column="bean" property="bean" />
        <result column="method" property="method" />
        <result column="status" property="status" />
        <result column="remarks" property="remarks" />
        <result column="sys" property="sys" />
        <result column="last_run_date" property="lastRunDate" />
        <result column="version" property="version" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="edit_user" property="editUser" />
        <result column="edit_date" property="editDate" />
    </resultMap>

    <select id="getJobCodeById" resultType="java.lang.String">
        select code from scheduler_job where id = #{id}
    </select>
    <select id="selectSchedulerJobCodes" resultType="com.rc.admin.scheduler.model.SchedulerJob">
        select code from scheduler_job
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getById" resultType="com.rc.admin.scheduler.model.SchedulerJob">
        select id, name, code, cron, bean, method, status, sys, remarks, version from scheduler_job where id = #{id}
    </select>
    <select id="select" resultType="com.rc.admin.scheduler.model.SchedulerJob">
        select id, name, code, cron, bean, method, status, sys, last_run_date
        from scheduler_job
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
