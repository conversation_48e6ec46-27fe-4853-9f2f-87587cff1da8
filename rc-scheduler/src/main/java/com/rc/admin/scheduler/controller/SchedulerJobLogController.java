package com.rc.admin.scheduler.controller;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.scheduler.model.SchedulerJobLog;
import com.rc.admin.scheduler.service.SchedulerJobLogService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定时任务执行日志
 *
 * <AUTHOR>
 * @date 2019-05-11
 */
@RestController
@ResponseResult
@RequestMapping("/api/auth/scheduler/job/log")
public class SchedulerJobLogController  {

    /**
     * 定时任务执行日志 service
     */
    @Autowired
    private SchedulerJobLogService service;


    /**
     * 列表
     *
     * @param schedulerJobLog 查询条件
     * @return Page<SchedulerJobLog>
     */
    @GetMapping()
    @RequiresPermissions("scheduler:job:select")
    public Page<SchedulerJobLog> select(SchedulerJobLog schedulerJobLog, Page<SchedulerJobLog> page) {
        return service.select(schedulerJobLog, page);
    }
}
