package com.rc.admin.scheduler.quartz;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.core.util.SpringContextHolder;
import com.rc.admin.scheduler.common.constant.SchedulerConst;
import com.rc.admin.scheduler.model.SchedulerJob;
import com.rc.admin.scheduler.model.SchedulerJobLog;
import com.rc.admin.scheduler.service.SchedulerJobLogService;
import com.rc.admin.scheduler.service.SchedulerJobService;
import com.rc.admin.sys.service.SysExceptionService;
import com.rc.admin.util.ToolUtil;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;

/**
 * 任务调度工厂
 *
 * <AUTHOR>
 * @date 2019-05-11
 */
@Component
public class QuartzFactory implements Job {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SysExceptionService sysExceptionService;

    @Override
    public void execute(JobExecutionContext context) {
        // 获取定时任务
        SchedulerJob schedulerJob = (SchedulerJob) context.getMergedJobDataMap().get(SchedulerConst.SCHEDULER_JOB_KEY);
        if (StrUtil.isNotBlank(schedulerJob.getBean())) {
            Object object = SpringContextHolder.getBean(schedulerJob.getBean());
            if (object != null) {
                try {
                    // 获取方法
                    Method method = object.getClass().getMethod(schedulerJob.getMethod());
                    Date startDate = new Date();
                    // 执行
                    method.invoke(object);
                    // 更新最后执行时间
                    updateLastRunDate(schedulerJob.getId());
                    // 保存执行日志
                    saveJobLog(schedulerJob, startDate);
                } catch (Exception e) {
                    EasyException easyException = new EasyException("调用定时任务[" + schedulerJob.getName() + "] method[" + schedulerJob.getMethod() + "]失败", ToolUtil.getExceptionMessage(e));
                    logger.error("调用定时任务[" + schedulerJob.getName() + "] method[" + schedulerJob.getMethod() + "]失败", ToolUtil.getExceptionMessage(e));
                    sysExceptionService.saveLog(500, schedulerJob.getName() + "/"+ schedulerJob.getMethod(), easyException);
                    sendWeiXinMsg(schedulerJob.getName(), schedulerJob.getMethod());
                }
            } else {
                logger.warn("定时任务[" + schedulerJob.getName() + "]bean不存在");
                EasyException easyException = new EasyException("定时任务[" + schedulerJob.getName() + "]bean不存在");
                sysExceptionService.saveLog(500, schedulerJob.getName() + "bean不存在", easyException);
                sendWeiXinMsg(schedulerJob.getName(),"bean不存在");
            }
        } else {
            logger.warn("定时任务[" + schedulerJob.getName() + "]缺少bean");
            EasyException easyException = new EasyException("定时任务[" + schedulerJob.getName() + "]缺少bean");
            sysExceptionService.saveLog(500, schedulerJob.getName() + "缺少bean", easyException);
            sendWeiXinMsg(schedulerJob.getName(),"缺少bean");
        }
    }
    private static final String cpwechat_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b0358386-a64c-44bb-9d61-df5c6e20399b";

    public static void sendWeiXinMsg(String name,String method)
    {
        JSONObject text = new JSONObject();
        String msg = "";
        JSONObject json = new JSONObject();
        json.put("msgtype", "markdown");
        msg = "<font color=\"red\">【警告】</font>\n"+name + "/"+ method+" 定时任务执行异常。";
        text.put("mentioned_mobile_list", Collections.singletonList("@all"));
        text.put("content", msg);
        json.put("markdown", text);
        HttpUtil.post(cpwechat_url, json.toJSONString());
    }

    /**
     * 更新最后执行时间
     *
     * @param id 任务id
     */
    private void updateLastRunDate(String id) {
        SchedulerJobService schedulerJobService = SpringContextHolder.getBean("schedulerJobServiceImpl");
        schedulerJobService.updateLastRunDate(id);
    }

    /**
     * 保存执行日志
     *
     * @param schedulerJob 任务详情
     * @param startDate    任务开始执行时间
     */
    private void saveJobLog(SchedulerJob schedulerJob, Date startDate) {
        SchedulerJobLogService schedulerJobLogService = SpringContextHolder.getBean("schedulerJobLogServiceImpl");
        schedulerJobLogService.saveData(new SchedulerJobLog(schedulerJob.getId(),
                startDate, System.currentTimeMillis() - startDate.getTime()));
    }
}
