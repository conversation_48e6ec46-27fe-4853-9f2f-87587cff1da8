#============================================================================
# \u914D\u7F6E\u4E3B\u5B9A\u65F6\u4EFB\u52A1\u5C5E\u6027
#============================================================================
# \u8C03\u5EA6\u6807\u8BC6\u540D \u96C6\u7FA4\u4E2D\u6BCF\u4E00\u4E2A\u5B9E\u4F8B\u90FD\u5FC5\u987B\u4F7F\u7528\u76F8\u540C\u7684\u540D\u79F0
org.quartz.scheduler.instanceName=easy-admin-quartz
# ID\u8BBE\u7F6E\u4E3A\u81EA\u52A8\u83B7\u53D6 \u6BCF\u4E00\u4E2A\u5FC5\u987B\u4E0D\u540C
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.skipUpdateCheck=true

#============================================================================
# \u914D\u7F6E\u7EBF\u7A0B\u6C60
#============================================================================
# ThreadPool \u5B9E\u73B0\u7684\u7C7B\u540D
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
# \u7EBF\u7A0B\u6570\u91CF
org.quartz.threadPool.threadCount=5
org.quartz.threadPool.threadPriority=5

#============================================================================
# \u914D\u7F6E\u4F5C\u4E1A\u5B58\u50A8
#============================================================================
# \u5BB9\u8BB8\u7684\u6700\u5927\u4F5C\u4E1A\u5EF6\u957F\u65F6\u95F4
org.quartz.jobStore.misfireThreshold=60000
#  \u6570\u636E\u4FDD\u5B58\u65B9\u5F0F\u4E3A\u6301\u4E45\u5316
org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
# \u8BBE\u7F6E\u4E3ATRUE\u4E0D\u4F1A\u51FA\u73B0\u5E8F\u5217\u5316\u975E\u5B57\u7B26\u4E32\u7C7B\u5230 BLOB \u65F6\u4EA7\u751F\u7684\u7C7B\u7248\u672C\u95EE\u9898
org.quartz.jobStore.useProperties=false
# \u8868\u524D\u7F00
org.quartz.jobStore.tablePrefix=qrtz_
# \u52A0\u5165\u96C6\u7FA4 true \u4E3A\u96C6\u7FA4 false\u4E0D\u662F\u96C6\u7FA4
org.quartz.jobStore.isClustered=true