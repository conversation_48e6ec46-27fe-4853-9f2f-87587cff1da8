// BUFLT_5006_Model_10342 (拖泵4.0模型) 的数据准确性检查代码

def this_pt = $timestamp()
def last_pt = $lastStamp()
// 相邻工况时间差
def pt_inc = (this_pt - last_pt) / 1000
// 顺序是否正常，工况顺序逆序时不做经纬度漂移和逆增长异常判断
def order_flag = true
if (pt_inc < 0) {
    order_flag = false
}
// 上次异常标签值，默认正常0，对应位置非正常时不做经纬度漂移和逆增长异常判断
// 根据物模型bit后最大数字(2)+1=3
def lastAbnormalFlag = $lastState("abnormal_flag") == null ? "000".toCharArray() : $lastState("abnormal_flag").toCharArray()

// 累计值属性的增量上限，超过这个值增量异常
def st_diff_val = 2
// 累计值属性的逆增长下限，低于这个值逆增长异常
def decreasing_cfg_val = -2
// 增量异常的相邻时间差上限，相邻时间在这个时间以内才算增量异常，单位为ms
def st_diff_time = 10 * 1000

//剔除标签,注意默认位数
// 根据物模型bit后最大数字(2)+1=3
def removeFlag = $recent("remove_flag") == null ? "111".toCharArray() : $recent("remove_flag").toCharArray()
// 定义状态常量 0=正常 1=属性值异常 2=逆增长 3= 位置漂移 9=不支持
def NORMAL = 0
def ABNORMAL = 1
def DECREASING = 2
def POSITION_DRIFT = 3
def UNSUPPORTED = 9
//静默标签
def checkBIT0 = silence_flag != null ? silence_flag : 1

// 初始化检查位
def checkBIT1 = removeFlag[1] == '0' ? NORMAL : (removeFlag[1] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT2 = removeFlag[2] == '0' ? NORMAL : (removeFlag[2] == '9' ? UNSUPPORTED : ABNORMAL)

//checkBIT1 检查BIT1 - 设备位置
if (checkBIT1 == 1) {
    def this_longitude = GPS_Longitude;  // 当前经度
    def this_latitude = GPS_Latitude;   // 当前纬度
    def last_longitude = $lastState("GPS_Longitude");  // 上个经度
    def last_latitude = $lastState("GPS_Latitude");   // 上个纬度
    checkBIT1 = NORMAL

    if (this_longitude == 0 || this_latitude == 0) {
        // 如果当前经纬为0，需要判断为0异常
        checkBIT1 = ABNORMAL
    } else if ((this_longitude == null && this_latitude == null)) {
        // 都为null
        checkBIT1 = NORMAL
    } else if (this_longitude > 180 || (this_longitude != null &&  this_longitude < -180)) {
        // 如果当前经度＞180或者＜-180
        checkBIT1 = ABNORMAL
    } else if (this_latitude > 90 || (this_latitude != null &&  this_latitude < -90)) {
        // 如果当前纬度＞90或者＜-90
        checkBIT1 = ABNORMAL
    } else if (last_longitude != null && last_latitude != null && last_longitude != 0 && last_latitude != 0 &&
            Math.abs(last_longitude) <= 180 && Math.abs(last_latitude) <= 90 && order_flag && lastAbnormalFlag[1] == '0') {
        // 如果当前经纬度没有值，取上次的值
        this_latitude = this_latitude == null ? last_latitude : this_latitude;
        this_longitude = this_longitude == null ? last_longitude : this_longitude;
        if (this_latitude == null || this_longitude == null) {
            checkBIT1 = NORMAL
        } else {
            def tmp1 = Math.sin(this_latitude / 57.29578);
            def tmp2 = Math.sin(last_latitude / 57.29578);
            def tmp3 = Math.cos(this_latitude / 57.29578);
            def tmp4 = Math.cos(last_latitude / 57.29578);
            def tmp5 = Math.cos((this_longitude - last_longitude) / 57.29578);
            def tmp6 = tmp1 * tmp2 + tmp3 * tmp4 * tmp5;
            // 计算相邻经纬度的地球距离，单位km
            def gps_distance = 6371.393 * Math.acos(tmp6);
            // 计算相邻经纬度的时间差，单位ms
            def gps_duration = device_location_context.this_pt - device_location_context.last_pt;
            // 若时间差大于1小时不再计算定位漂移，时间差小于10秒按10秒来计算
            gps_duration = gps_duration < 10 * 1000 ? 10 * 1000 : gps_duration
            if (gps_duration <= (60 * 60 * 1000)) {
                /* 计算相邻经纬度的速度，单位km/h */
                def gps_speed = (gps_duration == 0) ? 0 : (3600 * 1000 * gps_distance / gps_duration);

                /* 判断定位距离÷相邻采样时长速度是否≥150 km/h */
                if (!Double.isNaN(gps_distance) && gps_speed >= 150) {
                    checkBIT1 = POSITION_DRIFT; // 漂移异常
                }
            }
        }
    }
}

//checkBIT2 检查BIT2 - 设备状态
if (checkBIT2 == 1) {
    def this_pv = DeviceStatus;   // 当前值
    checkBIT2 = NORMAL
}

// 生成标签
removeFlag[0] = checkBIT0.toString();
removeFlag[1] = checkBIT1.toString();
removeFlag[2] = checkBIT2.toString();

// 将状态数组转换为二进制字符串
return removeFlag.join('')
