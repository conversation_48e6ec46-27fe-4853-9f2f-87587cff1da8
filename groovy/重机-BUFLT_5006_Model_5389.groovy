// BUFLT_5006_Model_5389 (华兴云大挖) 的数据准确性检查代码

def this_pt = $timestamp()
def last_pt = $lastStamp()
// 相邻工况时间差
def pt_inc = (this_pt - last_pt) / 1000
// 顺序是否正常，工况顺序逆序时不做经纬度漂移和逆增长异常判断
def order_flag = true
if (pt_inc < 0) {
    order_flag = false
}
// 上次异常标签值，默认正常0，对应位置非正常时不做经纬度漂移和逆增长异常判断
// 根据物模型bit后最大数字(25)+1=26
def lastAbnormalFlag = $lastState("abnormal_flag") == null ? "00000000000000000000000000".toCharArray() : $lastState("abnormal_flag").toCharArray()

// 累计值属性的增量上限，超过这个值增量异常
def st_diff_val = 2
// 累计值属性的逆增长下限，低于这个值逆增长异常
def decreasing_cfg_val = -2
// 增量异常的相邻时间差上限，相邻时间在这个时间以内才算增量异常，单位为ms
def st_diff_time = 10 * 1000

//剔除标签,注意默认位数
// 根据物模型bit后最大数字(25)+1=26
def removeFlag = $recent("remove_flag") == null ? "11111111111111111111111111".toCharArray() : $recent("remove_flag").toCharArray()
// 定义状态常量 0=正常 1=属性值异常 2=逆增长 3= 位置漂移 9=不支持
def NORMAL = 0
def ABNORMAL = 1
def DECREASING = 2
def POSITION_DRIFT = 3
def UNSUPPORTED = 9
//静默标签
def checkBIT0 = silence_flag != null ? silence_flag : 1

// 初始化检查位
def checkBIT1 = removeFlag[1] == '0' ? NORMAL : (removeFlag[1] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT2 = removeFlag[2] == '0' ? NORMAL : (removeFlag[2] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT4 = removeFlag[4] == '0' ? NORMAL : (removeFlag[4] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT5 = removeFlag[5] == '0' ? NORMAL : (removeFlag[5] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT6 = removeFlag[6] == '0' ? NORMAL : (removeFlag[6] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT7 = removeFlag[7] == '0' ? NORMAL : (removeFlag[7] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT8 = removeFlag[8] == '0' ? NORMAL : (removeFlag[8] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT9 = removeFlag[9] == '0' ? NORMAL : (removeFlag[9] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT10 = removeFlag[10] == '0' ? NORMAL : (removeFlag[10] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT11 = removeFlag[11] == '0' ? NORMAL : (removeFlag[11] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT12 = removeFlag[12] == '0' ? NORMAL : (removeFlag[12] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT13 = removeFlag[13] == '0' ? NORMAL : (removeFlag[13] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT14 = removeFlag[14] == '0' ? NORMAL : (removeFlag[14] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT15 = removeFlag[15] == '0' ? NORMAL : (removeFlag[15] == '9' ? UNSUPPORTED : ABNORMAL)

def checkBIT24 = removeFlag[24] == '0' ? NORMAL : (removeFlag[24] == '9' ? UNSUPPORTED : ABNORMAL)
def checkBIT25 = removeFlag[25] == '0' ? NORMAL : (removeFlag[25] == '9' ? UNSUPPORTED : ABNORMAL)

//checkBIT1 检查BIT1 - 设备位置
if (checkBIT1 == 1) {
    def this_longitude = longitude;  // 当前经度
    def this_latitude = latitude;   // 当前纬度
    def last_longitude = $lastState("longitude") != null ? $lastState("longitude") : $lastState("GPS_Longitude");  // 上个经度
    def last_latitude = $lastState("latitude") != null ? $lastState("latitude") : $lastState("GPS_Latitude");   // 上个纬度
    checkBIT1 = NORMAL

    if (this_longitude == 0 || this_latitude == 0) {
        // 如果当前经纬为0，需要判断为0异常
        checkBIT1 = ABNORMAL
    } else if ((this_longitude == null && this_latitude == null)) {
        // 都为null
        checkBIT1 = NORMAL
    } else if (this_longitude > 180 || this_longitude < -180) {
        // 如果当前经度＞180或者＜-180
        checkBIT1 = ABNORMAL
    } else if (this_latitude > 90 || this_latitude < -90) {
        // 如果当前纬度＞90或者＜-90
        checkBIT1 = ABNORMAL
    } else if (last_longitude != null && last_latitude != null && last_longitude != 0 && last_latitude != 0 &&
            Math.abs(last_longitude) <= 180 && Math.abs(last_latitude) <= 90 && order_flag && lastAbnormalFlag[1] == '0') {

        // 如果当前经纬度没有值，取上次的值
        this_latitude = this_latitude == null ? last_latitude : this_latitude;
        this_longitude = this_longitude == null ? last_longitude : this_longitude;
        if (this_latitude == null || this_longitude == null) {
            checkBIT1 = NORMAL
        } else {
            def tmp1 = Math.sin(this_latitude / 57.29578);
            def tmp2 = Math.sin(last_latitude / 57.29578);
            def tmp3 = Math.cos(this_latitude / 57.29578);
            def tmp4 = Math.cos(last_latitude / 57.29578);
            def tmp5 = Math.cos((this_longitude - last_longitude) / 57.29578);
            def tmp6 = tmp1 * tmp2 + tmp3 * tmp4 * tmp5;
            // 计算相邻经纬度的地球距离，单位km
            def gps_distance = 6371.393 * Math.acos(tmp6);
            // 计算相邻经纬度的时间差，单位ms
            def gps_duration = device_location_context.this_pt - device_location_context.last_pt;
            // 若时间差大于1小时不再计算定位漂移，时间差小于10秒按10秒来计算
            gps_duration = gps_duration < 10 * 1000 ? 10 * 1000 : gps_duration
            if (gps_duration <= (60 * 60 * 1000)) {
                /* 计算相邻经纬度的速度，单位km/h */
                def gps_speed = (gps_duration == 0) ? 0 : (3600 * 1000 * gps_distance / gps_duration);

                /* 判断定位距离÷相邻采样时长速度是否≥150 km/h */
                if (gps_speed >= 150) {
                    checkBIT1 = POSITION_DRIFT; // 漂移异常
                }
            }
        }
    }
}

//checkBIT2 检查BIT2 - 设备状态
if (checkBIT2 == 1) {
    def this_pv = DeviceStatus;   // 当前值
    checkBIT2 = NORMAL
}

//检查BIT4 - 工作时间
if (checkBIT4 == 1) {
    def this_pv = total_worktime;   // 当前值
    def last_pv = $lastState("total_worktime");  // 上个值
    checkBIT4 = NORMAL
    
    if (this_pv == 0) {
        //工作时间为0
        checkBIT4 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[4] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = working_time_context.this_pt - working_time_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT4 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT4 = DECREASING
        }
    }
}

//检查BIT5 - 总油耗
if (checkBIT5 == 1) {
    def this_pv = total_fuel_consumption;   // 当前值
    def last_pv = $lastState("total_fuel_consumption");  // 上个值
    checkBIT5 = NORMAL
    
    if (this_pv == 0) {
        //总油耗为0
        checkBIT5 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[5] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_fuel_consumption_context.this_pt - total_fuel_consumption_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT5 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT5 = DECREASING
        }
    }
}

//检查BIT6 - 油位
if (checkBIT6 == 1) {
    def this_pv = fuel_level;   // 当前值
    checkBIT6 = NORMAL
    
    if (this_pv == null) {
        checkBIT6 = NORMAL
    } else if (this_pv > 100 || this_pv < 0) {
        // 油位大于100%或者＜0%为异常
        checkBIT6 = ABNORMAL
    }
}

//检查BIT7 - 发动机转速
if (checkBIT7 == 1) {
    def this_pv = engine_speed;   // 当前值
    checkBIT7 = NORMAL
    
    if (this_pv == null) {
        checkBIT7 = NORMAL
    } else if (this_pv < 0 || this_pv > 10000) {
        // 发动机转速小于0或大于10000为异常
        checkBIT7 = ABNORMAL
    }
}

//检查BIT8 - 发动机水温
if (checkBIT8 == 1) {
    def this_pv = cooling_water_temperature;   // 当前值
    checkBIT8 = NORMAL
    
    if (this_pv == null) {
        checkBIT8 = NORMAL
    } else if (this_pv > 120 || this_pv < -10) {
        // 发动机水温小于-10或大于120为异常
        checkBIT8 = ABNORMAL
    }
}

//检查BIT9 - 机油压力
if (checkBIT9 == 1) {
    def this_pv = oil_pressure;   // 当前值
    checkBIT9 = NORMAL
    
    if (this_pv == null) {
        checkBIT9 = NORMAL
    } else if (this_pv < 0) {
        // 机油压力小于0为异常
        checkBIT9 = ABNORMAL
    }
}

//检查BIT10 - 左行走工时
if (checkBIT10 == 1) {
    def this_pv = total_time_left_moving;   // 当前值
    def last_pv = $lastState("total_time_left_moving");  // 上个值
    checkBIT10 = NORMAL
    
    if (this_pv == 0) {
        //左行走工时为0
        checkBIT10 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[10] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_time_left_moving_context.this_pt - total_time_left_moving_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT10 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT10 = DECREASING
        }
    }
}

//检查BIT11 - 右行走工时
if (checkBIT11 == 1) {
    def this_pv = total_time_right_moving;   // 当前值
    def last_pv = $lastState("total_time_right_moving");  // 上个值
    checkBIT11 = NORMAL
    
    if (this_pv == 0) {
        //右行走工时为0
        checkBIT11 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[11] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_time_right_moving_context.this_pt - total_time_right_moving_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT11 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT11 = DECREASING
        }
    }
}

//检查BIT12 - 泵吸收功率
if (checkBIT12 == 1) {
    def this_pv = pump_total_absorbed_power;   // 当前值
    checkBIT12 = NORMAL
    
    if (this_pv == null) {
        checkBIT12 = NORMAL
    } else if (this_pv < 0 || this_pv > 1620) {
        // 泵吸收功率小于0或大于1620为异常
        checkBIT12 = ABNORMAL
    }
}

//检查BIT13 - 怠速时长
if (checkBIT13 == 1) {
    def this_pv = total_idle_time;   // 当前值
    def last_pv = $lastState("total_idle_time");  // 上个值
    checkBIT13 = NORMAL
    
    if (this_pv == 0) {
        //怠速时长为0
        checkBIT13 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[13] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_idle_time_context.this_pt - total_idle_time_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT13 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT13 = DECREASING
        }
    }
}

//检查BIT14 - 怠速油耗
if (checkBIT14 == 1) {
    def this_pv = total_idle_fuel_consumption;   // 当前值
    def last_pv = $lastState("total_idle_fuel_consumption");  // 上个值
    checkBIT14 = NORMAL
    
    if (this_pv == 0) {
        //怠速油耗为0
        checkBIT14 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[14] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_idle_fuel_consumption_context.this_pt - total_idle_fuel_consumption_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT14 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT14 = DECREASING
        }
    }
}

//检查BIT15 - 档位
if (checkBIT15 == 1) {
    def this_pv = gear;   // 当前值
    checkBIT15 = NORMAL
    
    if (this_pv == null) {
        checkBIT15 = NORMAL
    } else if (this_pv < 0 || this_pv > 11) {
        // 档位小于0或大于11为异常
        checkBIT15 = ABNORMAL
    }
}


//检查BIT24 - 动作编码
if (checkBIT24 == 1) {
    def this_pv = action_code;   // 当前值
    checkBIT24 = NORMAL
    
    if (this_pv == null) {
        checkBIT24 = NORMAL
    } else if (this_pv < 0 || this_pv > 511) {
        // 动作编码小于0或大于511为异常
        checkBIT24 = ABNORMAL
    }
}

//检查BIT25 - 回转时间
if (checkBIT25 == 1) {
    def this_pv = total_time_rotation;   // 当前值
    def last_pv = $lastState("total_time_rotation");  // 上个值
    checkBIT25 = NORMAL
    
    if (this_pv == 0) {
        //回转时间为0
        checkBIT25 = ABNORMAL
    } else if (last_pv != null && this_pv != null && order_flag && lastAbnormalFlag[25] == '0') {
        def diff_val = this_pv - last_pv;  // 差值
        def diff_time = total_time_rotation_context.this_pt - total_time_rotation_context.last_pt;  // 时间差
        
        if (diff_val > st_diff_val && diff_time <= st_diff_time) {
            //若相邻采样时间的属性值之差在±2之外 不含2，算属性值异常  并且在10S内
            checkBIT25 = ABNORMAL
        } else if (diff_val < decreasing_cfg_val) {
            // 值反向增长
            checkBIT25 = DECREASING
        }
    }
}

// 生成标签
removeFlag[0] = checkBIT0.toString();
removeFlag[1] = checkBIT1.toString();
removeFlag[2] = checkBIT2.toString();
removeFlag[3] = '9';
removeFlag[4] = checkBIT4.toString();
removeFlag[5] = checkBIT5.toString();
removeFlag[6] = checkBIT6.toString();
removeFlag[7] = checkBIT7.toString();
removeFlag[8] = checkBIT8.toString();
removeFlag[9] = checkBIT9.toString();
removeFlag[10] = checkBIT10.toString();
removeFlag[11] = checkBIT11.toString();
removeFlag[12] = checkBIT12.toString();
removeFlag[13] = checkBIT13.toString();
removeFlag[14] = checkBIT14.toString();
removeFlag[15] = checkBIT15.toString();
removeFlag[16] = '9';
removeFlag[17] = '9';
removeFlag[18] = '9';
removeFlag[19] = '9';
removeFlag[20] = '9';
removeFlag[21] = '9';
removeFlag[22] = '9';
removeFlag[23] = '9';
removeFlag[24] = checkBIT24.toString();
removeFlag[25] = checkBIT25.toString();

// 将状态数组转换为二进制字符串
return removeFlag.join('')
