---
description: RC Tool API (DQM) 项目开发指南
globs:
alwaysApply: true
---
# RC TOOL API (DQM) 数据质量管理系统开发指南

**关键指令**: 在实施任何软件解决方案时：

1. **项目理解优先** - 始终分析现有项目结构、技术栈和模式后再建议实现方案
2. **适配项目环境** - 与项目既定约定、框架和最佳实践保持一致
3. **人机协作模式** - 人类负责设计方向，AI负责实现细节

## Phase 1: RC Tool API 项目特定分析

### 1.1 项目技术栈配置

**核心技术组件:**
- **Java版本**: JDK 8
- **Spring Boot版本**: 2.7.3
- **构建工具**: Maven 多模块项目
- **数据库**: MySQL 8.0+ (主库) + 多数据源支持
- **连接池**: Druid 1.2.11 (带监控)
- **ORM框架**: MyBatis Plus 3.5.2 (非JPA/Hibernate)
- **安全框架**: Apache Shiro 1.11.0 (非Spring Security)
- **工作流引擎**: Activiti
- **任务调度**: Quartz (集群模式)
- **缓存**: Redis (Jedis连接池)
- **消息队列**: Apache Kafka 2.0.0
- **搜索引擎**: Elasticsearch
- **API文档**: Knife4j 4.3.0 + Swagger
- **模板引擎**: Beetl 3.10.0
- **工具库**: HuTool 5.8.3
- **高性能并发**: Disruptor (LMAX)
- **脚本引擎**: Groovy 3.0.12
- **本地缓存**: Caffeine
- **邮件服务**: JavaMail
- **文件处理**: Apache POI 5.2.2 + EasyPOI 4.4.0
- **验证码**: AJ-Captcha 1.3.0

**项目架构特点:**
```
rc-admin (父项目)
├── rc-api             # API入口模块 (Spring Boot启动类)
├── rc-common          # 公共模块
│   ├── rc-core        # 核心工具类
│   ├── rc-mybatis     # MyBatis配置
│   ├── rc-redis       # Redis配置
│   ├── rc-swagger     # API文档配置
│   └── rc-elasticsearch # ES配置
├── rc-sys             # 系统管理模块 (用户、权限、部门)
├── rc-activiti        # 工作流模块
├── rc-scheduler       # 定时任务模块
├── rc-file            # 文件管理模块
├── rc-generator       # 代码生成模块
├── rc-pst             # PST业务模块
├── rc-sany            # 三一集成模块
├── rc-rule            # 规则引擎模块
└── rc-rule-ors        # ORS数据质量管理模块 (核心业务)
```

### 1.2 数据库配置模式

**多数据源配置:**
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMulQueries=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullDatabaseMeansCurrent=true&useInformationSchema=true
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
      primary: 'master'
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 100
        max-wait: 60000
```

**MyBatis Plus配置:**
```yaml
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
```

### 1.3 安全框架配置 (Apache Shiro)

**Shiro配置特点:**
```java
@Bean
public SecurityManager securityManager() {
    DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
    // 使用Redis缓存
    securityManager.setCacheManager(redisCacheManager());
    // 使用Redis Session管理
    securityManager.setSessionManager(sessionManager());
    securityManager.setRealm(shiroRealm());
    return securityManager;
}
```

**注意事项:**
- 使用Shiro而非Spring Security
- Session存储在Redis中
- 支持集群部署
- 密码加密使用自定义盐值

### 1.4 高性能组件配置

**Disruptor配置:**
```java
@Bean(name = "workIotRingBuffer")
public RingBuffer<DeviceDataModel> workIotRingBuffer(){
    EventFactory<DeviceDataModel> factory = new WorkIotEventFactory();
    int ringBufferSize = 1024 * 1024;
    Disruptor<DeviceDataModel> disruptor = new Disruptor<>(factory, ringBufferSize, 
        threadFactory, ProducerType.SINGLE, new SleepingWaitStrategy());
    return disruptor.getRingBuffer();
}
```

**异步线程池配置:**
```yaml
async:
  executor:
    thread:
      core-pool-size: 5
      max-pool-size: 5
      queue-capacity: 999
      name-prefix: async-
      keep-alive-seconds: 30
```

## Phase 2: 项目特定最佳实践

### 2.1 后端开发模式

**Controller层模式:**
```java
@RestController
@RequestMapping("/api/v1/device")
@Api(value = "设备管理")
@ResponseResult  // 统一响应格式
public class DeviceController {
    
    @Resource  // 使用@Resource而非@Autowired
    private DeviceService deviceService;
    
    @ApiOperation(value = "设备列表查询")
    @PostMapping("/page")
    @SysLog(modular = "device", method = "设备列表查询")  // 系统日志
    public Page<DeviceResp> getDevicePage(@RequestBody DeviceReq req) {
        return deviceService.getDevicePage(req);
    }
}
```

**Service层模式:**
```java
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {
    
    @Resource
    private DeviceMapper deviceMapper;
    
    @Async("asyncExecutor")  // 异步处理
    @Transactional  // 事务管理
    public void processDeviceData(DeviceData data) {
        // 业务逻辑处理
    }
    
    // 使用CompletableFuture进行并发处理
    private CompletableFuture<Void> processAsync(String param) {
        return CompletableFuture.runAsync(() -> {
            // 异步处理逻辑
        }, asyncExecutor);
    }
}
```

**Mapper层模式:**
```java
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {
    // 复杂查询使用XML映射
    List<DeviceStatResp> getDeviceStatistics(@Param("query") DeviceStatQuery query);
    
    // 使用@DS注解切换数据源
    @DS("slave")
    List<Device> getDeviceFromSlave();
}
```

### 2.2 数据质量管理特定模式

**设备数据处理模式:**
```java
// 使用Disruptor处理高并发设备数据
@Component
public class DeviceDataProcessor {
    
    @Resource
    private RingBuffer<DeviceDataModel> workIotRingBuffer;
    
    public void processDeviceData(DeviceDataModel data) {
        long sequence = workIotRingBuffer.next();
        try {
            DeviceDataModel event = workIotRingBuffer.get(sequence);
            event.copyFrom(data);
        } finally {
            workIotRingBuffer.publish(sequence);
        }
    }
}
```

**多线程数据处理优化:**
```java
// 并行处理不同类型的数据
List<String> dataTypes = Arrays.asList("type1", "type2", "type3");
List<CompletableFuture<Void>> futures = dataTypes.stream()
    .map(type -> processDataAsync(type))
    .collect(Collectors.toList());

CompletableFuture<Void> allFutures = CompletableFuture.allOf(
    futures.toArray(new CompletableFuture[0])
);
allFutures.get(); // 等待所有任务完成
```

### 2.3 规则引擎模式

**Groovy脚本执行:**
```java
@Component
public class GroovyRuleEngine {
    
    public Object executeRule(String script, Map<String, Object> params) {
        GroovyShell shell = new GroovyShell();
        Script compiledScript = shell.parse(script);
        
        // 设置参数
        params.forEach(compiledScript::setProperty);
        
        return compiledScript.run();
    }
}
```

### 2.4 集成服务模式

**Kafka消息处理:**
```java
@KafkaListener(topics = "device-data", groupId = "rc-rule")
public void handleDeviceData(@Payload String message) {
    // 处理设备数据消息
}
```

**飞书集成:**
```java
@Component
public class FeishuService {
    
    @Value("${feishu.appId}")
    private String appId;
    
    @Value("${feishu.appSecret}")
    private String appSecret;
    
    public void sendMessage(String content, String receiveId) {
        // 发送飞书消息
    }
}
```

## Phase 3: 前后端交互模式

### 3.1 API设计规范

**统一响应格式:**
```java
@Data
public class Response<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> Response<T> success(T data) {
        return new Response<>(200, "success", data, System.currentTimeMillis());
    }
}
```

**分页查询模式:**
```java
// 后端
public Page<DeviceResp> getDevicePage(Page<Device> page, DeviceReq req) {
    QueryWrapper<Device> wrapper = new QueryWrapper<>();
    // 构建查询条件
    return this.page(page, wrapper).convert(this::convertToResp);
}

// 前端调用 (TypeScript)
interface DevicePageReq {
    current: number;
    size: number;
    deviceName?: string;
}

export function getDevicePage(params: DevicePageReq) {
    return defHttp.post<Page<DeviceResp>>({
        url: '/api/v1/device/page',
        data: params
    });
}
```

### 3.2 前端技术栈 (基于代码生成模板)

**前端架构 (Vue 3 + TypeScript):**
```typescript
// API服务层
import { defHttp } from '/@/utils/http/axios';

const BASE_URL = '/api/v1/device';

export function selectDevices(params: DeviceQuery) {
    return defHttp.get<Page<Device>>({
        url: BASE_URL,
        params: params,
    });
}
```

**前端特点:**
- 使用Vue 3 + TypeScript
- 基于Vite构建
- 支持代码生成 (Beetl模板)
- 统一的HTTP请求封装
- 响应式设计支持

## Phase 4: 开发工作流程

### 4.1 人机协作模式

| 阶段 | 人类职责 | AI职责 | 技术重点 |
|------|----------|--------|----------|
| **需求分析** | ★★★ 主导 | ★☆☆ 辅助 | 业务规则定义、数据质量标准 |
| **数据库设计** | ★★★ 主导 | ★☆☆ 辅助 | 多数据源设计、分表策略 |
| **API设计** | ★★☆ 主导 | ★★☆ 协作 | RESTful设计、响应格式统一 |
| **代码实现** | ★☆☆ 审查 | ★★★ 执行 | MyBatis Plus、Shiro、异步处理 |
| **性能优化** | ★★☆ 指导 | ★★★ 实现 | Disruptor、多线程、缓存策略 |
| **测试编写** | ★★☆ 策略 | ★★★ 执行 | 单元测试、集成测试 |

### 4.2 开发规范

**代码规范:**
1. 使用`@Resource`注解进行依赖注入
2. Controller使用`@ResponseResult`统一响应格式
3. Service层使用`@Transactional`管理事务
4. 异步方法使用`@Async("asyncExecutor")`
5. 多数据源使用`@DS("dataSourceName")`
6. 系统日志使用`@SysLog`注解

**性能优化规范:**
1. 大数据量处理优先使用Disruptor
2. 并发处理使用CompletableFuture
3. 缓存优先使用Redis，本地缓存使用Caffeine
4. 复杂查询优化使用MyBatis Plus的QueryWrapper

**安全规范:**
1. 使用Shiro进行权限控制
2. 敏感操作记录系统日志
3. 密码使用自定义盐值加密
4. Session存储在Redis中支持集群

## Phase 5: 测试策略

### 5.1 单元测试模式

```java
@ExtendWith(MockitoExtension.class)
class DeviceServiceTest {
    
    @Mock
    private DeviceMapper deviceMapper;
    
    @InjectMocks
    private DeviceServiceImpl deviceService;
    
    @Test
    void shouldProcessDeviceDataAsync() {
        // Given
        DeviceData data = new DeviceData();
        when(deviceMapper.insert(any())).thenReturn(1);
        
        // When
        CompletableFuture<Void> future = deviceService.processAsync(data);
        
        // Then
        assertDoesNotThrow(() -> future.get(5, TimeUnit.SECONDS));
        verify(deviceMapper).insert(any());
    }
}
```

### 5.2 集成测试模式

```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class DeviceControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldGetDevicePage() {
        DevicePageReq request = new DevicePageReq();
        request.setCurrent(1);
        request.setSize(10);
        
        ResponseEntity<Response<Page<DeviceResp>>> response = 
            restTemplate.postForEntity("/api/v1/device/page", request, 
                new ParameterizedTypeReference<Response<Page<DeviceResp>>>() {});
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);
    }
}
```

## 成功指标

- **代码质量**: 遵循项目既定的技术栈和架构模式
- **性能表现**: 合理使用Disruptor、多线程、缓存等高性能组件
- **安全合规**: 正确使用Shiro权限框架和安全配置
- **可维护性**: 清晰的模块划分和统一的编码规范
- **业务价值**: 满足数据质量管理的业务需求

记住：始终理解并适配项目现有的技术选型和架构模式，而不是强行套用标准的Spring Boot + JPA + Spring Security模式。

## Phase 6: 项目特定配置详解

### 6.1 环境配置管理

**多环境配置:**
```yaml
# application.yml (基础配置)
spring:
  profiles:
    active: dev  # 默认开发环境

# application-dev.yml (开发环境)
server:
  port: 80
  servlet:
    context-path: /dmq

# application-pre.yml (预发布环境)
server:
  port: 8085
  servlet:
    context-path: /dmq

# application-prod.yml (生产环境)
server:
  port: 8085
  servlet:
    context-path: /dmq
```

**关键配置项:**
- 服务端口: 开发环境80，其他环境8085
- 上下文路径: `/dmq`
- 前端访问地址: `http://localhost:3100`
- 文件上传路径: `/home/<USER>/dmq/dmq-api`

### 6.2 第三方集成配置

**Kafka配置:**
```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:*************:12002}
    listener:
      missing-topics-fatal: false
      type: batch
      concurrency: 4
    consumer:
      auto-commit-interval: 1000ms
      auto-offset-reset: latest
      max-poll-records: 300
      group-id: rc-rule
```

**Elasticsearch配置:**
```yaml
spring:
  elasticsearch:
    rest:
      connection-timeout: 1s
      read-timeout: 30s
      uris: ${ES_HOST:127.0.0.1}:${ES_PORT:9200}
```

**飞书集成配置:**
```yaml
feishu:
  appId: cli_a73d60549238d077
  appSecret: qUdE5o36UrmDFpDm7qDywhekVLIdLYiX
  httpToken: https://open.work.sany.com.cn/open-apis/auth/v3/tenant_access_token/internal
  httpMessage: https://open.work.sany.com.cn/open-apis/message/v4/batch_send/
```

**邮件服务配置:**
```yaml
mail:
  username: <EMAIL>
  password: 29neQnB#
  host: smtp.sany.com.cn
  port: 25
```

### 6.3 监控和运维配置

**Druid监控:**
```yaml
spring:
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        allow: ""
        login-username: admin
        login-password: 123
      filter:
        stat:
          merge-sql: false
```

**日志配置:**
```yaml
logging:
  file:
    path: logs
```

**Quartz集群配置:**
```properties
# quartz.properties
org.quartz.scheduler.instanceName=easy-admin-quartz
org.quartz.scheduler.instanceId=AUTO
org.quartz.threadPool.threadCount=5
org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
org.quartz.jobStore.isClustered=true
```

## Phase 7: 业务模块开发指南

### 7.1 数据质量管理模块 (rc-rule-ors)

**核心业务实体:**
```java
// 设备信息实体
@Data
@TableName("dqm.ors_base_device_info")
public class OrsBaseDeviceInfo implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("device_code")
    private String deviceCode;

    @TableField("device_name")
    private String deviceName;

    // 其他字段...
}

// 版本更新记录实体
@Data
@TableName("dqm.ors_version_update_log")
public class OrsVersionUpdateLog implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("version_name")
    private String versionName;

    @TableField("version_update_date")
    private Date versionUpdateDate;

    // 关联的图片和比率记录
    @TableField(exist = false)
    private List<OrsVersionUpdateLogImg> logImgs;

    @TableField(exist = false)
    private List<OrsVersionUpdateLogRate> logRate;
}
```

**业务服务模式:**
```java
@Service
public class OrsVersionUpdateService extends ServiceImpl<OrsVersionUpdateLogMapper, OrsVersionUpdateLog> {

    @Resource
    private OrsBaseDeviceInfoMapper orsBaseDeviceInfoMapper;

    @Resource
    private Executor asyncExecutor;

    // 异步处理设备比率数据
    @Async("asyncExecutor")
    public void handleDeviceRatio(Date versionUpdateDate, Integer versionUpdateId) {
        // 使用多线程并行处理
        List<String> doubleRateSigns = Arrays.asList("1", "2", "3");

        List<CompletableFuture<Void>> futures = doubleRateSigns.stream()
            .map(sign -> processDeviceRatioAsync(startDate, endDate, sign, versionUpdateId))
            .collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private CompletableFuture<Void> processDeviceRatioAsync(String startDate, String endDate,
                                                           String doubleRateSign, Integer versionUpdateId) {
        return CompletableFuture.runAsync(() -> {
            // 具体的业务处理逻辑
            DeviceQuelityCountQuery query = new DeviceQuelityCountQuery();
            query.setStartTime(startDate);
            query.setEndTime(endDate);
            query.setDoubleRateSign(doubleRateSign);

            DeviceRatioResp result = orsBaseDeviceInfoMapper.deviceRatioChangeVersion(query);
            // 保存结果...
        }, asyncExecutor);
    }
}
```

### 7.2 规则引擎模块 (rc-rule)

**Groovy规则执行:**
```java
@Component
public class GroovyRuleProcessor {

    private final Map<String, Script> scriptCache = new ConcurrentHashMap<>();

    public Object executeRule(String ruleCode, String scriptContent, Map<String, Object> context) {
        Script script = scriptCache.computeIfAbsent(ruleCode, k -> {
            GroovyShell shell = new GroovyShell();
            return shell.parse(scriptContent);
        });

        // 设置上下文变量
        context.forEach(script::setProperty);

        return script.run();
    }
}
```

**规则配置实体:**
```java
@Data
@TableName("device_data_rule")
public class DeviceDataRule implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("rule_name")
    private String ruleName;

    @TableField("rule_script")
    private String ruleScript;  // Groovy脚本内容

    @TableField("rule_type")
    private String ruleType;    // 规则类型

    @TableField("status")
    private Integer status;     // 规则状态
}
```

### 7.3 系统管理模块 (rc-sys)

**用户权限管理:**
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Resource
    private ShiroService shiroService;

    @PostMapping("/login")
    public Response<LoginResp> login(@RequestBody LoginReq req) {
        try {
            SysUser user = shiroService.validateUser(req.getUsername(), req.getPassword());
            String token = shiroService.generateToken(user);

            return Response.success(new LoginResp(token, user));
        } catch (Exception e) {
            return Response.fail("登录失败: " + e.getMessage());
        }
    }
}
```

**Shiro权限配置:**
```java
@Configuration
public class ShiroConfig {

    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);

        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
        filterChainDefinitionMap.put("/api/auth/login", "anon");
        filterChainDefinitionMap.put("/api/auth/logout", "anon");
        filterChainDefinitionMap.put("/doc.html", "anon");
        filterChainDefinitionMap.put("/swagger-resources/**", "anon");
        filterChainDefinitionMap.put("/api/**", "authc");

        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }
}
```

## Phase 8: 部署和运维指南

### 8.1 数据库初始化

**必需的SQL脚本:**
```sql
-- 基础数据
source db/rc-admin.sql;

-- 工作流数据
source db/activiti/;

-- 定时任务数据
source db/quartz.sql;
```

### 8.2 环境变量配置

**关键环境变量:**
```bash
# 数据库配置
DB_HOST=**********
DB_PORT=3306
DB_NAME=rc_iot_tool
DB_USERNAME=root
DB_PASSWORD=sanyECM_3188

# Redis配置
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PWD=ecm123456

# Kafka配置
KAFKA_BOOTSTRAP_SERVERS=*************:12002

# Elasticsearch配置
ES_HOST=127.0.0.1
ES_PORT=9200

# 服务端口
SERVER_PORT=8085
```

### 8.3 启动命令

**开发环境启动:**
```bash
mvn clean compile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

**生产环境部署:**
```bash
mvn clean package -Dmaven.test.skip=true
java -jar -Dspring.profiles.active=prod rc-api/target/rc-api-0.1.0.jar
```

### 8.4 监控检查

**健康检查端点:**
- Druid监控: `http://localhost:8085/dmq/druid/index.html`
- API文档: `http://localhost:8085/dmq/doc.html`
- 应用状态: `http://localhost:8085/dmq/actuator/health`

## 总结

本项目是一个企业级的数据质量管理系统，具有以下特点：

1. **技术栈成熟**: 基于Spring Boot 2.7.3 + MyBatis Plus + Shiro的稳定技术栈
2. **高性能设计**: 使用Disruptor、多线程、Redis缓存等高性能组件
3. **业务专业**: 专注于设备数据质量监控和管理
4. **集成丰富**: 支持Kafka、Elasticsearch、飞书等多种集成
5. **运维友好**: 支持多环境配置、集群部署、监控告警

开发时请严格遵循项目既定的技术选型和架构模式，确保代码质量和系统稳定性。
