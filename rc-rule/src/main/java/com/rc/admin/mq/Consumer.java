package com.rc.admin.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

@Slf4j
public class Consumer extends AbstractDisruptorConsumer<String> {

    private String name;

    public Consumer() {}

    public Consumer(String name) {
        this.name = name;
    }

    @Override
    public void consume(String data) {
        Assert.notNull(data, "数据不能为空");
//        log.info("消费者[{}]开始消费数据[{}]", this.name, data);
        System.out.println("消费者开始消费数据" + data);
        // TODO 匹配结果写入

    }

}
