package com.rc.admin.mq;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rc.admin.auth.model.entity.RealTimeDeviceWorkConditionResp;
import com.rc.admin.auth.model.entity.RealWorkConditionResp;
import com.rc.admin.mq.model.IotDataQualityInspectionHistory;
import com.rc.admin.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class RealtimeWorkDataDealResultConsumer extends AbstractDisruptorConsumer<JSONObject> {

    private RuleService ruleService;

    @Autowired
    public RealtimeWorkDataDealResultConsumer(RuleService ruleService) {
        this.ruleService=ruleService;
    }

    @Override
    public void consume(JSONObject data) {
        log.debug("RealtimeWorkDataDealResultConsumer 消费到工况规则检测结果：[{}]", JSONUtil.toJsonStr(data));
        Assert.notNull(data, "数据不能为空");
        List<IotDataQualityInspectionHistory> list = new ArrayList<>();
        setData(list, data);
        ruleService.iotDataQualityInspectionHistorySave(list);
    }

    private static void setData(List<IotDataQualityInspectionHistory> list, JSONObject j) {
        Object deviceCode = j.get("__assetId__");
        Object destAddressName = j.get("pointName");
        RealWorkConditionResp rawData = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(j.get("rawData")), RealWorkConditionResp.class);
        List<RealTimeDeviceWorkConditionResp> itemList = rawData.getItemList();
        Map<String, List<RealTimeDeviceWorkConditionResp>> collect = itemList.stream().collect(Collectors.groupingBy(RealTimeDeviceWorkConditionResp::getName));
        List<RealTimeDeviceWorkConditionResp> realTimeDeviceWorkConditionResps = collect.get((String) destAddressName);
        RealTimeDeviceWorkConditionResp resp = new RealTimeDeviceWorkConditionResp();
        if (realTimeDeviceWorkConditionResps != null) {
            resp = realTimeDeviceWorkConditionResps.get(0);
        }
        IotDataQualityInspectionHistory iotDataQualityInspectionHistory = new IotDataQualityInspectionHistory();
        iotDataQualityInspectionHistory.setDeviceCode((String)deviceCode);
        iotDataQualityInspectionHistory.setDestAddressName((String)destAddressName);
        iotDataQualityInspectionHistory.setDescription(Objects.isNull(j.get("pointDesc")) ? "" : (String) j.get("pointDesc"));
        iotDataQualityInspectionHistory.setGlValue(resp.getValue());
        iotDataQualityInspectionHistory.setGyValue(resp.getRootCloudValue());
        iotDataQualityInspectionHistory.setScanInterval(resp.getScanInterval());
        iotDataQualityInspectionHistory.setUpdateTime(resp.getUpdateTime());
        iotDataQualityInspectionHistory.setChangeTime(resp.getChangeTime());
        iotDataQualityInspectionHistory.setGyTimeCloud(resp.getRootCloudTimeCloud());
        iotDataQualityInspectionHistory.setGyTimeLocal(resp.getRootCloudTimeLocal());
        iotDataQualityInspectionHistory.setGyWriteTime(resp.getRootCloudWriteTime());
        iotDataQualityInspectionHistory.setRuleSubject(Objects.isNull(j.get("ruleSubject")) ? "" : (String) j.get("ruleSubject"));
        iotDataQualityInspectionHistory.setRuleScript(Objects.isNull(j.get("ruleScript")) ? "" : (String) j.get("ruleScript"));
        iotDataQualityInspectionHistory.setRuleRemarks(Objects.isNull(j.get("ruleRemarks")) ? "" : (String) j.get("ruleRemarks"));
        list.add(iotDataQualityInspectionHistory);
    }

}
