package com.rc.admin.mq;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rc.admin.service.RuleAdaptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 历史工况监听器
 */
@Slf4j
@Component
public class HistoryWorkDataListener {

    private static final String CONSUMER_ID = "rc-rule-01";
    private static final String GROUP_ID = "rc-rule";
    private static final String HISTORY_WORK_DATA_TOPIC = "share_history_work_data";

    private final RuleAdaptService ruleAdaptService;

    @Autowired
    public HistoryWorkDataListener(RuleAdaptService ruleAdaptService) {
        this.ruleAdaptService = ruleAdaptService;
    }

    @KafkaListener(id = CONSUMER_ID, groupId = GROUP_ID, topics = HISTORY_WORK_DATA_TOPIC)
    //@KafkaListener(id = CONSUMER_ID + "liumingzhe1", groupId = GROUP_ID + "liumingzhe", topics = HISTORY_WORK_DATA_TOPIC)
    public void listenWorkData1(List<ConsumerRecord<String, String>> datas) {

      datas.forEach(data -> {
//            log.info("HistoryWorkDataListener.listenWorkData 从Topic-[{}] Partition-[{}] Offset-[{}]监听到工况：[{}]",
//                    data.topic(), data.partition(), data.offset(), data.value());
            JSONObject workData = JSONUtil.parseObj(data.value());
            ruleAdaptService.historyDataRuleAdapt(workData);
        });
    }


}
