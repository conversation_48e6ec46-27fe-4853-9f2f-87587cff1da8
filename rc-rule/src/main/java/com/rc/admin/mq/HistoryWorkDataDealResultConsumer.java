package com.rc.admin.mq;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rc.admin.dao.DeviceInfoMapper;
import com.rc.admin.dao.RuleAdaptHistoryDataMapper;
import com.rc.admin.model.DeviceInfo;
import com.rc.admin.model.RuleAdaptHistoryData;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class HistoryWorkDataDealResultConsumer extends AbstractDisruptorConsumer<JSONObject> {

    private final RuleAdaptHistoryDataMapper ruleAdaptHistoryDataMapper;
    private final DeviceInfoMapper deviceInfoMapper;

    @Autowired
    public HistoryWorkDataDealResultConsumer(RuleAdaptHistoryDataMapper ruleAdaptHistoryDataMapper, DeviceInfoMapper deviceInfoMapper) {
        this.ruleAdaptHistoryDataMapper = ruleAdaptHistoryDataMapper;
        this.deviceInfoMapper = deviceInfoMapper;
    }

    @Override
    public void consume(JSONObject data) {
        log.debug("HistoryWorkDataDealResultConsumer 消费到工况规则检测结果：[{}]", JSONUtil.toJsonStr(data));
        String deviceCode = data.getStr("__assetId__");
        DeviceInfo deviceInfo = deviceInfoMapper.selectDeviceInfo(deviceCode);
        if (ObjectUtil.isAllNotEmpty(deviceInfo, data.getLong("ruleId"), data.getLong("startTimestamp"))) {
            RuleAdaptHistoryData ruleAdaptHistoryData = buildRuleAdaptHistoryData(data, deviceInfo);
            ruleAdaptHistoryDataMapper.upsertRuleAdaptHistoryData(ruleAdaptHistoryData);
        } else {
            log.error("规则检测结果有误，数据无法写入-[{}]", JSONUtil.toJsonStr(data));
        }
    }

    @NotNull
    private RuleAdaptHistoryData buildRuleAdaptHistoryData(JSONObject data, DeviceInfo deviceInfo) {
        RuleAdaptHistoryData ruleAdaptHistoryData = new RuleAdaptHistoryData();
        ruleAdaptHistoryData.setPlatform(deviceInfo.getPlatform());
        ruleAdaptHistoryData.setCompanyName(deviceInfo.getChildCompanyName());
        ruleAdaptHistoryData.setDeviceCode(deviceInfo.getDeviceCode());
        ruleAdaptHistoryData.setDeviceName(deviceInfo.getName());
        ruleAdaptHistoryData.setProtocols(deviceInfo.getProtocolType());
        ruleAdaptHistoryData.setFirstProcess(deviceInfo.getFirstProcess());
        ruleAdaptHistoryData.setFirstProcessName(deviceInfo.getFirstProcessName());
        ruleAdaptHistoryData.setSecondProcess(deviceInfo.getSecondProcess());
        ruleAdaptHistoryData.setSecondProcessName(deviceInfo.getSecondProcessName());
        ruleAdaptHistoryData.setRuleSubject(data.getStr("ruleSubject"));
        ruleAdaptHistoryData.setRuleScript(data.getStr("ruleScript"));
        ruleAdaptHistoryData.setRuleId(data.getLong("ruleId"));
        ruleAdaptHistoryData.setAdaptPoints(data.getStr("adaptPoints"));
        ruleAdaptHistoryData.setRemarks(data.getStr("ruleRemarks"));
        ruleAdaptHistoryData.setStartTime(LocalDateTimeUtil.of(data.getLong("startTimestamp")));
        if (data.getLong("endTimestamp") != null) {
            ruleAdaptHistoryData.setEndTime(LocalDateTimeUtil.of(data.getLong("endTimestamp")));
        }
        return ruleAdaptHistoryData;
    }
}
