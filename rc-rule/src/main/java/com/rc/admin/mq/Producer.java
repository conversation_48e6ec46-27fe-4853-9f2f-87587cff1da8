package com.rc.admin.mq;

import java.util.Calendar;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 测试生产者
 */
public class Producer implements Runnable {

    private String name;
    private DisruptorQueue disruptorQueue;
    private volatile boolean flag = true;
    private static AtomicInteger count = new AtomicInteger();

    public Producer(String name, DisruptorQueue disruptorQueue) {
        this.name = name;
        this.disruptorQueue = disruptorQueue;
    }

    @Override
    public void run() {
        try {
            System.out.println(now() + this.name + "：线程启动。");
            while (flag && count.get() < 100) {
                String data = count.incrementAndGet() + "";
                disruptorQueue.add(data);
                System.out.println(now() + this.name + "：存入" + data + "到队列中。");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println(now() + this.name + "：退出线程。");
        }
    }

    public void stopThread() {
        this.flag = false;
    }

    // 获取当前时间（分:秒）
    public String now() {
        Calendar now = Calendar.getInstance();
        return "[" + now.get(Calendar.MINUTE) + ":" + now.get(Calendar.SECOND) + "] ";
    }

    public static void main(String[] args) throws InterruptedException {

        Consumer consumer = new Consumer("测试消费者");
        DisruptorQueue disruptorQueue = DisruptorQueueFactory.getHandleEventsQueue(4,
                true, consumer);

        Producer producer = new Producer("测试生产者", disruptorQueue);
        for (int i = 0; i < 10; i++) {
            new Thread(producer).start();
        }
        Thread.sleep(30 * 1000);
        producer.stopThread();
    }
}
