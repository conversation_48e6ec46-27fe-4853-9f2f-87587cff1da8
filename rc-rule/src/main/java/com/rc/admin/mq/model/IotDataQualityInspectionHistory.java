package com.rc.admin.mq.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rc_iot_data_quality_inspection_history")
@ApiModel(value = "rc_iot_data_quality_inspection_history对象", description = "数据质量检查历史表")
public class IotDataQualityInspectionHistory {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(value = "device_code")
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;
    @TableField(value = "dest_address_name")
    @ApiModelProperty(value = "'点位名称'")
    private String destAddressName;
    @TableField(value = "description")
    @ApiModelProperty(value = "'描述'")
    private String description;
    @TableField(value = "gl_value")
    @ApiModelProperty(value = "'根连值'")
    private String glValue;
    @TableField(value = "gy_value")
    @ApiModelProperty(value = "'根云值'")
    private String gyValue;
    @TableField(value = "scan_interval")
    @ApiModelProperty(value = "'采集频率'")
    private String scanInterval;
    @TableField(value = "update_time")
    @ApiModelProperty(value = "'根连值更新时间'")
    private String updateTime;
    @TableField(value = "change_time")
    @ApiModelProperty(value = "'值改变时间'")
    private String changeTime;
    @TableField(value = "gy_time_cloud")
    @ApiModelProperty(value = "'根云入云时间'")
    private String gyTimeCloud;
    @TableField(value = "gy_time_local")
    @ApiModelProperty(value = "'根云本地时间'")
    private String gyTimeLocal;
    @TableField(value = "gy_write_time")
    @ApiModelProperty(value = "'根云写入时间'")
    private String gyWriteTime;
    @TableField(value = "rule_subject")
    @ApiModelProperty(value = "'检查规则主题'")
    private String ruleSubject;
    @TableField(value = "rule_script")
    @ApiModelProperty(value = "'检查规则表达式'")
    private String ruleScript;
    @TableField(value = "rule_remarks")
    @ApiModelProperty(value = "'检查规则公式说明'")
    private String ruleRemarks;
    @TableField(value = "create_time")
    @ApiModelProperty(value = "'创建时间'")
    private Date createTime;
}
