package com.rc.admin.mq;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Map;

@Slf4j
public class DataQualityInspectionHistoryConsumer extends AbstractDisruptorConsumer<JSONObject> {

    private String name;

    public DataQualityInspectionHistoryConsumer() {}

    public DataQualityInspectionHistoryConsumer(String name) {
        this.name = name;
    }


    @Override
    public void consume(JSONObject data) {
        Assert.notNull(data, "数据不能为空");
        Map dataQualityInspectionHistory = data.get("DataQualityInspectionHistory", Map.class);
        System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(dataQualityInspectionHistory));
       // todo 数据处理

    }
}
