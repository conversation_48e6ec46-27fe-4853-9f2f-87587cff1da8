package com.rc.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.model.DynamicColumn;
import com.rc.admin.model.RuleAdaptHistoryData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RuleAdaptHistoryDataMapper extends BaseMapper<RuleAdaptHistoryData> {
    void upsertRuleAdaptHistoryData(RuleAdaptHistoryData ruleAdaptHistoryData);

    List<DynamicColumn> selectPoints(String[] pointArr);

    DynamicColumn selectPoint(@Param("point") String point, @Param("deviceCode") String deviceCode);
}
