<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.dao.DeviceProfileMapper">
    <insert id="upsertDeviceProfiles" parameterType="java.util.List">
        insert into rc_iot_device_profile
        (device_code, device_name, org_code, org_name, company_code, company_name,
        first_process, first_process_name, second_process, second_process_name,
        location, ip_address)
        values
        <foreach collection="deviceProfiles" item="item" separator=",">
            (
            #{item.deviceCode},
            #{item.deviceName},
            #{item.orgCode},
            #{item.orgName},
            #{item.companyCode},
            #{item.companyName},
            #{item.firstProcess},
            #{item.firstProcessName},
            #{item.secondProcess},
            #{item.secondProcessName},
            #{item.location},
            #{item.ipAddress}
            )
        </foreach>
        on duplicate key update
        device_name = values(device_name),
        org_code = values(org_code),
        org_name = values(org_name),
        company_code = values(company_code),
        company_name = values(company_name),
        first_process = values(first_process),
        first_process_name = values(first_process_name),
        second_process = values(second_process),
        second_process_name = values(second_process_name),
        location = values(location),
        ip_address = values(ip_address),
        updated_time = current_timestamp()
    </insert>
</mapper>
