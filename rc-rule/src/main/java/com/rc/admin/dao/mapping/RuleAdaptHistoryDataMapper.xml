<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.dao.RuleAdaptHistoryDataMapper">
    <insert id="upsertRuleAdaptHistoryData" parameterType="com.rc.admin.model.RuleAdaptHistoryData">
        insert into rc_iot_rule_adapt_history_data
        (platform, device_code, device_name, company_name,
        first_process, first_process_name, second_process, second_process_name, protocols,
        rule_id, rule_subject, rule_script, remarks, adapt_points,
        start_time, end_time)
        values
            (
            #{platform},
            #{deviceCode},
            #{deviceName},
            #{companyName},
            #{firstProcess},
            #{firstProcessName},
            #{secondProcess},
            #{secondProcessName},
            #{protocols},
            #{ruleId},
            #{ruleSubject},
            #{ruleScript},
            #{remarks},
            #{adaptPoints},
            #{startTime},
            #{endTime}
            )
        on duplicate key update end_time = values(end_time)
    </insert>

    <select id="selectPoints" resultType="com.rc.admin.model.DynamicColumn">
        select distinct description as zhColumn, dest_address_name as enColumn, true as isRootLink from rc_iot_point_verification where 1=1 and dest_address_name in
        <foreach collection="array" item="item" index="index" separator="," open="(" close=")">
          #{item}
        </foreach>
        union all
        select distinct description as zhColumn, dest_address_name as enColumn, false as isRootLink from rc_iot_point_verification where 1=1 and dest_address_name in
        <foreach collection="array" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPoint" resultType="com.rc.admin.model.DynamicColumn">
        select t.zhColumn, t.enColumn, t.isRootLink from (
        select distinct description as zhColumn, dest_address_name as enColumn, true as isRootLink
          from rc_iot_point_verification
         where dest_address_name = #{point}
        union
        select distinct description as zhColumn, dest_address_name as enColumn, true as isRootLink
          from rc_iot_collection_point
         where device_code = #{deviceCode}
           and dest_address_name = #{point}
        union
        select distinct description as zhColumn, dest_address_name as enColumn, true as isRootLink
          from rc_iot_collection_point
         where device_code = #{deviceCode}
           and dest_address_name = #{point}) t limit 1
    </select>
</mapper>
