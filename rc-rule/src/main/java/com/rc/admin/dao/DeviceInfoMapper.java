package com.rc.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.model.DeviceInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

public interface DeviceInfoMapper extends BaseMapper<DeviceInfo> {

    @Select("select t1.device_code as deviceCode, " +
            "t1.name as name, " +
            "t1.child_company_name as childCompanyName, " +
            "t1.protocol_type as protocolType, " +
            "t1.first_process, " +
            "t1.first_process_name, " +
            "t1.second_process, " +
            "t1.second_process_name, " +
            "t2.region as platform " +
            "from rc_iot_device_info t1 " +
            "left join rc_iot_region_config t2 on t1.source = t2.ip " +
            "where t1.device_code = #{deviceCode} " +
            "and t2.delete_flag = 1")
    DeviceInfo selectDeviceInfo(String deviceCode);

    @Select("select   asset_id as  assetId,  model_id as modelId,  thing_id as thingId from rc_iot_root_cloud_device_info where asset_id  = #{deviceCode} limit 1")
    Map<String,String> getRootCloudDeviceInfo(String deviceCode);

}
