package com.rc.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.model.DeviceProfile;
import com.rc.admin.mq.model.IotDataQualityInspectionHistory;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface DeviceProfileMapper extends BaseMapper<DeviceProfile> {

    void upsertDeviceProfiles(List<DeviceProfile> deviceProfiles);

    @Update("truncate table rc_iot_device_profile")
    void clearDeviceProfile();

    /**
     * 点位数据异常入库
     * @param list 异常数据集合
     */
    @Insert("<script> INSERT INTO rc_iot_data_quality_inspection_history(" +
            "device_code,dest_address_name,description,gl_value,gy_value,scan_interval,update_time,change_time,gy_time_cloud,gy_time_local,gy_write_time,rule_subject,rule_script,rule_remarks,create_time) values  " +
            "  <foreach collection='list' item='item' separator=',' > " +
            " (#{item.deviceCode},#{item.destAddressName},#{item.description},#{item.glValue},#{item.gyValue}, #{item.scanInterval}, #{item.updateTime}, #{item.changeTime}, #{item.gyTimeCloud}, #{item.gyTimeLocal}, #{item.gyWriteTime}, #{item.ruleSubject}, #{item.ruleScript},#{item.ruleRemarks},NOW())" +
            "  </foreach> " +
            "</script>")
    void iotDataQualityInspectionHistorySave(@Param("list") List<IotDataQualityInspectionHistory> list);

}
