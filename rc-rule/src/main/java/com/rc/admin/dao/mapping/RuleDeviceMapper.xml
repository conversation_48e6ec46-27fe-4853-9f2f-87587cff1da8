<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.dao.RuleDeviceMapper">
    <insert id="insertRuleDevices" parameterType="java.util.List">
        insert into rc_iot_rule_device
        (rule_id, device_code, rule_status, rule_subject, rule_script, adapt_points,
        remarks, rule_use)
        values
        <foreach collection="ruleDevices" item="item" separator=",">
            (
            #{item.ruleId},
            #{item.deviceCode},
            #{item.ruleStatus},
            #{item.ruleSubject},
            #{item.ruleScript},
            #{item.adaptPoints},
            #{item.remarks},
            #{item.ruleUse}
            )
        </foreach>
    </insert>
</mapper>
