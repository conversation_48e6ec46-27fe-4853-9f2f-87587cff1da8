package com.rc.admin.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class DateUtil extends DateUtils {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static Long subStrDate(String sourceDataStr, String targetDateStr) {
        try {
            LocalDate sourceData = LocalDate.parse(sourceDataStr, DATE_TIME_FORMATTER);
            LocalDate targetDate = LocalDate.parse(targetDateStr, DATE_TIME_FORMATTER);
            return targetDate.until(sourceData, ChronoUnit.DAYS);
        } catch (Exception exception) {
            throw new RuntimeException("date format error: " + exception.getMessage());
        }
    }

    public static String localDate() {
        LocalDate localDate = LocalDate.now();
        return localDate.format(DATE_TIME_FORMATTER);
    }

    public static Integer compareLocalDate(String targetDateStr) {

        try {
            LocalDate localDate = LocalDate.now();
            LocalDate targetDate = LocalDate.parse(targetDateStr, DATE_TIME_FORMATTER);
            return localDate.isAfter(targetDate) ? 1 : 0;
        } catch (Exception exception) {
            throw new RuntimeException("db get date format error: " + exception.getMessage());
        }
    }

    public static String localDatePlusDays(Integer count) {

        LocalDate localDate = LocalDate.now();
        return localDate.plusDays(count).format(DATE_TIME_FORMATTER);

    }

    public static String getDate(LocalDate date)
    {
       return  date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }


    /** 正则表达式:中文日期 */
    public static final String REG_CH_DATE = "^\\d{4}-\\d{2}-\\d{2}$";

    /** 正则表达式:中文日期时间 */
    public static final String REG_CH_DATETIME = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyyMMdd","yyyyMM"};

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    public static String timeFormat(String time){
        String str= null;
        try {
            String tempTime = time.replace("Z", " UTC");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date d = sdf.parse(tempTime);
            SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
            str = sdft.format(d)+"Z";
        } catch (ParseException e) {
            log.error("世界时间解析异常:{}",e.getMessage());
        }
        return str;
    }
    public static String FormatTime(String time){
        String str= null;
        try {
            String tempTime = time.replace("Z", " UTC");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
            Date d = sdf.parse(tempTime);
            d = getPlusHour(d,8);
            SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            str = sdft.format(d);
        } catch (ParseException e) {
            log.error("世界时间解析异常:{}",e.getMessage());
        }
        return str;
    }
    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 日期型字符串转化为日期 格式
     * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     *   "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm",
     *   "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null){
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }



    /**
     * 获取过去的天数
     */
    public static long pastDays(Date date) {
        long t = System.currentTimeMillis()-date.getTime();
        return t/(24*60*60*1000);
    }

    /**
     * 获取过去的小时
     */
    public static long pastHour(Date date) {
        long t = System.currentTimeMillis()-date.getTime();
        return t/(60*60*1000);
    }

    /**
     * 获取过去的分钟
     */
    public static long pastMinutes(Date date) {
        long t = System.currentTimeMillis()-date.getTime();
        return t/(60*1000);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     */
    public static String formatDateTime(long timeMillis){
        long day = timeMillis/(24*60*60*1000);
        long hour = (timeMillis/(60*60*1000)-day*24);
        long min = ((timeMillis/(60*1000))-day*24*60-hour*60);
        long s = (timeMillis/1000-day*24*60*60-hour*60*60-min*60);
        long sss = (timeMillis-day*24*60*60*1000-hour*60*60*1000-min*60*1000-s*1000);
        return (day>0?day+",":"")+hour+":"+min+":"+s+"."+sss;
    }

    /**
     * 获取两个日期之间的分钟数
     */
    public static long pastMinutesTwoDate(Date date1, Date date2) {
        long t = date1.getTime()-date2.getTime();
        return t/(60*1000);
    }

    /**
     * 获取两个日期之间的天数
     */
    public static double getDistanceOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
    }

    /**
     * 获取某日期往前多少天的日期
     */
    public static Date getBeforeDate(Date nowDate, Integer beforeNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);
        calendar.add(Calendar.DAY_OF_MONTH, -beforeNum);
        return calendar.getTime();
    }

    /**
     * 获取某日期往前多少个月的日期
     */
    public static Date getBeforeMonthDate(Date nowDate, Integer beforeNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);
        calendar.add(Calendar.MONTH, -beforeNum);
        return calendar.getTime();
    }

    /**
     * 获取某日期往前多少小时的日期
     */
    public static Date getBeforeHour(Date nowDate, Integer beforeNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);
        calendar.add(Calendar.HOUR_OF_DAY, -beforeNum);
        return calendar.getTime();
    }

    /**
     * 获取某日期往后多少小时的日期
     */
    public static Date getPlusHour(Date nowDate, Integer beforeNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);
        calendar.add(Calendar.HOUR_OF_DAY, beforeNum);
        return calendar.getTime();
    }

    /**
     *  Java中传入一个时间段，取出该时间段内所有日期的集合
     */
    public static List<String> findDates(String dBegin, String dEnd) throws ParseException {
        //日期工具类准备
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        //设置开始时间
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(format.parse(dBegin));

        //设置结束时间
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(format.parse(dEnd));

        //装返回的日期集合容器
        List<String> dateList = new ArrayList<String>();
        dateList.add(format.format(calBegin.getTime()));
        // 每次循环给calBegin日期加一天，直到calBegin.getTime()时间等于dEnd
        while (format.parse(dEnd).after(calBegin.getTime()))  {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            dateList.add(format.format(calBegin.getTime()));
        }
        return dateList;
    }

    /**
     * 计算两个日期之间相差的天数
     * @param smdate 较小的时间
     * @param bdate  较大的时间
     * @return 相差天数
     */
    public static double daysBetween(Date smdate,Date bdate) throws ParseException
    {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        smdate=sdf.parse(sdf.format(smdate));
        bdate=sdf.parse(sdf.format(bdate));
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();

        return ((time2-time1)/(1000.00*3600*24))****;
    }
    public static String getTimeDay(String simpleDateFormat,int index){
        TimeZone tz = TimeZone.getTimeZone("Asia/Shanghai");
        TimeZone.setDefault(tz);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat fmt = new SimpleDateFormat(simpleDateFormat);
        calendar.add(Calendar.DAY_OF_MONTH,index);
        String date = fmt.format(calendar.getTime());
        return date;
    }

    /**
     * 获取上个月第一天日期
     */
    public static String getLastMonthFirstDay(String thisMonth) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        //获取前月的第一天
        //获取当前日期
        Calendar calOne = Calendar.getInstance();
        calOne.setTime(format.parse(thisMonth));
        calOne.add(Calendar.MONTH, -1);
        //设置为1号,当前日期既为本月第一天
        calOne.set(Calendar.DAY_OF_MONTH,1);
        return format.format(calOne.getTime());
    }

    /**
     * 获取上个月最后一天的日期
     */
    public static String getLastMonthEndDay(String thisMonth) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        //获取前月的最后一天
        Calendar cale = Calendar.getInstance();
        cale.setTime(format.parse(thisMonth));
        //设置为1号,当前日期既为本月第一天
        cale.set(Calendar.DAY_OF_MONTH,0);

        return format.format(cale.getTime());
    }

    /**
     * 获得某一个月第一天0点时间  2018.09
     */
    public static Date getTimesMonthMorning(String date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(format.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        return  cal.getTime();
    }

    /**
     * 获得某一月最后一天23点59分59秒时间
     */
    public static Date getTimesMonthNight(String date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(format.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 24);
        cal.add(Calendar.SECOND, -1);
        return cal.getTime();
    }

    /**
     *   Java中传入一个日期，取出该日期往前多少个月的月份的集合
     */
    public static List<String> findMonth(String dBegin, Integer monthNum) throws ParseException {
        //日期工具类准备
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        //设置开始时间
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(format.parse(dBegin));

        //装返回的日期集合容器
        List<String> dateList = new ArrayList<String>();
        dateList.add(calBegin.get(Calendar.YEAR) + "" + String.format("%02d",(calBegin.get(Calendar.MONTH)+1)));
        // 每次循环给calBegin日期加一天，直到calBegin.getTime()时间等于dEnd
        for (Integer i = 0; i < monthNum - 1; i++) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.MONTH, -1);
            dateList.add(calBegin.get(Calendar.YEAR) + "" + String.format("%02d",(calBegin.get(Calendar.MONTH)+1)));
        }
        Collections.reverse(dateList);
        return dateList;
    }


}
