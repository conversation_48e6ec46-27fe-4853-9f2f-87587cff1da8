package com.rc.admin.util;

import com.rc.admin.common.redis.util.RedisUtil;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * groovy脚本执行工具类
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
public class GroovyShellUtil {

    private static final long CACHE_EXPIRY_TIME_MINUTES = 10; // 缓存过期时间，单位为分钟
    private static final long CACHE_EXPIRY_TIME_MILLISECONDS = CACHE_EXPIRY_TIME_MINUTES * 60 * 1000; // 转换为毫秒
    private static final Map<String, CachedScript> compiledScripts = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService cleanupExecutor = Executors.newScheduledThreadPool(1);

    /**
     * 执行groovy脚本
     *
     * @param script  脚本语句
     * @param binding 参数
     * @return 脚本返回的对象
     */
    public static Object evaluate(String script, Binding binding) {
        CachedScript cachedScript = compiledScripts.computeIfAbsent(script, key -> {
            GroovyShell groovyShell = new GroovyShell();
            Script compiledScript = groovyShell.parse(script);
            return new CachedScript(compiledScript);
        });

        Object result;
        synchronized (cachedScript) {
            cachedScript.setBinding(binding);
            cachedScript.updateLastAccessTime();
            result = cachedScript.run();
        }
        return result;
    }

    static {
        // 使用定时任务执行缓存清理操作
        cleanupExecutor.scheduleAtFixedRate(GroovyShellUtil::cleanupExpiredScripts, CACHE_EXPIRY_TIME_MILLISECONDS, CACHE_EXPIRY_TIME_MILLISECONDS, TimeUnit.MILLISECONDS);
    }

    private static void cleanupExpiredScripts() {
        long currentTime = System.currentTimeMillis();
        // 迭代并移除过期的缓存项
        compiledScripts.entrySet().removeIf(entry -> entry.getValue().isExpired(currentTime));
    }

    public static void deleteCache(String script) {
        compiledScripts.remove(script);
    }

    private static class CachedScript {

        private final Script script;

        /**
         * 最后访问时间，用于判断缓存是否过期
         */
        private final AtomicLong lastAccessTime = new AtomicLong();

        public CachedScript(Script script) {
            this.script = script;
        }

        public void setBinding(Binding binding) {
            script.setBinding(binding);
        }

        public Object run() {
            return script.run();
        }

        public void updateLastAccessTime() {
            // 迭代并移除过期的缓存项
            lastAccessTime.set(System.currentTimeMillis());
        }

        public boolean isExpired(long currentTime) {
            // 迭代并移除过期的缓存项
            return lastAccessTime.get() + CACHE_EXPIRY_TIME_MILLISECONDS < currentTime;
        }
    }

}
