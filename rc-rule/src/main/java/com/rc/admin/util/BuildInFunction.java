package com.rc.admin.util;

import com.rc.admin.common.redis.util.RedisUtil;

public class BuildInFunction {

    // 设置redis缓存
    static void setRedisCache(Object key, Object value, long expire) {
        RedisUtil.set(String.valueOf(key), value, expire);
    }

    static void getRedisCache(String key) {
        RedisUtil.get(key);
    }
    static void delRedisCache(String key) {
        RedisUtil.del(key);
    }

}
