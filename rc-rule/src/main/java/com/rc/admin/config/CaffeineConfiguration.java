package com.rc.admin.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.rc.admin.model.RuleDevice;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
public class CaffeineConfiguration {

   @Bean(name = "caffeineCache")
   public Cache<String, Map<Long, RuleDevice>> caffeineCache() {
       return Caffeine.newBuilder()
               .expireAfterAccess(10, TimeUnit.MINUTES)
               .initialCapacity(500)
               .maximumSize(2000L)
               .softValues()
               .build();
   }

}
