package com.rc.admin.config;

import cn.hutool.json.JSONObject;
import com.rc.admin.mq.*;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DisruptorQueueConfiguration {

    @Autowired
    private ObjectFactory<HistoryWorkDataDealResultConsumer> historyWorkDataDealResultConsumerObjectFactory;

    @Autowired
    private ObjectFactory<RealtimeWorkDataDealResultConsumer> realtimeWorkDataDealResultConsumerObjectFactory;

    @Bean(name = "realtimeWorkDataDealResultQueue")
    public DisruptorQueue realtimeWorkDataDealResultQueue() {
        AbstractDisruptorConsumer<JSONObject>[] consumers = new RealtimeWorkDataDealResultConsumer[4];
        for (int i = 0; i < consumers.length; i++) {
            consumers[i] = realtimeWorkDataDealResultConsumerObjectFactory.getObject();
        }
        return DisruptorQueueFactory.getWorkPoolQueue(4, true, consumers);
    }

    @Bean(name = "historyWorkDataDealResultQueue")
    public DisruptorQueue historyWorkDataDealResultQueue() {
        AbstractDisruptorConsumer<JSONObject>[] consumers = new HistoryWorkDataDealResultConsumer[4];
        for (int i = 0; i < consumers.length; i++) {
            consumers[i] = historyWorkDataDealResultConsumerObjectFactory.getObject();
        }
        return DisruptorQueueFactory.getWorkPoolQueue(4, true, consumers);
    }

}
