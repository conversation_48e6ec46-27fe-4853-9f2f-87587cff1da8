package com.rc.admin.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "rc_iot_device_info")
public class DeviceInfo {

    @TableField(value = "device_code")
    private String deviceCode;

    @TableField(value = "name")
    private String name;

    @TableField(value = "child_company_name")
    private String childCompanyName;

    @TableField(value = "factory")
    private String factory;

    @TableField(value = "work_center")
    private String workCenter;

    @TableField(value = "work_group")
    private String workGroup;

    @TableField(value = "protocol_type")
    private String protocolType;

    @TableField(value = "paraItem_value")
    private String paraItemValue;

    @TableField(value = "station_number")
    private String stationNumber;

    @TableField(value = "scan_interval_time")
    private String scanIntervalTime;

    @TableField(value = "con_timeout")
    private String conTimeout;

    @TableField(value = "recon_delay")
    private String reconDelay;

    @TableField(value = "customer_param")
    private String customerParam;

    @TableField(value = "descript")
    private String descript;

    @TableField(value = "collection_type")
    private String collectionType;

    @TableField(value = "source")
    private String source;

    @TableField(value = "first_process")
    private String firstProcess;

    @TableField(value = "first_process_name")
    private String firstProcessName;

    @TableField(value = "second_process")
    private String secondProcess;

    @TableField(value = "second_process_name")
    private String secondProcessName;

    @TableField(exist = false)
    private String platform;

    @TableField(value = "device_id")
    private String deviceId;

}
