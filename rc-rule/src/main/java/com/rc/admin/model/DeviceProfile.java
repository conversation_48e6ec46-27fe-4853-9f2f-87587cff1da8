package com.rc.admin.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@TableName("rc_iot_device_profile")
public class DeviceProfile extends Model<DeviceProfile> {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Excel(name = "设备编号（IOT）", width = 9, orderNum = "2")
    private String deviceCode;

    @Excel(name = "设备名称", width = 15, orderNum = "3")
    private String deviceName;

    private String orgCode;

    @Excel(name = "事业部", width = 15, orderNum = "0")
    private String orgName;

    private String companyCode;

    @Excel(name = "子公司", width = 9, orderNum = "1")
    private String companyName;

    @Excel(name = "一级工艺编码", width = 9, orderNum = "4")
    private String firstProcess;

    @Excel(name = "工艺类别", width = 9, orderNum = "5")
    private String firstProcessName;

    @Excel(name = "二级工艺编码", width = 9, orderNum = "6")
    private String secondProcess;

    @Excel(name = "设备类型（二级工艺名称）", width = 9, orderNum = "7")
    private String secondProcessName;

    @Excel(name = "位置", width = 9, orderNum = "8")
    private String location;

    @Excel(name = "设备IP地址分配", width = 9, orderNum = "9")
    private String ipAddress;

}
