package com.rc.admin.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rc_iot_rule")
public class Rule extends Model<Rule> {

    public static final String GENERAL_FIRST_PROCESS_CODE = "S00";

    /**
     * 草稿状态
     */
    public static final int DRAFT_STATUS = 1;

    /**
     * 启用状态
     */
    public static final int ENABLED_STATUS = 2;

    /**
     * 停用状态
     */
    public static final int DELETED_STATUS = 3;

    /**
     * 实时用途
     */
    public static final int REALTIME_USE = 1;

    /**
     * 历史用途
     */
    public static final int HISTORY_USE = 2;

    /**
     * 所有用途
     */
    public static final int ANY_USE = 3;


    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 一级工艺
     */
    @NotBlank(message = "一级工艺编码不能为空")
    @Excel(name = "一级工艺编码", width = 15, orderNum = "0")
    private String firstProcess;

    /**
     * 二级工艺
     */
    private String secondProcess;

    /**
     * 二级工艺名称
     */
    private String secondProcessName;

    /**
     * 协议
     */
    @Excel(name = "协议名称", width = 9, orderNum = "2")
    private String protocols;

    /**
     * 用途
     */
    @NotNull(message = "规则用途不能为空")
    @Min(value = 1, message = "规则状态对应的编码只能为1或2或3")
    @Max(value = 3, message = "规则状态对应的编码只能为1或2或3")
    private Integer ruleUse;

    @Excel(name = "规则用途", width = 9, orderNum = "3")
    @TableField(exist = false)
    private String ruleUseDesc;

    public void setRuleUse(Integer ruleUse) {
        this.ruleUse = ruleUse;
        switch (ruleUse) {
            case REALTIME_USE:
                this.ruleUseDesc = "实时检查";
                break;
            case HISTORY_USE:
                this.ruleUseDesc = "历史检查";
                break;
            case ANY_USE:
                this.ruleUseDesc = "实时+历史检查";
                break;
        }
    }

    /**
     * 状态：1-草稿；2-启用；3-已停用
     */
    @NotNull(message = "规则状态不能为空")
    @Min(value = 1, message = "规则状态对应的编码只能为1或2或3")
    @Max(value = 3, message = "规则状态对应的编码只能为1或2或3")
    private Integer ruleStatus;

    @Excel(name = "状态", width = 15, orderNum = "7")
    @TableField(exist = false)
    private String ruleStatusDesc;

    public void setRuleStatus(Integer ruleStatus) {
        this.ruleStatus = ruleStatus;
        switch (ruleStatus) {
            case DRAFT_STATUS:
                this.ruleStatusDesc = "草稿";
                break;
            case ENABLED_STATUS:
                this.ruleStatusDesc = "发布";
                break;
            case DELETED_STATUS:
                this.ruleStatusDesc = "已停用";
                break;
        }
    }

    /**
     * 主题
     */
    @NotBlank(message = "规则主题不能为空")
    @Excel(name = "规则主题", width = 15, orderNum = "4")
    private String ruleSubject;

    /**
     * 公式
     */
    @NotBlank(message = "规则公式不能为空")
    @Excel(name = "规则检查公式", width = 15, orderNum = "5")
    private String ruleScript;

    /**
     * 适配的点位
     */
    @NotBlank(message = "点位不能为空")
    private String adaptPoints;

    /**
     * 说明
     */
    @NotBlank(message = "规则说明不能为空")
    @Excel(name = "规则检查公式说明", width = 15, orderNum = "6")
    private String remarks;

    @Excel(name = "二级工艺编码/名称", width = 15, orderNum = "1")
    @TableField(exist = false)
    private String secondProcessCompoundValue;

    public void setSecondProcessName(String secondProcessName) {
        this.secondProcessName = secondProcessName;
        if (StringUtils.isNotBlank(secondProcessName)) {
            this.secondProcessCompoundValue = this.secondProcess + "/" + secondProcessName;
        } else {
            this.secondProcessCompoundValue = this.secondProcess;
        }
    }
}
