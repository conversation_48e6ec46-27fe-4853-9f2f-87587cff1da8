package com.rc.admin.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@TableName("rc_iot_rule_adapt_history_data")
public class RuleAdaptHistoryData {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Excel(name = "数采平台", width = 15, orderNum = "0")
    private String platform;

    @Excel(name = "子公司名称", width = 15, orderNum = "1")
    private String companyName;

    @Excel(name = "设备编码", width = 9, orderNum = "3")
    private String deviceCode;

    @Excel(name = "设备名称", width = 15, orderNum = "2")
    private String deviceName;

    @Excel(name = "一级工艺编码", width = 9, orderNum = "4")
    private String firstProcess;

    private String firstProcessName;

    private String secondProcess;

    private String secondProcessName;

    @Excel(name = "协议", width = 9, orderNum = "6")
    private String protocols;

    private Long ruleId;

    @Excel(name = "规则检查主题", width = 15, orderNum = "7")
    private String ruleSubject;

    @Excel(name = "规则检查公式", width = 15, orderNum = "8")
    private String ruleScript;

    @Excel(name = "规则检查说明", width = 15, orderNum = "9")
    private String remarks;

    private String adaptPoints;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Excel(name = "二级工艺编码/名称", width = 15, orderNum = "5")
    @TableField(exist = false)
    private String secondProcessCompoundValue;

    public void setSecondProcessName(String secondProcessName) {
        this.secondProcessName = secondProcessName;
        if (StringUtils.isNotBlank(secondProcessName)) {
            this.secondProcessCompoundValue = this.secondProcess + "/" + secondProcessName;
        } else {
            this.secondProcessCompoundValue = this.secondProcess;
        }
    }

    @Excel(name = "不合规开始时间", width = 15, orderNum = "10")
    @TableField(exist = false)
    private String startTimeStr;

    @Excel(name = "不合规结束时间", width = 15, orderNum = "11")
    @TableField(exist = false)
    private String endTimeStr;

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        if (startTime != null) {
            this.startTimeStr = LocalDateTimeUtil.format(this.startTime, "yyyy-MM-dd HH:mm:ss.SSS");
        }
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        if (endTime != null) {
            this.endTimeStr = LocalDateTimeUtil.format(this.endTime, "yyyy-MM-dd HH:mm:ss.SSS");
        }
    }
}
