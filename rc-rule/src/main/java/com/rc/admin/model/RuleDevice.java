package com.rc.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
@TableName("rc_iot_rule_device")
public class RuleDevice extends Model<RuleDevice> {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 用途
     */
    private Integer ruleUse;

    /**
     * 状态：1-草稿；2-启用；3-删除
     */
    private Integer ruleStatus;

    /**
     * 主题
     */
    private String ruleSubject;

    /**
     * 公式
     */
    private String ruleScript;

    /**
     * 适配的点位
     */
    private String adaptPoints;

    /**
     * 说明
     */
    private String remarks;

}
