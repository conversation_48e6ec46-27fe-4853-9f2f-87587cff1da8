package com.rc.admin.model;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 历史工况
 */
@Data
public class HistoryWorkData {

    private List<DynamicColumn> dynamicColumns;

    private List<Data> datas;

    @Getter
    @Setter
    public static class Data {
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
        private LocalDateTime dateTime;
        private JSONObject rootLinkDatas;
        private JSONObject rootCloudDatas;
    }

}