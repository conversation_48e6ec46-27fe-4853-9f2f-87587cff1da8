package com.rc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.common.status.ResultCode;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.dao.DeviceInfoMapper;
import com.rc.admin.dao.DeviceProfileMapper;
import com.rc.admin.dao.RuleDeviceMapper;
import com.rc.admin.dao.RuleMapper;
import com.rc.admin.model.DeviceInfo;
import com.rc.admin.model.Rule;
import com.rc.admin.model.RuleDevice;
import com.rc.admin.mq.model.IotDataQualityInspectionHistory;
import com.rc.admin.service.RuleService;
import com.rc.admin.util.office.ExcelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleService {

    private final RuleDeviceMapper ruleDeviceMapper;

    private final DeviceProfileMapper mapper;
    private final RuleMapper ruleMapper;

    private final DeviceInfoMapper deviceInfoMapper;

    private final Cache<String, Map<Long, RuleDevice>> caffeineCache;

    public RuleServiceImpl(RuleDeviceMapper ruleDeviceMapper, RuleMapper ruleMapper,
                           DeviceInfoMapper deviceInfoMapper, Cache<String, Map<Long, RuleDevice>> caffeineCache,DeviceProfileMapper mapper) {
        this.ruleDeviceMapper = ruleDeviceMapper;
        this.ruleMapper = ruleMapper;
        this.deviceInfoMapper = deviceInfoMapper;
        this.caffeineCache = caffeineCache;
        this.mapper = mapper;
    }

    @Override
    public Map<Long, RuleDevice> selectRuleDevices(String deviceCode, int use) {
        String cacheKey = deviceCode + "_" + use;
        Map<Long, RuleDevice> ruleDeviceMap = caffeineCache.getIfPresent(cacheKey);
        if (ruleDeviceMap == null) {
            List<RuleDevice> ruleDevices = ruleDeviceMapper.selectList(new QueryWrapper<RuleDevice>().lambda()
                    .eq(RuleDevice::getDeviceCode, deviceCode)
                    .eq(RuleDevice::getRuleStatus, Rule.ENABLED_STATUS)
                    .in(RuleDevice::getRuleUse, use, Rule.ANY_USE)
            );
            ruleDeviceMap = ruleDevices.stream().collect(Collectors.toMap(RuleDevice::getRuleId, Function.identity(), (v1, v2) -> v1));
        }
        if (MapUtil.isNotEmpty(ruleDeviceMap)) {
            caffeineCache.put(cacheKey, ruleDeviceMap);
        }
        return ruleDeviceMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rule addRule(Rule rule) {
        if (existedRule(rule)) {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "同一级工艺编码、二级工艺编码/名称、协议名称、规则用途、检查规则主题已存在！");
        }
        if (1 == ruleMapper.insert(rule)) {
            List<DeviceInfo> deviceInfos = deviceInfoMapper.selectList(new QueryWrapper<DeviceInfo>().lambda()
                    .eq(!StringUtils.equals(rule.getFirstProcess(), Rule.GENERAL_FIRST_PROCESS_CODE), DeviceInfo::getFirstProcess, rule.getFirstProcess())
                    .eq(StringUtils.isNotBlank(rule.getSecondProcess()), DeviceInfo::getSecondProcess, rule.getSecondProcess())
                    .eq(StringUtils.isNotBlank(rule.getProtocols()), DeviceInfo::getProtocolType, rule.getProtocols())
            );
            List<RuleDevice> ruleDevices = deviceInfos.stream().map(item -> {
                RuleDevice ruleDevice = new RuleDevice();
                BeanUtil.copyProperties(rule, ruleDevice, "id", "firstProcess", "secondProcess", "secondProcessName", "protocols");
                ruleDevice.setRuleId(rule.getId());
                ruleDevice.setDeviceCode(item.getDeviceCode());
                return ruleDevice;
            }).collect(Collectors.toList());
            insertRuleDevices(ruleDevices);
        }
        return rule;
    }

    @Override
    public Page<Rule> selectPage(Page<Rule> page, Rule rule) {
        String lastSql = "";
        if (StringUtils.isNotBlank(page.getSortField())) {
            String sortField = page.getSortField();
            switch (sortField) {
                case "secondProcessCompoundValue":
                    lastSql = "order by second_process " + page.getSortOrder() + ",second_process_name " + page.getSortOrder();
                    break;
//                case "ruleStatusDesc":
//                case "ruleStatusDesc":
//                    lastSql = "order by rule_status " + page.getSortOrder();
//                    break;
                case "ruleUseDesc":
                    lastSql = "order by rule_use " + page.getSortOrder();
                    break;
                default:
                    lastSql = "order by " + StrUtil.toUnderlineCase(page.getSortField()) + " " + page.getSortOrder();
                    break;
            }
        }
        if (StringUtils.isBlank(lastSql)) {
            lastSql = "order by id desc";
        } else {
            lastSql = lastSql + ", id desc";
        }

        return ruleMapper.selectPage(page, new QueryWrapper<Rule>().lambda()
                .eq(StringUtils.isNotBlank(rule.getFirstProcess()), Rule::getFirstProcess, rule.getFirstProcess())
                .like(StringUtils.isNotBlank(rule.getRuleSubject()), Rule::getRuleSubject, rule.getRuleSubject())
                .eq(StringUtils.isNotBlank(rule.getSecondProcess()), Rule::getSecondProcess, rule.getSecondProcess())
                .eq(rule.getRuleUse() != null, Rule::getRuleUse, rule.getRuleUse())
                .like(StringUtils.isNotBlank(rule.getProtocols()), Rule::getProtocols, rule.getProtocols())
                .eq(rule.getRuleStatus() != null, Rule::getRuleStatus, rule.getRuleStatus())
                .last(StringUtils.isNotBlank(lastSql), lastSql)
        );
    }

    @Override
    public String exportData(Rule rule) {
        List<Rule> rules = ruleMapper.selectList(new QueryWrapper<Rule>().lambda()
                .eq(StringUtils.isNotBlank(rule.getFirstProcess()), Rule::getFirstProcess, rule.getFirstProcess())
                .eq(StringUtils.isNotBlank(rule.getSecondProcess()), Rule::getSecondProcess, rule.getSecondProcess())
                .like(StringUtils.isNotBlank(rule.getProtocols()), Rule::getProtocols, rule.getProtocols())
                .like(StringUtils.isNotBlank(rule.getRuleSubject()), Rule::getRuleSubject, rule.getRuleSubject())
                .eq(rule.getRuleUse() != null, Rule::getRuleUse, rule.getRuleUse())
                .eq(rule.getRuleStatus() != null, Rule::getRuleStatus, rule.getRuleStatus())
                .orderByDesc(Rule::getId));
        return ExcelUtil.writeAndGetDownloadId("数据质量检查规则配置", "数据质量检查规则配置", rules, Rule.class);
    }

    @Override
    public Rule selectRule(Long ruleId) {
        return ruleMapper.selectById(ruleId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rule updateRule(Rule rule) {
        if (Objects.isNull(rule.getId())) {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "规则更新，规则ID不允许为空");
        }
        if (existedRule(rule)) {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "同一级工艺编码、二级工艺编码/名称、协议名称、规则用途、检查规则主题已存在！");
        }
        int rows = ruleMapper.update(rule, new QueryWrapper<Rule>().lambda()
                .eq(Rule::getId, rule.getId())
                .ne(Rule::getRuleStatus, Rule.ENABLED_STATUS)
        );
        if (rows == 1) {
            RuleDevice ruleDevice = new RuleDevice();
            BeanUtil.copyProperties(rule, ruleDevice, "id", "firstProcess", "secondProcess", "secondProcessName", "protocols", "ruleUse");
            ruleDevice.setRuleId(rule.getId());
            ruleDeviceMapper.update(ruleDevice, new QueryWrapper<RuleDevice>().lambda().eq(RuleDevice::getRuleId, rule.getId()));
            if (rule.getRuleStatus() == Rule.ENABLED_STATUS) {
                clearRuleDeviceCache(rule.getId());
            }
        } else {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "已发布的规则不允许更新提交");
        }
        return rule;
    }

    private boolean existedRule(Rule rule) {
        return ruleMapper.exists(new QueryWrapper<Rule>().lambda()
                .eq(Rule::getFirstProcess, rule.getFirstProcess())
                .eq(StringUtils.isNotEmpty(rule.getSecondProcess()), Rule::getSecondProcess, rule.getSecondProcess())
                .eq(StringUtils.isNotEmpty(rule.getProtocols()), Rule::getProtocols, rule.getProtocols())
                .eq(StringUtils.equals(rule.getSecondProcess(), ""), Rule::getSecondProcess, "")
                .eq(StringUtils.equals(rule.getProtocols(), ""), Rule::getProtocols, "")
                .isNull(rule.getSecondProcess() == null, Rule::getSecondProcess)
                .isNull(rule.getProtocols() == null, Rule::getProtocols)
                .eq(Rule::getRuleUse, rule.getRuleUse())
                .eq(Rule::getRuleSubject, rule.getRuleSubject())
                .ne(rule.getId() != null, Rule::getId, rule.getId())
        );
    }

    private void clearRuleDeviceCache(Long ruleId) {
        List<String> ruleDeviceCacheKeys = ruleDeviceMapper.selectList(new QueryWrapper<RuleDevice>().lambda()
                .eq(RuleDevice::getRuleId, ruleId))
                .stream()
                .map(item -> item.getDeviceCode() + "_" + item.getRuleUse()).collect(Collectors.toList());
        caffeineCache.invalidateAll(ruleDeviceCacheKeys);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRule(Long ruleId) {
        if(1 == ruleMapper.delete(new QueryWrapper<Rule>().lambda()
                .eq(Rule::getId, ruleId)
                .eq(Rule::getRuleStatus, Rule.DRAFT_STATUS))) {
            ruleDeviceMapper.delete(new QueryWrapper<RuleDevice>().lambda()
                .eq(RuleDevice::getRuleId, ruleId)
                .eq(RuleDevice::getRuleStatus, Rule.DRAFT_STATUS)
            );
        } else {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "仅允许删除草稿状态的规则");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disableRule(Long ruleId) {
        if (1 == ruleMapper.update(null, new UpdateWrapper<Rule>().lambda()
                .set(Rule::getRuleStatus, Rule.DELETED_STATUS)
                .eq(Rule::getId, ruleId)
                .eq(Rule::getRuleStatus, Rule.ENABLED_STATUS))) {
            // 停用规则关联的设备
            ruleDeviceMapper.update(null, new UpdateWrapper<RuleDevice>().lambda()
                .set(RuleDevice::getRuleStatus, Rule.DELETED_STATUS)
                .eq(RuleDevice::getRuleId, ruleId)
            );
            // 删除关联设备的规则缓存
            clearRuleDeviceCache(ruleId);
        } else {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "仅允许停用已发布的规则");
        }

    }

    @Override
    public void iotDataQualityInspectionHistorySave(List<IotDataQualityInspectionHistory> list) {
        mapper.iotDataQualityInspectionHistorySave(list);
    }

    private void insertRuleDevices(List<RuleDevice> ruleDevices) {
        int batchSize = 1000;
        int size = ruleDevices.size();
        int idxLimit = Math.min(batchSize, size);
        int i = 1;
        List<RuleDevice> batchRuleDevices = new ArrayList<>();
        for (Iterator<RuleDevice> iterator = ruleDevices.iterator(); iterator.hasNext(); ++i) {
            RuleDevice ruleDevice = iterator.next();
            batchRuleDevices.add(ruleDevice);
            if (i == idxLimit) {
                ruleDeviceMapper.insertRuleDevices(batchRuleDevices);
                batchRuleDevices.clear();
                idxLimit = Math.min(idxLimit + batchSize, size);
            }
        }
    }
}
