package com.rc.admin.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rc.admin.auth.model.entity.RealTimeDeviceWorkConditionResp;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.common.status.ResultCode;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.dao.DeviceInfoMapper;
import com.rc.admin.dao.RuleAdaptHistoryDataMapper;
import com.rc.admin.exception.CustomException;
import com.rc.admin.model.*;
import com.rc.admin.util.office.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleAdaptHistoryDataService {

    private final RuleAdaptHistoryDataMapper ruleAdaptHistoryDataMapper;
    private final DeviceInfoMapper deviceInfoMapper;

    @Autowired
    public RuleAdaptHistoryDataService(RuleAdaptHistoryDataMapper ruleAdaptHistoryDataMapper, DeviceInfoMapper deviceInfoMapper) {
        this.ruleAdaptHistoryDataMapper = ruleAdaptHistoryDataMapper;
        this.deviceInfoMapper = deviceInfoMapper;
    }

    public Page<RuleAdaptHistoryData> selectPage(Page<RuleAdaptHistoryData> page, RuleAdaptHistoryData ruleAdaptHistoryData) {
        return ruleAdaptHistoryDataMapper.selectPage(page, getQueryWrapper(ruleAdaptHistoryData)
        );
    }

    public String exportData(RuleAdaptHistoryData ruleAdaptHistoryData) {
        List<RuleAdaptHistoryData> ruleAdaptHistoryDatas = ruleAdaptHistoryDataMapper.selectList(getQueryWrapper(ruleAdaptHistoryData));
        return ExcelUtil.writeAndGetDownloadId("历史数据质量检查不合规数据", "历史数据质量检查不合规数据", ruleAdaptHistoryDatas, RuleAdaptHistoryData.class);
    }

    private LambdaQueryWrapper<RuleAdaptHistoryData> getQueryWrapper(RuleAdaptHistoryData ruleAdaptHistoryData) {
        return new QueryWrapper<RuleAdaptHistoryData>().lambda()
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getCompanyName()), RuleAdaptHistoryData::getCompanyName, ruleAdaptHistoryData.getCompanyName())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getFirstProcess()), RuleAdaptHistoryData::getFirstProcess, ruleAdaptHistoryData.getFirstProcess())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getSecondProcess()), RuleAdaptHistoryData::getSecondProcess, ruleAdaptHistoryData.getSecondProcess())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getProtocols()), RuleAdaptHistoryData::getProtocols, ruleAdaptHistoryData.getProtocols())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getRuleSubject()), RuleAdaptHistoryData::getRuleSubject, ruleAdaptHistoryData.getRuleSubject())
                .ge(ruleAdaptHistoryData.getStartTime() != null, RuleAdaptHistoryData::getStartTime, ruleAdaptHistoryData.getStartTime())
                .le(ruleAdaptHistoryData.getEndTime() != null, RuleAdaptHistoryData::getStartTime, ruleAdaptHistoryData.getEndTime())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getDeviceCode()), RuleAdaptHistoryData::getDeviceCode, ruleAdaptHistoryData.getDeviceCode())
                .eq(StringUtils.isNotBlank(ruleAdaptHistoryData.getDeviceName()), RuleAdaptHistoryData::getDeviceName, ruleAdaptHistoryData.getDeviceName())
                .orderByDesc(RuleAdaptHistoryData::getStartTime);
    }

    public HistoryWorkData selectWorkData(HistoryWorkDataRequestDTO workDataRequestDTO) {
        String deviceCode = workDataRequestDTO.getDeviceCode();
        DeviceInfo deviceInfo = getDeviceInfo(deviceCode);
        String rootLinkDeviceId = deviceInfo.getDeviceId();
        String dataSourceIp = deviceInfo.getSource();
        // 查询根连的历史工况
        List<JSONObject> rootLinkWorkDatas = selectRootLinkWorkData(rootLinkDeviceId, dataSourceIp, workDataRequestDTO);
        // 查询根云的 thingId modelId
        JSONObject rootCloudDevice = selectRootCloudDevice(deviceCode);
        String thingId = rootCloudDevice.getStr("thingId");
        String modelId = rootCloudDevice.getStr("modelId");
        // 查询根云的历史工况
        List<JSONObject> rootCloudWorkDatas = selectRootCloudWorkDatas(thingId, modelId, workDataRequestDTO);
        return mergeWorkData(workDataRequestDTO, rootLinkWorkDatas, rootCloudWorkDatas);
    }

    private HistoryWorkData mergeWorkData(HistoryWorkDataRequestDTO workDataRequestDTO, List<JSONObject> rootLinkWorkDatas, List<JSONObject> rootCloudWorkDatas) {
        HistoryWorkData historyWorkData = new HistoryWorkData();
        String points = workDataRequestDTO.getPoints();
        String[] pointArr = points.split(",");
        List<DynamicColumn> dynamicColumns = getDynamicColumns(pointArr, workDataRequestDTO.getDeviceCode());
        historyWorkData.setDynamicColumns(dynamicColumns);
        Map<Long, JSONObject> rootCloudDataMap = rootCloudWorkDatas.stream().collect(Collectors.toMap(item -> item.getLong("timestamp"), Function.identity(), (v1, v2) -> v2));
        List<HistoryWorkData.Data> datas = new ArrayList<>();
        // 遍历根连工况
        rootLinkWorkDatas.forEach(rootLinkWorkData -> {
            Long timestamp = rootLinkWorkData.getLong("timestamp");
            JSONObject rootCloudWorkData = rootCloudDataMap.get(timestamp);
            if (rootCloudWorkData != null) {
                HistoryWorkData.Data merged = mergeTwoObject(rootLinkWorkData, rootCloudWorkData);
                datas.add(merged);
                rootCloudDataMap.remove(timestamp);
            } else {
                HistoryWorkData.Data merged = mergeWithDefault(rootLinkWorkData, true);
                datas.add(merged);
            }
        });
        // 未和根连工况匹配上的根云工况
        rootCloudDataMap.forEach((key, rootRootWorkData) -> {
            HistoryWorkData.Data merged = mergeWithDefault(rootRootWorkData, false);
            datas.add(merged);
        });
        Comparator<HistoryWorkData.Data> name = Comparator.comparing(HistoryWorkData.Data::getDateTime);
        datas.sort(name);
        historyWorkData.setDatas(datas);
        return historyWorkData;
    }

    private HistoryWorkData.Data mergeWithDefault(JSONObject workData, boolean isRootLinkWorkData) {
        HistoryWorkData.Data data = new HistoryWorkData.Data();
        data.setDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(workData.getLong("timestamp")), ZoneId.systemDefault()));
        JSONObject rootLinkDatas = new JSONObject();
        JSONObject rootCloudDatas = new JSONObject();
        if (isRootLinkWorkData) {
            workData.forEach((key, value) -> {
                if (!"timestamp".equals(key)) {
                    rootLinkDatas.set(key, value);
                    rootCloudDatas.set(key, "");
                }
            });
        } else {
            workData.forEach((key, value) -> {
                if (!"timestamp".equals(key)) {
                    rootCloudDatas.set(key, value);
                    rootLinkDatas.set(key, "");
                }
            });
        }
        data.setRootLinkDatas(rootLinkDatas);
        data.setRootCloudDatas(rootCloudDatas);
        return data;
    }

    private HistoryWorkData.Data mergeTwoObject(JSONObject rootLinkWorkData, JSONObject rootCloudWorkData) {
        HistoryWorkData.Data data = new HistoryWorkData.Data();
        Long timestamp = rootLinkWorkData.getLong("timestamp");
        data.setDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()));
        JSONObject rootLinkDatas = new JSONObject();
        JSONObject rootCloudDatas = new JSONObject();
        rootLinkWorkData.forEach((key, value) -> {
            if (!"timestamp".equals(key)) {
                rootLinkDatas.set(key, value);
            }
        });
        rootCloudWorkData.forEach((key, value) -> {
            if (!"timestamp".equals(key)) {
                rootCloudDatas.set(key, value);
            }
        });
        data.setRootLinkDatas(rootLinkDatas);
        data.setRootCloudDatas(rootCloudDatas);
        return data;
    }

    private List<DynamicColumn> getDynamicColumns(String[] pointArr, String deviceCode) {
        List<DynamicColumn> dynamicColumns = new ArrayList<>();
        for (String point : pointArr) {
            DynamicColumn column = ruleAdaptHistoryDataMapper.selectPoint(point, deviceCode);
            if (column == null) {
                column = new DynamicColumn();
                column.setEnColumn(point);
                column.setZhColumn(point);
                column.setIsRootLink(true);
            }
            dynamicColumns.add(column);
        }
        List<DynamicColumn> rootCloudColumns = dynamicColumns.stream().map(item -> {
            DynamicColumn dynamicColumn = new DynamicColumn();
            dynamicColumn.setEnColumn(item.getEnColumn());
            dynamicColumn.setZhColumn(item.getZhColumn());
            dynamicColumn.setIsRootLink(false);
            return dynamicColumn;
        }).collect(Collectors.toList());
        dynamicColumns.addAll(rootCloudColumns);
        return dynamicColumns;
    }

    private List<JSONObject> selectRootCloudWorkDatas(String thingId, String modelId, HistoryWorkDataRequestDTO workDataRequestDTO) {
        LocalDateTime startTime = workDataRequestDTO.getStartTime();
        LocalDateTime endTime = startTime.plusHours(1);
        String[] points = workDataRequestDTO.getPoints().split(",");
        String url = String.format("http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn/historian-manage/v1/historian/models/%s/things/%s", modelId, thingId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("startTime", startTime.toString());
        requestBody.set("endTime", endTime.toString());
        requestBody.set("properties", JSONUtil.toJsonStr(points));
        requestBody.set("sort", "DESC");
        requestBody.set("limit", 3620);
        HttpResponse httpResponse = HttpRequest.post(url)
                .header("Authorization", "Bearer " + getRootCloudAccessToken())
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根云设备历史工况查询失败，deviceId：" + thingId);
        }
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        List<JSONObject> payload = responseBody.getBeanList("payload", JSONObject.class);
        List<JSONObject> rootCloudWorkDatas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(payload)) {
            JSONArray rows = payload.get(0).getJSONArray("rows");
            List<String[]> datas = JSONUtil.toList(rows, String[].class);
            for (String[] data : datas) {
                JSONObject workData = new JSONObject();
                long ts = Instant.parse(data[0]).toEpochMilli();
                workData.set("timestamp", ts);
                for (int j = 1; j < data.length; j++) {
                    workData.set(points[j-1], data[j]);
                }
                rootCloudWorkDatas.add(workData);
            }
        } else {
            log.warn("设备[{}]查询根云历史工况为空", workDataRequestDTO.getDeviceCode());
        }
        return rootCloudWorkDatas;
    }

    private JSONObject selectRootCloudDevice(String deviceCode) {
        String url = "http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn/thing-instance/v1/device/device-instances-query";
        JSONObject requestBody = new JSONObject();
        requestBody.set("classId", "DEVICE");
        requestBody.set("assetIds", new String[]{deviceCode});
        HttpResponse httpResponse = HttpRequest.post(url)
                .header("Authorization", "Bearer " + getRootCloudAccessToken())
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根云设备实例查询失败，设备编号：" + deviceCode);
        }
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        if (!responseBody.getBool("success") || CollectionUtil.isEmpty(responseBody.getJSONArray("payload"))) {
            throw new CustomException(httpResponse.getStatus(), "根云设备实例查询失败或不存在的设备，设备编号：" + deviceCode);
        }
        JSONObject rootCloudDevice = new JSONObject();
        JSONObject payload = responseBody.getBeanList("payload", JSONObject.class).get(0);
        rootCloudDevice.set("thingId", payload.getStr("thingId"));
        rootCloudDevice.set("modelId", payload.getJSONObject("model").getStr("modelId"));
        return rootCloudDevice;
    }

    private List<JSONObject> selectRootLinkWorkData(String rootLinkDeviceId, String dataSourceIp, HistoryWorkDataRequestDTO workDataRequestDTO) {
        LocalDateTime startTime = workDataRequestDTO.getStartTime();
        LocalDateTime timestamp = startTime.plusHours(1);
        long ts = LocalDateTimeUtil.toEpochMilli(timestamp);
        String url = "http://" + dataSourceIp + ":9882/api/db/Device/GetHistoricalConditions";
        JSONObject requestBody = new JSONObject();
        requestBody.set("deviceId", rootLinkDeviceId);
        requestBody.set("endTimestamp", ts);
        requestBody.set("pageIndex", 1);
        requestBody.set("pageSize", 9999);
        String signKey = "fbc660a1d085438a807c4137ff1e3315";
        String origin = requestBody.toString() + "&" + timestamp + "&" + signKey;
        String sign = DigestUtils.md5DigestAsHex(origin.getBytes(StandardCharsets.UTF_8));
        List<String> pointList = Arrays.asList(workDataRequestDTO.getPoints().split(","));
        HttpResponse httpResponse = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("rootlink_language", "zh-CN")
                .header("rootlink_sign", sign)
                .header("rootlink_timestamp", String.valueOf(timestamp))
                .header("TOKEN", getRootCloudAccessToken())
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根连工况查询失败，IP=" + dataSourceIp);
        }
        JSONObject responseBody = getResponseBody(httpResponse);
        JSONObject data = responseBody.getJSONObject("data");
        JSONArray items = data.getJSONArray("items");
        List<JSONObject> itemList = JSONUtil.toList(items, JSONObject.class);
        List<JSONObject> workDatas = itemList.stream().map(item -> {
            JSONObject workData = new JSONObject();
            workData.set("timestamp", item.getLong("ts"));
            item.getJSONObject("addresses").forEach((k, v) -> {
                if (pointList.contains(k)) {
                    String value = JSONUtil.parseObj(v).getStr("v");
                    workData.set(k, value);
                }
            });
            return workData;
        }).collect(Collectors.toList());
        log.debug("RuleAdaptHistoryDataService.requestRootLink 处理完的数据-{}", JSONUtil.toJsonStr(workDatas));
        return workDatas;
    }

    private JSONObject getResponseBody(HttpResponse httpResponse) {
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        if (200 != responseBody.getInt("code")) {
            throw new CustomException(responseBody.getInt("code"), "根连工况查询失败");
        }
        return responseBody;
    }

    private DeviceInfo getDeviceInfo(String deviceCode) {
        DeviceInfo deviceInfo = deviceInfoMapper.selectOne(new QueryWrapper<DeviceInfo>().lambda().eq(DeviceInfo::getDeviceCode, deviceCode));
        if (deviceInfo == null || StringUtils.isBlank(deviceInfo.getDeviceId())) {
            throw new EasyException(ResultCode.BAD_REQUEST.getCode(), "设备-[" + deviceCode + "]在数据表[rc_iot_device_info]中不存在或对应根连DeviceID暂未同步");
        }
        return deviceInfo;
    }

    private String getRootCloudAccessToken() {
        String url = "http://federation-openapi-gateway-zone-china.sanyiot.sany.com.cn/account-manage/v2/auth/login";
        JSONObject requestBody = new JSONObject();
        requestBody.putOnce("grant_type", "client_credentials");
        requestBody.putOnce("client_id", "20230724555453cba4cf1abe");
        requestBody.putOnce("client_secret", "1c17cb6c8d6519cb08bbefa24e9db665");
        HttpResponse httpResponse = HttpRequest.post(url)
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();
        if (!httpResponse.isOk()) {
            throw new CustomException(httpResponse.getStatus(), "根云请求失败");
        }
        JSONObject responseBody = JSONUtil.toBean(httpResponse.body(), JSONObject.class);
        return responseBody.getStr("access_token");
    }

}


