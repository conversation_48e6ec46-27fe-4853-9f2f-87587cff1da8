package com.rc.admin.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.model.Rule;
import com.rc.admin.model.RuleDevice;
import com.rc.admin.mq.model.IotDataQualityInspectionHistory;

import java.util.List;
import java.util.Map;

public interface RuleService {

    Map<Long, RuleDevice> selectRuleDevices(String deviceCode, int use);

    Rule addRule(Rule rule);

    Page<Rule> selectPage(Page<Rule> page, Rule rule);

    String exportData(Rule rule);

    Rule selectRule(Long ruleId);

    Rule updateRule(Rule rule);

    void deleteRule(Long ruleId);

    void disableRule(Long ruleId);
    void iotDataQualityInspectionHistorySave(List<IotDataQualityInspectionHistory> list);


}
