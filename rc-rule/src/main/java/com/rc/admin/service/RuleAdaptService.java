package com.rc.admin.service;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rc.admin.common.redis.lock.RedisLock;
import com.rc.admin.model.Rule;
import com.rc.admin.model.RuleDevice;
import com.rc.admin.mq.DisruptorQueue;
import com.rc.admin.util.BuildInFunction;
import com.rc.admin.util.GroovyShellUtil;
import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleAdaptService {
    private static final BuildInFunction buildInFunction = new BuildInFunction();
    private final RuleService ruleService;

    @Autowired
    @Qualifier("realtimeWorkDataDealResultQueue")
    private DisruptorQueue realtimeWorkDataDealResultQueue;

    @Autowired
    @Qualifier("historyWorkDataDealResultQueue")
    private DisruptorQueue historyWorkDataDealResultQueue;

    @Autowired
    @Qualifier("reentrantSpinLock")
    private RedisLock reentrantSpinLock;

    @Autowired
    public RuleAdaptService(RuleService ruleService) {
        this.ruleService = ruleService;
    }

    private static final String HISTORY_DATA_CALC_LOCK_KEY_PREFIX = "history_";
    private static final String REALTIME_DATA_CALC_LOCK_KEY_PREFIX = "realtime_";

    /**
     * 实时工况规则适配入口
     * @param data 实时工况
     */

    public void realTimeDataRuleAdapt(JSONObject data) {
        ruleAdapt(data, Rule.REALTIME_USE);
    }

    /**
     * 历史工况规则适配入口
     * @param data 历史工况
     */
    public void historyDataRuleAdapt(JSONObject data) {
        String deviceCode = data.getStr("__assetId__");
        ruleService.selectRuleDevices(deviceCode, Rule.HISTORY_USE).entrySet().parallelStream().forEach(item -> {
            Long ruleId = item.getValue().getRuleId();
            Binding binding = buildBinding(data, ruleId, true);
            Object resultObject = ruleScriptExecute(binding, deviceCode, ruleId, item.getValue().getRuleScript(), true);
            if (!Objects.isNull(resultObject)) {
                JSONObject adaptResult = JSONUtil.parseObj(resultObject);
                bindRuleInfo(item.getValue(), adaptResult);
                historyWorkDataDealResultQueue.add(adaptResult);
            }
        });
    }

    @NotNull
    private Binding buildBinding(JSONObject data, Long ruleId, boolean isHistoryWorkData) {
        Binding binding = new Binding();
        binding.setVariable("data", data);
        binding.setVariable("buildInFunction", buildInFunction);
        binding.setVariable("time", System.currentTimeMillis());
        binding.setVariable("ruleId", ruleId);
        binding.setVariable("isHistoryWorkData", isHistoryWorkData);
        return binding;
    }

    private Object ruleScriptExecute(Binding binding, String deviceCode, Long ruleId, String ruleScript, boolean isHistoryWorkData) {
        String lockKeyPrefix = isHistoryWorkData ? HISTORY_DATA_CALC_LOCK_KEY_PREFIX : REALTIME_DATA_CALC_LOCK_KEY_PREFIX;
        String lockKey = lockKeyPrefix + deviceCode + "_" + ruleId;
        //reentrantSpinLock.tryLock(lockKey, 10, TimeUnit.SECONDS);
        Object resultObject = null;
        try {
            resultObject = GroovyShellUtil.evaluate(ruleScript, binding);

        } catch (Exception e) {
            log.error("RuleAdaptService.ruleScriptExecute 脚本执行发生报错：", e);
        } finally {
            //reentrantSpinLock.releaseLock(lockKey);
        }
        return resultObject;
    }

    /**
     * 规则适配
     * @param data 工况
     */
    public void ruleAdapt(JSONObject data, int use) {
        String deviceCode = data.getStr("__assetId__");
        Map<String, String> points = getPoints(data);
        ruleService.selectRuleDevices(deviceCode, use).entrySet().parallelStream().forEach(item -> {
            Long ruleId = item.getValue().getRuleId();
            Binding binding = buildBinding(data, ruleId, false);
            Object resultObject = ruleScriptExecute(binding, deviceCode, item.getValue().getRuleId(), item.getValue().getRuleScript(), false);
            if (!Objects.isNull(resultObject)) {
                JSONObject adaptResult = JSONUtil.parseObj(resultObject);
                bindRuleInfo(item.getValue(), adaptResult);
                String[] rulePoints = item.getValue().getAdaptPoints().split(",");
                List<JSONObject> adaptResults = new ArrayList<>(rulePoints.length);
                for (String point : rulePoints) {
                    JSONObject adaptDetails = ObjectUtil.cloneByStream(adaptResult);
                    adaptDetails.set("pointName", point);
                    adaptDetails.set("pointDesc", points.get(point));
                    adaptResults.add(adaptDetails);
                }
                realtimeWorkDataDealResultQueue.addAll(adaptResults);
            }
        });
    }

    private void bindRuleInfo(RuleDevice ruleDevice, JSONObject adaptResult) {
        adaptResult.set("ruleSubject", ruleDevice.getRuleSubject());
        adaptResult.set("ruleScript", ruleDevice.getRuleScript());
        adaptResult.set("ruleRemarks", ruleDevice.getRemarks());
        adaptResult.set("adaptPoints", ruleDevice.getAdaptPoints());
        adaptResult.set("ruleId", ruleDevice.getRuleId());
    }

    private Map<String, String> getPoints(JSONObject data) {
        JSONArray rawData = data.getJSONObject("rawData").getJSONArray("ItemList");
        List<JSONObject> rawDataList = JSONUtil.toList(rawData, JSONObject.class);
        return rawDataList.stream().collect(Collectors.toMap(k -> k.getStr("Name"), v -> v.getStr("Explain") == null  ? "":v.getStr("Explain"), (v1, v2) -> v1));
    }
}
