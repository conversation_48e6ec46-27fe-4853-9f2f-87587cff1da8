<h1 align="center">RC API</h1>

<div align="center">

</div>

基于SpringBoot2、Druid、Mybatis Plus、Apache Shiro、Activiti、Beetl、HuTool、Quartz等开源框架开发，内置权限、部门、参数、字典、定时任务、代码生成等模块。  

## 文档
[在线文档]('在线文档')

## 下载

从仓库中直接安装最新的代码

```
$ git clone http://gitlab.irootech.com/rc-admin/rc-api.git rc-admin
```

## 目录结构

```
├── db                   # Sql脚本
│   ├── activiti         # 工作流
│   ├── rc-admin.sql    # 基础 sql
│   └── quartz.sql       # 定时任务
├── rc-activiti        # 工作流
├── rc-api             # 入口
├── rc-common          # 工具
│   ├── rc-core        # 基础
│   ├── rc-mybatis     # MyBatis
│   └── rc-redis       # Redis
├── rc-file            # 文件
├── rc-generator       # 代码生成
├── rc-sample          # 示例
├── rc-scheduler       # 定时任务
└── rc-sys             # 系统
```

## 创建数据库

- 创建数据库并执行`/rc-admin/db/rc-admin.sql`初始化表
- 执行`/rc-admin/db/activiti/*`创建工作流表
- 执行`/rc-admin/db/quartz.sql`创建定时任务表

## 配置数据源&Redis
打开`/rc-admin/rc-api/src/main/resources/application-dev.yml`文件，修改`Redis`与`数据源`配置。 如果你使用默认的参数安装的`Redis`和`MySQL`
只需修改`spring.datasource.password`即可。

```yaml {19}
spring:
  # Redis
  redis:
    # 数据库索引（默认为0）
    database: 0
    # 服务器地址
    host: 127.0.0.1
    # 服务器连接端口
    port: 6379
    # 服务器连接密码（默认为空）
    password:
  # 数据源，集成多数据源，此处仅需配置主数据源
  datasource:
    dynamic:
      datasource:
        master:
          url: *********************************************************************************************************************************************************************************************************************************************************
          username: root
          password: xxx
```
多数据源配置请参考[多数据源配置]('多数据源配置')
## 配置上传的文件存放路径

打开`/rc-admin/rc-api/src/main/resources/application-dev.yml`文件，修改文件存放路径

```yaml {3}
project:
  # 文件上传路径(不要写以~开头的路径会导致无法访问)
  file-upload-path: /data/uploads/rc-admin
```

> 此路径会被添加为静态资源映射地址

## 启动服务

执行`com.rc.admin.Application`启动服务