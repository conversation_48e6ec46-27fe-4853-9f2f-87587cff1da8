# 分组配置前端API接口文档

## 概述

本文档描述了分组配置功能的所有API接口，供前端开发人员使用。所有接口都遵循统一的响应格式，成功时返回 `success: true`，失败时返回 `success: false` 和错误信息。

## 统一响应格式

### 成功响应
```json
{
  "success": true,
  "data": "响应数据",
  "timeStamp": 1696147200000
}
```

### 失败响应
```json
{
  "success": false,
  "errorCode": "00500",
  "errorMessage": "错误信息",
  "showType": 3,
  "timeStamp": 1696147200000
}
```

## 1. 分组配置管理接口

### 1.1 新增分组配置

**接口地址**: `POST /api/groupConfig/add`

**接口描述**: 创建新的分组配置，系统会自动生成分组标识

**请求参数**:
```json
{
  "doubleRateName": "设备质量组",
  "regionCodes": ["NA", "EU"],
  "countryCodes": ["US", "DE"],
  "modelCheckItems": [
    {
      "modelId": "MOD-001",
      "paramCode": "1001"
    },
    {
      "modelId": "MOD-002", 
      "paramCode": "2001"
    }
  ]
}
```

**参数说明**:
- `doubleRateName`: 分组名称，必填，最大20个字符
- `regionCodes`: 大区代码列表，必填，至少选择一个大区
- `countryCodes`: 国家代码列表，必填，至少选择一个国家
- `modelCheckItems`: 物模型检查项配置列表，必填，至少配置一组
  - `modelId`: 物模型ID
  - `paramCode`: 检查项编码

**成功响应**:
```json
{
  "success": true,
  "data": {
    "doubleRateSign": "1",
    "doubleRateName": "设备质量组"
  }
}
```

**失败响应**:
```json
{
  "success": false,
  "errorMessage": "新增分组配置失败：分组名称已存在"
}
```

**业务规则**:
- 分组名称必须唯一
- 同一分组内物模型+检查项组合不能重复
- 分组标识由系统自动生成，从1开始递增

---

### 1.2 编辑分组配置

**接口地址**: `POST /api/groupConfig/edit`

**接口描述**: 更新现有分组配置信息

**请求参数**:
```json
{
  "doubleRateSign": "1",
  "doubleRateName": "设备质量组-更新",
  "regionCodes": ["NA", "EU", "AS"],
  "countryCodes": ["US", "DE", "JP"],
  "modelCheckItems": [
    {
      "modelId": "MOD-001",
      "paramCode": "1001"
    },
    {
      "modelId": "MOD-002",
      "paramCode": "2001"
    }
  ]
}
```

**参数说明**:
- `doubleRateSign`: 分组标识，必填，用于标识要编辑的分组

**成功响应**:
```json
{
  "success": true,
  "data": "编辑分组配置成功"
}
```

**失败响应**:
```json
{
  "success": false,
  "errorMessage": "编辑分组配置失败：分组配置不存在"
}
```

---

### 1.3 删除分组配置

**接口地址**: `POST /api/groupConfig/delete`

**接口描述**: 批量删除分组配置

**请求参数**:
```json
["1", "2", "3"]
```

**参数说明**:
- 分组标识列表，支持批量删除

**成功响应**:
```json
{
  "success": true,
  "data": "删除分组配置成功"
}
```

**失败响应**:
```json
{
  "success": false,
  "errorMessage": "删除分组配置失败：分组配置不存在"
}
```

---

### 1.4 分页查询分组配置

**接口地址**: `POST /api/groupConfig/list?pageNum=1&pageSize=10`

**接口描述**: 分页查询分组配置列表，支持条件筛选

**请求参数**:
```json
{
  "doubleRateName": "设备质量"
}
```

**查询参数**:
- `pageNum`: 页码，默认1
- `pageSize`: 页大小，默认10

**参数说明**:
- `doubleRateName`: 分组名称关键字，支持模糊查询

**成功响应**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "doubleRateSign": "1",
        "doubleRateName": "设备质量组",
        "regionCount": 2,
        "countryCount": 3,
        "modelCount": 2,
        "checkItemCount": 2,
        "createTime": "2023-10-01 10:00:00",
        "updateTime": "2023-10-01 15:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

**字段说明**:
- `regionCount`: 大区数量
- `countryCount`: 国家数量
- `modelCount`: 物模型数量
- `checkItemCount`: 检查项数量

---

### 1.5 查询分组配置详情

**接口地址**: `GET /api/groupConfig/get/{doubleRateSign}`

**接口描述**: 根据分组标识查询分组配置详情

**路径参数**:
- `doubleRateSign`: 分组标识

**成功响应**:
```json
{
  "success": true,
  "data": {
    "doubleRateSign": "1",
    "doubleRateName": "设备质量组",
    "regionCount": 2,
    "countryCount": 3,
    "modelCount": 2,
    "checkItemCount": 2,
    "createTime": "2023-10-01 10:00:00",
    "updateTime": "2023-10-01 15:30:00"
  }
}
```

**失败响应**:
```json
{
  "success": false,
  "errorMessage": "分组配置不存在"
}
```

---

## 2. 数据字典接口

### 2.1 获取大区列表

**接口地址**: `GET /api/groupConfig/dict/regionList`

**接口描述**: 获取所有可用的大区列表

**请求参数**: 无

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "NA", "name": "北美大区"},
    {"code": "EU", "name": "欧洲大区"},
    {"code": "AS", "name": "亚洲大区"}
  ]
}
```

**字段说明**:
- `code`: 大区代码
- `name`: 大区名称

---

### 2.2 根据大区获取国家列表

**接口地址**: `GET /api/groupConfig/dict/countryList?regionCode=NA`

**接口描述**: 根据大区代码获取对应的国家列表

**查询参数**:
- `regionCode`: 大区代码，可选，不传则返回所有国家

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "US", "name": "美国"},
    {"code": "CA", "name": "加拿大"}
  ]
}
```

---

### 2.3 获取所有国家列表

**接口地址**: `GET /api/groupConfig/dict/allCountryList`

**接口描述**: 获取所有可用的国家列表

**请求参数**: 无

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "US", "name": "美国"},
    {"code": "DE", "name": "德国"},
    {"code": "JP", "name": "日本"}
  ]
}
```

---

### 2.4 根据关键字搜索物模型列表

**接口地址**: `GET /api/groupConfig/dict/modelList?keyword=温度`

**接口描述**: 根据关键字搜索物模型列表

**查询参数**:
- `keyword`: 搜索关键字，可选，支持模糊搜索

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "MOD-001", "name": "温度传感器"},
    {"code": "MOD-002", "name": "温度控制器"}
  ]
}
```

---

### 2.5 获取所有物模型列表

**接口地址**: `GET /api/groupConfig/dict/modelListAll`

**接口描述**: 获取所有可用的物模型列表

**请求参数**: 无

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "MOD-001", "name": "温度传感器"},
    {"code": "MOD-002", "name": "温度控制器"},
    {"code": "MOD-003", "name": "压力传感器"}
  ]
}
```

---

### 2.6 根据物模型获取检查项列表

**接口地址**: `GET /api/groupConfig/dict/checkItemList?modelId=MOD-001`

**接口描述**: 根据物模型ID获取对应的检查项列表

**查询参数**:
- `modelId`: 物模型ID，必填

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"code": "1001", "name": "温度异常检查"},
    {"code": "1002", "name": "温度范围检查"}
  ]
}
```

---

## 3. 导入导出接口

### 3.1 导出分组配置

**接口地址**: `GET /api/groupConfig/importExport/export`

**接口描述**: 导出所有分组配置数据为Excel文件

**请求参数**: 无

**响应**: 直接下载Excel文件

**文件格式**:
- 文件名: `分组配置_导出时间.xlsx`
- 包含列: 分组名称、大区列表、国家列表、物模型ID、物模型名称、检查项编码、检查项名称

---

### 3.2 下载导入模板

**接口地址**: `GET /api/groupConfig/importExport/template`

**接口描述**: 下载分组配置导入模板

**请求参数**: 无

**响应**: 直接下载Excel模板文件

**模板说明**:
- 文件名: `分组配置导入模板.xlsx`
- 包含示例数据和格式说明

---

### 3.3 导入分组配置

**接口地址**: `POST /api/groupConfig/importExport/import`

**接口描述**: 导入Excel文件中的分组配置数据

**请求参数**: `multipart/form-data`

**表单参数**:
- `dto`: 导入参数（JSON字符串）
- `file`: Excel文件

**导入参数示例**:
```json
{
  "doubleRateName": "新分组",
  "regionCodes": ["NA"],
  "countryCodes": ["US"]
}
```

**成功响应**:
```json
{
  "success": true,
  "data": {
    "successCount": 95,
    "errorCount": 5,
    "errorFilePath": "/temp/error_20231001.xlsx"
  }
}
```

**字段说明**:
- `successCount`: 成功导入的记录数
- `errorCount`: 导入失败的记录数
- `errorFilePath`: 错误数据文件路径（如果有错误）

**导入流程说明**:
1. 前端先填写分组名称、选择大区、国家
2. 确认区域配置后，上传Excel文件
3. Excel文件仅包含物模型code和检查项目code
4. 后端创建分组并保存配置明细

---

## 4. 分组详情接口

### 4.1 获取分组详情

**接口地址**: `GET /api/groupConfig/detail/detail/{doubleRateSign}`

**接口描述**: 获取分组的完整配置详情

**路径参数**:
- `doubleRateSign`: 分组标识

**成功响应**:
```json
{
  "success": true,
  "data": {
    "doubleRateSign": "1",
    "doubleRateName": "设备质量组",
    "regions": [
      {"code": "NA", "name": "北美大区"},
      {"code": "EU", "name": "欧洲大区"}
    ],
    "countries": [
      {"code": "US", "name": "美国"},
      {"code": "DE", "name": "德国"}
    ],
    "configDetails": [
      {
        "modelId": "MOD-001",
        "modelName": "温度传感器",
        "paramCode": "1001",
        "paramName": "温度异常检查"
      }
    ]
  }
}
```

---

## 5. 统计接口

### 5.1 获取可用分组列表

**接口地址**: `GET /api/groupConfig/stats/groups`

**接口描述**: 获取可用于统计的分组列表

**请求参数**: 无

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"doubleRateSign": "1", "doubleRateName": "设备质量组"},
    {"doubleRateSign": "2", "doubleRateName": "设备维护组"}
  ]
}
```

---

### 5.2 获取分组统计数据

**接口地址**: `GET /api/groupConfig/stats/data?groupId=1`

**接口描述**: 获取指定分组的统计数据

**查询参数**:
- `groupId`: 分组标识

**成功响应**:
```json
{
  "success": true,
  "data": {
    "accuracyRate": 95.5,
    "completenessRate": 92.3,
    "totalDevices": 1250,
    "abnormalDevices": 58
  }
}
```

**字段说明**:
- `accuracyRate`: 准确率（%）
- `completenessRate`: 完整率（%）
- `totalDevices`: 总设备数
- `abnormalDevices`: 异常设备数

---

## 6. 物模型检查项配置接口

### 6.1 保存物模型检查项配置

**接口地址**: `POST /api/groupConfig/modelConfig/save`

**接口描述**: 保存分组的物模型检查项配置

**请求参数**:
```json
{
  "doubleRateSign": "1",
  "configItems": [
    {
      "modelId": "MOD-001",
      "paramCode": "1001"
    },
    {
      "modelId": "MOD-002",
      "paramCode": "2001"
    }
  ]
}
```

**成功响应**:
```json
{
  "success": true,
  "data": "保存物模型检查项配置成功"
}
```

---

### 6.2 获取物模型检查项配置

**接口地址**: `GET /api/groupConfig/modelConfig/get/{doubleRateSign}`

**接口描述**: 获取分组的物模型检查项配置

**路径参数**:
- `doubleRateSign`: 分组标识

**成功响应**:
```json
{
  "success": true,
  "data": [
    {"modelId": "MOD-001", "paramCode": "1001"},
    {"modelId": "MOD-002", "paramCode": "2001"}
  ]
}
```

---

### 6.3 删除物模型检查项配置

**接口地址**: `DELETE /api/groupConfig/modelConfig/delete/{doubleRateSign}`

**接口描述**: 删除分组的物模型检查项配置

**路径参数**:
- `doubleRateSign`: 分组标识

**成功响应**:
```json
{
  "success": true,
  "data": "删除物模型检查项配置成功"
}
```

---

## 7. 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 00500 | 系统内部错误 | 联系后端开发人员 |
| 00400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 00401 | 未授权访问 | 检查用户登录状态和权限 |
| 00404 | 资源不存在 | 检查请求的资源ID是否正确 |

## 8. 前端开发建议

### 8.1 数据校验
- 分组名称长度限制：1-20个字符
- 大区和国家至少各选择一个
- 物模型检查项至少配置一组

### 8.2 用户体验
- 新增/编辑时实时校验分组名称唯一性
- 物模型选择支持搜索功能
- 检查项根据物模型动态加载
- 导入时显示进度和结果统计

### 8.3 错误处理
- 网络错误时显示友好提示
- 业务错误时显示具体错误信息
- 表单验证失败时高亮错误字段

### 8.4 性能优化
- 数据字典数据缓存到本地
- 分页查询时使用虚拟滚动
- 大量数据导入时使用分片上传

## 9. 接口调用示例

### 9.1 JavaScript/TypeScript 示例

```typescript
// 新增分组配置
async function addGroupConfig(config: GroupConfigDTO) {
  try {
    const response = await fetch('/api/groupConfig/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('新增成功，分组标识：', result.data.doubleRateSign);
      return result.data;
    } else {
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error('新增失败：', error);
    throw error;
  }
}

// 分页查询分组配置
async function getGroupConfigList(query: any, pageNum: number, pageSize: number) {
  try {
    const response = await fetch(`/api/groupConfig/list?pageNum=${pageNum}&pageSize=${pageSize}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error('查询失败：', error);
    throw error;
  }
}

// 获取大区列表
async function getRegionList() {
  try {
    const response = await fetch('/api/groupConfig/dict/regionList');
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error('获取大区列表失败：', error);
    throw error;
  }
}
```

### 9.2 Vue.js 示例

```vue
<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="分组名称" prop="doubleRateName">
        <el-input v-model="form.doubleRateName" maxlength="20" />
      </el-form-item>
      
      <el-form-item label="大区" prop="regionCodes">
        <el-select v-model="form.regionCodes" multiple placeholder="请选择大区">
          <el-option
            v-for="region in regionList"
            :key="region.code"
            :label="region.name"
            :value="region.code"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="国家" prop="countryCodes">
        <el-select v-model="form.countryCodes" multiple placeholder="请选择国家">
          <el-option
            v-for="country in countryList"
            :key="country.code"
            :label="country.name"
            :value="country.code"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  setup() {
    const formRef = ref()
    const regionList = ref([])
    const countryList = ref([])
    
    const form = reactive({
      doubleRateName: '',
      regionCodes: [],
      countryCodes: [],
      modelCheckItems: []
    })
    
    const rules = {
      doubleRateName: [
        { required: true, message: '请输入分组名称', trigger: 'blur' },
        { max: 20, message: '分组名称不能超过20个字符', trigger: 'blur' }
      ],
      regionCodes: [
        { required: true, message: '请选择大区', trigger: 'change' }
      ],
      countryCodes: [
        { required: true, message: '请选择国家', trigger: 'change' }
      ]
    }
    
    // 获取大区列表
    const getRegionList = async () => {
      try {
        const response = await fetch('/api/groupConfig/dict/regionList')
        const result = await response.json()
        
        if (result.success) {
          regionList.value = result.data
        }
      } catch (error) {
        ElMessage.error('获取大区列表失败')
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      try {
        const valid = await formRef.value.validate()
        if (!valid) return
        
        const response = await fetch('/api/groupConfig/add', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(form),
        })
        
        const result = await response.json()
        
        if (result.success) {
          ElMessage.success('新增成功')
          // 重置表单
          formRef.value.resetFields()
        } else {
          ElMessage.error(result.errorMessage)
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
    
    onMounted(() => {
      getRegionList()
    })
    
    return {
      formRef,
      form,
      rules,
      regionList,
      countryList,
      submitForm
    }
  }
}
</script>
```

---

## 文档版本信息

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2023-10-01 | 初始版本创建 | 系统 |

---

**文档说明**: 本文档为前端开发人员提供分组配置功能的完整API接口说明，包含接口地址、请求参数、响应格式、错误处理等详细信息，以及前端开发的最佳实践建议。


