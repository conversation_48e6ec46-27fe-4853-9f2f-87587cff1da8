# 分组配置功能详细设计文档

## 1. 系统概述

### 1.1 功能简介
分组配置功能是WDCL RC Tool API系统的核心模块，用于管理设备质量检查的分组配置。该功能支持创建、编辑、删除分组，配置大区、国家范围，以及物模型检查项组合，为设备质量管理系统提供灵活的分组策略配置。

### 1.2 业务价值
- **灵活配置**: 支持按大区、国家、物模型等维度灵活分组
- **统一管理**: 集中管理设备质量检查规则和配置
- **数据隔离**: 支持多租户数据隔离，确保数据安全
- **批量操作**: 支持Excel导入导出，提高配置效率

### 1.3 核心特性
- 分组标识自动生成（从1开始递增）
- 大区、国家多选配置
- 物模型检查项动态配置
- Excel导入导出支持
- 分组详情查看和统计
- 数据校验和唯一性检查

## 2. 系统架构设计

### 2.1 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Controller)                        │
├─────────────────────────────────────────────────────────────┤
│  GroupConfigController          GroupConfigDictController    │
│  GroupConfigDetailController    GroupConfigStatsController   │
│  GroupConfigImportExportController                          │
│  ModelCheckItemConfigController                              │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Service)                       │
├─────────────────────────────────────────────────────────────┤
│  GroupConfigService              GroupConfigDictService      │
│  GroupConfigDetailService        GroupConfigStatsService     │
│  GroupConfigImportExportService  ModelCheckItemConfigService │
│  GroupConfigIdGeneratorService                              │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (DAO)                          │
├─────────────────────────────────────────────────────────────┤
│  OrsCountryDoubleRateConfigMapper OrsDoubleRateConfigMapper │
│  GroupConfigDictMapper                                       │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (Database)                     │
├─────────────────────────────────────────────────────────────┤
│  ors_country_double_rate_config  ors_double_rate_config     │
│  ors_country_region_sync         ors_model_division         │
│  ors_model_properties_config                                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块结构
```
rc-api/src/main/java/com/rc/admin/groupconfig/
├── controller/           # REST API控制器
│   ├── GroupConfigController.java              # 分组配置管理
│   ├── GroupConfigDictController.java          # 数据字典
│   ├── GroupConfigImportExportController.java  # 导入导出
│   ├── GroupConfigDetailController.java        # 分组详情
│   ├── GroupConfigStatsController.java         # 统计接口
│   └── ModelCheckItemConfigController.java     # 物模型检查项配置
├── service/             # 业务逻辑层
│   ├── GroupConfigService.java                 # 分组配置服务
│   ├── GroupConfigDictService.java             # 数据字典服务
│   ├── GroupConfigImportExportService.java     # 导入导出服务
│   ├── GroupConfigDetailService.java           # 分组详情服务
│   ├── GroupConfigStatsService.java            # 统计服务
│   ├── ModelCheckItemConfigService.java        # 物模型检查项配置服务
│   ├── GroupConfigIdGeneratorService.java      # 分组标识生成服务
│   └── impl/            # 服务实现类
├── dao/                 # 数据访问层
│   ├── OrsCountryDoubleRateConfigMapper.java   # 主表Mapper
│   ├── OrsDoubleRateConfigMapper.java          # 明细表Mapper
│   └── GroupConfigDictMapper.java              # 数据字典Mapper
├── entity/              # 实体类
│   ├── OrsCountryDoubleRateConfig.java         # 主表实体
│   └── OrsDoubleRateConfig.java                # 明细表实体
├── dto/                 # 数据传输对象
│   ├── GroupConfigDTO.java                     # 分组配置DTO
│   ├── ImportGroupConfigDTO.java               # 导入DTO
│   └── ModelCheckItemConfigDTO.java            # 物模型检查项配置DTO
├── vo/                  # 视图对象
│   ├── GroupConfigVO.java                      # 分组配置VO
│   ├── GroupConfigDetailVO.java                # 分组详情VO
│   ├── GroupStatsVO.java                       # 统计VO
│   ├── AddGroupConfigResultVO.java             # 新增结果VO
│   ├── ImportResultVO.java                     # 导入结果VO
│   ├── ExportGroupConfigVO.java                # 导出VO
│   ├── TemplateVO.java                         # 模板VO
│   └── OptionVO.java                           # 选项VO
└── util/                # 工具类
    └── GroupConfigValidator.java                # 数据校验工具
```

## 3. 数据库设计

### 3.1 核心数据表

#### 3.1.1 ors_country_double_rate_config (分组配置主表)
```sql
CREATE TABLE `ors_country_double_rate_config` (
  `double_rate_sign` varchar(50) NOT NULL COMMENT '分组标识',
  `country_code` varchar(500) NOT NULL COMMENT '国家代码列表(逗号分隔)',
  `region_code` varchar(500) NOT NULL COMMENT '大区代码列表(逗号分隔)',
  `double_rate_name` varchar(100) NOT NULL COMMENT '分组名称',
  PRIMARY KEY (`double_rate_sign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分组配置主表';
```

#### 3.1.2 ors_double_rate_config (分组配置明细表)
```sql
CREATE TABLE `ors_double_rate_config` (
  `double_rate_sign` varchar(50) NOT NULL COMMENT '分组标识',
  `model_id` varchar(100) NOT NULL COMMENT '物模型ID',
  `param_code` int NOT NULL COMMENT '检查项编码',
  PRIMARY KEY (`double_rate_sign`,`model_id`,`param_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分组配置明细表';
```

### 3.2 关联数据表

#### 3.2.1 ors_country_region_sync (国家大区同步表)
- 提供大区和国家的基础数据
- 支持按大区筛选国家列表

#### 3.2.2 ors_model_division (物模型事业部表)
- 提供物模型的基础信息
- 包含物模型的中文名称

#### 3.2.3 ors_model_properties_config (物模型属性配置表)
- 提供检查项的基础信息
- 支持按物模型查询检查项列表

### 3.3 索引设计
```sql
-- 分组配置主表索引
CREATE INDEX idx_country_double_rate_name ON ors_country_double_rate_config(double_rate_name);
CREATE INDEX idx_country_tenant_id ON ors_country_double_rate_config(tenant_id);

-- 分组配置明细表索引
CREATE INDEX idx_double_rate_model ON ors_double_rate_config(model_id, param_code);
CREATE INDEX idx_double_rate_tenant_id ON ors_double_rate_config(tenant_id);
```

## 4. 核心业务逻辑

### 4.1 分组标识生成逻辑

#### 4.1.1 生成规则
- 从数字1开始递增
- 使用字符串格式存储，支持前导零
- 使用ReentrantLock确保并发安全

#### 4.1.2 实现代码
```java
@Service
public class GroupConfigIdGeneratorServiceImpl implements GroupConfigIdGeneratorService {
    
    private final ReentrantLock lock = new ReentrantLock();
    
    @Override
    public String generateNextGroupId() {
        lock.lock();
        try {
            String maxId = countryDoubleRateConfigMapper.selectMaxDoubleRateSign();
            
            if (maxId == null || maxId.trim().isEmpty()) {
                return "1";
            }
            
            int nextId = Integer.parseInt(maxId) + 1;
            return String.valueOf(nextId);
        } finally {
            lock.unlock();
        }
    }
}
```

### 4.2 分组配置保存逻辑

#### 4.2.1 主表保存
```java
private void saveMainConfig(String doubleRateSign, GroupConfigDTO dto) {
    OrsCountryDoubleRateConfig entity = new OrsCountryDoubleRateConfig();
    entity.setDoubleRateSign(doubleRateSign);
    entity.setDoubleRateName(dto.getDoubleRateName());
    
    // 将列表转换为逗号分隔的字符串
    entity.setRegionCodes(String.join(",", dto.getRegionCodes()));
    entity.setCountryCodes(String.join(",", dto.getCountryCodes()));
    
    // 设置审计字段
    entity.setCreateBy(getCurrentUserId());
    entity.setCreateTime(new Date());
    entity.setTenantId(getCurrentTenantId());
    
    countryDoubleRateConfigMapper.insert(entity);
}
```

#### 4.2.2 明细表保存
```java
private void saveDetailConfig(String doubleRateSign, List<GroupConfigDTO.ModelCheckItemDTO> items) {
    for (GroupConfigDTO.ModelCheckItemDTO item : items) {
        OrsDoubleRateConfig entity = new OrsDoubleRateConfig();
        entity.setDoubleRateSign(doubleRateSign);
        entity.setModelId(item.getModelId());
        entity.setParamCode(Integer.parseInt(item.getParamCode()));
        
        // 设置审计字段
        entity.setCreateBy(getCurrentUserId());
        entity.setCreateTime(new Date());
        entity.setTenantId(getCurrentTenantId());
        
        doubleRateConfigMapper.insert(entity);
    }
}
```

### 4.3 数据校验逻辑

#### 4.3.1 分组名称唯一性校验
```java
private void validateGroupNameUnique(String groupName, String excludeId) {
    QueryWrapper<OrsCountryDoubleRateConfig> wrapper = new QueryWrapper<>();
    wrapper.eq("double_rate_name", groupName);
    
    if (StringUtils.isNotBlank(excludeId)) {
        wrapper.ne("double_rate_sign", excludeId);
    }
    
    if (countryDoubleRateConfigMapper.selectCount(wrapper) > 0) {
        throw new RuntimeException("分组名称已存在");
    }
}
```

#### 4.3.2 物模型检查项组合唯一性校验
```java
private void validateConfigItem(String doubleRateSign, ModelCheckItemConfigDTO.ConfigItemDTO item) {
    QueryWrapper<OrsDoubleRateConfig> wrapper = new QueryWrapper<>();
    wrapper.eq("double_rate_sign", doubleRateSign);
    wrapper.eq("model_id", item.getModelId());
    wrapper.eq("param_code", Integer.parseInt(item.getParamCode()));
    
    if (doubleRateConfigMapper.selectCount(wrapper) > 0) {
        throw new RuntimeException("物模型与检查项的组合已存在");
    }
}
```

## 5. API接口设计

### 5.1 分组配置管理接口

#### 5.1.1 新增分组配置
```http
POST /api/groupConfig/add
Content-Type: application/json

{
  "doubleRateName": "设备质量组",
  "regionCodes": ["NA", "EU"],
  "countryCodes": ["US", "DE"],
  "modelCheckItems": [
    {
      "modelId": "MOD-001",
      "paramCode": "1001"
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "doubleRateSign": "1",
    "doubleRateName": "设备质量组",
    "message": "新增分组配置成功"
  }
}
```

#### 5.1.2 编辑分组配置
```http
POST /api/groupConfig/edit
Content-Type: application/json

{
  "doubleRateSign": "1",
  "doubleRateName": "设备质量组-更新",
  "regionCodes": ["NA", "EU", "AS"],
  "countryCodes": ["US", "DE", "JP"],
  "modelCheckItems": [
    {
      "modelId": "MOD-001",
      "paramCode": "1001"
    },
    {
      "modelId": "MOD-002",
      "paramCode": "2001"
    }
  ]
}

Response:
{
  "success": true,
  "data": "编辑分组配置成功"
}
```

#### 5.1.3 删除分组配置
```http
POST /api/groupConfig/delete
Content-Type: application/json

["1", "2", "3"]

Response:
{
  "success": true,
  "data": "删除分组配置成功"
}
```

#### 5.1.4 分页查询分组配置
```http
POST /api/groupConfig/list?pageNum=1&pageSize=10
Content-Type: application/json

{
  "doubleRateName": "设备质量"
}

Response:
{
  "success": true,
  "data": {
    "records": [
      {
        "doubleRateSign": "1",
        "doubleRateName": "设备质量组",
        "regionCount": 2,
        "countryCount": 3,
        "modelCount": 2,
        "checkItemCount": 2,
        "createTime": "2023-10-01 10:00:00",
        "updateTime": "2023-10-01 15:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

### 5.2 数据字典接口

#### 5.2.1 获取大区列表
```http
GET /api/groupConfig/dict/regionList

Response:
{
  "success": true,
  "data": [
    {"code": "NA", "name": "北美大区"},
    {"code": "EU", "name": "欧洲大区"},
    {"code": "AS", "name": "亚洲大区"}
  ]
}
```

#### 5.2.2 根据大区获取国家列表
```http
GET /api/groupConfig/dict/countryList?regionCode=NA

Response:
{
  "success": true,
  "data": [
    {"code": "US", "name": "美国"},
    {"code": "CA", "name": "加拿大"}
  ]
}
```

#### 5.2.3 获取物模型列表
```http
GET /api/groupConfig/dict/modelList?keyword=温度

Response:
{
  "success": true,
  "data": [
    {"code": "MOD-001", "name": "温度传感器"},
    {"code": "MOD-002", "name": "温度控制器"}
  ]
}
```

#### 5.2.4 根据物模型获取检查项列表
```http
GET /api/groupConfig/dict/checkItemList?modelId=MOD-001

Response:
{
  "success": true,
  "data": [
    {"code": "1001", "name": "温度异常检查"},
    {"code": "1002", "name": "温度范围检查"}
  ]
}
```

### 5.3 导入导出接口

#### 5.3.1 导出分组配置
```http
GET /api/groupConfig/importExport/export

Response: Excel文件下载
```

#### 5.3.2 下载导入模板
```http
GET /api/groupConfig/importExport/template

Response: Excel模板文件下载
```

#### 5.3.3 导入分组配置
```http
POST /api/groupConfig/importExport/import
Content-Type: multipart/form-data

dto: {
  "doubleRateName": "新分组",
  "regionCodes": ["NA"],
  "countryCodes": ["US"]
}
file: Excel文件

Response:
{
  "success": true,
  "data": {
    "successCount": 95,
    "errorCount": 5,
    "errorFilePath": "/temp/error_20231001.xlsx"
  }
}
```

### 5.4 分组详情接口

#### 5.4.1 获取分组详情
```http
GET /api/groupConfig/detail/detail/1

Response:
{
  "success": true,
  "data": {
    "doubleRateSign": "1",
    "doubleRateName": "设备质量组",
    "regions": [
      {"code": "NA", "name": "北美大区"},
      {"code": "EU", "name": "欧洲大区"}
    ],
    "countries": [
      {"code": "US", "name": "美国"},
      {"code": "DE", "name": "德国"}
    ],
    "configDetails": [
      {
        "modelId": "MOD-001",
        "modelName": "温度传感器",
        "paramCode": "1001",
        "paramName": "温度异常检查"
      }
    ]
  }
}
```

### 5.5 统计接口

#### 5.5.1 获取可用分组列表
```http
GET /api/groupConfig/stats/groups

Response:
{
  "success": true,
  "data": [
    {"doubleRateSign": "1", "doubleRateName": "设备质量组"},
    {"doubleRateSign": "2", "doubleRateName": "设备维护组"}
  ]
}
```

#### 5.5.2 获取分组统计数据
```http
GET /api/groupConfig/stats/data?groupId=1

Response:
{
  "success": true,
  "data": {
    "accuracyRate": 95.5,
    "completenessRate": 92.3,
    "totalDevices": 1250,
    "abnormalDevices": 58
  }
}
```

## 6. 数据流转设计

### 6.1 新增分组配置流程
```
1. 前端提交分组配置数据
   ↓
2. Controller接收请求，调用Service
   ↓
3. Service进行数据校验
   - 分组名称唯一性校验
   - 必填字段校验
   ↓
4. 生成分组标识
   - 获取当前最大标识
   - 递增生成新标识
   ↓
5. 保存主表数据
   - 保存分组基础信息
   - 保存大区、国家配置
   ↓
6. 保存明细表数据
   - 保存物模型检查项配置
   - 校验组合唯一性
   ↓
7. 返回成功结果
   - 返回生成的分组标识
   - 返回成功消息
```

### 6.2 分页查询流程
```
1. 前端提交查询条件
   ↓
2. Controller接收查询参数
   ↓
3. Service构建查询条件
   - 分组名称模糊查询
   - 租户数据隔离
   ↓
4. 执行分页查询
   - 查询主表数据
   - 统计关联数据数量
   ↓
5. 数据转换处理
   - 计算大区、国家数量
   - 计算模型、检查项数量
   ↓
6. 返回分页结果
   - 返回分页数据
   - 返回总数信息
```

### 6.3 导入导出流程
```
1. 导出流程
   前端请求导出 → 查询分组配置数据 → 转换为Excel格式 → 设置响应头 → 返回文件流

2. 导入流程
   前端上传文件 → 解析Excel数据 → 数据校验 → 保存分组配置 → 返回导入结果
```

## 7. 异常处理设计

### 7.1 业务异常
```java
// 分组名称重复异常
if (countryDoubleRateConfigMapper.selectCount(wrapper) > 0) {
    throw new RuntimeException("分组名称已存在");
}

// 物模型检查项组合重复异常
if (doubleRateConfigMapper.selectCount(wrapper) > 0) {
    throw new RuntimeException("物模型与检查项的组合已存在");
}

// 数据不存在异常
if (result == null) {
    return Response.failError("分组配置不存在");
}
```

### 7.2 系统异常
```java
try {
    // 业务逻辑处理
    String doubleRateSign = groupConfigService.add(dto);
    return Response.success(result);
} catch (Exception e) {
    log.error("新增分组配置失败", e);
    return Response.failError("新增分组配置失败：" + e.getMessage());
}
```

### 7.3 参数校验异常
```java
@NotBlank(message = "分组名称不能为空")
@Size(max = 20, message = "分组名称不能超过20个字符")
private String doubleRateName;

@NotEmpty(message = "请至少选择一个大区")
private List<String> regionCodes;
```

## 8. 性能优化设计

### 8.1 数据库查询优化
- 添加必要的数据库索引
- 使用分页查询避免大数据量查询
- 优化关联查询SQL语句
- 使用批量操作减少数据库访问

### 8.2 缓存策略
- 数据字典数据使用Redis缓存
- 分组配置数据按需缓存
- 统计计算结果缓存

### 8.3 异步处理
- 大数据量导入使用异步处理
- 统计计算使用异步任务
- 文件处理使用异步队列

## 9. 安全设计

### 9.1 权限控制
- 使用Apache Shiro进行身份认证
- 基于角色的访问控制
- 操作权限验证

### 9.2 数据安全
- 多租户数据隔离
- 敏感数据脱敏处理
- 操作日志记录

### 9.3 输入验证
- 参数长度限制
- SQL注入防护
- XSS攻击防护

## 10. 监控与日志

### 10.1 操作日志
```java
@OperationLog("新增分组配置")
@PostMapping("/add")
public Response add(@RequestBody GroupConfigDTO dto) {
    // 业务逻辑
}
```

### 10.2 性能监控
- 接口响应时间监控
- 数据库查询性能监控
- 系统资源使用监控

### 10.3 异常监控
- 业务异常统计
- 系统异常告警
- 错误日志分析

## 11. 部署配置

### 11.1 环境配置
```yaml
# application-dev.yml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: ****************************************
          username: root
          password: dev_password

# application-prod.yml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: **********************************
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
```

### 11.2 日志配置
```xml
<!-- logback-spring.xml -->
<springProfile name="prod">
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/groupconfig.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/groupconfig.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
</springProfile>
```

## 12. 测试策略

### 12.1 单元测试
- Service层业务逻辑测试
- DAO层数据访问测试
- 工具类功能测试

### 12.2 集成测试
- API接口端到端测试
- 数据库事务测试
- 异常处理测试

### 12.3 性能测试
- 并发用户测试
- 大数据量测试
- 响应时间测试

## 13. 维护与扩展

### 13.1 数据维护
- 定期数据备份
- 数据一致性检查
- 性能监控和调优

### 13.2 功能扩展
- 支持更多分组维度
- 增加高级搜索功能
- 支持分组模板功能

### 13.3 版本升级
- 向后兼容性保证
- 数据迁移脚本
- 升级回滚方案

---

## 文档版本信息

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2023-10-01 | 初始版本创建 | 系统 |
| v1.1 | 2023-10-01 | 完善接口设计 | 系统 |
| v1.2 | 2023-10-01 | 补充异常处理 | 系统 |

---

**文档说明**: 本文档详细描述了分组配置功能的系统设计，包括架构设计、数据库设计、接口设计、业务逻辑等各个方面，为开发团队提供完整的技术指导。


