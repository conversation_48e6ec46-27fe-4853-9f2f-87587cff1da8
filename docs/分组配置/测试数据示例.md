# 分组配置导入测试数据示例

## Excel文件格式要求

### 1. 文件格式
- 支持 `.xlsx` 和 `.xls` 格式
- 必须包含表头行
- 数据从第二行开始

### 2. 列结构
| 物模型ID | 检查项编码 |
|---------|-----------|
| 示例数据1 | 1001      |
| 示例数据2 | 1002      |
| 示例数据3 | 1003      |

### 3. 列头要求
- 第一列：**物模型ID** - 必须包含这个标题
- 第二列：**检查项编码** - 必须包含这个标题

## 测试数据示例

### 示例1：基本数据
```
物模型ID,检查项编码
MODEL001,1001
MODEL002,1002
MODEL003,1003
```

### 示例2：实际业务数据
```
物模型ID,检查项编码
ZZ001,2001
ZZ002,2002
ZZ003,2003
```

## 创建测试文件的步骤

### 方法1：使用Excel软件
1. 打开Excel
2. 在第一行输入：`物模型ID` 和 `检查项编码`
3. 从第二行开始输入测试数据
4. 保存为 `.xlsx` 格式

### 方法2：使用在线工具
1. 访问在线Excel编辑器
2. 按照上述格式输入数据
3. 下载为Excel格式

### 方法3：使用代码生成
```java
// 使用EasyExcel生成测试文件
List<TemplateVO> testData = Arrays.asList(
    new TemplateVO("TEST001", "1001"),
    new TemplateVO("TEST002", "1002"),
    new TemplateVO("TEST003", "1003")
);

EasyExcel.write("test_data.xlsx", TemplateVO.class)
    .sheet("测试数据")
    .doWrite(testData);
```

## 常见问题

### 1. 列头不匹配
- 确保列头完全匹配：`物模型ID` 和 `检查项编码`
- 注意大小写和空格

### 2. 数据为空
- 确保每行都有数据
- 检查是否有隐藏的空行

### 3. 文件格式错误
- 确保文件是有效的Excel格式
- 避免使用CSV格式

## 测试步骤

1. **下载模板**：使用API下载标准模板
2. **填写数据**：在模板中填入测试数据
3. **保存文件**：确保文件格式正确
4. **测试导入**：使用导入API进行测试
5. **检查结果**：验证导入是否成功

## 验证要点

- [ ] Excel文件包含正确的列头
- [ ] 数据行不为空
- [ ] 文件格式为.xlsx或.xls
- [ ] 文件大小大于0字节
- [ ] 数据格式符合业务要求

