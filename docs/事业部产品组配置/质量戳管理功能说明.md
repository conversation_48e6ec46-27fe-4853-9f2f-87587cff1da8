# 质量戳管理功能说明

## 功能概述

质量戳管理功能允许用户在编辑物模型信息时，不仅可以选择现有的质量戳，还可以新增自定义的质量戳检查项。新增的质量戳会同时保存到 `orc_iot_model_abnormal_bit_info` 表中，确保数据的完整性和一致性。

## 接口说明

### 1. 编辑产品组配置（集成质量戳管理）

**接口地址**: `POST /api/divisionalProductGroup/edit`

**请求参数**:
```json
{
  "originalModelId": "MODEL_001",
  "modelId": "MODEL_001",
  "divisionCode": "DIV_001",
  "divisionName": "事业部A",
  "productGroupCode": "PG_001",
  "productGroupName": "产品组A",
  "newQualityStamps": [
    {
      "modelId": "MODEL_001",
      "bitKey": "quality_check_1",
      "bitDesc": "质量检查项1",
      "bitIndex": 1,
      "maxValue": "100",
      "minValue": "0"
    }
  ],
  "selectedQualityStamps": ["quality_check_1", "quality_check_2"]
}
```

**业务逻辑**:
- 如果 `newQualityStamps` 不为空，系统会先批量新增质量戳
- 如果 `selectedQualityStamps` 不为空，系统会生成质量戳脚本
- 最后更新产品组配置信息

### 2. 新增单个质量戳（独立接口）

**请求参数**:
```json
{
  "modelId": "MODEL_001",
  "bitKey": "quality_check_1",
  "bitDesc": "质量检查项1",
  "bitIndex": 1,
  "maxValue": "100",
  "minValue": "0",
  "dictId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "data": null,
  "errorCode": null,
  "errorMessage": null
}
```

### 2. 批量新增质量戳

**接口地址**: `POST /api/divisionalProductGroup/qualityStamps/batchAdd`

**请求参数**:
```json
[
  {
    "modelId": "MODEL_001",
    "bitKey": "quality_check_1",
    "bitDesc": "质量检查项1",
    "bitIndex": 1,
    "maxValue": "100",
    "minValue": "0"
  },
  {
    "modelId": "MODEL_001",
    "bitKey": "quality_check_2",
    "bitDesc": "质量检查项2",
    "bitIndex": 2,
    "maxValue": "200",
    "minValue": "50"
  }
]
```

### 3. 获取所有质量戳（包括新增的）

**接口地址**: `GET /api/divisionalProductGroup/qualityStamps/all/{modelId}`

### 4. 确认质量戳状态

**接口地址**: `POST /api/divisionalProductGroup/confirmQualityStatus/{modelId}`

**功能说明**: 将质量戳状态从"待生效"改为"生效"

**业务规则**:
- 只有"待生效"状态的质量戳才能确认生效
- 必须已配置质量戳脚本才能确认生效
- 确认后状态变为"生效"，不可再修改

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "bitKey": "quality_check_1",
      "bitDesc": "质量检查项1",
      "bitIndex": 1,
      "maxValue": "100",
      "minValue": "0",
      "dictId": 1
    },
    {
      "bitKey": "quality_check_2",
      "bitDesc": "质量检查项2",
      "bitIndex": 2,
      "maxValue": "200",
      "minValue": "50",
      "dictId": null
    }
  ]
}
```

## 业务规则

### 1. 编辑时的质量戳处理规则
- **集成处理**: 质量戳新增和产品组配置修改在同一个编辑请求中处理
- **顺序执行**: 先处理质量戳新增，再处理产品组配置修改
- **事务保证**: 如果质量戳新增失败，整个编辑操作会回滚

### 2. 质量戳状态流转规则
- **初始状态**: 新增或编辑产品组配置时，质量戳状态为"待生效"(0)
- **确认生效**: 通过"确认"操作将状态改为"生效"(1)
- **状态限制**: 只有"待生效"状态才能进行"确认"操作
- **生效后限制**: "生效"状态后不可再修改质量戳配置

### 2. 位序号唯一性
- 同一物模型下的位序号必须唯一
- 新增质量戳时会自动检查位序号是否已存在
- 如果位序号冲突，会抛出异常提示

### 3. 数据完整性
- 新增质量戳会同时保存到 `orc_iot_model_abnormal_bit_info` 表
- 确保质量戳数据与物模型配置的一致性

### 4. 参数校验
- `modelId`: 必填，物模型ID
- `bitKey`: 必填，质量戳键值
- `bitDesc`: 必填，质量戳描述
- `bitIndex`: 必填，位序号
- `maxValue`, `minValue`, `dictId`: 可选

## 使用流程

### 1. 编辑物模型配置
1. 选择物模型ID
2. 系统自动加载该物模型的现有质量戳
3. 用户可以选择现有质量戳或新增自定义质量戳

### 2. 编辑时的质量戳处理
1. **如果有新增质量戳**：在编辑请求中包含 `newQualityStamps` 字段，系统会自动批量新增质量戳
2. **如果没有新增质量戳**：按照原有逻辑正常修改产品组配置
3. **质量戳选择**：在编辑请求中包含 `selectedQualityStamps` 字段，系统会生成质量戳脚本

### 3. 配置质量戳
1. 选择需要使用的质量戳（包括新增的）
2. 系统自动生成质量戳脚本
3. 保存物模型配置

### 4. 确认质量戳状态
1. 配置完成后，质量戳状态为"待生效"
2. 检查配置无误后，点击"确认"按钮
3. 系统将状态改为"生效"，质量戳正式生效
4. 生效后不可再修改质量戳配置

## 技术实现

### 1. 数据层
- **复用现有实体**: `com.rc.admin.easyapi.entity.IotModelAbnormalBitInfo` - 项目中已存在的质量戳实体类
- **扩展现有Mapper**: `com.rc.admin.easyapi.dao.IotModelAbnormalBitInfoMapper` - 在现有Mapper基础上添加新的查询方法
- **支持功能**: 位序号唯一性检查、最大位序号查询、按物模型ID查询质量戳列表

### 2. 业务层
- `addQualityStamp`: 新增单个质量戳
- `batchAddQualityStamps`: 批量新增质量戳
- `getAllQualityStampsByModelId`: 获取所有质量戳

### 3. 控制层
- 提供RESTful API接口
- 统一的异常处理和响应格式
- 操作日志记录

## 注意事项

1. **位序号管理**: 建议在新增质量戳时，位序号从1开始递增，避免冲突
2. **数据同步**: 新增质量戳后，需要刷新相关缓存和列表数据
3. **权限控制**: 质量戳新增操作需要相应的权限控制
4. **数据备份**: 建议在新增质量戳前进行数据备份

## 代码复用说明

### 1. 复用现有组件
- **实体类**: 使用 `rc-api` 模块下 `easyapi` 包中已存在的 `IotModelAbnormalBitInfo` 实体
- **Mapper接口**: 扩展现有的 `IotModelAbnormalBitInfoMapper` 接口，添加新的查询方法
- **避免重复**: 不创建重复的实体类和Mapper，保持代码的一致性和可维护性

### 2. 扩展方式
- 在现有Mapper接口中添加新的 `@Select` 注解方法
- 使用MyBatis Plus的 `BaseMapper` 提供的基础CRUD操作
- 保持与现有代码风格和架构的一致性

## 扩展功能

未来可以考虑添加以下功能：
1. 质量戳编辑和删除
2. 质量戳模板管理
3. 质量戳数据导入导出
4. 质量戳使用统计和分析
