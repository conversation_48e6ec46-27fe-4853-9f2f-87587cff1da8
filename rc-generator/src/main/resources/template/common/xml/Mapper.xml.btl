<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${basicsConfig.packagePath}.dao.${tableInfo.mapperName}">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="${basicsConfig.packagePath}.model.${basicsConfig.modelName}">
        <% for(field in tableInfo.fields){ %>
        <% if(field.keyIdentityFlag) { %>
        <id column="${field.name}" property="${field.propertyName}" />
        <% } else { %>
        <result column="${field.name}" property="${field.propertyName}" />
        <% } %>
        <% } %>
    </resultMap>
    <% if(basicsConfig.isGeneratorMethodsSelect){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    <select id="select" resultType="${basicsConfig.packagePath}.model.${basicsConfig.modelName}">
        select t.id, t.parent_id<% for(item in tableConfig){ %>, ${GeneratorXmlUtil.getSelectField(item)}<% } %>
        from ${tableInfo.name} t
        <% for(item in tableConfig){ %>
        <% if(item.name == 'create_user' || item.name == 'edit_user'){ %>
        left join sys_user su_${item.name} on su_${item.name}.id = t.${item.name}
        <% } %>
        <% } %>
        <where>
            \${ew.sqlSegment}
        </where>
    </select>

    <select id="selectAll" resultType="com.rc.admin.common.core.common.tree.Tree">
        select t.id, t.parent_id, t.name as title, t.id as key
        from ${tableInfo.name} t
        order by t.order_no
    </select>
    <% } else { %>
    <select id="select" resultType="${basicsConfig.packagePath}.model.${basicsConfig.modelName}">
        select t.id<% for(item in tableConfig){ %>, ${GeneratorXmlUtil.getSelectField(item)}<% } %>
        from ${tableInfo.name} t
        <% for(item in tableConfig){ %>
        <% if(item.name == 'create_user' || item.name == 'edit_user'){ %>
        left join sys_user su_${item.name} on su_${item.name}.id = t.${item.name}
        <% } %>
        <% } %>
        <where>
            \${ew.sqlSegment}
        </where>
    </select>
    <% } %>

    <%index = 0;%>
    <select id="getById" resultType="${basicsConfig.packagePath}.model.${basicsConfig.modelName}">
        select <% for(item in tableInfo.fields){ %>${index == 0 ? '' : ', '}t.${item.name}<% index++;} %>
        from ${tableInfo.name} t
        where t.id = #{id}
    </select>
    <% } %>
    <% if(basicsConfig.isGeneratorMethodsSave){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    <select id="getMaxOrderNo" resultType="java.lang.Integer">
        select COALESCE(max(order_no), 0)
        from ${tableInfo.name} t
        where t.parent_id = #{parentId}
    </select>
    <update id="updateOrderBatch">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update ${tableInfo.name}
            <set>
                parent_id = #{item.parentId},
                order_no = #{item.orderNo}
            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <% } %>
    <% } %>

    <% if(basicsConfig.isGeneratorMethodsExport){ %>
    <%index = 0;%>
    <select id="exportData" resultType="${basicsConfig.packagePath}.model.${basicsConfig.modelName}">
        select <% for(item in exportConfig!){ %>${index == 0 ? '' : ', '}${GeneratorXmlUtil.getExportField(item)}<% index++;} %>
        from ${tableInfo.name} t
        <% for(item in exportConfig!){ %>
        <% if(strUtil.isNotBlank(item.dictType)){ %>
        left join sys_dict sd_${item.name} on sd_${item.name}.code = t.${item.name} and sd_${item.name}.dict_type = '${item.dictType}'
        <% } else if(item.name == 'create_user' || item.name == 'edit_user'){ %>
        left join sys_user su_${item.name} on su_${item.name}.id = t.${item.name}
        <% } %>
        <% } %>
        <where>
            \${ew.sqlSegment}
        </where>
    </select>
    <% } %>
</mapper>
