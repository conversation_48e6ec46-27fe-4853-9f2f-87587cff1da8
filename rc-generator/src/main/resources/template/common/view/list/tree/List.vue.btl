<template>
  <PageWrapper dense>
    <!-- 表格 -->
    <BasicTable
      :searchInfo="searchInfo"
      @register="registerTable"
      :rowSelection="{ type: 'checkbox', onChange: onSelectChange }"
    >
      <template #toolbar>
        <% if(basicsConfig.generatorMethodsAdd){ %>
        <a-button-add ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':save" ' : ''}@click="handleCreate" />
        <% } %>
        <% if(basicsConfig.generatorMethodsRemove){ %>
        <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
        <a-button-remove-batch
          auth="${basicsConfig.permissionCode}:remove"
          :id="checkedKeys"
          :api="remove"
          @success="reload"
        />
        <% } else { %>
        <a-button-remove-batch :id="checkedKeys" :api="remove" @success="reload" />
        <% } %>
        <% } %>
        <% if(basicsConfig.isGeneratorMethodsImport){ %>
        <a-button-import ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':import:data" ' : ''} import-code="${basicsConfig.permissionCode}" />
        <% } %>
        <% if(basicsConfig.isGeneratorMethodsExport){ %>
        <a-button-export
          :loading="exportBtnLoading"
          ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':select" ' : ''}
          @click="handelExportData"
        />
        <% } %>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div class="basic-table-action center">
            <% if(basicsConfig.generatorMethodsSave){ %>
            <a-button-edit small ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':save" ' : ''}:id="record.id" @click="handleEdit" />
            <% } %>
            <% if(basicsConfig.generatorMethodsRemove){ %>
            <a-divider type="vertical" />
            <a-button-remove
              small
              ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':remove" ' : ''}
              :id="record.id"
              :api="remove"
              @success="reload"
            />
            <% } %>
          </div>
        </template>
      </template>
    </BasicTable>
    <!-- 表单 -->
    <${basicsConfig.modelName}Input @register="registerDrawer" @success="reload" />
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTable, useTable } from '/@/components/Table';
  import { searchFormSchema, columns } from '${dataTsPath}';
  import { useDrawer } from '/@/components/Drawer';
  import { ${GeneratorVueUtil.getImportButton(basicsConfig)} } from '/@/components/Button';
  <% if(basicsConfig.isGeneratorMethodsExport){ %>
  import { downloadFileById } from '/@/utils/file/download';
  import { ${basicsConfig.modelName} } from '${modelTsPath! != null ? modelTsPath : 'todo: Model Path' }';
  <% } %>
  import { ${GeneratorVueUtil.getListImport(basicsConfig)} } from '${apiTsPath! != null ? apiTsPath : 'todo: Api Path'}';
  import ${basicsConfig.modelName}Input from '${viewPath}/Input.vue';


  export default defineComponent({
    name: '${basicsConfig.modelName}List',
    components: {
      PageWrapper,
      ${basicsConfig.modelName}Input,
      <% if(basicsConfig.generatorMethodsAdd){ %>
      AButtonAdd,
      <% } %>
      <% if(basicsConfig.generatorMethodsRemove){ %>
      AButtonRemoveBatch,
      AButtonRemove,
      <% } %>
      <% if(basicsConfig.generatorMethodsSave){ %>
      AButtonEdit,
      <% } %>
      <% if(basicsConfig.isGeneratorMethodsImport){ %>
      AButtonImport,
      <% } %>
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      AButtonExport,
      <% } %>
      BasicTable,
    },
    setup() {
      // 表格选中数据
      const checkedKeys = ref<Array<string>>([]);
      // 查询条件
      const searchInfo = reactive<Recordable>({});
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      // 导出按钮状态
      const exportBtnLoading = ref<boolean>(false);
      <% } %>

      const [registerDrawer, { openDrawer }] = useDrawer();
      /**
       * 初始化表格
       */
      const [registerTable, { reload, getForm }] = useTable({
        title: '${basicsConfig.businessName}',
        api: select,
        columns,
        useSearchForm: true,
        formConfig: {
          schemas: searchFormSchema,
        },
        actionColumn: {
          width: 140,
          title: '操作',
          key: 'action',
        },
      });

      <% if(basicsConfig.generatorMethodsAdd){ %>
      const handleCreate = () => {
        add().then((data) => {
          openDrawer(true, data);
        });
      };

      <% } %>
      <% if(basicsConfig.generatorMethodsSave){ %>
      const handleEdit = (id: string) => {
        get(id).then((data) => {
          openDrawer(true, data);
        });
      };

      <% } %>
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      const handelExportData = async () => {
        exportBtnLoading.value = true;
        try {
          await exportData(Object.assign(searchInfo, getForm().getFieldsValue())).then((id) => {
            downloadFileById(id);
          });
        } catch (e) {
          console.error('导出数据错误', e);
        } finally {
          exportBtnLoading.value = false;
        }
      };

      <% } %>
      return {
        checkedKeys,
        onSelectChange(selectedRowKeys: string[]) {
          checkedKeys.value = selectedRowKeys;
        },
        searchInfo,
        <% if(basicsConfig.isGeneratorMethodsExport){ %>
        exportBtnLoading,
        handelExportData,
        <% } %>
        <% if(basicsConfig.generatorMethodsRemove){ %>
        remove,
        <% } %>
        registerDrawer,
        registerTable,
        <% if(basicsConfig.generatorMethodsAdd){ %>
        handleCreate,
        <% } %>
        <% if(basicsConfig.generatorMethodsSave){ %>
        handleEdit,
        <% } %>
        reload,
      };
    },
  });
</script>
