<template>
  <PageWrapper dense>
    <!-- 表格 -->
    <BasicTable
      :searchInfo="searchInfo"
      @register="registerTable"
      :rowSelection="{ type: 'checkbox', onChange: onSelectChange }"
    >
      <template #toolbar>
        <% if(basicsConfig.generatorMethodsAdd){ %>
        <a-button-add ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':save" ' : ''}@click="handleCreate" />
        <% } %>
        <% if(basicsConfig.generatorMethodsRemove){ %>
        <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
        <a-button-remove-batch
          auth="${basicsConfig.permissionCode}:remove"
          :id="checkedKeys"
          :api="remove"
          @success="reload"
        />
        <% } else { %>
        <a-button-remove-batch :id="checkedKeys" :api="remove" @success="reload" />
        <% } %>
        <% } %>
        <Authority value="sys:dept:save">
          <a-button @click="handleOrder">
            <template #icon> <Icon icon="ant-design:ordered-list-outlined" /> </template> 排序
          </a-button>
        </Authority>

        <a-button @click="expandAll">
          <template #icon> <Icon icon="ant-design:plus-outlined" /> </template> 展开全部
        </a-button>
        <a-button @click="collapseAll">
          <template #icon> <Icon icon="ant-design:minus-outlined" /> </template>折叠全部
        </a-button>
        <% if(basicsConfig.isGeneratorMethodsImport){ %>
        <a-button-import ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':import:data"' : ''} import-code="${basicsConfig.permissionCode}" />
        <% } %>
        <% if(basicsConfig.isGeneratorMethodsExport){ %>
        <a-button-export
          :loading="exportBtnLoading"
          ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':select"' : ''}
          @click="handelExportData"
        />
        <% } %>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div class="basic-table-action center">
            <% if(basicsConfig.generatorMethodsSave){ %>
            <a-button-edit small ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':save" ' : ''}:id="record.id" @click="handleEdit" />
            <% } %>
            <% if(basicsConfig.generatorMethodsRemove){ %>
            <a-divider type="vertical" />
            <a-button-remove
              small
              ${strUtil.isNotBlank(basicsConfig.permissionCode) ? 'auth="' + basicsConfig.permissionCode + ':remove"' : ''}
              :id="record.id"
              :api="remove"
              @success="reload"
            />
            <% } %>
          </div>
        </template>
      </template>
    </BasicTable>
    <!-- 表单 -->
    <${basicsConfig.modelName}Input @register="registerDrawer" @success="reload" />
    <${basicsConfig.modelName}Order @register="registerOrderDrawer" @success="reload" />
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTable, useTable } from '/@/components/Table';
  import { clearTreeEmptyChildren, listToTree } from '/@/utils/helper/treeHelper';
  import { searchFormSchema, columns } from '${dataTsPath}';
  import { Icon } from '/@/components/Icon';
  import { useDrawer } from '/@/components/Drawer';
  import { ${GeneratorVueUtil.getImportButton(basicsConfig)} } from '/@/components/Button';
  <% if(basicsConfig.isGeneratorMethodsExport){ %>
  import { downloadFileById } from '/@/utils/file/download';
  <% } %>
  import { ${GeneratorVueUtil.getListImport(basicsConfig)} } from '${apiTsPath! != null ? apiTsPath : 'todo: Api Path'}';
  import ${basicsConfig.modelName}Input from '${viewPath}/Input.vue';
  import ${basicsConfig.modelName}Order from '${viewPath}/Order.vue';

  export default defineComponent({
    name: '${basicsConfig.modelName}List',
    components: {
      PageWrapper,
      ${basicsConfig.modelName}Input,
      ${basicsConfig.modelName}Order,
      <% if(basicsConfig.generatorMethodsAdd){ %>
      AButtonAdd,
      <% } %>
      <% if(basicsConfig.generatorMethodsRemove){ %>
      AButtonRemoveBatch,
      AButtonRemove,
      <% } %>
      <% if(basicsConfig.generatorMethodsSave){ %>
      AButtonEdit,
      <% } %>
      <% if(basicsConfig.isGeneratorMethodsImport){ %>
      AButtonImport,
      <% } %>
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      AButtonExport,
      <% } %>
      BasicTable,
    },
    setup() {
      // 表格选中数据
      const checkedKeys = ref<Array<string>>([]);
      // 查询条件
      const searchInfo = reactive<Recordable>({});
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      // 导出按钮状态
      const exportBtnLoading = ref<boolean>(false);
      <% } %>

      const [registerDrawer, { openDrawer }] = useDrawer();
      // 排序
      const [registerOrderDrawer, { openDrawer: openOrderDrawer }] = useDrawer();
      /**
       * 初始化表格
       */
      const [registerTable, { reload, getForm, expandAll, collapseAll }] = useTable({
        title: '${basicsConfig.businessName}',
        api: select,
        afterFetch: (items) => {
          const tree = listToTree(items);
          return clearTreeEmptyChildren(tree, 'children');
        },
        isTreeTable: true,
        pagination: false,
        showIndexColumn: false,
        columns,
        useSearchForm: true,
        formConfig: {
          schemas: searchFormSchema,
        },
        actionColumn: {
          width: 140,
          title: '操作',
          key: 'action',
        },
      });

      <% if(basicsConfig.generatorMethodsAdd){ %>
      const handleCreate = () => {
        add().then((data) => {
          openDrawer(true, data);
        });
      };

      <% } %>
      <% if(basicsConfig.generatorMethodsSave){ %>
      const handleEdit = (id: string) => {
        get(id).then((data) => {
          openDrawer(true, data);
        });
      };

      <% } %>
      /**
       * 排序
       */
      const handleOrder = () => {
        openOrderDrawer(true, {});
      };
      <% if(basicsConfig.isGeneratorMethodsExport){ %>
      const handelExportData = async () => {
        exportBtnLoading.value = true;
        try {
          await exportData(Object.assign(searchInfo, getForm().getFieldsValue())).then((id) => {
            downloadFileById(id);
          });
        } catch (e) {
          console.error('导出数据错误', e);
        } finally {
          exportBtnLoading.value = false;
        }
      };

      <% } %>
      return {
        checkedKeys,
        onSelectChange(selectedRowKeys: string[]) {
          checkedKeys.value = selectedRowKeys;
        },
        searchInfo,
        <% if(basicsConfig.isGeneratorMethodsExport){ %>
        exportBtnLoading,
        handelExportData,
        <% } %>
        <% if(basicsConfig.generatorMethodsRemove){ %>
        remove,
        <% } %>
        registerDrawer,
        registerTable,
        registerOrderDrawer,
        openOrderDrawer,
        <% if(basicsConfig.generatorMethodsAdd){ %>
        handleCreate,
        <% } %>
        <% if(basicsConfig.generatorMethodsSave){ %>
        handleEdit,
        <% } %>
        reload,
        expandAll,
        collapseAll,
        handleOrder,
      };
    },
  });
</script>
