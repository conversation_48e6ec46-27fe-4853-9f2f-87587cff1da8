package ${basicsConfig.packagePath}.service.impl;

<% for(import in imports!){ %>
import ${import.name};
<% } %>
<% if (!isMaster) { %>
import com.baomidou.dynamic.datasource.annotation.DS;
<% } %>
import ${basicsConfig.packagePath}.model.${basicsConfig.modelName};
import ${basicsConfig.packagePath}.service.${basicsConfig.modelName}Service;
import ${basicsConfig.packagePath}.dao.${basicsConfig.modelName}Mapper;

/**
 * ${basicsConfig.businessName}
 *
 * <AUTHOR>
 * @date ${date}
 */
@Service
<% if (!isMaster) { %>
@DS("${basicsConfig.dataSource}")
<% } %>
public class ${basicsConfig.modelName}ServiceImpl extends ServiceImpl<${basicsConfig.modelName}Mapper, ${basicsConfig.modelName}> implements ${basicsConfig.modelName}Service<% if(basicsConfig.isGeneratorMethodsImport){ %>, ImportService<% }%> {

    <% if(basicsConfig.isGeneratorMethodsSelect){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    @Override
    public List<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}) {
        QueryWrapper<${basicsConfig.modelName}> queryWrapper = getQueryWrapper(${strUtil.lowerFirst(basicsConfig.modelName)});
        return baseMapper.select(queryWrapper);
    }

    @Override
    public List<Tree> selectAll() {
        return baseMapper.selectAll();
    }
    <% } else { %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @param page   分页
     * @return Page<${basicsConfig.modelName}>
     */
    @Override
    public Page<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}, Page<${basicsConfig.modelName}> page) {
        QueryWrapper<${basicsConfig.modelName}> queryWrapper = getQueryWrapper(${strUtil.lowerFirst(basicsConfig.modelName)});
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }
    <% } %>

    /**
     * 获取查询条件
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return QueryWrapper<${basicsConfig.modelName}>
     */
    private QueryWrapper<${basicsConfig.modelName}> getQueryWrapper(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}){
        QueryWrapper<${basicsConfig.modelName}> queryWrapper = new QueryWrapper<>();
        if(${strUtil.lowerFirst(basicsConfig.modelName)} != null){
            // 查询条件
            <% if(queryConfig != null) { %>
            <% for(item in queryConfig){ %>
            <% queryField = GeneratorJavaUtil.getSelectField(item); %>
            <% if('start_end' == item.matchingMode) { %>
            // ${item.label} - 开始时间
            if (Validator.isNotEmpty(${strUtil.lowerFirst(basicsConfig.modelName)}.getStart${strUtil.upperFirst(item.propertyName)}())) {
                queryWrapper.ge("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.getStart${strUtil.upperFirst(item.propertyName)}());
            }
            // ${item.label} - 结束时间
            if (Validator.isNotEmpty(${strUtil.lowerFirst(basicsConfig.modelName)}.getEnd${strUtil.upperFirst(item.propertyName)}())) {
                queryWrapper.le("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.getEnd${strUtil.upperFirst(item.propertyName)}());
            }
            <% } else { %>
            // ${item.label}
            if (Validator.isNotEmpty(${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}())) {
                <% if(strUtil.isNotBlank(item.dictType)) { %>
                if (${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                }
                <% } else if('gte' == item.matchingMode) { %>
                queryWrapper.ge("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                <% } else if('lte' == item.matchingMode) {%>
                queryWrapper.le("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                <% } else if('left_like' == item.matchingMode) {%>
                queryWrapper.likeLeft("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                <% } else if('right_like' == item.matchingMode) {%>
                queryWrapper.likeRight("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                <% } else {%>
                queryWrapper.${item.matchingMode}("${queryField}", ${strUtil.lowerFirst(basicsConfig.modelName)}.get${strUtil.upperFirst(item.propertyName)}());
                <% } %>
            }
            <% } %>
            <% } %>
            <% } %>
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return ${basicsConfig.modelName}
     */
    @Override
    public ${basicsConfig.modelName} get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsAdd) { %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table') { %>
    /**
     * 新增
     *
     * @param parentId 上级id
     * @return ${basicsConfig.modelName}
     */
    @Override
    public ${basicsConfig.modelName} add(String parentId) {
        ${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)} = new ${basicsConfig.modelName}();
        // 设置默认值
        if (Validator.isNotEmpty(parentId)) {
            ${basicsConfig.modelName} parent${basicsConfig.modelName} = getById(parentId);
            if (parent${basicsConfig.modelName} != null) {
                ${strUtil.lowerFirst(basicsConfig.modelName)}.setParentId(parentId);
                ${strUtil.lowerFirst(basicsConfig.modelName)}.setOrderNo(baseMapper.getMaxOrderNo(parentId) + 1);
            }
        }
        return ${strUtil.lowerFirst(basicsConfig.modelName)};
    }
    <% } else { %>
    /**
     * 新增
     *
     * @return ${basicsConfig.modelName}
     */
    @Override
    public ${basicsConfig.modelName} add() {
        ${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)} = new ${basicsConfig.modelName}();
        // 设置默认值
        return ${strUtil.lowerFirst(basicsConfig.modelName)};
    }
    <% } %>
    <% } %>
    <% if(basicsConfig.isGeneratorMethodsRemove){ %>

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsSave){ %>
    /**
     * 保存
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 表单内容
     * @return ${basicsConfig.modelName}
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public ${basicsConfig.modelName} saveData(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}) {
        ToolUtil.checkParams(${strUtil.lowerFirst(basicsConfig.modelName)});
        if (Validator.isEmpty(${strUtil.lowerFirst(basicsConfig.modelName)}.getId())) {
            // 新增,设置默认值
        }
        return (${basicsConfig.modelName}) ToolUtil.checkResult(saveOrUpdate(${strUtil.lowerFirst(basicsConfig.modelName)}), ${strUtil.lowerFirst(basicsConfig.modelName)});
    }
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>

    @Override
    public boolean saveOrder(List<${basicsConfig.modelName}> ${strUtil.lowerFirst(basicsConfig.modelName)}List) {
        return baseMapper.updateOrderBatch(${strUtil.lowerFirst(basicsConfig.modelName)}List) > 0;
    }
    <% } %>

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsImport){ %>
    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    <% }%>
    <% if(basicsConfig.isGeneratorMethodsExport){ %>
    @Override
    public String exportData(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}) {
        QueryWrapper<${basicsConfig.modelName}> queryWrapper = getQueryWrapper(${strUtil.lowerFirst(basicsConfig.modelName)});
        List<${basicsConfig.modelName}> list = baseMapper.exportData(queryWrapper);
        return ExcelUtil.writeAndGetDownloadId("${basicsConfig.businessName}", "${basicsConfig.businessName}", list, ${basicsConfig.modelName}.class);
    }

    <% } %>
}