package ${basicsConfig.packagePath}.service;

<% for(import in imports!){ %>
import ${import.name};
<% } %>
import ${basicsConfig.packagePath}.model.${basicsConfig.modelName};

/**
 * ${basicsConfig.businessName}
 *
 * <AUTHOR>
 * @date ${date}
 */
public interface ${basicsConfig.modelName}Service {
    <% if(basicsConfig.isGeneratorMethodsSelect){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    List<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)});

    /**
     * 获取所有数据
     *
     * @return List<Tree>
     */
    List<Tree> selectAll();
    <% } else { %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @param page   分页
     * @return Page<${basicsConfig.modelName}>
     */
    Page<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}, Page<${basicsConfig.modelName}> page);
    <% } %>

    /**
     * 详情
     *
     * @param id id
     * @return ${basicsConfig.modelName}
     */
    ${basicsConfig.modelName} get(String id);

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsAdd){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 新增
     * @param parentId 上级id
     * @return ${basicsConfig.modelName}
     */
    ${basicsConfig.modelName} add(String parentId);
    <% } else { %>
    /**
     * 新增
     * @return ${basicsConfig.modelName}
     */
    ${basicsConfig.modelName} add();
    <% } %>
    <% } %>
    <% if(basicsConfig.isGeneratorMethodsRemove){ %>
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsSave){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 保存排序
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)}List 排序
     * @return true/false
     */
    boolean saveOrder(List<${basicsConfig.modelName}> ${strUtil.lowerFirst(basicsConfig.modelName)}List);
    <% } %>
    /**
     * 保存
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 表单内容
     * @return ${basicsConfig.modelName}
     */
    ${basicsConfig.modelName} saveData(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)});

    <% } %>
    <% if(basicsConfig.isGeneratorMethodsExport){ %>
    /**
     * 导出数据
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return 文件下载id
     */
    String exportData(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)});

    <% } %>
}
