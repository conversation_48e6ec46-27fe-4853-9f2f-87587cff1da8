package ${basicsConfig.packagePath}.controller;

<% for(import in imports!){ %>
import ${import.name};
<% } %>
import ${basicsConfig.packagePath}.model.${basicsConfig.modelName};
import ${basicsConfig.packagePath}.service.${basicsConfig.modelName}Service;

/**
 * ${basicsConfig.businessName}
 *
 * <AUTHOR>
 * @date ${date}
 */
@RestController
@ResponseResult
@RequestMapping("/api${basicsConfig.controllerMapping}")
public class ${basicsConfig.modelName}Controller {

    /**
     * ${basicsConfig.businessName} service
     */
    @Autowired
    private ${basicsConfig.modelName}Service service;

    <% if(basicsConfig.generatorMethodsSelect){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    @GetMapping()
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:select")
    <% } %>
    public List<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}){
        return service.select(${strUtil.lowerFirst(basicsConfig.modelName)});
    }

    /**
     * 获取全部数据
     *
     * @return List<Tree>
     */
    @GetMapping("all")
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:select")
    <% } %>
    public List<Tree> selectAll() {
        return service.selectAll();
    }
    <% } else { %>
    /**
     * 列表
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @param page 分页
     * @return Page<${basicsConfig.modelName}>
     */
    @GetMapping()
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:select")
    <% } %>
    public Page<${basicsConfig.modelName}> select(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}, Page<${basicsConfig.modelName}> page){
        return service.select(${strUtil.lowerFirst(basicsConfig.modelName)}, page);
    }
    <% } %>

    /**
     * 详情
     *
     * @param id id
     * @return ${basicsConfig.modelName}
     */
    @GetMapping("{id}")
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:select")
    <% } %>
    public ${basicsConfig.modelName} get(@PathVariable("id") String id) {
        return service.get(id);
    }

    <% } %>
    <% if(basicsConfig.generatorMethodsAdd){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 新增
     * @param parentId 上级id
     * @return ${basicsConfig.modelName}
     */
    @GetMapping({"/add/{parentId}", "/add"})
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:save")
    <% } %>
    public ${basicsConfig.modelName} add(@PathVariable(value = "parentId", required = false) String parentId) {
        return service.add(parentId);
    }
    <% } else { %>
    /**
     * 新增
     *
     * @return ${basicsConfig.modelName}
     */
    @GetMapping("add")
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:save")
    <% } %>
    public ${basicsConfig.modelName} add() {
        return service.add();
    }
    <% } %>
    <% } %>
    <% if(basicsConfig.generatorMethodsRemove){ %>
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:remove")
    <% } %>
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    <% } %>
    <% if(basicsConfig.generatorMethodsSave){ %>
    /**
     * 保存
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 表单内容
     * @return ${basicsConfig.modelName}
     */
    @PostMapping()
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:save")
    <% } %>
    public ${basicsConfig.modelName} saveData(@Valid @RequestBody ${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}){
        return service.saveData(${strUtil.lowerFirst(basicsConfig.modelName)});
    }
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 保存排序
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)}List 排序
     * @return true/false
     */
    @PostMapping("order")
    <% if(strUtil.isNotBlank(basicsConfig.permissionCode)) { %>
    @RequiresPermissions("${basicsConfig.permissionCode}:save")
    <% } %>
    public boolean saveOrder(@RequestBody List<${basicsConfig.modelName}> ${strUtil.lowerFirst(basicsConfig.modelName)}List){
        return service.saveOrder(${strUtil.lowerFirst(basicsConfig.modelName)}List);
    }
    <% } %>
    <% } %>
    <% if(basicsConfig.isGeneratorMethodsExport){ %>
    /**
     * 导出数据
     *
     * @param ${strUtil.lowerFirst(basicsConfig.modelName)} 查询条件
     * @return 文件下载id
     */
    @GetMapping("export/data")
    @RequiresPermissions("${basicsConfig.permissionCode}:select")
    public String exportData(${basicsConfig.modelName} ${strUtil.lowerFirst(basicsConfig.modelName)}){
        return service.exportData(${strUtil.lowerFirst(basicsConfig.modelName)});
    }

    <% } %>
}
