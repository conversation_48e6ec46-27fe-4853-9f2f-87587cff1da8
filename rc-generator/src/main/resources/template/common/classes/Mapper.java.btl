package ${basicsConfig.packagePath}.dao;

<% for(import in imports!){ %>
import ${import.name};
<% } %>
import ${basicsConfig.packagePath}.model.${basicsConfig.modelName};

/**
 * ${basicsConfig.businessName}
 *
 * <AUTHOR>
 * @date ${date}
 */
public interface ${basicsConfig.modelName}Mapper extends BaseMapper<${basicsConfig.modelName}> {
    <% if(basicsConfig.isGeneratorMethodsSelect){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    List<${basicsConfig.modelName}> select(@Param("ew") QueryWrapper<${basicsConfig.modelName}> queryWrapper);

    /**
     * 获取所有数据
     * @return List<JsTree>
     */
    List<Tree> selectAll();
    <% } else { %>
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    List<${basicsConfig.modelName}> select(Page<${basicsConfig.modelName}> page, @Param("ew") QueryWrapper<${basicsConfig.modelName}> queryWrapper);
    <% } %>
    /**
     * 查询详细信息
     *
     * @param id id
     * @return ${basicsConfig.modelName}
     */
    ${basicsConfig.modelName} getById(@Param("id") String id);
    <% } %>
    <% if(basicsConfig.isGeneratorMethodsSave){ %>
    <% if(basicsConfig.listGeneratorTemplate == 'tree-table'){ %>
    /**
     * 获取parentId下子级最大排序值
     *
     * @param parentId parentId
     * @return int
     */
    int getMaxOrderNo(@Param("parentId") String parentId);
    /**
     * 批量更新排序&结构
     *
     * @param list 数据
     * @return 更新条数
     */
    Integer updateOrderBatch(List<${basicsConfig.modelName}> list);
    <% } %>
    <% } %>

    <% if(basicsConfig.isGeneratorMethodsExport){ %>
    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<${basicsConfig.modelName}>
     */
    List<${basicsConfig.modelName}> exportData(@Param("ew") QueryWrapper<${basicsConfig.modelName}> queryWrapper);
    <% } %>
}