package com.rc.admin.generator.controller;

import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.generator.model.CascaderDistrict;
import com.rc.admin.generator.model.District;
import com.rc.admin.generator.service.DistrictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 行政区划
 *
 * <AUTHOR>
 * @date 2022/3/8
 */
@RestController
@ResponseResult
public class DistrictController {
    @Autowired
    private DistrictService service;

    @GetMapping("/district")
    public List<District> selectDistrict(){
        return service.selectDistrict();
    }

    @GetMapping("/cascader/district")
    public List<CascaderDistrict> selectCascaderDistrict(){
        return service.selectCascaderDistrict();
    }
}
