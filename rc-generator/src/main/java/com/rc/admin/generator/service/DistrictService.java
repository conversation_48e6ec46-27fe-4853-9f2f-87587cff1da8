package com.rc.admin.generator.service;

import com.rc.admin.generator.model.CascaderDistrict;
import com.rc.admin.generator.model.District;

import java.util.List;

/**
 * 行政区划
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface DistrictService {

    /**
     * 获取级联行政区划数据
     *
     * @return 级联行政区划数据
     */
    List<District> selectDistrict();

    /**
     * 获取级联行政区划数据
     *
     * @return 级联行政区划数据
     */
    List<CascaderDistrict> selectCascaderDistrict();
}
