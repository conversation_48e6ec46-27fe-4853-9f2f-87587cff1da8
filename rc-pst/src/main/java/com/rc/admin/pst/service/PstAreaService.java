package com.rc.admin.pst.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.pst.model.PstArea;

/**
 * 住宿费用
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstAreaService {
    /**
     * 列表
     *
     * @param pstArea 查询条件
     * @param page   分页
     * @return Page<PstArea>
     */
    Page<PstArea> select(PstArea pstArea, Page<PstArea> page);

    /**
     * 详情
     *
     * @param id id
     * @return PstArea
     */
    PstArea get(String id);

    /**
     * 新增
     * @return PstArea
     */
    PstArea add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param pstArea 表单内容
     * @return PstArea
     */
    PstArea saveData(PstArea pstArea);

    /**
     * 导出数据
     *
     * @param pstArea 查询条件
     * @return 文件下载id
     */
    String exportData(PstArea pstArea);

}
