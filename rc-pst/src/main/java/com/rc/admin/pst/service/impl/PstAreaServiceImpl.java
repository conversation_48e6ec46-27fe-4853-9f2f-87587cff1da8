package com.rc.admin.pst.service.impl;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.util.ToolUtil;
import com.rc.admin.util.office.ExcelUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Arrays;
import java.util.List;
import com.rc.admin.pst.model.PstArea;
import com.rc.admin.pst.service.PstAreaService;
import com.rc.admin.pst.dao.PstAreaMapper;

/**
 * 住宿费用
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Service
public class PstAreaServiceImpl extends ServiceImpl<PstAreaMapper, PstArea> implements PstAreaService, ImportService {

    /**
     * 列表
     *
     * @param pstArea 查询条件
     * @param page   分页
     * @return Page<PstArea>
     */
    @Override
    public Page<PstArea> select(PstArea pstArea, Page<PstArea> page) {
        QueryWrapper<PstArea> queryWrapper = getQueryWrapper(pstArea);
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    /**
     * 获取查询条件
     *
     * @param pstArea 查询条件
     * @return QueryWrapper<PstArea>
     */
    private QueryWrapper<PstArea> getQueryWrapper(PstArea pstArea){
        QueryWrapper<PstArea> queryWrapper = new QueryWrapper<>();
        if(pstArea != null){
            // 查询条件
            // 区域
            if (Validator.isNotEmpty(pstArea.getArea())) {
                queryWrapper.like("t.area", pstArea.getArea());
            }
            // 职级
            if (Validator.isNotEmpty(pstArea.getRank())) {
                if (pstArea.getRank().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("t.rank", pstArea.getRank().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("t.rank", pstArea.getRank());
                }
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstArea
     */
    @Override
    public PstArea get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return PstArea
     */
    @Override
    public PstArea add() {
        PstArea pstArea = new PstArea();
        // 设置默认值
        return pstArea;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param pstArea 表单内容
     * @return PstArea
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public PstArea saveData(PstArea pstArea) {
        ToolUtil.checkParams(pstArea);
        if (Validator.isEmpty(pstArea.getId())) {
            // 新增,设置默认值
        }
        return (PstArea) ToolUtil.checkResult(saveOrUpdate(pstArea), pstArea);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public String exportData(PstArea pstArea) {
        QueryWrapper<PstArea> queryWrapper = getQueryWrapper(pstArea);
        List<PstArea> list = baseMapper.exportData(queryWrapper);
        return ExcelUtil.writeAndGetDownloadId("住宿费用", "住宿费用", list, PstArea.class);
    }

}