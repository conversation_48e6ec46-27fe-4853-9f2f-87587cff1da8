package com.rc.admin.pst.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.rc.admin.pst.model.PstArea;

/**
 * 住宿费用
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstAreaMapper extends BaseMapper<PstArea> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<PstArea>
     */
    List<PstArea> select(Page<PstArea> page, @Param("ew") QueryWrapper<PstArea> queryWrapper);
    /**
     * 查询详细信息
     *
     * @param id id
     * @return PstArea
     */
    PstArea getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<PstArea>
     */
    List<PstArea> exportData(@Param("ew") QueryWrapper<PstArea> queryWrapper);
}