package com.rc.admin.pst.service.impl;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.util.ToolUtil;
import com.rc.admin.util.office.ExcelUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Arrays;
import java.util.List;
import com.rc.admin.pst.model.PstPersonPrice;
import com.rc.admin.pst.service.PstPersonPriceService;
import com.rc.admin.pst.dao.PstPersonPriceMapper;

/**
 * 人天成本单价
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Service
public class PstPersonPriceServiceImpl extends ServiceImpl<PstPersonPriceMapper, PstPersonPrice> implements PstPersonPriceService, ImportService {

    /**
     * 列表
     *
     * @param pstPersonPrice 查询条件
     * @param page   分页
     * @return Page<PstPersonPrice>
     */
    @Override
    public Page<PstPersonPrice> select(PstPersonPrice pstPersonPrice, Page<PstPersonPrice> page) {
        QueryWrapper<PstPersonPrice> queryWrapper = getQueryWrapper(pstPersonPrice);
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    /**
     * 获取查询条件
     *
     * @param pstPersonPrice 查询条件
     * @return QueryWrapper<PstPersonPrice>
     */
    private QueryWrapper<PstPersonPrice> getQueryWrapper(PstPersonPrice pstPersonPrice){
        QueryWrapper<PstPersonPrice> queryWrapper = new QueryWrapper<>();
        if(pstPersonPrice != null){
            // 查询条件
            // 岗位类型
            if (Validator.isNotEmpty(pstPersonPrice.getPositionType())) {
                if (pstPersonPrice.getPositionType().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("t.position_type", pstPersonPrice.getPositionType().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("t.position_type", pstPersonPrice.getPositionType());
                }
            }
            // 岗位
            if (Validator.isNotEmpty(pstPersonPrice.getPosition())) {
                if (pstPersonPrice.getPosition().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("t.position", pstPersonPrice.getPosition().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("t.position", pstPersonPrice.getPosition());
                }
            }
            // 职级
            if (Validator.isNotEmpty(pstPersonPrice.getRank())) {
                if (pstPersonPrice.getRank().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("t.rank", pstPersonPrice.getRank().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("t.rank", pstPersonPrice.getRank());
                }
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstPersonPrice
     */
    @Override
    public PstPersonPrice get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return PstPersonPrice
     */
    @Override
    public PstPersonPrice add() {
        PstPersonPrice pstPersonPrice = new PstPersonPrice();
        // 设置默认值
        return pstPersonPrice;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param pstPersonPrice 表单内容
     * @return PstPersonPrice
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public PstPersonPrice saveData(PstPersonPrice pstPersonPrice) {
        ToolUtil.checkParams(pstPersonPrice);
        if (Validator.isEmpty(pstPersonPrice.getId())) {
            // 新增,设置默认值
        }
        return (PstPersonPrice) ToolUtil.checkResult(saveOrUpdate(pstPersonPrice), pstPersonPrice);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public String exportData(PstPersonPrice pstPersonPrice) {
        QueryWrapper<PstPersonPrice> queryWrapper = getQueryWrapper(pstPersonPrice);
        List<PstPersonPrice> list = baseMapper.exportData(queryWrapper);
        return ExcelUtil.writeAndGetDownloadId("人天成本单价", "人天成本单价", list, PstPersonPrice.class);
    }

}