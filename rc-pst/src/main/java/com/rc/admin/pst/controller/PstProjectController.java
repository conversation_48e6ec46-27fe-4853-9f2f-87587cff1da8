package com.rc.admin.pst.controller;

import com.rc.admin.common.core.common.select.Select;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import com.rc.admin.core.annotation.ResponseResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import com.rc.admin.common.core.common.pagination.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
import com.rc.admin.pst.model.PstProject;
import com.rc.admin.pst.service.PstProjectService;

import java.util.List;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@ResponseResult
@RequestMapping("/api/pst/project")
public class PstProjectController {

    /**
     * 项目信息 service
     */
    @Autowired
    private PstProjectService service;

    /**
     * 列表
     *
     * @param pstProject 查询条件
     * @param page 分页
     * @return Page<PstProject>
     */
    @GetMapping()
    @RequiresPermissions("pst:project:select")
    public Page<PstProject> select(PstProject pstProject, Page<PstProject> page){
        return service.select(pstProject, page);
    }

    /**
     * 查询所有
     *
     * @return List<Select>
     */
    @GetMapping("all")
    @RequiresPermissions("pst:project:select")
    public List<Select> selectAll() {
        return service.selectAll();
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstProject
     */
    @GetMapping("{id}")
    @RequiresPermissions("pst:project:select")
    public PstProject get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return PstProject
     */
    @GetMapping("add")
    @RequiresPermissions("pst:project:save")
    public PstProject add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    @RequiresPermissions("pst:project:remove")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param pstProject 表单内容
     * @return PstProject
     */
    @PostMapping()
    @RequiresPermissions("pst:project:save")
    public PstProject saveData(@Valid @RequestBody PstProject pstProject){
        return service.saveData(pstProject);
    }
    /**
     * 导出数据
     *
     * @param pstProject 查询条件
     * @return 文件下载id
     */
    @GetMapping("export/data")
    @RequiresPermissions("pst:project:select")
    public String exportData(PstProject pstProject){
        return service.exportData(pstProject);
    }

}
