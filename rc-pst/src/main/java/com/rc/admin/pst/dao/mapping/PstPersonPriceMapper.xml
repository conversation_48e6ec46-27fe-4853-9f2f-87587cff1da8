<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.pst.dao.PstPersonPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.pst.model.PstPersonPrice">
        <result column="id" property="id" />
        <result column="position_type" property="positionType" />
        <result column="position" property="position" />
        <result column="rank" property="rank" />
        <result column="price" property="price" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="edit_user" property="editUser" />
        <result column="edit_date" property="editDate" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.pst.model.PstPersonPrice">
        select t.id, t.position_type, t.position, t.rank, t.price, su_edit_user.nickname as edit_user, t.edit_date
        from pst_person_price t
        left join sys_user su_edit_user on su_edit_user.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getById" resultType="com.rc.admin.pst.model.PstPersonPrice">
        select t.id, t.position_type, t.position, t.rank, t.price, t.create_user, t.create_date, t.edit_user, t.edit_date
        from pst_person_price t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.pst.model.PstPersonPrice">
        select sd_position_type.name as position_type, sd_position.name as position, sd_rank.name as rank, t.price
        from pst_person_price t
        left join sys_dict sd_position_type on sd_position_type.code = t.position_type and sd_position_type.dict_type = 'positionType'
        left join sys_dict sd_position on sd_position.code = t.position and sd_position.dict_type = 'position'
        left join sys_dict sd_rank on sd_rank.code = t.rank and sd_rank.dict_type = 'rank'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
