package com.rc.admin.pst.service.impl;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.common.select.Select;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.sys.model.SysDictType;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.util.ToolUtil;
import com.rc.admin.util.office.ExcelUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Arrays;
import java.util.List;
import com.rc.admin.pst.model.PstProject;
import com.rc.admin.pst.service.PstProjectService;
import com.rc.admin.pst.dao.PstProjectMapper;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Service
public class PstProjectServiceImpl extends ServiceImpl<PstProjectMapper, PstProject> implements PstProjectService, ImportService {

    /**
     * 列表
     *
     * @param pstProject 查询条件
     * @param page   分页
     * @return Page<PstProject>
     */
    @Override
    public Page<PstProject> select(PstProject pstProject, Page<PstProject> page) {
        QueryWrapper<PstProject> queryWrapper = getQueryWrapper(pstProject);
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    @Override
    public List<Select> selectAll() {
        QueryWrapper<SysDictType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_status", "presales");
        return baseMapper.selectAll(queryWrapper);
    }

    /**
     * 获取查询条件
     *
     * @param pstProject 查询条件
     * @return QueryWrapper<PstProject>
     */
    private QueryWrapper<PstProject> getQueryWrapper(PstProject pstProject){
        QueryWrapper<PstProject> queryWrapper = new QueryWrapper<>();
        if(pstProject != null){
            // 查询条件
            // 项目code
            if (Validator.isNotEmpty(pstProject.getProjectCode())) {
                queryWrapper.like("t.project_code", pstProject.getProjectCode());
            }
            // 项目名称
            if (Validator.isNotEmpty(pstProject.getProjectName())) {
                queryWrapper.like("t.project_name", pstProject.getProjectName());
            }
            // 项目状态
            if (Validator.isNotEmpty(pstProject.getProjectStatus())) {
                if (pstProject.getProjectStatus().contains(CommonConst.SPLIT)) {
                    queryWrapper.in("t.project_status", pstProject.getProjectStatus().split(CommonConst.SPLIT));
                } else {
                    queryWrapper.eq("t.project_status", pstProject.getProjectStatus());
                }
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstProject
     */
    @Override
    public PstProject get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return PstProject
     */
    @Override
    public PstProject add() {
        PstProject pstProject = new PstProject();
        // 设置默认值
        return pstProject;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param pstProject 表单内容
     * @return PstProject
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public PstProject saveData(PstProject pstProject) {
        ToolUtil.checkParams(pstProject);
        if (Validator.isEmpty(pstProject.getId())) {
            // 新增,设置默认值
        }
        return (PstProject) ToolUtil.checkResult(saveOrUpdate(pstProject), pstProject);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public String exportData(PstProject pstProject) {
        QueryWrapper<PstProject> queryWrapper = getQueryWrapper(pstProject);
        List<PstProject> list = baseMapper.exportData(queryWrapper);
        return ExcelUtil.writeAndGetDownloadId("项目信息", "项目信息", list, PstProject.class);
    }

}