package com.rc.admin.pst.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import com.rc.admin.core.annotation.ResponseResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import com.rc.admin.common.core.common.pagination.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
import com.rc.admin.pst.model.PstPersonPrice;
import com.rc.admin.pst.service.PstPersonPriceService;

/**
 * 人天成本单价
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@RestController
@ResponseResult
@RequestMapping("/api/pst/person/price")
public class PstPersonPriceController {

    /**
     * 人天成本单价 service
     */
    @Autowired
    private PstPersonPriceService service;

    /**
     * 列表
     *
     * @param pstPersonPrice 查询条件
     * @param page 分页
     * @return Page<PstPersonPrice>
     */
    @GetMapping()
    @RequiresPermissions("pst:person:price:select")
    public Page<PstPersonPrice> select(PstPersonPrice pstPersonPrice, Page<PstPersonPrice> page){
        return service.select(pstPersonPrice, page);
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstPersonPrice
     */
    @GetMapping("{id}")
    @RequiresPermissions("pst:person:price:select")
    public PstPersonPrice get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return PstPersonPrice
     */
    @GetMapping("add")
    @RequiresPermissions("pst:person:price:save")
    public PstPersonPrice add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    @RequiresPermissions("pst:person:price:remove")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param pstPersonPrice 表单内容
     * @return PstPersonPrice
     */
    @PostMapping()
    @RequiresPermissions("pst:person:price:save")
    public PstPersonPrice saveData(@Valid @RequestBody PstPersonPrice pstPersonPrice){
        return service.saveData(pstPersonPrice);
    }
    /**
     * 导出数据
     *
     * @param pstPersonPrice 查询条件
     * @return 文件下载id
     */
    @GetMapping("export/data")
    @RequiresPermissions("pst:person:price:select")
    public String exportData(PstPersonPrice pstPersonPrice){
        return service.exportData(pstPersonPrice);
    }

}
