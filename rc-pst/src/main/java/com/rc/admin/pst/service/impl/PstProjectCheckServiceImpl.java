package com.rc.admin.pst.service.impl;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.constant.CommonConst;
import com.rc.admin.sys.service.ImportService;
import com.rc.admin.util.ToolUtil;
import com.rc.admin.util.office.ExcelUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Arrays;
import java.util.List;
import com.rc.admin.pst.model.PstProjectCheck;
import com.rc.admin.pst.service.PstProjectCheckService;
import com.rc.admin.pst.dao.PstProjectCheckMapper;

/**
 * 项目检查信息
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Service
public class PstProjectCheckServiceImpl extends ServiceImpl<PstProjectCheckMapper, PstProjectCheck> implements PstProjectCheckService, ImportService {

    /**
     * 列表
     *
     * @param pstProjectCheck 查询条件
     * @param page   分页
     * @return Page<PstProjectCheck>
     */
    @Override
    public Page<PstProjectCheck> select(PstProjectCheck pstProjectCheck, Page<PstProjectCheck> page) {
        QueryWrapper<PstProjectCheck> queryWrapper = getQueryWrapper(pstProjectCheck);
        page.setRecords(baseMapper.select(page, queryWrapper));
        return page;
    }

    /**
     * 获取查询条件
     *
     * @param pstProjectCheck 查询条件
     * @return QueryWrapper<PstProjectCheck>
     */
    private QueryWrapper<PstProjectCheck> getQueryWrapper(PstProjectCheck pstProjectCheck){
        QueryWrapper<PstProjectCheck> queryWrapper = new QueryWrapper<>();
        if(pstProjectCheck != null){
            // 查询条件
            // 项目code
            if (Validator.isNotEmpty(pstProjectCheck.getProjectCode())) {
                queryWrapper.eq("t.project_code", pstProjectCheck.getProjectCode());
            }
        }
        return queryWrapper;
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstProjectCheck
     */
    @Override
    public PstProjectCheck get(String id) {
        ToolUtil.checkParams(id);
        return baseMapper.getById(id);
    }

    /**
     * 新增
     *
     * @return PstProjectCheck
     */
    @Override
    public PstProjectCheck add() {
        PstProjectCheck pstProjectCheck = new PstProjectCheck();
        // 设置默认值
        return pstProjectCheck;
    }

    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean remove(String ids) {
        ToolUtil.checkParams(ids);
        List<String> idList = Arrays.asList(ids.split(CommonConst.SPLIT));
        return removeByIds(idList);
    }

    /**
     * 保存
     *
     * @param pstProjectCheck 表单内容
     * @return PstProjectCheck
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public PstProjectCheck saveData(PstProjectCheck pstProjectCheck) {
        ToolUtil.checkParams(pstProjectCheck);
        if (Validator.isEmpty(pstProjectCheck.getId())) {
            // 新增,设置默认值
        }
        return (PstProjectCheck) ToolUtil.checkResult(saveOrUpdate(pstProjectCheck), pstProjectCheck);
    }

    /**
     * 验证数据，插入临时表后调用
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean verificationData(String templateId, String userId) {
        return true;
    }

    /**
     * 导入前回调，插入正式表之前会调用此方法，建议导入正式表之前使用次方法再次验证数据，防止验证 ~ 导入之间数据发送变动
     * 注: 返回false会触发异常回滚
     *
     * @param templateId 模板id
     * @param userId 用户id
     * @return true/false
     */
    @Override
    public boolean beforeImport(String templateId, String userId) {
        return verificationData(templateId, userId);
    }

    /**
     * 导入后回调，插入正式表后会调用此方法
     * 注: 返回false会触发异常回滚
     *
     * @return true/false
     */
    @Override
    public boolean afterImport() {
        return true;
    }

    @Override
    public String exportData(PstProjectCheck pstProjectCheck) {
        QueryWrapper<PstProjectCheck> queryWrapper = getQueryWrapper(pstProjectCheck);
        List<PstProjectCheck> list = baseMapper.exportData(queryWrapper);
        return ExcelUtil.writeAndGetDownloadId("项目检查信息", "项目检查信息", list, PstProjectCheck.class);
    }

}