<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.pst.dao.PstProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.pst.model.PstProject">
        <result column="id" property="id" />
        <result column="project_code" property="projectCode" />
        <result column="project_name" property="projectName" />
        <result column="project_status" property="projectStatus" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="edit_user" property="editUser" />
        <result column="edit_date" property="editDate" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.pst.model.PstProject">
        select t.id, t.project_code, t.project_name, t.project_status, su_edit_user.nickname as edit_user, t.edit_date
        from pst_project t
        left join sys_user su_edit_user on su_edit_user.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectAll" resultType="com.rc.admin.common.core.common.select.Select">
        select project_code as value,concat(project_name, '（', project_code, '）') as label from pst_project
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getById" resultType="com.rc.admin.pst.model.PstProject">
        select t.id, t.project_code, t.project_name, t.project_status, t.create_user, t.create_date, t.edit_user, t.edit_date
        from pst_project t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.pst.model.PstProject">
        select t.project_code, t.project_name, sd_project_status.name as project_status
        from pst_project t
        left join sys_dict sd_project_status on sd_project_status.code = t.project_status and sd_project_status.dict_type = 'projectStatus'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
