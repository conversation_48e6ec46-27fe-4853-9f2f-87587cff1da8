package com.rc.admin.pst.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.common.select.Select;
import com.rc.admin.sys.model.SysDictType;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.rc.admin.pst.model.PstProject;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
public interface PstProjectMapper extends BaseMapper<PstProject> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<PstProject>
     */
    List<PstProject> select(Page<PstProject> page, @Param("ew") QueryWrapper<PstProject> queryWrapper);

    /**
     * 获取项目列表
     *
     * @param queryWrapper 查询条件
     * @return List<Select>
     */
    List<Select> selectAll(@Param("ew") QueryWrapper<SysDictType> queryWrapper);

    /**
     * 查询详细信息
     *
     * @param id id
     * @return PstProject
     */
    PstProject getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<PstProject>
     */
    List<PstProject> exportData(@Param("ew") QueryWrapper<PstProject> queryWrapper);
}