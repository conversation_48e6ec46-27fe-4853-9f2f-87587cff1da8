package com.rc.admin.pst.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import com.rc.admin.core.annotation.ResponseResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import com.rc.admin.common.core.common.pagination.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
import com.rc.admin.pst.model.PstProjectCheck;
import com.rc.admin.pst.service.PstProjectCheckService;

/**
 * 项目检查信息
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@RestController
@ResponseResult
@RequestMapping("/api/pst/project/check")
public class PstProjectCheckController {

    /**
     * 项目检查信息 service
     */
    @Autowired
    private PstProjectCheckService service;

    /**
     * 列表
     *
     * @param pstProjectCheck 查询条件
     * @param page 分页
     * @return Page<PstProjectCheck>
     */
    @GetMapping()
    @RequiresPermissions("pst:project:check:select")
    public Page<PstProjectCheck> select(PstProjectCheck pstProjectCheck, Page<PstProjectCheck> page){
        return service.select(pstProjectCheck, page);
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstProjectCheck
     */
    @GetMapping("{id}")
    @RequiresPermissions("pst:project:check:select")
    public PstProjectCheck get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return PstProjectCheck
     */
    @GetMapping("add")
    @RequiresPermissions("pst:project:check:save")
    public PstProjectCheck add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    @RequiresPermissions("pst:project:check:remove")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param pstProjectCheck 表单内容
     * @return PstProjectCheck
     */
    @PostMapping()
    @RequiresPermissions("pst:project:check:save")
    public PstProjectCheck saveData(@Valid @RequestBody PstProjectCheck pstProjectCheck){
        return service.saveData(pstProjectCheck);
    }
    /**
     * 导出数据
     *
     * @param pstProjectCheck 查询条件
     * @return 文件下载id
     */
    @GetMapping("export/data")
    @RequiresPermissions("pst:project:check:select")
    public String exportData(PstProjectCheck pstProjectCheck){
        return service.exportData(pstProjectCheck);
    }

}
