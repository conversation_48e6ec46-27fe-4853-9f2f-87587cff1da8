package com.rc.admin.pst.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.rc.admin.pst.model.PstProjectCheck;

/**
 * 项目检查信息
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstProjectCheckMapper extends BaseMapper<PstProjectCheck> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<PstProjectCheck>
     */
    List<PstProjectCheck> select(Page<PstProjectCheck> page, @Param("ew") QueryWrapper<PstProjectCheck> queryWrapper);
    /**
     * 查询详细信息
     *
     * @param id id
     * @return PstProjectCheck
     */
    PstProjectCheck getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<PstProjectCheck>
     */
    List<PstProjectCheck> exportData(@Param("ew") QueryWrapper<PstProjectCheck> queryWrapper);
}