package com.rc.admin.pst.model;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.Version;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 项目检查信息
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@TableName("pst_project_check")
public class PstProjectCheck extends Model<PstProjectCheck> {

    /**
     * id
     */
    @TableId
    private String id;
    /**
     * 项目code
     */
    @Excel(name = "项目code", width = 15, orderNum = "0")
    @NotBlank(message = "项目code不能为空")
    private String projectCode;
    /**
     * 检查项
     */
    @Excel(name = "检查项", width = 4, orderNum = "1")
    @NotBlank(message = "检查项不能为空")
    private String inspection;
    /**
     * 检查项说明
     */
    @Excel(name = "检查项说明", width = 15, orderNum = "2")
    @NotBlank(message = "检查项说明不能为空")
    private String inspectionDesc;
    /**
     * 检查项值(%)
     */
    @Excel(name = "检查项值(%)", width = 6, orderNum = "3")
    @NotNull(message = "检查项值(%)不能为空")
    private BigDecimal inspectionValue;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String editUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date editDate;

    // 非表字段

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
    public String getInspection() {
        return inspection;
    }

    public void setInspection(String inspection) {
        this.inspection = inspection;
    }
    public String getInspectionDesc() {
        return inspectionDesc;
    }

    public void setInspectionDesc(String inspectionDesc) {
        this.inspectionDesc = inspectionDesc;
    }
    public BigDecimal getInspectionValue() {
        return inspectionValue;
    }

    public void setInspectionValue(BigDecimal inspectionValue) {
        this.inspectionValue = inspectionValue;
    }
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getEditUser() {
        return editUser;
    }

    public void setEditUser(String editUser) {
        this.editUser = editUser;
    }
    public Date getEditDate() {
        return editDate;
    }

    public void setEditDate(Date editDate) {
        this.editDate = editDate;
    }

}
