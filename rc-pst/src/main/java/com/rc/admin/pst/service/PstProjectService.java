package com.rc.admin.pst.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.common.core.common.select.Select;
import com.rc.admin.pst.model.PstProject;

import java.util.List;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
public interface PstProjectService {
    /**
     * 列表
     *
     * @param pstProject 查询条件
     * @param page   分页
     * @return Page<PstProject>
     */
    Page<PstProject> select(PstProject pstProject, Page<PstProject> page);

    /**
     * 查询所有
     *
     * @return List<Select>
     */
    List<Select> selectAll();

    /**
     * 详情
     *
     * @param id id
     * @return PstProject
     */
    PstProject get(String id);

    /**
     * 新增
     * @return PstProject
     */
    PstProject add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param pstProject 表单内容
     * @return PstProject
     */
    PstProject saveData(PstProject pstProject);

    /**
     * 导出数据
     *
     * @param pstProject 查询条件
     * @return 文件下载id
     */
    String exportData(PstProject pstProject);

}
