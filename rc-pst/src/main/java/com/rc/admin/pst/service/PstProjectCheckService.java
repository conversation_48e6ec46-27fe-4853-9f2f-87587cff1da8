package com.rc.admin.pst.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.pst.model.PstProjectCheck;

/**
 * 项目检查信息
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstProjectCheckService {
    /**
     * 列表
     *
     * @param pstProjectCheck 查询条件
     * @param page   分页
     * @return Page<PstProjectCheck>
     */
    Page<PstProjectCheck> select(PstProjectCheck pstProjectCheck, Page<PstProjectCheck> page);

    /**
     * 详情
     *
     * @param id id
     * @return PstProjectCheck
     */
    PstProjectCheck get(String id);

    /**
     * 新增
     * @return PstProjectCheck
     */
    PstProjectCheck add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param pstProjectCheck 表单内容
     * @return PstProjectCheck
     */
    PstProjectCheck saveData(PstProjectCheck pstProjectCheck);

    /**
     * 导出数据
     *
     * @param pstProjectCheck 查询条件
     * @return 文件下载id
     */
    String exportData(PstProjectCheck pstProjectCheck);

}
