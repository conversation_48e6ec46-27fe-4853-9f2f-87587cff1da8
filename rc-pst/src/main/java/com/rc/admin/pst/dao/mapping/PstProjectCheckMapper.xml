<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.pst.dao.PstProjectCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.pst.model.PstProjectCheck">
        <result column="id" property="id" />
        <result column="project_code" property="projectCode" />
        <result column="inspection" property="inspection" />
        <result column="inspection_desc" property="inspectionDesc" />
        <result column="inspection_value" property="inspectionValue" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="edit_user" property="editUser" />
        <result column="edit_date" property="editDate" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.pst.model.PstProjectCheck">
        select t.id, t.project_code, t.inspection, t.inspection_desc, t.inspection_value, su_edit_user.nickname as edit_user, t.edit_date
        from pst_project_check t
        left join sys_user su_edit_user on su_edit_user.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getById" resultType="com.rc.admin.pst.model.PstProjectCheck">
        select t.id, t.project_code, t.inspection, t.inspection_desc, t.inspection_value, t.create_user, t.create_date, t.edit_user, t.edit_date
        from pst_project_check t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.pst.model.PstProjectCheck">
        select t.project_code, t.inspection, t.inspection_desc, t.inspection_value
        from pst_project_check t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
