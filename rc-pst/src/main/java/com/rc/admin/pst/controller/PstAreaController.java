package com.rc.admin.pst.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import com.rc.admin.core.annotation.ResponseResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import com.rc.admin.common.core.common.pagination.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
import com.rc.admin.pst.model.PstArea;
import com.rc.admin.pst.service.PstAreaService;

/**
 * 住宿费用
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@RestController
@ResponseResult
@RequestMapping("/api/pst/area")
public class PstAreaController {

    /**
     * 住宿费用 service
     */
    @Autowired
    private PstAreaService service;

    /**
     * 列表
     *
     * @param pstArea 查询条件
     * @param page 分页
     * @return Page<PstArea>
     */
    @GetMapping()
    @RequiresPermissions("pst:area:select")
    public Page<PstArea> select(PstArea pstArea, Page<PstArea> page){
        return service.select(pstArea, page);
    }

    /**
     * 详情
     *
     * @param id id
     * @return PstArea
     */
    @GetMapping("{id}")
    @RequiresPermissions("pst:area:select")
    public PstArea get(@PathVariable("id") String id) {
        return service.get(id);
    }

    /**
     * 新增
     *
     * @return PstArea
     */
    @GetMapping("add")
    @RequiresPermissions("pst:area:save")
    public PstArea add() {
        return service.add();
    }
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    @DeleteMapping("{ids}")
    @RequiresPermissions("pst:area:remove")
    public boolean delete(@PathVariable("ids") String ids) {
        return service.remove(ids);
    }

    /**
     * 保存
     *
     * @param pstArea 表单内容
     * @return PstArea
     */
    @PostMapping()
    @RequiresPermissions("pst:area:save")
    public PstArea saveData(@Valid @RequestBody PstArea pstArea){
        return service.saveData(pstArea);
    }
    /**
     * 导出数据
     *
     * @param pstArea 查询条件
     * @return 文件下载id
     */
    @GetMapping("export/data")
    @RequiresPermissions("pst:area:select")
    public String exportData(PstArea pstArea){
        return service.exportData(pstArea);
    }

}
