package com.rc.admin.pst.service;

import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.pst.model.PstPersonPrice;

/**
 * 人天成本单价
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstPersonPriceService {
    /**
     * 列表
     *
     * @param pstPersonPrice 查询条件
     * @param page   分页
     * @return Page<PstPersonPrice>
     */
    Page<PstPersonPrice> select(PstPersonPrice pstPersonPrice, Page<PstPersonPrice> page);

    /**
     * 详情
     *
     * @param id id
     * @return PstPersonPrice
     */
    PstPersonPrice get(String id);

    /**
     * 新增
     * @return PstPersonPrice
     */
    PstPersonPrice add();
    /**
     * 删除
     *
     * @param ids 数据ids
     * @return true/false
     */
    boolean remove(String ids);

    /**
     * 保存
     *
     * @param pstPersonPrice 表单内容
     * @return PstPersonPrice
     */
    PstPersonPrice saveData(PstPersonPrice pstPersonPrice);

    /**
     * 导出数据
     *
     * @param pstPersonPrice 查询条件
     * @return 文件下载id
     */
    String exportData(PstPersonPrice pstPersonPrice);

}
