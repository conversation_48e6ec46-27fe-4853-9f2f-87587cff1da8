package com.rc.admin.pst.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.rc.admin.pst.model.PstPersonPrice;

/**
 * 人天成本单价
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface PstPersonPriceMapper extends BaseMapper<PstPersonPrice> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<PstPersonPrice>
     */
    List<PstPersonPrice> select(Page<PstPersonPrice> page, @Param("ew") QueryWrapper<PstPersonPrice> queryWrapper);
    /**
     * 查询详细信息
     *
     * @param id id
     * @return PstPersonPrice
     */
    PstPersonPrice getById(@Param("id") String id);

    /**
     * 获取列表数据
     *
     * @param queryWrapper 查询条件
     * @return List<PstPersonPrice>
     */
    List<PstPersonPrice> exportData(@Param("ew") QueryWrapper<PstPersonPrice> queryWrapper);
}