<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.pst.dao.PstAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.pst.model.PstArea">
        <result column="id" property="id" />
        <result column="area" property="area" />
        <result column="rank" property="rank" />
        <result column="price" property="price" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="edit_user" property="editUser" />
        <result column="edit_date" property="editDate" />
    </resultMap>
    <select id="select" resultType="com.rc.admin.pst.model.PstArea">
        select t.id, t.area, t.rank, t.price, su_edit_user.nickname as edit_user, t.edit_date
        from pst_area t
        left join sys_user su_edit_user on su_edit_user.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getById" resultType="com.rc.admin.pst.model.PstArea">
        select t.id, t.area, t.rank, t.price, t.create_user, t.create_date, t.edit_user, t.edit_date
        from pst_area t
        where t.id = #{id}
    </select>

    <select id="exportData" resultType="com.rc.admin.pst.model.PstArea">
        select sd_area.name as area, sd_rank.name as rank, t.price
        from pst_area t
        left join sys_dict sd_area on sd_area.code = t.area and sd_area.dict_type = 'travelArea'
        left join sys_dict sd_rank on sd_rank.code = t.rank and sd_rank.dict_type = 'rank'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
