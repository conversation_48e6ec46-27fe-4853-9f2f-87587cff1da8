package com.rc.admin.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.rc.admin.model.BaseFeishuRichText;
import com.rc.admin.model.OpenInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSONString;

@Slf4j
@Component
public class FeishuUtil {

    @Value("${feishu.appId}")
    private String appId;

    @Value("${feishu.appSecret}")
    private String appSecret;

    @Value("${feishu.httpToken}")
    private String httpToken;

    @Value("${feishu.httpMessage}")
    private String httpMessage;

    @Value("${feishu.httpOpenId}")
    private String httpOpenId;

    /**
     * 获取飞书 token
     * @return
     */
    public String getFeishuToken() {
        final CloseableHttpClient httpclient = HttpClients.createDefault();
        final HttpPost httpPost;
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("app_id", appId));
        params.add(new BasicNameValuePair("app_secret", appSecret));
        try {
            httpPost = new HttpPost(httpToken);
            httpPost.setEntity(new UrlEncodedFormEntity(params, HTTP.UTF_8));
            HttpResponse httpResponse = httpclient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            String respString = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(respString);
            if (0 == jsonObject.getInteger("code")) {
                return jsonObject.getString("tenant_access_token");
            }
        } catch (Exception e) {
            log.error("登录失败", e);
        }
        return null;
    }

    /**
     * 推送飞书富文本消息
     */
    public boolean pushFeishuRichText(String token, BaseFeishuRichText text) {
        final CloseableHttpClient httpclient = HttpClients.createDefault();
        final HttpPost httpPost;

        try {
            StringEntity entity = new StringEntity(toJSONString(text), "UTF-8");
            httpPost = new HttpPost(httpMessage);
            httpPost.setEntity(entity);
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            HttpResponse httpResponse = httpclient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            String respString = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(respString);
            if (0 == jsonObject.getInteger("code")) {
                // 推送成功
                return true;
            }
        } catch (Exception e) {
            log.error("飞书推送失败", e);
        }
        return false;
    }
    /**
     * 获取用户OpenId
     */
    public List<String> getHttpOpenId(String token,  Map<String,Object> params) {
        final CloseableHttpClient httpclient = HttpClients.createDefault();
        final HttpPost httpPost;
        try {
            String url = httpOpenId+"?user_id_type=user_id";
            log.info("飞书查询url,{}", url);
            log.info("飞书查询入参,{}", JSONObject.toJSONString(params));
            httpPost = new HttpPost(url);
            if (params != null && !params.isEmpty()) {
                httpPost.setEntity(new StringEntity(JSON.toJSONString(params),"utf-8"));
            }
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            log.info("飞书查询详情,{}", JSONObject.toJSONString(httpPost));
            HttpResponse httpResponse = httpclient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            String respString = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(respString);
            log.info("飞书查询结果,{}", jsonObject);
            if (0 == jsonObject.getInteger("code")) {
                // 推送成功
                OpenInfoResp data = JSONObject.parseObject(jsonObject.getString("data"), OpenInfoResp.class);
                if (data!=null && CollectionUtils.isNotEmpty(data.getUser_list())){
                   return data.getUser_list().stream().map(OpenInfoResp.UserResp::getUser_id).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("飞书推送失败", e);
        }
        return null;
    }
    /**
     * 获取用户OpenId
     */
    public boolean getHttpOpenId1(String token,  Map<String,Object> params) {
        final CloseableHttpClient httpclient = HttpClients.createDefault();
        final HttpGet httpPost;
        try {
            String url = "https://open.work.sany.com.cn/open-apis/user/v1/batch_get_id?mobiles=15200357487";
            log.info("飞书查询url,{}", url);
            log.info("飞书查询入参,{}", JSONObject.toJSONString(params));
            httpPost = new HttpGet(url);
//            if (params != null && !params.isEmpty()) {
//                httpPost.setEntity(new StringEntity(JSON.toJSONString(params),"utf-8"));
//            }
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            log.info("飞书查询详情,{}", JSONObject.toJSONString(httpPost));
            HttpResponse httpResponse = httpclient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            String respString = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(respString);
            log.info("飞书查询结果,{}", jsonObject);
            if (0 == jsonObject.getInteger("code")) {
                // 推送成功
                return true;
            }
        } catch (Exception e) {
            log.error("飞书推送失败", e);
        }
        return false;
    }


    public  String doPostFeishuSend(String token, Map<String, Object> params) {
        Map<String, String> heard = new HashMap<>(10);
        heard.put("Authorization", "Bearer " + token);
        String strResult = "";
        RequestConfig config = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(10000)
                .build();

        HttpPost httpPost = new HttpPost(httpMessage);
        httpPost.setConfig(config);
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

        for (Map.Entry<String, String> entry : heard.entrySet()) {
            httpPost.addHeader(entry.getKey(), entry.getValue());
        }

        if (params != null && !params.isEmpty()) {
            try {
                httpPost.setEntity(new StringEntity(JSON.toJSONString(params), "utf-8"));
            } catch (Exception e) {
                log.error("Unsupported encoding error: ", e);
                return strResult;
            }
        }

        try (CloseableHttpClient client = HttpClients.createDefault();
             CloseableHttpResponse resp = client.execute(httpPost)) {

            HttpEntity respEntity = resp.getEntity();
            if (respEntity != null) {
                strResult = EntityUtils.toString(respEntity, "UTF-8");
            }

        } catch (IOException e) {
            log.error("IO error during HTTP POST request: ", e);
        }

        return strResult;
    }

}
