package com.rc.admin.service.impl;

import com.rc.admin.auth.common.constant.SanyOuTypeEnum;
import com.rc.admin.client.SanyEsbClient;
import com.rc.admin.client.dto.EmployeeInfo;
import com.rc.admin.client.dto.EmployeeResp;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.dao.SysSanyUserMapper;
import com.rc.admin.service.SysSanyUserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysSanyUserServiceImpl implements SysSanyUserService {
    @Autowired
    private SanyEsbClient sanyEsbClient;

    @Resource
    private SysSanyUserMapper sysSanyUserMapper;

    @Override
    public void syncUser(SanyOuTypeEnum outType, String startDate, String endDate) {
        int i = 0;

        while (true) {
            List<EmployeeInfo> sysSanyUsers = sysSanyUserMapper.selectList(null);
            List<String> userIds = sysSanyUsers.stream().map(e -> e.getUserId())
                    .collect(Collectors.toList());
            EmployeeResp employeeResp = sanyEsbClient.getEmployeeList(outType, startDate, endDate,
                    (i * 1000L), 1000L);
            if (null == employeeResp) {
                break;
            }
            List<EmployeeInfo> employeeInfos = employeeResp.getReturnInfoArray();
            if (!CollectionUtils.isEmpty(employeeInfos)) {

                for (EmployeeInfo userEntity : employeeInfos) {
                    try {
                        if (!userIds.contains(userEntity.getUserId())) {
                            sysSanyUserMapper.insert(userEntity);
                        }

                    } catch (Exception e) {
                        throw new EasyException("唯一索引错误:" + userEntity.getUserId());
                    }
                }
            }
            if (!employeeResp.getResultFlag()) {
                break;
            }
            i++;
        }
    }

}
