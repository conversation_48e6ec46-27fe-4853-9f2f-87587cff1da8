package com.rc.admin.model;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AlarmMsgFeishu extends BaseFeishuRichText {

    public void setContent(JSONObject body) {
        JSONObject post = new JSONObject();

        JSONObject zh_cn = new JSONObject();

        String title = body.getString("title");
        String text = body.getString("text");

        zh_cn.put("title", title);
        List<List<JSONObject>> contents = new ArrayList<>();
        List<JSONObject> contentItem = new ArrayList<>();
        JSONObject timeObj = new JSONObject();
        timeObj.put("tag", "text");
        timeObj.put("text", "内容： " + text );
        contentItem.add(timeObj);

        contents.add(contentItem);
        zh_cn.put("content", contents);
        post.put("zh_cn", zh_cn);

        super.setContent(post.toJSONString());
    }
}
