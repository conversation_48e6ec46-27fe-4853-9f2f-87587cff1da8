package com.rc.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.model.AlarmMsgFeishu;
import com.rc.admin.util.FeishuUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/sany/feishu")
public class FeishuController {

    @Value("${feishu.receiveId}")
    private String fetshuReceiveId;

    @Autowired
    private FeishuUtil feishuUtil;

    @PostMapping("message")
    @ApiOperation("消息发送")
    public Response submit(@RequestBody JSONObject body) {

        AlarmMsgFeishu alarmMsgFeishu = new AlarmMsgFeishu();
        alarmMsgFeishu.setContent(body);
        alarmMsgFeishu.setReceive_id(fetshuReceiveId);
        String feishuToken = feishuUtil.getFeishuToken();
        if (StringUtils.isEmpty(feishuToken)) {
            return Response.failError("获取飞书token失败请检查配置！！！");
        }
        boolean result = feishuUtil.pushFeishuRichText(feishuToken, alarmMsgFeishu);
        if (!result) {
            return Response.failError("推送飞书失败：" + alarmMsgFeishu);
        }
        return Response.success();
    }
}
