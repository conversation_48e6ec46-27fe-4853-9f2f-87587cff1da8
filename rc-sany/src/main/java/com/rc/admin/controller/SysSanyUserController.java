package com.rc.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.rc.admin.auth.common.constant.SanyOuTypeEnum;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.service.SysSanyUserService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/sany/user")
public class SysSanyUserController {
    @Autowired
    private SysSanyUserService sysSanyUserService;

    /**
     * 同步三一组织机构和用户（三一域、crm域、顾问域、代理商域）
     * outType
     * @param body
     * @return
     */
    @PostMapping("syncUser")
    @ApiOperation( "同步用户")
    public Response submit(@RequestBody JSONObject body) {
        String outType = body.getString("outType");
        String startDate = body.getString("startDate");
        String endDate = body.getString("endDate");
        if (!StringUtils.isNotEmpty(startDate)) {
            startDate = "1970-11-11";
        }
        if (!StringUtils.isNotEmpty(endDate)) {
            endDate = "2023-05-22";
        }

        if(StringUtils.isNotEmpty(outType)){
            if (SanyOuTypeEnum.SANY.getOuType().equals(outType)) {
                sysSanyUserService.syncUser(SanyOuTypeEnum.SANY, startDate, endDate);
            } else if (SanyOuTypeEnum.SANY_VDR.getOuType().equals(outType)) {
                sysSanyUserService.syncUser(SanyOuTypeEnum.SANY_VDR, startDate, endDate);
            } else if (SanyOuTypeEnum.SANY_CRM.getOuType().equals(outType)) {
                sysSanyUserService.syncUser(SanyOuTypeEnum.SANY_CRM, startDate, endDate);
            } else if (SanyOuTypeEnum.SANY_GROUP.getOuType().equals(outType)) {
                sysSanyUserService.syncUser(SanyOuTypeEnum.SANY_GROUP, startDate, endDate);
            }
        }else{
            for (SanyOuTypeEnum value : SanyOuTypeEnum.values()) {
                sysSanyUserService.syncUser(value, startDate, endDate);
            }
        }



        return Response.success();
    }
}
