<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.auth.dao.SysUserRoleMapper">

    <resultMap id="BaseResultMap" type="com.rc.admin.auth.model.SysUserRole">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
    </resultMap>

    <select id="selectPermissionCodesByUserId" resultType="java.lang.String">
        select p.code
        from sys_permission p
                 left join sys_role_permission rp on rp.permission_id = p.id
                 left join sys_user_role ur on ur.role_id = rp.role_id
        where ur.user_id = #{userId}
          and p.status = #{status}
          and p.code is not null
    </select>
    <select id="selectRoleByUserId" resultType="com.rc.admin.auth.model.SysRole">
        select r.id, r.name, r.code
        from sys_role r
         left join sys_user_role ur on ur.role_id = r.id
        where ur.user_id = #{userId}
          and r.status = #{status}
          and r.code is not null
    </select>
    <select id="selectPermissionByUserId" resultType="com.rc.admin.auth.model.SysPermission">
        select p.id, p.parent_id, p.type, p.name, p.title, p.icon, p.code, p.path, p.component, p.external,
               p.order_no, p.display, p.open_mode
        from sys_permission p
                 left join sys_role_permission rp on rp.permission_id = p.id
                 left join sys_user_role ur on ur.role_id = rp.role_id
        where ur.user_id = #{userId}
          and p.status = #{status}
        group by p.path, p.component, p.name, p.id, p.icon, p.parent_id, p.order_no
        order by p.order_no
    </select>

</mapper>