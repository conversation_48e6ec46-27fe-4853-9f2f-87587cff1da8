package com.rc.admin.auth.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoResp {

  private String userId;

  private String phoneNo;

  private String loginFlag;

  private String timestamp;

  private String username;

  private String employeeNo;

  private String loginAddr;

  private String domainPath;

  private String authGrpInfo;

  private String ctfType;

  private String ctfMth;

  private String ctfDmnInfo;

  private String longTokenFlag;

  private String departmentPath;

  private String mailAddr;

  private String token;

  private String firstDeptNo;

  private String secondDeptNo;

  private String thirdDeptNo;

  private String fourthDeptNo;

  private String fifthDeptNo;

  private String sixthDeptNo;

}