package com.rc.admin.auth.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公共实体
 */
@Data
public class RealTimeDeviceInfoResp {
    @ApiModelProperty(value = "状态")
    private String Status;
    @ApiModelProperty(value = "扫描花费时间")
    private String ScanSpendTime;
    @ApiModelProperty(value = "设备标识")
    private String DeviceID;
    @ApiModelProperty(value = "解释")
    private String Explain;
    @ApiModelProperty(value = "最后断开连接时间")
    private String LastDisconnectTime;
    @ApiModelProperty(value = "配置时间")
    private String ConfigTime;
    @ApiModelProperty(value = "名称")
    private String Name;
    @ApiModelProperty(value = "公司名称")
    private String CompanyName;
    @ApiModelProperty(value = "类型")
    private String Type;
    @ApiModelProperty(value = "最后链接时间")
    private String LastConnectTime;
    @ApiModelProperty(value = "协议地址")
    private String ProtocolType;
    @ApiModelProperty(value = "模型")
    private String Model;
    @ApiModelProperty(value = "更新时间")
    private String UpdateTime;
    @ApiModelProperty(value = "设备代码")
    private String DeviceCode;
    @ApiModelProperty(value = "渠道类型")
    private String ChannelType;
    @ApiModelProperty(value = "链接信息")
    private String ConnectInfo;
}
