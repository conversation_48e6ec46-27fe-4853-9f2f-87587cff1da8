package com.rc.admin.auth.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rc.admin.auth.common.constant.AuthConstant;
import com.rc.admin.auth.common.constant.SessionConst;
import com.rc.admin.auth.dao.SysUserMapper;
import com.rc.admin.auth.model.SysUser;
import com.rc.admin.auth.model.dto.UserInfoResp;
import com.rc.admin.auth.service.AuthService;
import com.rc.admin.client.SanyEsbClient;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.common.redis.cache.AIValueOperations;
import com.rc.admin.common.redis.constant.RedisPrefix;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisUtil;
import com.rc.admin.config.shiro.service.ShiroService;
import com.rc.admin.sys.common.constant.SexConst;
import com.rc.admin.sys.common.constant.SysConfigConst;
import com.rc.admin.sys.model.LoginVO;
import com.rc.admin.sys.model.SysLog;
import com.rc.admin.sys.service.SysCaptchaService;
import com.rc.admin.sys.service.SysLogService;
import com.rc.admin.util.PasswordUtil;
import com.rc.admin.util.RSAUtils;
import com.rc.admin.util.ShiroUtil;
import com.rc.admin.util.SysConfigUtil;
import com.rc.admin.util.file.FileUtil;

import java.security.GeneralSecurityException;
import java.util.Date;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 会话
 *
 * <AUTHOR>
 * @date 2020/9/29
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

  @Autowired
  private ShiroService shiroService;

  @Autowired
  private SysCaptchaService service;

  @Autowired
  private SanyEsbClient sanyEsbClient;

  @Resource
  private SysUserMapper sysUserMapper;

  @Resource
  private AIValueOperations aiValueOperations;

  @Autowired
  private SysLogService sysLogService;

  @Override
  public Subject login(LoginVO loginVO, HttpServletRequest request) {

    log.info("开始登录请求");

    // 解密
    loginVO = decryptPassword(loginVO);

    Subject subject = SecurityUtils.getSubject();
    // 如果已登录，表示调用此方法是为了解除锁屏，则不验证验证码
    if (!subject.isAuthenticated()) {
      // 检查验证码
      captchaVerification(loginVO.getCaptchaVerification());
    }

    // 三一域帐号登录
    Boolean sanyCasLogin =
        (Boolean) SysConfigUtil.get(SysConfigConst.SANY_CAS_LOGIN);
    if (sanyCasLogin==null){
      sanyCasLogin=false;
    }
    if (sanyCasLogin) {
      //图形验证码登录
      imgVerification(loginVO.getVerifyCode(), request.getServerName());
      sanyLogin(loginVO);
    }

    UsernamePasswordToken token = new UsernamePasswordToken(loginVO.getUsername(),
        SecureUtil.md5(loginVO.getPassword()),
            loginVO.getRememberMe() != null && loginVO.getRememberMe());
    subject.login(token);

    SysUser sysUser = (SysUser) subject.getPrincipals().getPrimaryPrincipal();
    // 清空登录计数
    RedisUtil.del(RedisPrefix.LOGIN_LOCK + loginVO.getUsername());

    // 是否为记住我
    if (loginVO.getRememberMe() == null) {
      loginVO.setRememberMe(false);
    }
    subject.getSession().setAttribute(SessionConst.REMEMBER_ME, loginVO.getRememberMe());

    // 更新最后登录时间
    shiroService.updateUserLastLoginDate(sysUser.getId());
    // 检查是否允许用户在多处登录，默认false
    if (!Convert.toBool(SysConfigUtil.get(SysConfigConst.LOGIN_MULTIPOINT), false)) {
      shiroService.kickOutSession(sysUser);
    }
    // 用户信息放在session里
    ShiroUtil.setAttribute(SessionConst.USER_SESSION_KEY, FileUtil.initAvatar(sysUser));

    log.info("结束登录请求");
    return subject;
  }

  /**
   * 解密密码
   *
   * @param loginVO
   * @return
   */
  private LoginVO decryptPassword(LoginVO loginVO) {
    //获取私钥
    String privateKey = aiValueOperations.get(ServiceEnum.TCS_PLATFORM_SERVICE,
        AuthConstant.PRIVATE_KEY_REDIS_KEY);
    if (privateKey ==null)
     privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIQYChuObQnsVWRpsOtgXPaHXrhjU9GFAS6wqWgYpGItuNO95BLude6yR72yZ7565DbtN14SWOvzrIWVluMOLyk5ckmbOOMdqg53nJOaNUMPPXkYchASkH4ETAZs+uLPxHVCZ+9fM47GRiZDGVE3youeEDLWRQdQ7QdpvXfaw6klAgMBAAECgYB2I13e2VmR12ljvdb6HBvobJd9R3BuYYr2LA/Gxss193hm/1zcxaJ50b6x3L5GMHis2yl6m+QlWrCAZJInDceEwyEJ2EbwfniqylkrMXX3QmxNWHpiQlX5ypghva22yJTg8wl21lJ7RL5VrFWFLrr3XQR7Dt7ceUhoaAjtdrHUAQJBAN6o+RBmsQ2O0Wu2/u/g7V/VWelNAseunIMccVzapd5BKdVQZszoeB0e2a4W1IWlJlkuiJ/dVq0/BBlpc2IN+QECQQCX33nhaijZi5TgB0rHnRwCR7OvQd3IWsoa0DNSEiDrXV5AQaI13BdgRcdukKI/aGGssSqX2OXDIsznfY9pcqwlAkEA1CiRWY1f+xkwdsBhXlQmGoCCMYjNs1Kvk0ZQxjcYgRP6NmVr7qc7fBPfOsBKNCXOyn9Kc3lFk8JNt6EA7ld+AQJATqiBAJ2yEJI5hhpgwaf+dLPcPX4YtwdGu8Jvgic/g4d1JESaFdkrBkK9uucQEVUnmYCc9c5xmbJxe8fNOikZPQJAP9jerVhk6E1M9YtqZ4QrMdTvziqofVDt1o2VZhZhr0Ds+Vj35eQSjuE4tXtpPyTU8kl+ubRw5T1QM1bhT/A33A==";
    try {
      loginVO.setPassword(RSAUtils.decrypt(loginVO.getPassword(), privateKey));
    } catch (GeneralSecurityException e) {
      throw new EasyException("解密失败");
    }
    return loginVO;
  }

  public static void main(String[] args) {
  String  privateKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCuGUSRrWgirDwTWfHCYBav2Jk0A8MtUfKQwOz76seRt8bA+kIzvF6V37+i0KeEVqIY/7Y/l8Lj4hPC2R/5tJG0rVBteZG32WL/aGJWrcAZAb5thbxYACr2aSDyR0BIvSZqrcuzTliDCZgXiIl0IXPvzOKTlxUaWaRW2LMBG9ieBwIDAQAB";
//  String  privateKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCiby2AJCMn5S2r7UpUe9rcsc0GNaejdlL94EOuqobiHrZHpRTPrpM2Uk2ZVk+Xpc94ktnfMiZ5pkISXp26cJdwRvMwIZd8/n3Jxja33WubHFYdVICSKz4dowgQx0vKx6gWPEHJRYzhcKuiGmFH2+ANoc8Vz4ffBKPldBTcXE3JlwIDAQAB";


      String decrypt = null;
      try {
        RSAUtils.StrKeyPair keyPair = RSAUtils.getKeyPair();

        decrypt = RSAUtils.encrypt("admin123",privateKey);
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
      System.out.println(decrypt);


  }
  /**
   * 三一域帐号登录
   *
   * @param loginVO
   */
  private void sanyLogin(LoginVO loginVO) {
    log.info("准备登入:**************");
    UserInfoResp userInfoResp = sanyEsbClient.getLogin(loginVO.getUsername(),
        loginVO.getPassword());
    log.info("login result:**************" + JSON.toJSONString(userInfoResp));

    LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysUser::getUsername, loginVO.getUsername());
    SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
    if (userInfoResp.getUserId() == null) {
        if(sysUser == null) {
            throw new EasyException("没有该用户");
        }
    } else {
      // 用户存在但密码已修改
      if (!Objects.isNull(sysUser) && !PasswordUtil.generatingPasswords(loginVO.getPassword(),
          sysUser.getSalt()).equals(sysUser.getPassword())) {
        sysUser.setPassword(PasswordUtil.generatingPasswords(loginVO.getPassword(),
            sysUser.getSalt()));
        sysUserMapper.updateById(sysUser);
      }

      // 用户不存新建用户
      if (Objects.isNull(sysUser)) {
        initAddUser(loginVO, userInfoResp);
      }

    }
  }

  /**
   * 初始增加信息
   * @param loginVO
   * @param userInfoResp
   */
  private void initAddUser(LoginVO loginVO, UserInfoResp userInfoResp) {
    SysUser sysUser;
    sysUser = new SysUser();
    sysUser.setUsername(loginVO.getUsername());
    sysUser.setSalt(RandomUtil.randomString(10));
    sysUser.setPassword(PasswordUtil.generatingPasswords(loginVO.getPassword(),
        sysUser.getSalt()));
    sysUser.setStatus("1");
    sysUser.setDeptId("1");
    sysUser.setPhoneNumber(userInfoResp.getPhoneNo());
    sysUser.setNickname(userInfoResp.getUsername());
    sysUser.setSex(SexConst.BOY);
    sysUserMapper.insert(sysUser);

    SysLog sysLog = new SysLog();
    sysLog.setMethod("增加域帐号");
    sysLog.setModular("sys");
    sysLog.setParams(sysUser.toString());
    // 操作用户信息
    sysLog.setOperationDate(new Date());
    SysUser currentUser = ShiroUtil.getCurrentUser();
    if (currentUser != null) {
      sysLog.setOperationUser(currentUser.getId());
    }
    sysLog.setMethodName("initAddUser");
    sysLog.setHttpMethod("POST");
    sysLogService.saveData(sysLog);
  }

  /**
   * 检查登录验证码
   *
   * @param code code
   */
  private void captchaVerification(String code) {
    Boolean loginVerificationCode =
        (Boolean) SysConfigUtil.get(SysConfigConst.LOGIN_VERIFICATION_CODE);
    if (!loginVerificationCode) {
      return;
    }
    if (StrUtil.isBlank(code)) {
      throw new EasyException("获取验证码数据失败，请重试");
    }
    if (!service.verification(code)) {
      throw new EasyException("验证码无效，请重试");
    }

  }

  /**
   * 检查登录图形验证码
   *
   * @param verifyCode 验证码
   * @param codeKey    redis中的验证码
   */
  private void imgVerification(String verifyCode, String codeKey) {
    Boolean loginImgVerificationCode =
        (Boolean) SysConfigUtil.get(SysConfigConst.LOGIN_IMG_VERIFICATION_CODE);
    if (!loginImgVerificationCode) {
      return;
    }

    String key = ServiceEnum.SANY_TCS.keyPrefix + ":" + "verifyCode:" + codeKey;
    String code = "";
    String objectCode = aiValueOperations.get(key);
    if (StringUtils.isNotEmpty(objectCode)) {
      code = objectCode.toString();
    }
    RedisUtil.del(key);
    if (StringUtils.isEmpty(code) || !code.equalsIgnoreCase(verifyCode)) {
      throw new EasyException("验证码错误");
    }

  }
}
