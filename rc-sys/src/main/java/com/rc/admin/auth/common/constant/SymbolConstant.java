package com.rc.admin.auth.common.constant;

/**
 * 符号常量类
 *
 * <AUTHOR>
 * @date 2021-07-30 14:48
 */
public class SymbolConstant {

  /**
   * 问号
   */
  public static final String QUESTION_MARK = "?";

  /**
   * 属性内容分隔符
   */
  public static final String PROPERTY_DELIMITER = "```";

  /**
   * 下划线常量
   */
  public static final String UNDERSCORE = "_";

  /**
   * 星号
   */
  public static final String ASTERISK = "*";

  /**
   * 冒号常量
   */
  public static final String COLON = ":";

  /**
   * 逗号常量
   */
  public static final String COMMA = ",";

  /**
   * 英文分号
   */
  public static final String DIVIDE_WELL = ";";

  /**
   * 中文分号
   */
  public static final String DIVIDE_WELL_CHINESE = "；";

  /**
   * 单引号
   */
  public static final String APOSTROPHE = "\"";

  /**
   * 反斜杠
   */
  public static final String BACKSLASH = "/";

  /**
   * 左大括号
   */
  public static final String OPENING_BRACE = "{";

  /**
   * 右大括号
   */
  public static final String CLOSING_BRACE = "}";

  /**
   * 左中括号
   */
  public static final String OPENING_BRACKETS = "[";

  /**
   * 右中括号
   */
  public static final String CLOSING_BRACKETS = "]";

  /**
   * 井号常量 用于属性名称和类型的分隔
   */
  public static final String WELL_NUMBER = "#";

  /**
   * 等于号
   */
  public static final String EQUAL = "=";

  /**
   * 点号
   */
  public static final String POINT = ".";

  /**
   * 点号-转义
   */
  public static final String TRANSFER_POINT = "\\.";

  /**
   * and符号
   */
  public static final String AMPERSAND = "&";

}