package com.rc.admin.auth.common.esb;

import lombok.Getter;

public enum SanyEsbServiceEnum {

  INTERNAL_EMPLOYEE_ALL("查询所有用户列表", "0202300101", "/faods/v2/internal/employee/all/employees"),
  EXTERNAL_EMPLOYEE_LIST("通用用户列表信息查询", "0202300112", "/faods/v2/external/employee/list"),
  TICKET_INFO("获取认证", "0503300101", "/v1/tickets"),
  TICKET_CODE("获取票据","0503300102",""),
  USER_INFO("根据票据获取用户信息","0202300107",""),

  INTERNAL_EMPLOYEE_ORG("根据组织筛选用户","","/sany5a/faods/v2/internal/employee/all/org/list"),
  ORG_LIST("查询组织","","/sany5a/faods/v2/org/list");


  @Getter
  private String serviceName;

  @Getter
  private String serviceId;

  @Getter
  private String path;

  SanyEsbServiceEnum(String serviceName, String serviceId, String path) {
    this.serviceName = serviceName;
    this.serviceId = serviceId;
    this.path = path;
  }
}
