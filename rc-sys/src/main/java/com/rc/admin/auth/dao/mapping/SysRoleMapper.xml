<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.auth.dao.SysRoleMapper">
    <resultMap id="BaseResultMap" type="com.rc.admin.auth.model.SysRole">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="sys" property="sys"/>
        <result column="status" property="status"/>
        <result column="order_no" property="orderNo"/>
        <result column="remarks" property="remarks"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="version" property="version"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user" property="createUser"/>
        <result column="edit_user" property="editUser"/>
        <result column="edit_date" property="editDate"/>
    </resultMap>

    <select id="select" resultType="com.rc.admin.auth.model.SysRole">
        select t.id, t.name, t.code, t.sys, t.status, t.order_no, t.remarks, t.tenant_id, t.version, t.edit_date, su.nickname as edit_user
        from sys_role t
        left join sys_user su on su.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getById" resultType="com.rc.admin.auth.model.SysRole">
        select t.id, t.name, t.code, t.sys, t.status, t.order_no, t.remarks, t.tenant_id, t.version
        from sys_role t
        where t.id= #{id}
    </select>
    <select id="selectPermissions" resultType="java.lang.String">
        select permission_id from sys_role_permission rp where rp.role_id = #{id}
    </select>
    <select id="getMaxOrderNo" resultType="java.lang.Integer">
        select COALESCE(max(order_no), 0) from sys_role t
    </select>

    <select id="selectRoleCodeByUserId" resultType="java.lang.String">
        select code from sys_role t
          left join sys_user_role sur on t.id = sur.role_id
        where sur.user_id = #{userId}
    </select>
    <select id="selectAllRoleCodes" resultType="java.lang.String">
        select code from sys_role t
    </select>
    <select id="selectRole" resultType="com.rc.admin.auth.model.SysRole">
        select t.id, t.code, t.name, t.remarks from sys_role t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectAll" resultType="com.rc.admin.auth.model.SysRole">
        select t.id, t.name, t.code from sys_role t where t.status = #{status} order by t.order_no
    </select>
</mapper>