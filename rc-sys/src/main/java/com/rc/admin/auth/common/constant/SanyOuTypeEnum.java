package com.rc.admin.auth.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @date 2022-02-17 10:19
 */
public enum SanyOuTypeEnum {

  /**
   * 标识三一域用户
   */
  SANY("三一域用户", "sany"),

  /**
   * 标识第三方域用户
   */
  SANY_VDR("第三方域用户", "sanyvdr"),

  /**
   * 标识三一CRM域
   */
  SANY_CRM("三一CRM域", "sanycrm"),

  /**
   * 标识SanyGroup域用户
   */
  SANY_GROUP("SanyGroup域用户", "sanyGroup");

  @Getter
  private String ouType;

  @Getter
  private String ouName;

  SanyOuTypeEnum(String ouName, String ouType) {
    this.ouName = ouName;
    this.ouType = ouType;
  }


  @Override
  public String toString() {
    return "SanyOuTypeEnum{" +
        "ouType='" + ouType + '\'' +
        ", ouName='" + ouName + '\'' +
        '}';
  }
}
