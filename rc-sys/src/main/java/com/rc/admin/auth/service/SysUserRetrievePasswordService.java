package com.rc.admin.auth.service;

import java.io.File;
import java.util.List;

/**
 * 找回密码
 *
 * <AUTHOR>
 * @date 2019-03-28
 */
public interface SysUserRetrievePasswordService {
    /**
     * 发送重置密码邮件
     *
     * @param username 账号
     * @param email     邮箱
     * @return true/false
     */
    boolean sendEmail(String username, String email);
    /**
     * 发送富文本邮件
     *
     * @param username 账号
     * @param email     邮箱
     * @return true/false
     */
    boolean sendEmailRichText(String username, String email, String content, File file);

    /**
     * 发送重置密码短信
     *
     * @param username 账号
     * @param mobile     手机号
     * @return 验证码
     */
    String sendSms(String username, String mobile);

    /**
     * 验证验证码
     *
     * @param username 账号
     * @param code     验证码
     * @return true/false
     */
    boolean verifiesCode(String username, String code);

    /**
     * 重置密码
     *
     * @param username 账号
     * @param code     校验码
     * @param password 新密码
     * @return true/false
     */
    boolean resetPassword(String username, String code, String password);

}
