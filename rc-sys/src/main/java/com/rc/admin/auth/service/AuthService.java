package com.rc.admin.auth.service;

import com.rc.admin.sys.model.LoginVO;

import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.subject.Subject;

/**
 * 会话
 *
 * <AUTHOR>
 * @date 2020/9/29
 */
public interface AuthService {

    /**
     * 登录
     *
     * @param loginVO 登录信息
     * @param request 请求信息
     * @return Subject
     */
    Subject login(LoginVO loginVO, HttpServletRequest request);
}
