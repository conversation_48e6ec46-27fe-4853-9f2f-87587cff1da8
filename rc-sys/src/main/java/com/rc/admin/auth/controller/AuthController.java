package com.rc.admin.auth.controller;

import com.rc.admin.auth.common.constant.AuthConstant;
import com.rc.admin.auth.model.dto.VerifyCode;
import com.rc.admin.auth.service.AuthService;
import com.rc.admin.common.core.util.Response;
import com.rc.admin.common.redis.cache.AIValueOperations;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisComposeUtils;
import com.rc.admin.common.redis.util.RedisUtil;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.core.annotation.SysLog;
import com.rc.admin.sys.model.LoginVO;
import com.rc.admin.util.VerifyCodeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 会话
 *
 * <AUTHOR>
 * @date 2020/9/26
 */
@Api(tags = {"会话管理"})
@RestController
@ResponseResult
@Slf4j
@RequestMapping("/api/auth/")
public class AuthController {

  @Autowired
  private AuthService authService;

  @Resource
  private AIValueOperations aiValueOperations;

  @Autowired
  private RedisUtil redisUtil;

  /**
   * 登录
   *
   * @param loginVO loginVO
   * @return token
   */
  @ApiOperation("登录")
  @PostMapping(value = "/login")
  @SysLog(modular = "sys", method = "用户登录")
  public String login(@RequestBody @Valid LoginVO loginVO,
                      HttpServletRequest request) {
    Subject subject = authService.login(loginVO, request);
    return subject.getSession().getId().toString();
  }

  /**
   * 退出
   */
  @ApiOperation("退出")
  @PostMapping("/logout")
  public void logout() {
    SecurityUtils.getSubject().logout();
  }

  /**
   * 获取rsa私钥
   */
  @ApiOperation("获取rsa公钥")
  @GetMapping("/key")
  public Response getKey() {
    String publicKey = aiValueOperations.get(ServiceEnum.TCS_PLATFORM_SERVICE,
        AuthConstant.PUBLIC_KEY_REDIS_KEY);
    return Response.success(publicKey);
  }

  @ApiOperation("验证码")
  @GetMapping("/verify/code")
  public void getVerifyCode(HttpServletRequest request, HttpServletResponse response) {
    VerifyCodeUtil verifyCodeUtil = new VerifyCodeUtil();
    VerifyCode verifyCode = verifyCodeUtil.generate(80, 28);
    String code = verifyCode.getCode();
    String serverName = request.getServerName();

    String key = RedisComposeUtils.composeKey(ServiceEnum.SANY_TCS, "verifyCode:" + serverName);
    aiValueOperations.set(key, code);

    redisUtil.setExpire(key, 100, TimeUnit.MINUTES);
    response.setHeader("Pragma", "no-cache");
    response.setHeader("Cache-Control", "no-cache");
    response.setDateHeader("Expires", 0);
    response.setContentType("image/jpeg");
    try {
      response.getOutputStream().write(verifyCode.getImgBytes());
      response.getOutputStream().flush();
    } catch (IOException e) {
      log.error("return verify code exception", e);
    }
  }

}
