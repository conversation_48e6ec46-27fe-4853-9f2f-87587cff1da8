package com.rc.admin.auth.controller;

import com.rc.admin.common.core.base.BaseController;
import com.rc.admin.core.annotation.ResponseResult;
import com.rc.admin.auth.model.SysUserOnline;
import com.rc.admin.auth.service.SysUserOnlineService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会话管理
 *
 * <AUTHOR>
 * @date 2018/9/12
 */
@RestController
@ResponseResult
@RequestMapping("/api/auth/sys/online")
public class SysOnlineController extends BaseController {

    @Autowired
    private SysUserOnlineService service;

    /**
     * 获取在线用户
     *
     * @return List<SysUserOnline>
     */
    @GetMapping
    @RequiresPermissions("sys:online:select")
    public List<SysUserOnline> select(SysUserOnline sysUserOnline) {
        return service.select(sysUserOnline);
    }

    /**
     * 踢出用户
     *
     * @param token token
     *
     * @return true/false
     */
    @PostMapping("force/logout/{token}")
    @RequiresPermissions("sys:online:force")
    public boolean forceLogin(@PathVariable("token") String token) {
        return service.forceLogout(token);
    }
}
