<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.auth.dao.SysUserMapper">
    <resultMap id="BaseResultMap" type="com.rc.admin.auth.model.SysUser">
        <id column="id" property="id"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
        <result column="nickname" property="nickname"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="sex" property="sex"/>
        <result column="email" property="email"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="birthday" property="birthday"/>
        <result column="avatar" property="avatar"/>
        <result column="status" property="status"/>
        <result column="source" property="source"/>
        <result column="last_login_date" property="lastLoginDate"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="version" property="version"/>
        <result column="create_user" property="createUser"/>
        <result column="create_date" property="createDate"/>
        <result column="edit_user" property="editUser"/>
        <result column="edit_date" property="editDate"/>
    </resultMap>
    <update id="updateUserStatus">
        update sys_user set status = #{status}
        <where>
            ${ew.sqlSegment}
        </where>
    </update>
    <update id="updateUserLastLoginDate">
        update sys_user
        set last_login_date = #{lastLogin}
        where id = #{id}
    </update>
    <update id="resetPassword">
        update sys_user set password = #{password}, salt = #{salt}
        <where>
            ${ew.sqlSegment}
        </where>
    </update>
    <select id="select" resultMap="BaseResultMap">
        select t.id, t.dept_id, t.post_id, t.nickname, t.username, t.sex, t.email, t.phone_number, t.birthday, t.avatar, t.status, t.source,
               t.last_login_date, t.tenant_id, t.create_user, t.create_date, t.edit_user, t.edit_date
        from sys_user t
        left join sys_user_role sur on sur.user_id = t.id
        left join sys_role sr on sr.id = sur.role_id
        <where>
            ${ew.sqlSegment}
        </where>
        group by t.id, t.dept_id, t.post_id, t.nickname, t.username, t.sex, t.email, t.phone_number, t.birthday, t.avatar, t.status, t.source,
        t.last_login_date, t.tenant_id, t.create_user, t.create_date, t.edit_user, t.edit_date
    </select>
    <select id="selectUser" resultType="com.rc.admin.auth.model.SysUser">
        select distinct su.id, su.nickname, su.username, sd.name as depart_name from sys_user su
        left join sys_dept sd on sd.id = su.dept_id
        left join sys_user_role sur on sur.user_id = su.id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getById" resultType="com.rc.admin.auth.model.SysUser">
        select t.id, t.dept_id, t.post_id, t.nickname, t.username, t.sex, t.email, t.phone_number, t.birthday, t.avatar, t.version, t.status, t.source, t.tenant_id
        from sys_user t
        where t.id = #{id}
    </select>
    <select id="selectRoles" resultType="java.lang.String">
        select role_id from sys_user_role ur where ur.user_id = #{id}
    </select>
    <select id="search" resultType="com.rc.admin.auth.model.SysUser">
        select u.id, u.username, u.nickname, u.email, u.phone_number, sd.name as deptName, u.avatar
        from sys_user u
            left join sys_dept sd on sd.id = u.dept_id
            left join sys_dept_type sdt on sdt.code = sd.type_code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectPasswordAndSalt" resultType="com.rc.admin.auth.model.SysUser">
        select u.password, u.salt from sys_user u where u.id = #{id}
    </select>
    <select id="selectUsersByIds" resultType="com.rc.admin.auth.model.SysUser">
        select u.id, u.username, u.nickname, u.email, u.phone_number, sd.name as dept_name, u.avatar
        from sys_user u
             left join sys_dept sd on sd.id = u.dept_id
             left join sys_dept_type sdt on sdt.code = sd.type_code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getSysUserByUserName" resultType="com.rc.admin.auth.model.SysUser">
        select t.id, t.dept_id, t.post_id, t.nickname, t.username, t.password, t.salt, t.sex, t.email, t.phone_number,
               t.birthday, t.avatar, t.status, t.source, t.last_login_date, t.tenant_id
        from sys_user t where t.username = #{account} or t.phone_number = #{account} or t.email = #{account}
    </select>


    <delete id="deleteSubscriptions">
        DELETE
        FROM
            dqm.orc_subscriptions
        WHERE
            account = #{account}
    </delete>
</mapper>