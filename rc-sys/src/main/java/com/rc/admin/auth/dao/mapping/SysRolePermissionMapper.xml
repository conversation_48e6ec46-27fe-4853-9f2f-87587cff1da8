<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.auth.dao.SysRolePermissionMapper">
    <resultMap id="BaseResultMap" type="com.rc.admin.auth.model.SysRolePermission">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="permission_id" property="permissionId"/>
    </resultMap>
    <select id="selectPermissionsByRoleId" resultType="com.rc.admin.auth.model.SysPermission">
        select * from sys_permission p
        left join sys_role_permission rp on rp.permission_id = p.id
        where p.status = #{status} and rp.role_id = #{roleId}
    </select>


</mapper>