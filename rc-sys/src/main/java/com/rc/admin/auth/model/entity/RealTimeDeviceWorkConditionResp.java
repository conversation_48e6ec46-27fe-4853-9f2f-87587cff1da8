package com.rc.admin.auth.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 公共实体
 */
@Data
public class RealTimeDeviceWorkConditionResp {
       @ApiModelProperty(value = "设备标识")
       private String deviceCode;
       @ApiModelProperty(value = "源值")
       private String SourceValue;
       @ApiModelProperty(value = "改变时间")
       private String ChangeTime;
       @ApiModelProperty(value = "地址")
       private String Address;
       @ApiModelProperty(value = "值类型")
       private String ValueType;
       @ApiModelProperty(value = "权威")
       private String Authority;
       @ApiModelProperty(value = "解释")
       private String Explain;
       @ApiModelProperty(value = "最近的价值")
       private String RecentValue;
       @ApiModelProperty(value = "值")
       private String Values;
       @ApiModelProperty(value = "单位")
       private String Unit;
       @ApiModelProperty(value = "配置时间")
       private String ConfigTime;
       @ApiModelProperty(value = "名称")
       private String Name;
       @ApiModelProperty(value = "兰")
       private String Len;
       @ApiModelProperty(value = "质量")
       private String Quality;
       @ApiModelProperty(value = "集合类型")
       private String CollectionType;
       @ApiModelProperty(value = "根连更新时间")
       private String UpdateTime;
       @ApiModelProperty(value = "根连值")
       private String Value;
       @ApiModelProperty(value = "id")
       private String Id;
       @ApiModelProperty(value = "采集频率")
       private String ScanInterval;
       @ApiModelProperty(value = "根云入云时间")
       private String rootCloudTimeCloud;
       @ApiModelProperty(value = "根云本地时间")
       private String rootCloudTimeLocal;
       @ApiModelProperty(value = "根云写入时间")
       private String rootCloudWriteTime;
       @ApiModelProperty(value = "根云值")
       private String rootCloudValue;
       @ApiModelProperty(value = "检查规则主题")
       private String ruleSubject;
       @ApiModelProperty(value = "检查规则公式说明")
       private String ruleRemarks;
       @ApiModelProperty(value = "检查规则表达式")
       private String ruleScript;
       @ApiModelProperty(value = "点位类型")
       private String pointType;
       @ApiModelProperty(value = "点位地址")
       private  String pointAddress;
       @ApiModelProperty(value = "值类型")
       private  String type;
       @ApiModelProperty(value = "读写权限")
       private  String readWritePower;
       @ApiModelProperty(value = "单位")
       private  String units;
       @ApiModelProperty(value = "高频采集")
       private  String frequencyAcquisition;
       @ApiModelProperty(value = "点位值运算")
       private  String express;
       @ApiModelProperty(value = "触发方式")
       private  String eventMode;
       @ApiModelProperty(value = "规则表达式")
       private  String eventCondition;

}
