package com.rc.admin.auth.service.impl;

import com.rc.admin.auth.common.constant.AuthConstant;
import com.rc.admin.common.redis.cache.AIValueOperations;
import com.rc.admin.common.redis.config.JedisFactory;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.RedisComposeUtils;
import com.rc.admin.util.RSAUtils;

import java.util.Collections;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;

/**
 * RSA密钥生成
 *
 * <AUTHOR>
 * @Date 2021/4/28 14:14
 **/
@Slf4j
@Component
public class RSAKeyGeneratingRunner implements ApplicationRunner {



  @Autowired
  private AIValueOperations valueOperations;

  @Autowired
  private JedisFactory jedisFactory;

  private static final String RSA_KEYS_MODIFY_LOCK = "RSA_LOCK";

  @Override
  public void run(ApplicationArguments args) throws Exception {
    Jedis jedis = jedisFactory.getJedis();
    String lockId = UUID.randomUUID().toString();
    String key = RedisComposeUtils.composeKey(ServiceEnum.TCS_PLATFORM_SERVICE, RSA_KEYS_MODIFY_LOCK);
    String set= jedis.set(key, "123", new SetParams().nx().ex(1));
    //尝试3次获取锁
    for (int i = 0; i < 3; i++) {
      if ("OK".equals(set)) {
        try {
          // Redis中没有
          if (!valueOperations.hasKey(ServiceEnum.TCS_PLATFORM_SERVICE,
              AuthConstant.PRIVATE_KEY_REDIS_KEY)) {
            RSAUtils.StrKeyPair keyPair = RSAUtils.getKeyPair();
            valueOperations.set(ServiceEnum.TCS_PLATFORM_SERVICE, AuthConstant.PRIVATE_KEY_REDIS_KEY,
                keyPair.getPrivateKey());
            valueOperations.set(ServiceEnum.TCS_PLATFORM_SERVICE, AuthConstant.PUBLIC_KEY_REDIS_KEY,
                keyPair.getPublicKey());
          } else {
            log.info("RSA keys have been generated.");
          }
        } finally {
          String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
          jedis.eval(script, Collections.singletonList(key), Collections.singletonList(lockId));
          //lock.unlock();
        }
        // 设置完毕，不需要重复获取锁并确认
        break;
      }
    }
  }
}