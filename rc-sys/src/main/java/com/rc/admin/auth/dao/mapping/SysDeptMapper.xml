<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.auth.dao.SysDeptMapper">
    <resultMap id="BaseResultMap" type="com.rc.admin.auth.model.SysDept">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="simple_name" property="simpleName"/>
        <result column="code" property="code"/>
        <result column="type_code" property="typeCode"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="order_no" property="orderNo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="version" property="version"/>
        <result column="create_user" property="createUser"/>
        <result column="create_date" property="createDate"/>
        <result column="edit_user" property="editUser"/>
        <result column="edit_date" property="editDate"/>
    </resultMap>
    <select id="select" resultMap="BaseResultMap">
        select t.id, t.parent_id, t.name, t.simple_name, t.code, t.type_code, dt.name as type_name,
               t.status, t.order_no, su.nickname as edit_user, t.edit_date
        from sys_dept t
        left join sys_dept_type dt on dt.code = t.type_code
        left join sys_user su on su.id = t.edit_user
        <where>
            ${ew.sqlSegment}
        </where>
        order by t.order_no
    </select>
    <select id="selectAll" resultType="com.rc.admin.common.core.common.tree.Tree">
        select t.id, t.parent_id, t.name as title, t.id as key, t.code as data
        from sys_dept t
        where t.status = #{status}
        order by t.order_no
    </select>
    <select id="getMaxOrderNo" resultType="java.lang.Integer">
        select COALESCE(max(order_no), 0)
        from sys_dept t
        where t.parent_id = #{parentId}
    </select>

    <select id="selectCountByTypeIds" resultType="java.lang.Integer">
        select count(1) from sys_dept t
        left join sys_dept_type dt on dt.code = t.type_code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectOptionByTypeCode" resultType="com.rc.admin.common.core.common.select.Select">
        select t.id as value, t.name as label
        from sys_dept t
        where t.type_code = #{typeCode}
    </select>
    <select id="selectOptionByParentTypeCode" resultType="com.rc.admin.common.core.common.select.Select">
        select t.id as value, t.name as label
        from sys_dept t
        where type_code = (select code from sys_dept_type t where t.id = (select parent_id from sys_dept_type where code = #{code}))
    </select>
    <select id="getById" resultType="com.rc.admin.auth.model.SysDept">
        select t.id, t.name, t.simple_name, t.code, t.type_code, t.status, t.tenant_id, sdt.name as type_name
        from sys_dept t
        left join sys_dept_type sdt on t.type_code = sdt.code
        where t.id = #{id}
    </select>
    <select id="selectDepartments" resultType="com.rc.admin.auth.model.SysDept">
        select sd.id, sd.name from sys_dept sd
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <update id="updateOrderBatch">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update sys_dept
            <set>
                parent_id = #{item.parentId},
                order_no = #{item.orderNo}
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>