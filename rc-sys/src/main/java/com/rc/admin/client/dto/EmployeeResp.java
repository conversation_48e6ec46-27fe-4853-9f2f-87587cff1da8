/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rc.admin.client.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页查询员工信息列表返回对象
 *
 * <AUTHOR>
 * @date 2022-02-17 14:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeResp {

  /**
   * true:还有更多用户 false:没有用户了
   */
  private Boolean resultFlag;

  /**
   * 用户信息集合
   */
  List<EmployeeInfo> returnInfoArray;


}
