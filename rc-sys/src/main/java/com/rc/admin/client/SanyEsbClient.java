package com.rc.admin.client;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSONObject;
import com.rc.admin.auth.common.constant.SanyOuTypeEnum;
import com.rc.admin.auth.common.esb.SanyEsbServiceEnum;
import com.rc.admin.auth.model.dto.UserInfoResp;
import com.rc.admin.client.dto.EmployeeResp;
import com.rc.admin.client.dto.OrgTicketResp;
import com.rc.admin.client.dto.SanyIntegratedPlatformRequestParam;
import com.rc.admin.client.dto.SystemHead;
import com.rc.admin.client.dto.TicketResp;
import com.rc.admin.client.dto.UserDataResult;
import com.rc.admin.client.dto.UserInfoResult;
import com.rc.admin.common.core.exception.EasyException;
import com.rc.admin.util.HttpUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 三一人员组织中心接口客户端
 *
 * <AUTHOR>
 * @date 2022-02-16 18:44
 */
@Component
@Slf4j
public class SanyEsbClient {


  //在单点登录系统注册的域名
  @Value("${esb.casService}")
  private String casService;

  @Value("${esb.authKey}")
  private String authKey;

  @Value("${esb.esbHost}")
  private String esbHost;


  public UserInfoResp getLogin(String username, String password) {
    UserInfoResp userInfoResp = UserInfoResp.builder().build();
    TicketResp ticketResp = getTicket(username, password);

    if (null != ticketResp && ticketResp.getSuccessFlag() != null && ticketResp.getSuccessFlag()) {
      String ticketCode = getTicketCode(ticketResp.getTicketInfo());
      if (!StringUtils.isEmpty(ticketCode)) {
        userInfoResp = getUserInfoByTicketCode(ticketCode);
        userInfoResp.setToken(ticketCode);
      }
    } else {
      //throw new EasyException("用户密码不正确");
    }
    return userInfoResp;
  }

  public TicketResp getTicket(String username, String password) {

    TicketResp ticketResp = TicketResp.builder().build();
    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.TICKET_INFO);
    /**
     * 组装body的请求参数
     */

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("clntServUrl", casService);
    bodyMap.put("userName", username);
    bodyMap.put("userPwd", password);

    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    Map<String, String> headerMap = getHeaderMap();
    String paramStr = "";
    try {
      paramStr = JSONUtil.toJsonStr(requestParam);
    } catch (Exception e) {
      throw new EasyException("解析异常");
    }
    String result = HttpUtil.post(esbHost, paramStr, headerMap);

    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          ticketResp = JSONObject.parseObject(body, TicketResp.class);
        }
      }
    }
    return ticketResp;
  }

  private SystemHead getSystemHead(SanyEsbServiceEnum sanyEsbServiceEnum) {
    /**
     * 组装请求参数的sysHead
     */
    String serviceId = sanyEsbServiceEnum.getServiceId();
    SystemHead systemHead = SystemHead.builder().build();
    systemHead.setServiceId(serviceId);
    systemHead.setConsumerId("SANYTCS");
    systemHead.setOrigCnsmrId("SANYTCS");
    systemHead.setCnsmrSeqNo(UUID.randomUUID().toString().replaceAll("-", ""));
    systemHead.setApplyDateTime(DateUtil.format(new Date(),"yyyyMMddHHmmss"));

    return systemHead;
  }

  private SanyIntegratedPlatformRequestParam getRequestParam(SystemHead systemHead,
                                                             Map<String, Object> bodyMap) {
    SanyIntegratedPlatformRequestParam requestParam = SanyIntegratedPlatformRequestParam.builder()
        .build();
    requestParam.setSysHead(systemHead);
    requestParam.setBody(bodyMap);
    return requestParam;
  }

  private Map<String, String> getHeaderMap() {
    Map<String, String> headerMap = new HashMap<>();
    headerMap.put("Content-Type", "application/json");
    headerMap.put("authKey", authKey);
    return headerMap;
  }

  public String getTicketCode(String ticketInfo) {
    String ticketCode = null;
    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.TICKET_CODE);
    /**
     * 组装body的请求参数
     */

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("clntServUrl", casService);
    bodyMap.put("ticketInfo", ticketInfo);

    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.post(esbHost, JSONObject.toJSONString(requestParam), headerMap);

    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          ticketCode = JSONObject.parseObject(body).getString("ticketCode");
        }
      }
    }
    return ticketCode;
  }

  public UserInfoResp getUserInfoByTicketCode(String ticket) {
    UserInfoResp userInfoResp = UserInfoResp.builder().build();
    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.USER_INFO);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("ticketCode", ticket);
    bodyMap.put("clntServUrl", casService);

    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.post(esbHost, JSONObject.toJSONString(requestParam), headerMap);

    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          userInfoResp = JSONObject.parseObject(body, UserInfoResp.class);
        }
      }
    }
    return userInfoResp;
  }


  /**
   * 查询所有用户列表
   */
  public UserDataResult getAllEmployeeByCode(String orgCode) {
    UserDataResult employeeResp = UserDataResult.builder().build();

    String host = esbHost;
    String path = SanyEsbServiceEnum.INTERNAL_EMPLOYEE_ORG.getPath();

    String url = StringUtils.join(host, path);
    url = url + "?isRecursion=1&orgCode=" + orgCode;
    log.info(
        "SanyEsbClient getAllEmployee url=" + url);
    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.get(url, headerMap);
    log.info(
        "SanyEsbClient getAllEmployee url=" + url
            + " result=" + result);
    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("data");
        if (!StringUtils.isEmpty(body)) {
          System.out.printf("data-1");
          employeeResp = JSONObject.parseObject(body, UserDataResult.class);
        }
      }
    }
    return employeeResp;
  }

  public UserInfoResult getAllEmployeeInfoByCode(String orgCode) {
    UserInfoResult employeeResp = UserInfoResult.builder().build();

    String host = esbHost;
    String path = SanyEsbServiceEnum.INTERNAL_EMPLOYEE_ORG.getPath();

    String url = StringUtils.join(host, path);
    url = url + "?isRecursion=1&orgCode=" + orgCode;
    log.info(
            "SanyEsbClient getAllEmployee url=" + url);
    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.get(url, headerMap);
    log.info(
            "SanyEsbClient getAllEmployee url=" + url
                    + " result=" + result);
    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("data");
        if (!StringUtils.isEmpty(body)) {
          System.out.printf("data-1");
          employeeResp = JSONObject.parseObject(body, UserInfoResult.class);
        }
      }
    }
    return employeeResp;
  }

  public EmployeeResp getInternalEmployeeList( String startDate,
                                              String endDate, Long startLineNum, Long returnLineNum){
    EmployeeResp employeeResp = EmployeeResp.builder().build();
    long fixStartLine = startLineNum < 0 ? 0 : startLineNum;
    long fixReturnLine = returnLineNum > 1000 ? 1000 : returnLineNum;

    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.INTERNAL_EMPLOYEE_ALL);
    /**
     * 组装body的请求参数
     */
    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("startDate", startDate);
    bodyMap.put("endDate", endDate);
    bodyMap.put("startLineNum", fixStartLine);
    bodyMap.put("returnLineNum", fixReturnLine);
    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    String host = esbHost;
    String path = SanyEsbServiceEnum.EXTERNAL_EMPLOYEE_LIST.getPath();

    String url = StringUtils.join(host, path);
    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.post(url, JSONObject.toJSONString(requestParam), headerMap);
    log.info("返回结果:result" + result);
    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          employeeResp = JSONObject.parseObject(body, EmployeeResp.class);
        }
      }
    }
    return employeeResp;
  }


  /**
   * 分页获取域用户列表 实现： 三一这边没有接口提供出来一次性查询集团所有用户，提供了根据域账号查询整个域下的用户列表，目前他们有四个域，每个域需要进行分页查询获取用户
   *
   * @param sanyOuTypeEnum 账号类别： "sany"（标识三一域用户）、"sanyvdr", （标识第三方域用户） 、"sanycrm",
   *                       （标识三一CRM域）、"sanyGroup"（标识SanyGroup域用户）
   */
  public EmployeeResp getEmployeeList(SanyOuTypeEnum sanyOuTypeEnum, String startDate,
                                      String endDate, Long startLineNum, Long returnLineNum) {
    EmployeeResp employeeResp = EmployeeResp.builder().build();
    long fixStartLine = startLineNum < 0 ? 0 : startLineNum;
    long fixReturnLine = returnLineNum > 1000 ? 1000 : returnLineNum;

    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.EXTERNAL_EMPLOYEE_LIST);
    /**
     * 组装body的请求参数
     */
    String ouType = sanyOuTypeEnum.getOuType();
    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("ouType", ouType);
    bodyMap.put("startDate", startDate);
    bodyMap.put("endDate", endDate);
    bodyMap.put("startLineNum", fixStartLine);
    bodyMap.put("returnLineNum", fixReturnLine);

    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    String host = esbHost;
    String path = SanyEsbServiceEnum.EXTERNAL_EMPLOYEE_LIST.getPath();

    String url = StringUtils.join(host, path);
    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.post(url, JSONObject.toJSONString(requestParam), headerMap);
    log.info("返回结果:result" + result);
    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          employeeResp = JSONObject.parseObject(body, EmployeeResp.class);
        }
      }
    }
    return employeeResp;
  }

  /**
   * 分页获取域用户列表 实现： 三一这边没有接口提供出来一次性查询集团所有用户，提供了根据域账号查询整个域下的用户列表，目前他们有四个域，每个域需要进行分页查询获取用户
   *
   * @param sanyOuTypeEnum 账号类别： "sany"（标识三一域用户）、"sanyvdr", （标识第三方域用户） 、"sanycrm",
   *                       （标识三一CRM域）、"sanyGroup"（标识SanyGroup域用户）
   */
  public EmployeeResp getEmployeeListByCode(SanyOuTypeEnum sanyOuTypeEnum, String startDate,
                                            String endDate, Long startLineNum, Long returnLineNum) {
    EmployeeResp employeeResp = EmployeeResp.builder().build();
    long fixStartLine = startLineNum < 0 ? 0 : startLineNum;
    long fixReturnLine = returnLineNum > 1000 ? 1000 : returnLineNum;

    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.EXTERNAL_EMPLOYEE_LIST);
    /**
     * 组装body的请求参数
     */
    String ouType = sanyOuTypeEnum.getOuType();
    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("ouType", ouType);
    bodyMap.put("startDate", startDate);
    bodyMap.put("endDate", endDate);
    bodyMap.put("startLineNum", fixStartLine);
    bodyMap.put("returnLineNum", fixReturnLine);

    SanyIntegratedPlatformRequestParam requestParam = getRequestParam(systemHead, bodyMap);

    String host = esbHost;
    String path = SanyEsbServiceEnum.INTERNAL_EMPLOYEE_ORG.getPath();

    String url = StringUtils.join(host, path);
    Map<String, String> headerMap = getHeaderMap();
    String result = HttpUtil.post(url, JSONObject.toJSONString(requestParam), headerMap);

    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        String body = data.getString("body");
        if (!StringUtils.isEmpty(body)) {
          employeeResp = JSONObject.parseObject(body, EmployeeResp.class);
        }
      }
    }
    return employeeResp;
  }


  /**
   * 获取组织
   */
  public OrgTicketResp getOrgList(String startDate,
                                  String endDate, Long startLineNum, Long returnLineNum) {
    OrgTicketResp employeeResp = OrgTicketResp.builder().build();
    long fixStartLine = startLineNum < 0 ? 0 : startLineNum;
    long fixReturnLine = returnLineNum > 1000 ? 1000 : returnLineNum;

    /**
     * 组装请求参数的sysHead
     */
    SystemHead systemHead = getSystemHead(SanyEsbServiceEnum.EXTERNAL_EMPLOYEE_LIST);
    /**
     * 组装body的请求参数
     */

    String host = esbHost;
    String path = SanyEsbServiceEnum.ORG_LIST.getPath();

    String url = StringUtils.join(host, path);
    url = url + "?beginDate=" + startDate + "&endDate=" + endDate + "&offset=" + fixStartLine
        + "&rows=" + fixReturnLine;

    log.info("url****" + url);
    Map<String, String> headerMap = getHeaderMap();

    String result = HttpUtil.get(url, headerMap);

    if (!StringUtils.isEmpty(result)) {
      JSONObject data = JSONObject.parseObject(result);
      if (data != null) {
        employeeResp = JSONObject.parseObject(result, OrgTicketResp.class);
      }
    }
    return employeeResp;
  }

}