package com.rc.admin.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeInfoUser {
    private String employeeId;

    private String userId;

    private String phoneNumber;

    private String orgCode;

    private String fullName;

    private  String resLocation;

    private  String institution;

    private  String major;

    private  String officeEmail;

    private  String idCard;

    private  String birthday;

    private  String gender;

    private  String rank;

    private  String picUrl ;

    private  String ouPath;

    private  String leaderEmployeeId;

}
