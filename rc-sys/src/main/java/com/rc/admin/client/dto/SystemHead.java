/*
 * Copyright (c) 2016, 2021, Rootcloud Technology Co., Ltd. All rights reserved.
 * Rootcloud Technology PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.rc.admin.client.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2022-02-17 9:49
 */
@Data
@Builder
public class SystemHead {

  /**
   * 接口编码，具体调用的接口编码
   */
  private String serviceId;

  /**
   * 消费系统编号
   */
  private String consumerId;

  /**
   * 原始消费系统编号
   */
  private String origCnsmrId;

  /**
   * 消费系统流水号，32位UUID
   */
  private String cnsmrSeqNo;

  /**
   * 服务请求系统的日期，格式为YYYYMMDDHHMMSS
   */
  private String applyDateTime;

}