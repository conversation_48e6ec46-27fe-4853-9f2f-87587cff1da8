/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rc.admin.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrgResp {

  private String orgCode;

  private String orgName;

  private String orgDescriptionEn;

  private String parentOrgCode;

  private String deptLevel;

  private String enable;

  private String orgNameEn;

  private String sortNo;

  private String description;

  private String leaderId;

  private String function;

}
