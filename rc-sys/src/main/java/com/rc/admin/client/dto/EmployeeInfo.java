/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rc.admin.client.dto;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 集团用户信息对象
 *
 * <AUTHOR>
 * @date 2022-02-17 14:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_sany_user")
public class EmployeeInfo {

  @TableId(value = "id")
  private String id;
  /**
   * 工号（内部用户，CRM用户，海外合资用户）身份证号（外部用户）
   */
  private String employeeNo;
  /**
   * 域账号
   */
  private String userId;
  /**
   * 职位代码（内部用户）
   */
  private String positionCode;
  /**
   * 职位名称（内部用户）
   */
  private String positionName;
  /**
   * 组织单位代码（内部用户）
   */
  private String orgCode;
  /**
   * 关键岗编码(0001:关键岗,0002:核心岗,0003:支持岗位,0006:类关键岗,0007:类核心岗,0008:类支持岗,0009:一线生产岗,0010:实习岗)
   */
  private String keyPostNo;
  /**
   * 岗系（内部用户）
   */
  private String postSrs;
  /**
   * 直接上级领导工号(内部用户)
   */
  private String supEmplNo;
  /**
   * 姓（内部用户）
   */
  private String lastName;
  /**
   * 名（内部用户）
   */
  private String firstName;
  /**
   * 性别代码：1男2女（内部用户）
   */
  private String genderCode;
  /**
   * 出生日期（内部用户） YYYYMMDD
   */
  private String birthDate;
  /**
   * 联系电话（所有用户）
   */
  private String phoneNo;
  /**
   * 办公电话（内部用户）
   */
  private String ofcPhnNo;
  /**
   * 一级部门（内部用户）
   */
  private String firstDeptNo;
  /**
   * 二级部门（内部用户）
   */
  private String secondDeptNo;
  /**
   * 三级部门（内部用户）
   */
  private String thirdDeptNo;
  /**
   * 工四级部门（内部用户）
   */
  private String fourthDeptNo;
  /**
   * 五级部门（内部用户）
   */
  private String fifthDeptNo;
  /**
   * 六级部门（内部用户）
   */
  private String sixthDeptNo;
  /**
   * 身份证号（内部用户）
   */
  private String idCardNo;
  /**
   * 离职日期（内部用户） YYYYMMDD
   */
  private Date quitDate;
  /**
   * 行政等级（内部用户）
   */
  private String adminLvl;
  /**
   * 国籍（内部用户）
   */
  private String nationName;
  /**
   * 员工职级（内部用户）
   */
  private String rankCode;
  /**
   * 干部类（内部用户）
   */
  private String cadreCode;
  /**
   * OA邮箱账号（内部用户）
   */
  private String mailAddr;
  /**
   * 入职日期（内部用户） YYYYMMDD
   */
  private String entryDate;
  /**
   * 更新时间 YYYYMMDD
   */
  private Date updateDate;
  /**
   * 员工类型( 只有三一内部用户才会有，值为11,12,15)
   */
  private String employeeType;
  /**
   * 护照号码（内部用户）
   */
  private String passportNo;
  /**
   * 员工照片URL（内部用户）
   */
  private String pictureUrl;
  /**
   * 岗位编码（内部用户）
   */
  private String postCode;
  /**
   * 英文姓名（内部用户）
   */
  private String englishName;
  /**
   * 一级部门名称
   */
  private String firstDeptName;
  /**
   * 二级部门名称
   */
  private String secondDeptName;
  /**
   * 三级部门名称
   */
  private String thirdDeptName;
  /**
   * 四级部门名称
   */
  private String fourthDeptName;
  /**
   * 五级部门名称
   */
  private String fifthDeptName;
  /**
   * 六级部门名称
   */
  private String sixthDeptName;
  /**
   * 部门完整路径
   */
  private String departmentPath;
  /**
   * ou路径(CN字段+OU路径)
   */
  private String ouPath;
  /**
   * 常驻地点名称（内部用户）
   */
  private String constantAddr;
  /**
   * 辅助研发岗位代码（内部用户）
   */
  private String subPostCode;
  /**
   * 研发辅助岗位名称（内部用户）
   */
  private String subPostName;
  /**
   * 用工关系（内部用户）
   */
  private String emplRelCode;
  /**
   * 学历（内部用户）
   */
  private String educationName;
  /**
   * 培训的机构/地点-毕业学校（内部用户）
   */
  private String schoolName;
  /**
   * 所学专业（内部用户）
   */
  private String majorName;
  /**
   * 姓名（所有用户）
   */
  private String employeeName;
  /**
   * 禁用时间（所有用户类型） YYYYMMDDHHMMSS
   */
  private Date disableTime;
  /**
   * 用户状态 0 正常 1 禁用 2 删除（所有用户）
   */
  private String userStatus;
  /**
   * 纳税单位ID（内部用户）
   */
  private String rtpyOrgCode;
  /**
   * 纳税单位名称（内部用户）
   */
  private String rtpyOrgName;

  private String userName;

}

