package com.rc.admin.exception;

public class CustomException extends  RuntimeException{
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public CustomException(Integer code,String message, Throwable e) {
        super(message, e);
        this.code = code;
        this.message = message;
    }

    public CustomException(Integer code,String message) {
        this.code = code;
        this.message = message;
    }

    public CustomException(ResultCode resultCode) {
        this.message = resultCode.getMsg();
        this.code = resultCode.getCode();
    }

    public CustomException(String message) {
        this.code = ResultCode.SERVER_ERROR.getCode();// 默认500
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }
}
