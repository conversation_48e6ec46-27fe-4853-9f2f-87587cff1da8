package com.rc.admin.util;

import com.baomidou.mybatisplus.core.toolkit.StringPool;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EnumConstraintValidator implements ConstraintValidator<Enum, Object> {
    private int[] intValues;
    private String[] strValues;
    private String strs;
    private boolean ignoreCase;

    @Override
    public void initialize(Enum constraintAnnotation) {
        intValues = constraintAnnotation.intValues();
        strValues = constraintAnnotation.strValues();
        ignoreCase = constraintAnnotation.ignoreCase();
        strs = constraintAnnotation.strs();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext constraintValidatorContext) {
        if (value instanceof String) {
            for (String s : strValues) {
                if (ignoreCase) {
                    if (s.equalsIgnoreCase(value.toString())) {
                        return true;
                    }
                } else {
                    if (s.equals(value)) {
                        return true;
                    }
                }
            }
            for (String s : strs.split(StringPool.COMMA)) {
                if (ignoreCase) {
                    if (s.equalsIgnoreCase(value.toString())) {
                        return true;
                    }
                } else {
                    if (s.equals(value)) {
                        return true;
                    }
                }
            }
        }
        if (value instanceof Integer) {
            for (int i : intValues) {
                if (i == Integer.parseInt(value.toString())) {
                    return true;
                }
            }
        }
        return false;
    }
}
