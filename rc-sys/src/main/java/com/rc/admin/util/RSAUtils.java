package com.rc.admin.util;

import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import javax.crypto.Cipher;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * RSA加解密工具类
 *
 * <AUTHOR>
 * @Date 2021/4/27 17:00
 **/
@Slf4j
public class RSAUtils {

  /**
   * 字符串形式的密钥对
   */
  @Data
  @Builder
  public static class StrKeyPair {

    private String privateKey;

    private String publicKey;

  }

  /**
   * 随机生成密钥对
   *
   * @throws NoSuchAlgorithmException
   */
  public static StrKeyPair getKeyPair() throws NoSuchAlgorithmException {
    // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
    KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
    // 初始化密钥对生成器，密钥大小为96-1024位
    keyPairGen.initialize(1024, new SecureRandom());
    // 生成一个密钥对，保存在keyPair中
    KeyPair keyPair = keyPairGen.generateKeyPair();
    // 得到私钥
    RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
    // 得到公钥
    RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
    String publicKeyString = new String(Base64.getEncoder().encode(publicKey.getEncoded()));
    // 得到私钥字符串
    String privateKeyString = new String(Base64.getEncoder().encode((privateKey.getEncoded())));
    return StrKeyPair.builder().privateKey(privateKeyString).publicKey(publicKeyString).build();
  }

  /**
   * RSA公钥加密
   *
   * @param str       加密字符串
   * @param publicKey 公钥
   * @return 密文
   * @throws Exception 加密过程中的异常信息
   */
  public static String encrypt(String str, String publicKey) throws Exception {
    // base64编码的公钥
    byte[] decoded = Base64.getDecoder().decode(publicKey);
    RSAPublicKey pubKey =
        (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
    // RSA加密
    // Cipher cipher = Cipher.getInstance("RSA");
    Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
    cipher.init(Cipher.ENCRYPT_MODE, pubKey);
    String outStr = Base64.getEncoder().encodeToString(cipher.doFinal(str.getBytes("UTF-8")));
    return outStr;
  }

  /**
   * RSA私钥解密
   *
   * @param str        加密字符串
   * @param privateKey 私钥
   * @return 铭文
   * @throws Exception 解密过程中的异常信息
   */
  public static String decrypt(String str, String privateKey) throws GeneralSecurityException {
    String outStr = null;
    // 64位解码加密后的字符串
    byte[] inputByte = Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8));
    // base64编码的私钥
    byte[] decoded = Base64.getDecoder().decode(privateKey);
    RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
    // RSA解密
    // Cipher cipher = Cipher.getInstance("RSA");
    Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
    // Cipher cipher = Cipher.getInstance("RSA/None/PKCS1Padding");
    cipher.init(Cipher.DECRYPT_MODE, priKey);
    outStr = new String(cipher.doFinal(inputByte));
    return outStr;
  }

}