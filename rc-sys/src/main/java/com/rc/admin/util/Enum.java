package com.rc.admin.util;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = EnumConstraintValidator.class)
public @interface Enum {
    //字符串数组类限制值
    String[] strValues() default {};

    //字符串类限制值 逗号分隔
    String strs() default "";

    //数字类限制值
    int[] intValues() default {};

    //限制值是否忽略大小写 对strValues()生效
    boolean ignoreCase() default false;

    String message() default "对应值非枚举字符属性";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @interface List {
        Enum[] value();
    }
}
