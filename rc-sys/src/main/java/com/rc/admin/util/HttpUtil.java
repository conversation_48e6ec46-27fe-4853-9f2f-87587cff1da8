package com.rc.admin.util;


import com.rc.admin.auth.common.constant.SymbolConstant;

import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

/**
 * http工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {

  static final int MAX_RETRY = 5;

  static final int TIME_OUT = 600 * 1000;

  private static CloseableHttpClient httpClient = null;

  private final static Object SYNC_LOCK = new Object();

  private static void config(HttpRequestBase httpRequestBase) {
    // 配置请求的超时设置
    RequestConfig requestConfig = RequestConfig.custom()
        .setConnectionRequestTimeout(TIME_OUT)
        .setConnectTimeout(TIME_OUT).setSocketTimeout(TIME_OUT).build();
    httpRequestBase.setConfig(requestConfig);
  }

  /**
   * 获取HttpClient对象
   */
  public static CloseableHttpClient getHttpClient(String url) {
    String hostname = url.split("/")[2];
    int port = 80;
    if (hostname.contains(SymbolConstant.COLON)) {
      String[] arr = hostname.split(SymbolConstant.COLON);
      hostname = arr[0];
      port = Integer.parseInt(arr[1]);
    }
    if (httpClient == null) {
      synchronized (SYNC_LOCK) {
        if (httpClient == null) {
          httpClient = createHttpClient(100, 40, 100, hostname, port);
        }
      }
    }
    return httpClient;
  }

  public static CloseableHttpClient createHttpClient(int maxTotal, int maxPerRoute, int maxRoute, String hostname, int port) {
    ConnectionSocketFactory plainsf = PlainConnectionSocketFactory.getSocketFactory();
    LayeredConnectionSocketFactory sslsf = SSLConnectionSocketFactory.getSocketFactory();
    Registry<ConnectionSocketFactory> registry = RegistryBuilder
        .<ConnectionSocketFactory>create().register("http", plainsf).register("https", sslsf).build();
    PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
    // 将最大连接数增加
    cm.setMaxTotal(maxTotal);
    // 将每个路由基础的连接增加
    cm.setDefaultMaxPerRoute(maxPerRoute);
    HttpHost httpHost = new HttpHost(hostname, port);
    // 将目标主机的最大连接数增加
    cm.setMaxPerRoute(new HttpRoute(httpHost), maxRoute);

    // 请求重试处理
    HttpRequestRetryHandler httpRequestRetryHandler = (exception, executionCount, context) -> {
      if (executionCount >= MAX_RETRY) {// 如果已经重试了5次，就放弃
        return false;
      }
      if (exception instanceof NoHttpResponseException) {// 如果服务器丢掉了连接，那么就重试
        return true;
      }
      if (exception instanceof SSLHandshakeException) {// 不要重试SSL握手异常
        return false;
      }
      if (exception instanceof InterruptedIOException) {// 超时
        return false;
      }
      if (exception instanceof UnknownHostException) {// 目标服务器不可达
        return false;
      }
      if (exception instanceof SSLException) {// SSL握手异常
        return false;
      }

      HttpClientContext clientContext = HttpClientContext.adapt(context);
      HttpRequest request = clientContext.getRequest();
      // 如果请求是幂等的，就再次尝试
      return !(request instanceof HttpEntityEnclosingRequest);
    };

    return HttpClients.custom()
        .setConnectionManager(cm)
        .setRetryHandler(httpRequestRetryHandler).build();
  }

  /**
   * post方法调用
   *
   * @param url       请求地址
   * @param data      请求数据
   * @param headerMap 请求头数据
   * @return 返回请求结果
   */
  public static String post(String url, String data, Map<String, String> headerMap) {
    HttpPost httppost = new HttpPost(url);
    config(httppost);
    if (headerMap != null) {
      for (Map.Entry<String, String> entry : headerMap.entrySet()) {
        httppost.setHeader(entry.getKey(), entry.getValue());
      }
    }
    if (null != data && !"".equals(data)) {
      httppost.setEntity(new ByteArrayEntity(data.getBytes(StandardCharsets.UTF_8)));
    }
    CloseableHttpResponse response = null;
    try {
      response = getHttpClient(url).execute(httppost, HttpClientContext.create());
      HttpEntity entity = response.getEntity();
      String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
      EntityUtils.consume(entity);
      return result;
    } catch (Exception e) {
      log.error("send post request failed, cause : ", e);
    } finally {
      try {
        if (response != null) {
          response.close();
        }
      } catch (IOException e) {
        log.error("close response failed, cause: ", e);
      }
    }
    return null;
  }

  /**
   * get方法请求
   *
   * @param url 请求url地址
   * @return 返回结果
   */
  public static String get(String url) {
    HttpGet httpget = new HttpGet(url);
    config(httpget);
    CloseableHttpResponse response = null;
    try {
      response = getHttpClient(url).execute(httpget,
          HttpClientContext.create());
      HttpEntity entity = response.getEntity();
      String result = EntityUtils.toString(entity, "utf-8");
      EntityUtils.consume(entity);
      return result;
    } catch (IOException e) {
      log.error("send get request failed, cause : ", e);
    } finally {
      try {
        if (response != null) {
          response.close();
        }
      } catch (IOException e) {
        log.error("close response failed, cause: ", e);
      }
    }
    return null;
  }

  /**
   * get方法请求
   *
   * @param url 请求url地址
   * @return 返回结果
   */
  public static String get(String url, Map<String, String> headers) {
    HttpGet httpget = new HttpGet(url);
    config(httpget);
    for (Map.Entry<String, String> entry : headers.entrySet()) {
      httpget.setHeader(entry.getKey(), entry.getValue());
    }
    CloseableHttpResponse response = null;
    try {
      response = getHttpClient(url).execute(httpget,
          HttpClientContext.create());
      HttpEntity entity = response.getEntity();
      String result = EntityUtils.toString(entity, "utf-8");
      EntityUtils.consume(entity);
      return result;
    } catch (IOException e) {
      log.error("send get request failed, cause : ", e);
    } finally {
      try {
        if (response != null) {
          response.close();
        }
      } catch (IOException e) {
        log.error("close response failed, cause: ", e);
      }
    }
    return null;
  }
}