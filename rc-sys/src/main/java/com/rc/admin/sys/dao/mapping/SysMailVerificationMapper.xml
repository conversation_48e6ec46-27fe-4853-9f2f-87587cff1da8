<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.sys.dao.SysMailVerificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.sys.model.SysMailVerification">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="mail" property="mail"/>
        <result column="code" property="code"/>
        <result column="expired" property="expired"/>
        <result column="type" property="type"/>
    </resultMap>

    <select id="getMailByUserId" resultType="java.lang.String">
        select mail
        from sys_mail_verification
        where user_id = #{userId}
          and type = #{type}
    </select>

</mapper>
