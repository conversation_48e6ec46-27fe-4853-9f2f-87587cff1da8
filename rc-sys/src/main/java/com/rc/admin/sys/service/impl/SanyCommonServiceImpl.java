package com.rc.admin.sys.service.impl;

import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.exception.CustomException;
import com.rc.admin.sys.common.constant.Constant;
import com.rc.admin.sys.dao.SanyCommonMapper;
import com.rc.admin.sys.model.RCLoad;
import com.rc.admin.sys.service.SanyCommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service()
@Transactional(rollbackFor = Exception.class)
public class SanyCommonServiceImpl implements SanyCommonService {
    @Resource
    SanyCommonMapper sanyCommonMapper ;


    @Override
    public IPage<JSONObject> load(String tenantId, RCLoad rcLoad) {
        int pageNum = Optional.ofNullable(rcLoad.getPage()).orElse(1);
        int pageSize = Optional.ofNullable(rcLoad.getLimit()).orElse(10);
        AtomicBoolean checkInner = new AtomicBoolean(true);

        rcLoad.getJQuery().getCustom().forEach(v -> {


            v.setSql(null);
            List<RCLoad.JQueryDTO.CustomDTO.GroupDTO> check = v.getCodeList();
            if (CollectionUtils.isEmpty(check)) {
                if (checkInner.get()) {
                    if (StringUtils.isBlank(v.getCode()) || StringUtils.isBlank(v.getType())) {
                        throw new CustomException(400, "code or type is null", null);
                    }
                    if (Arrays.stream(Constant.TYPES.split(StringPool.COMMA)).noneMatch(z -> z.equalsIgnoreCase(v.getType()))) {
                        throw new CustomException(400, "对应值非枚举字符属性 支持 " + Constant.TYPES, null);
                    }
                    if (Constant.BETWEEN.equalsIgnoreCase(v.getType())) {
                        String[] split = v.getValue().split(StringPool.COMMA);
                        if (split.length != 2) {
                            throw new CustomException(400, "between对应value值错误", null);
                        }
                    }
                }

                return;
            }
            List<RCLoad.JQueryDTO.CustomDTO.GroupDTO> codeList = check.stream().filter(cd -> StringUtils.isNotBlank(cd.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(codeList)) {
                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder conn = new StringBuilder();
                stringBuilder.append(v.getCn()).append(" ( ");
                for (int i = 0; i < codeList.size(); i++) {
                    String value = codeList.get(i).getValue();
                    String cn = codeList.get(i).getCn();
                    String code = codeList.get(i).getCode();
                    String type = codeList.get(i).getType();
                    if (i > 0) {
                        conn.append(cn);
                    }

                    conn.append(" ").append(code).append(" ").append(type);
                    if (Constant.LIKE.equalsIgnoreCase(type)) {
                        conn.append(String.format(" '%%%s%%' ", value));
                    } else if (Constant.BE_EQUAL.equals(type)) {
                        conn.append(String.format(" '%s' ", value));
                    } else if (Constant.BETWEEN.equalsIgnoreCase(type)) {
                        String[] split = codeList.get(i).getValue().split(StringPool.COMMA);
                        if (split.length != 2) {
                            throw new CustomException(400, String.format("嵌套between对应value值错误 codeList[%d].value", i), null);
                        }
                        conn.append(String.format(" '%s' and '%s' ", split[0], split[1]));
                    }
                }
                conn.append(" )");
                stringBuilder.append(conn);
                v.setSql(stringBuilder.toString());
            }
        });
        Page<JSONObject> page = new Page<>(pageNum, pageSize);
        String authCheck = "";

        IPage<JSONObject> load = sanyCommonMapper.load(page, rcLoad, authCheck);
        List<JSONObject> records = load.getRecords();
//        if(CollectionUtils.isNotEmpty(records)){
//            buildUp(tenantId, userId, records);
//        }
        return load;
    }
}
