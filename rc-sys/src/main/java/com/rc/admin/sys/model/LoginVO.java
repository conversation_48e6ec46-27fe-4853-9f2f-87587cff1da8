package com.rc.admin.sys.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户登录
 *
 * <AUTHOR>
 * @date 2020/12/11
 */
@ApiModel("用户登录")
@Data
public class LoginVO {
    @NotBlank(message = "账号不能为空")
    @ApiModelProperty(value = "账号", required = true)
    private String username;
    @NotBlank(message = "密码不能为空")
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @NotBlank(message = "登录类型不能为空")
    @ApiModelProperty(value = "登录类型1-普通，2-三一域帐号", required = true)
    private String loginType;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String verifyCode;
    /**
     * 记住我
     */
    @ApiModelProperty(value = "记住我", required = true)
    private Boolean rememberMe;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码，根据配置判断是否必须")
    private String captchaVerification;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    public String getCaptchaVerification() {
        return captchaVerification;
    }

    public void setCaptchaVerification(String captchaVerification) {
        this.captchaVerification = captchaVerification;
    }

    @Override
    public String toString() {
        return "LoginVO{" +
                "username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", rememberMe=" + rememberMe +
                ", captchaVerification='" + captchaVerification + '\'' +
                '}';
    }
}
