<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.sys.dao.SanyCommonMapper">


    <select id="load" resultType="com.alibaba.fastjson.JSONObject">
        select * from ${rcLoad.tableCode}
        <where>
            <if test="rcLoad.jQuery.custom != null and rcLoad.jQuery.custom.size>0">
                <foreach collection="rcLoad.jQuery.custom" item="item">
                    <choose>
                        <when test="item.sql == null or item.sql == '' ">
                            <if test="item.value != null and item.value != '' ">
                                ${item.cn} ${item.code} ${item.type}
                                <choose>
                                    <when test='"like".equalsIgnoreCase(item.type)'>
                                        CONCAT('%',#{item.value},'%')
                                    </when>
                                    <when test="item.type == '='.toString() ">
                                        #{item.value}
                                    </when>
                                    <when test='"between".equalsIgnoreCase(item.type)'>
                                        #{item.var1} and #{item.var2}
                                    </when>
                                    <otherwise>
                                        ${item.value}
                                    </otherwise>
                                </choose>
                            </if>
                        </when>

                        <when test="item.sql != null and item.sql != '' ">
                            ${item.sql}
                        </when>
                    </choose>
                </foreach>
            </if>
            ${authCheck}
        </where>
        <if test="rcLoad.jQuery.order != null and rcLoad.jQuery.order.size>0">
            <foreach collection="rcLoad.jQuery.order" open=" order by " item="item" separator=",">
                ${item.code} ${item.type}
            </foreach>
        </if>
    </select>

</mapper>
