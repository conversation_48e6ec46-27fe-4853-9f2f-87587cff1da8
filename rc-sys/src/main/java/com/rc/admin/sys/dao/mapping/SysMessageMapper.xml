<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.sys.dao.SysMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.sys.model.SysMessage">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="subtitle" property="subtitle"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="send_date" property="sendDate"/>
        <result column="icon" property="icon"/>
        <result column="important" property="important"/>
        <result column="type" property="type"/>
        <result column="version" property="version"/>
        <result column="create_user" property="createUser"/>
        <result column="create_date" property="createDate"/>
        <result column="edit_user" property="editUser"/>
        <result column="edit_date" property="editDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <select id="selectSend" resultType="com.rc.admin.sys.model.SysMessage">
        select m.id, m.title, m.type, m.create_date, m.send_date, m.important, u.nickname, u.avatar
        from sys_message m
        left join sys_user u on m.create_user = u.id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectReceive" resultType="com.rc.admin.sys.model.SysMessage">
        select d.id, d.status as details_status, d.star, m.id as messageId, m.title, m.type, m.create_date,
        m.send_date, d.read_date, m.important, u.nickname, u.avatar
        from sys_message_detail d
        left join sys_message m on m.id = d.message_id
        left join sys_user u on u.id = m.create_user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectUnreadCount" resultType="java.lang.Integer">
        select count(1)
        from sys_message_detail d
        left join sys_message m on m.id = d.message_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectInfoById" resultType="com.rc.admin.sys.model.SysMessage">
        select m.id, m.title, m.content, m.type, m.create_date, m.send_date,
        m.important, u.nickname, u.avatar
        from sys_message m
        left join sys_user u on u.id = m.create_user
        where m.id = #{id}
    </select>
</mapper>
