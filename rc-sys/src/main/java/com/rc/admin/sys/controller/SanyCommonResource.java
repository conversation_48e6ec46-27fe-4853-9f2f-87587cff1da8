package com.rc.admin.sys.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rc.admin.sys.model.RCLoad;
import com.rc.admin.sys.service.SanyCommonService;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("api/rc")
public class SanyCommonResource {
    @Resource
    private SanyCommonService sanyCommonService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @PostMapping("/load")
    public  Object load(@RequestParam Map<String,String> params, HttpServletRequest httpServletRequest){
        logger.debug("入参 ------params[{}]", params);
        String tenantId = httpServletRequest.getSession().getAttribute("tenantId")==null?"":httpServletRequest.getSession().getAttribute("tenantId").toString();
        String tableCode = params.get("tableCode");
        String j_query = StringEscapeUtils.unescapeHtml4(params.get("j_query"));
        String noFlag = params.get("noFlag");
//        log.info("load 入参：{}", j_query);
        String limit = Optional.ofNullable(params.get("limit")).orElse("10");
        String page = Optional.ofNullable(params.get("page")).orElse("1");

        RCLoad rcLoad = new RCLoad();
        if (StringUtils.isNotBlank(j_query)) {
            rcLoad.setJQuery(JSON.parseObject(j_query, RCLoad.JQueryDTO.class));
        }
        if (StringUtils.isNotBlank(noFlag)) {
            rcLoad.setNoFlag(Boolean.parseBoolean(noFlag));
        }
        rcLoad.setLimit(Integer.valueOf(limit));
        rcLoad.setPage(Integer.valueOf(page));
        rcLoad.setTableCode(tableCode);

        logger.debug("入参 ------rcLoad[{}]", rcLoad);
        logger.debug("入参 ------tenantId[{}]", tenantId);
        IPage<JSONObject> load = sanyCommonService.load(tenantId,rcLoad);


        Map<String, Object> ret = new HashMap<>();
        ret.put("totalCount", load.getTotal());
        ret.put("rows", load.getRecords());
        ret.put("success", true);
        ret.put("code", "200");
        ret.put("message", "操作成功");
        return ret;
    }

}
