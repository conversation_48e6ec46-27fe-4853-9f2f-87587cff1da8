package com.rc.admin.sys.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rc.admin.sys.common.constant.Constant;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import com.rc.admin.util.Enum;

@NoArgsConstructor
@Data
public class RCLoad implements Serializable {
    private static final long serialVersionUID = 4542756565041219143L;
    /**
     * tableCode
     */
    @JSONField(name = "tableCode")
    @NotBlank
    private String tableCode;
    /**
     * jQuery
     */
    @JSONField(name = "j_query")
    @JsonProperty("j_query")
    @Valid
    private JQueryDTO jQuery;
    /**
     * limit
     */
    @JSONField(name = "limit")
    private Integer limit;
    /**
     * page
     */
    @J<PERSON>NField(name = "page")
    private Integer page;

    /**
     * 是否权限校验 true 默认执行
     */
    @JSONField(name = "noFlag")
    private boolean noFlag = true;

    /**
     * JQueryDTO
     */
    @NoArgsConstructor
    @Data
    public static class JQueryDTO implements Serializable {
        private static final long serialVersionUID = -8688362801870325300L;
        /**
         * custom
         */
        @JSONField(name = "custom")
        @Valid
        private List<CustomDTO> custom;
        /**
         * order
         */
        @JSONField(name = "order")
        @Valid
        private List<OrderDTO> order;
        /**
         * customTypes
         */
        @JSONField(name = "_custom_types")
        private List<String> customTypes;

        /**
         * CustomDTO
         */
        @NoArgsConstructor
        @Data
        public static class CustomDTO implements Serializable {
            private static final long serialVersionUID = -233265067081113031L;
            /**
             * cn
             */
            @JSONField(name = "cn")
            @NotBlank
            @Enum(strs = Constant.CNS, ignoreCase = true, message = "对应值非枚举字符属性 支持 " + Constant.CNS)
            private String cn;
            /**
             * value
             */
            @JSONField(name = "value")
            private String value;
            /**
             * code
             */
            @JSONField(name = "code")
            private String code;

            /**
             * 聚合查询实体
             */
            @Valid
            private List<GroupDTO> codeList;

            /**
             * 聚合拼接sql
             */
            private String sql;
            /**
             * type
             */
            @JSONField(name = "type")
            private String type;

            private String var1;

            private String var2;

            public String getVar1() {
                return this.value.split(StringPool.COMMA)[0];
            }

            public String getVar2() {
                return this.value.split(StringPool.COMMA)[1];
            }

            /**
             * groupDTO
             */
            @NoArgsConstructor
            @Data
            public static class GroupDTO implements Serializable {

                private static final long serialVersionUID = -1359486751235844412L;

                /**
                 * cn
                 */
                @JSONField(name = "cn")
                @NotBlank
                @Enum(strs = Constant.CNS, ignoreCase = true, message = "对应值非枚举字符属性 支持 " + Constant.CNS)
                private String cn;
                /**
                 * value
                 */
                @JSONField(name = "value")
                private String value;
                /**
                 * code
                 */
                @JSONField(name = "code")
                @NotBlank
                private String code;
                /**
                 * type
                 */
                @JSONField(name = "type")
                @NotBlank
                @Enum(strs = Constant.TYPES, ignoreCase = true, message = "对应值非枚举字符属性 支持 " + Constant.TYPES)
                private String type;

                private String var1;

                private String var2;

                public String getVar1() {
                    return this.value.split(StringPool.COMMA)[0];
                }

                public String getVar2() {
                    return this.value.split(StringPool.COMMA)[1];
                }
            }
        }

        /**
         * OrderDTO
         */
        @NoArgsConstructor
        @Data
        public static class OrderDTO implements Serializable {
            private static final long serialVersionUID = 744066684278161500L;
            /**
             * code
             */
            @JSONField(name = "code")
            @NotBlank
            private String code;
            /**
             * type
             */
            @JSONField(name = "type")
            @Enum(strs = Constant.SORT, ignoreCase = true, message = "对应值非枚举字符属性 支持 " + Constant.SORT)
            private String type;
        }
    }
}
