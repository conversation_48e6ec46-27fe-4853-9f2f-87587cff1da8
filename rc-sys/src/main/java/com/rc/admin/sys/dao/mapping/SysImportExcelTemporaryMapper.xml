<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rc.admin.sys.dao.SysImportExcelTemporaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.rc.admin.sys.model.SysImportExcelTemporary">
        <id column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="user_id" property="userId"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="verification_results" property="verificationResults"/>
        <result column="field1" property="field1"/>
        <result column="field2" property="field2"/>
        <result column="field3" property="field3"/>
        <result column="field4" property="field4"/>
        <result column="field5" property="field5"/>
        <result column="field6" property="field6"/>
        <result column="field7" property="field7"/>
        <result column="field8" property="field8"/>
        <result column="field9" property="field9"/>
        <result column="field10" property="field10"/>
        <result column="field11" property="field11"/>
        <result column="field12" property="field12"/>
        <result column="field13" property="field13"/>
        <result column="field14" property="field14"/>
        <result column="field15" property="field15"/>
        <result column="field16" property="field16"/>
        <result column="field17" property="field17"/>
        <result column="field18" property="field18"/>
        <result column="field19" property="field19"/>
        <result column="field20" property="field20"/>
        <result column="field21" property="field21"/>
        <result column="field22" property="field22"/>
        <result column="field23" property="field23"/>
        <result column="field24" property="field24"/>
        <result column="field25" property="field25"/>
        <result column="field26" property="field26"/>
        <result column="field27" property="field27"/>
        <result column="field28" property="field28"/>
        <result column="field29" property="field29"/>
        <result column="field30" property="field30"/>
        <result column="field31" property="field31"/>
        <result column="field32" property="field32"/>
        <result column="field33" property="field33"/>
        <result column="field34" property="field34"/>
        <result column="field35" property="field35"/>
        <result column="field36" property="field36"/>
        <result column="field37" property="field37"/>
        <result column="field38" property="field38"/>
        <result column="field39" property="field39"/>
        <result column="field40" property="field40"/>
        <result column="field41" property="field41"/>
        <result column="field42" property="field42"/>
        <result column="field43" property="field43"/>
        <result column="field44" property="field44"/>
        <result column="field45" property="field45"/>
        <result column="field46" property="field46"/>
        <result column="field47" property="field47"/>
        <result column="field48" property="field48"/>
        <result column="field49" property="field49"/>
        <result column="field50" property="field50"/>
    </resultMap>
    <update id="updateDuplicateData">
        update sys_import_excel_temporary
        set verification_results = concat(COALESCE(verification_results, ''), ${field}, '已存在;'),
            verification_status  = #{status}
        where id in (select id
                     from (select id
                           from sys_import_excel_temporary t
                           where t.user_id = #{userId}
                             and t.template_id = #{templateId}
                             and ${field} in (select ${field}
                                              from sys_import_excel_temporary t2
                                              where t2.user_id = #{userId}
                                                and t2.template_id = #{templateId}
                                              group by ${field}
                                              having count(1) > 1)
                             and id not in (select min(id)
                                            from sys_import_excel_temporary t3
                                            where t3.user_id = #{userId}
                                              and t3.template_id = #{templateId}
                                            group by ${field}
                                            having count(1) > 1)) t)
    </update>

    <select id="selectImportSummary" resultType="com.rc.admin.sys.model.SysImportExcelTemporary">
        select verification_status, count(1) as field1
        from sys_import_excel_temporary
        where template_id = #{templateId}
          and user_id = #{userId}
        group by verification_status
    </select>
    <select id="select" resultType="com.rc.admin.sys.model.SysImportExcelTemporary">
        select temp.id, ${selectFields} temp.verification_results,temp.verification_status
        from sys_import_excel_temporary temp
        ${leftJoinTable}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getReplaceTableFieldValue" resultType="java.lang.String">
        select ${replaceTableFieldValue}
        from ${table}
        where ${replaceTableFieldName} = #{value}
    </select>
    <select id="getDictReplaceTableFieldValue" resultType="java.lang.String">
        select ${replaceTableFieldValue}
        from ${table}
        where ${replaceTableFieldName} = #{value}
          and dict_type = #{dictType}
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from sys_import_excel_temporary
        where ${fieldName} = #{value}
          and id != #{id}
    </select>
    <select id="getOne" resultType="com.rc.admin.sys.model.SysImportExcelTemporary">
        select temp.id, ${selectFields} temp.template_id, temp.verification_results, temp.verification_status
        from sys_import_excel_temporary temp
            ${leftJoinTable}
        where temp.id = #{id}
    </select>


</mapper>
