package com.rc.admin.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rc.admin.common.core.common.pagination.Page;
import com.rc.admin.sys.model.SysConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统参数
 *
 * <AUTHOR>
 * @date 2019-03-03 15:52:44
 */
public interface SysConfigMapper extends BaseMapper<SysConfig> {
    /**
     * 获取列表数据
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return List<SysConfig>
     */
    List<SysConfig> select(Page<SysConfig> page, @Param("ew") QueryWrapper<SysConfig> queryWrapper);

}