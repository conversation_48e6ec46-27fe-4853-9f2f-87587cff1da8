package com.rc.admin.sys.dao;

import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rc.admin.sys.model.RCLoad;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

public interface SanyCommonMapper extends BaseMapper<T> {
    Page<JSONObject> load(Page<JSONObject> page, @Param("rcLoad") RCLoad rcLoad, @Param("authCheck") String authCheck);

}
