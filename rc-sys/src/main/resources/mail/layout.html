<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--[if IE]>
<html xmlns="http://www.w3.org/1999/xhtml" class="ie">
<![endif]--><!--[if !IE]><!-->
<html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml">
<!--<![endif]-->
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <!--<![endif]-->
    <meta name="viewport" content="width=device-width"/>
    <style type="text/css">
        @media only screen and (min-width: 620px) {
            .wrapper {
                min-width: 600px !important
            }

            .wrapper h1 {
                font-size: 26px !important;
                line-height: 34px !important
            }
        }
    </style>
    <meta name="x-apple-disable-message-reformatting"/>
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
        }

        table {
            border-collapse: collapse;
            table-layout: fixed;
        }

        * {
            line-height: inherit;
        }

        [x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
        }


        .btn a:hover,
        .btn a:focus,
        .email-footer__links a:hover,
        .email-footer__links a:focus {
            opacity: 0.8;
        }

        .layout,
        .column {
            transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
        }

        .preheader td {
            padding-bottom: 8px;
        }

        .layout{
            max-width: 400px !important;
            -fallback-width: 95% !important;
            width: calc(100% - 20px) !important;
        }

        .stack .column {
            max-width: 400px !important;
            width: 100% !important;
        }

        .ie .stack .column {
            display: table-cell;
            float: none !important;
        }

        .ie .layout {
            max-width: 600px !important;
            width: 600px !important;
        }

        .ie .two-col .column {
            max-width: 300px !important;
            width: 300px !important;
        }

        .ie .three-col .column{
            max-width: 200px !important;
            width: 200px !important;
        }

        .ie .stack.two-col.has-gutter .column {
            max-width: 290px !important;
            width: 290px !important;
        }

        .ie .stack.three-col.has-gutter .column {
            max-width: 188px !important;
            width: 188px !important;
        }

        .ie .stack.two-col.has-gutter.has-border .column {
            max-width: 292px !important;
            width: 292px !important;
        }

        .ie .stack.three-col.has-gutter.has-border .column{
            max-width: 190px !important;
            width: 190px !important;
        }

        .layout-fixed-width {
            background-color: #ffffff;
        }

        @media only screen and (min-width: 620px) {
            .column {
                display: table-cell;
                Float: none !important;
                vertical-align: top;
            }

            .layout,
            .one-col .column {
                max-width: 600px !important;
                width: 600px !important;
            }

            .two-col .column {
                max-width: 300px !important;
                width: 300px !important;
            }

            .three-col .column {
                max-width: 200px !important;
                width: 200px !important;
            }

            .two-col.has-gutter .column,
            .two-col.x_has-gutter .column {
                max-width: 290px !important;
                width: 290px !important;
            }

            .three-col.has-gutter .column,
            .three-col.x_has-gutter .column{
                max-width: 188px !important;
                width: 188px !important;
            }

            .two-col.has-gutter.has-border .column,
            .two-col.x_has-gutter.x_has-border .column {
                max-width: 292px !important;
                width: 292px !important;
            }

            .three-col.has-gutter.has-border .column,
            .three-col.x_has-gutter.x_has-border .column {
                max-width: 190px !important;
                width: 190px !important;
            }
        }

        @media (max-width: 321px) {
            .layout,
            .stack .column {
                min-width: 320px !important;
                width: 320px !important;
            }
        }

        .mso div {
            border: 0 none white !important;
        }
    </style>

    <!--[if !mso]><!-->
    <style type="text/css">
        body {
            background-color: #ededf1
        }

        .logo a:hover, .logo a:focus {
            color: #859bb1 !important
        }

        .mso h1, .ie h1 {
            font-size: 26px !important;
            line-height: 34px !important
        }
    </style>
    <meta name="robots" content="noindex,nofollow"/>
    <meta property="og:title" content="${projectName}"/>
</head>
<!--[if mso]>
<body class="mso">
<![endif]-->
<!--[if !mso]><!-->
<body class="full-padding" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;">
<!--<![endif]-->
<table class="wrapper"
       style="border-collapse: collapse;table-layout: fixed;min-width: 320px;width: 100%;background-color: #ededf1;"
       cellpadding="0" cellspacing="0" role="presentation">
    <tbody>
    <tr>
        <td>
            <div style="mso-line-height-rule: exactly;line-height: 20px;font-size: 20px;">&nbsp;</div>
            <div>
                <div class="layout one-col fixed-width stack"
                     style="Margin: 0 auto;max-width: 600px;min-width: 320px; width: 320px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;">
                    <div class="layout__inner"
                         style="border-collapse: collapse;display: table;width: 100%;background-color: #ffffff;">
                        <!--[if (mso)|(IE)]>
                        <table align="center" cellpadding="0" cellspacing="0" role="presentation">
                            <tr class="layout-fixed-width" style="background-color: #ffffff;">
                                <td style="width: 600px" class="w560"><![endif]-->
                        <div class="column"
                             style="text-align: left;color: #7c7e7f;font-size: 14px;line-height: 21px;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';">

                            <div style="Margin-left: 20px;Margin-right: 20px;Margin-top: 24px;">
                                <div style="mso-line-height-rule: exactly;line-height: 10px;font-size: 1px;">&nbsp;
                                </div>
                            </div>

                            <div style="Margin-left: 20px;Margin-right: 20px;">
                                <div style="mso-line-height-rule: exactly;mso-text-raise: 11px;vertical-align: middle;">
                                    <h1 style="Margin-top: 12px;Margin-bottom: 0;font-style: normal;font-weight: normal;color: #3e4751;font-size: 22px;line-height: 31px;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';text-align: center;">${projectName}</h1>
                                    <p style="Margin-top: 20px;Margin-bottom: 20px;text-align: center;"><span>通用管理系统快速开发框架</span>
                                    </p>
                                </div>
                            </div>

                            <div style="Margin-left: 20px;Margin-right: 20px;">
                                <div class="divider"
                                     style="display: block;font-size: 2px;line-height: 2px;Margin-left: auto;Margin-right: auto;width: 40px;background-color: #b4b4c4;Margin-bottom: 20px;">
                                    &nbsp;
                                </div>
                            </div>

                            <div style="Margin-left: 20px;Margin-right: 20px;">
                                <div style="mso-line-height-rule: exactly;line-height: 5px;font-size: 1px;">&nbsp;</div>
                            </div>
                            ${layoutContent}
                        </div>
                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                    </div>
                </div>

                <div style="mso-line-height-rule: exactly;line-height: 20px;font-size: 20px;">&nbsp;</div>

                <div class="layout two-col has-gutter stack"
                     style="Margin: 0 auto;max-width: 600px;min-width: 320px; width: 320px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;">
                    <div class="layout__inner" style="border-collapse: collapse;display: table;width: 100%;">
                        <!--[if (mso)|(IE)]>
                        <table align="center" cellpadding="0" cellspacing="0" role="presentation">
                            <tr>
                                <td valign="top"><![endif]-->
                        <!--[if (mso)|(IE)]></td>
                    <td valign="top"><![endif]-->
                        <div class="column"
                             style="max-width: 600px;min-width: 320px; width: 320px;">
                            <table class="column__background"
                                   style="border-collapse: collapse;table-layout: fixed;background-color: #ffffff;"
                                   cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                <tbody>
                                <tr>
                                    <td style="text-align: left;color: #7c7e7f;font-size: 14px;line-height: 21px;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';">
                                        <div style="Margin-left: 20px;Margin-right: 20px;Margin-top: 24px;Margin-bottom: 24px;">
                                            <div style="mso-line-height-rule: exactly;mso-text-raise: 11px;vertical-align: middle;">
                                                <h2 style="Margin-top: 0;Margin-bottom: 0;font-style: normal;font-weight: normal;color: #3e4751;font-size: 16px;line-height: 24px;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';">
                                                    <strong>关于我们</strong></h2>
                                                <p style="Margin-top: 20px;Margin-bottom: 0;"><strong>官方网站：</strong>
                                                    <a style="text-decoration: underline;transition: opacity 0.1s ease-in;color: #008fff;" href="http://www.easy-frame.top/" data-emb-iscopy="true">http://www.easy-frame.top/</a>
                                                </p>

                                                <p style="Margin-top: 20px;Margin-bottom: 0;"><strong>邮箱地址：</strong> <EMAIL></p>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                    </div>
                </div>
            </div>
            <div style="line-height:40px;font-size:40px;">&nbsp;</div>
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>
