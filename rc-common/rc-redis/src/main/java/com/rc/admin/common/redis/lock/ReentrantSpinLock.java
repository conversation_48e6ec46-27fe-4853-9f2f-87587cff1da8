package com.rc.admin.common.redis.lock;

import com.rc.admin.common.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component("reentrantSpinLock")
public class ReentrantSpinLock implements RedisLock {

    private static ThreadLocal<String> localUID = new ThreadLocal<>();
    private static ThreadLocal<Integer> localCount = new ThreadLocal<>();
    private static final long REENTRY_INTERVAL = 1000;

    @Override
    public boolean tryLock(String key, long timeout, TimeUnit unit) {
        boolean isLock;
        if (localUID.get() == null) {
            String uid = UUID.randomUUID().toString();
            localUID.set(uid);
            while (true) {
                isLock = RedisUtil.setIfAbsent(key, uid, timeout, unit);
                if (isLock) {
                    localCount.set(0);
                    break;
                }
                try {
                    Thread.sleep(REENTRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error("Thread sleep err:", e);
                }
            }
        } else {
            isLock = true;
        }
        localCount.set(localCount.get() + 1);
        return isLock;
    }

    @Override
    public void releaseLock(String key) {
        if (localUID.get() != null && StringUtils.equalsIgnoreCase(localUID.get(), (String) RedisUtil.get(key))) {
            if (localCount.get() != null && localCount.get() > 0) {
                localCount.set(localCount.get() - 1);
            }
            if (localCount.get() == 0) {
                RedisUtil.del(key);
                localUID.remove();
                localCount.remove();
            }
        }
    }
}
