package com.rc.admin.common.redis.cache;

import com.rc.admin.common.redis.model.ServiceEnum;

import java.util.List;

/**
 * 权限缓存处理器
 *
 * <AUTHOR>
 * @Date 2021/7/2 12:36
 **/
public class AuthCacheProcessor {

  private AIValueOperations aiValueOperations;

  public AuthCacheProcessor(AIValueOperations aiValueOperations) {
    this.aiValueOperations = aiValueOperations;
  }


  /**
   * 检测企业角色权限缓存是否存在
   *
   * @param organizationId
   * @param roleId
   * @return
   */
  public boolean isOrgRoleAuthCacheExist(String organizationId, String roleId) {
    return aiValueOperations.hasKey(ServiceEnum.TCS_COMMON, buildOrgRoleCacheKey(organizationId, roleId));
  }




  public void addUserInfoCache(UserInfoCache userInfoCache, String userId) {
    aiValueOperations.set(ServiceEnum.TCS_COMMON, buildUserInfoCacheKey(userId), userInfoCache);
  }

  public UserInfoCache getUserInfoCache(String userId) {
    return aiValueOperations.get(ServiceEnum.TCS_COMMON, buildUserInfoCacheKey(userId), UserInfoCache.class);
  }

  public void deleteUserInfoCache(List<String> userIds) {
    for (String userId : userIds) {
      aiValueOperations.delete(ServiceEnum.TCS_COMMON, buildUserInfoCacheKey(userId));
    }
  }

  /**
   * 清空企业角色权限缓存
   * @param organizationId 企业ID
   * @param roleId         角色ID
   */
  public void clearOrgRoleCache(String organizationId, String roleId) {
    aiValueOperations.delete(ServiceEnum.TCS_COMMON, buildOrgRoleCacheKey(organizationId, roleId));
  }


  /**
   * 生成redis缓存KEY
   *
   * @param organizationId
   * @param roleId
   * @return
   */
  private String buildOrgRoleCacheKey(String organizationId, String roleId) {
    return "auth:" + organizationId + "-" + roleId;
  }

  private String buildUserInfoCacheKey(String userId) {
    return "auth:user:" + userId;
  }
}