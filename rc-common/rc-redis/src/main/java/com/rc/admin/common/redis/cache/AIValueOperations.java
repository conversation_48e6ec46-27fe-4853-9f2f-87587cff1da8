package com.rc.admin.common.redis.cache;

import cn.hutool.json.JSONUtil;
import com.rc.admin.common.redis.model.ServiceEnum;
import com.rc.admin.common.redis.util.JsonUtils;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

/**
 * Redis操作装饰接口 封装SpringRedisTemplate的OpsForValue实例，所有操作都需要增加ServiceEnum，用于做key前缀.
 *
 * <AUTHOR>
 * @Date 2021/4/28 15:11
 **/
public class AIValueOperations {

  private final ValueOperations<String, String> valueOperations;

  private final RedisTemplate<String, String> redisTemplate;

  public AIValueOperations(RedisTemplate<String, String> redisTemplate) {
    this.valueOperations = redisTemplate.opsForValue();
    this.redisTemplate = redisTemplate;
  }

  /**
   * redis SET 操作.
   */
  public void set(ServiceEnum serviceEnum, String key, Object value) {
    try {
      valueOperations.set(composeKey(serviceEnum, key), JSONUtil.toJsonStr(value));
    } catch (Exception e) {
      //log.error("Parse object to JSON String failed.", e);
    }
  }

  /**
   * redis SET 操作.
   */
  public void set(String key, String value) {
    valueOperations.set(key, value);
  }

  /**
   * Redis带失效时间的SET 操作.
   *
   * @param serviceEnum 服务枚举
   * @param key         key
   * @param value       值
   * @param timeout     失效时长
   * @param unit        失效时间单位
   */
  public void set(ServiceEnum serviceEnum, String key, Object value, long timeout, TimeUnit unit) {
    try {
      valueOperations.set(composeKey(serviceEnum, key), JSONUtil.toJsonStr(value),
          timeout, unit);
    } catch (Exception e) {
      //log.error("Parse object to JSON String failed.", e);
    }

  }

  /**
   * Redis GET 操作分装.
   */
  public <T> T get(ServiceEnum serviceEnum, String key, Class<T> clazz) {
    try {
      String s = valueOperations.get(composeKey(serviceEnum, key));
      return JsonUtils.getObjectFromJsonString(s,clazz);
    } catch (IOException e) {
      e.printStackTrace();
      //log.error("Parse JSON String to " + clazz + " object failed.", e);
    }
    return null;
  }

  public String get(ServiceEnum serviceEnum, String key) {
    return valueOperations.get(composeKey(serviceEnum, key));

  }
  public String get(String key) {
    return valueOperations.get(key);

  }

  /**
   * 将指定key 设置为指定的timeout失效时间.
   *
   * @param serviceEnum 服务枚举
   * @param key         redis key
   * @param timeout     新失效时间
   * @param timeUnit    时间单位
   */
  public void expire(ServiceEnum serviceEnum, String key, long timeout, TimeUnit timeUnit) {
    redisTemplate.expire(composeKey(serviceEnum, key), timeout, timeUnit);
  }

  /**
   * 移除指定key的REDIS缓存.
   *
   * @param serviceEnum 服务枚举
   * @param key         redis key
   * @return 是否移除成功
   */
  public Boolean delete(ServiceEnum serviceEnum, String key) {
    return redisTemplate.delete(composeKey(serviceEnum, key));
  }

  /**
   * 检测key是否存在.
   */
  public boolean hasKey(ServiceEnum serviceEnum, String key) {
    return redisTemplate.hasKey(composeKey(serviceEnum, key));
  }

  /**
   * 查看某个key的剩余时间.
   */
  public Long getExpire(ServiceEnum serviceEnum, String key) {
    return redisTemplate.getExpire(composeKey(serviceEnum, key));
  }

  /**
   * redis 对于值进行bit位的操作.
   *
   * @param serviceEnum 服务前缀
   * @param key         key
   * @param offset      偏移量 从高位到低位  比如 01001011  offset 0 =0  offset 1 = 1 offset 2 =0
   * @param value       是否设置该位
   */
  public void setBit(ServiceEnum serviceEnum, String key, long offset, boolean value) {
    valueOperations.setBit(composeKey(serviceEnum, key), offset, value);
  }

  /**
   * redis 获取bit位.
   *
   * @param serviceEnum 服务前缀
   * @param key         key
   * @param offset      偏移量 从高位到低位  比如 01001011  offset 0 =0  offset 1 = 1 offset 2 =0
   * @return 0 or 1
   */
  public Boolean getBit(ServiceEnum serviceEnum, String key, long offset) {
    return Optional.ofNullable(valueOperations.getBit(composeKey(serviceEnum, key), offset))
        .orElse(false);
  }

  /**
   * redis 获取bit位为1的总数.
   *
   * @param serviceEnum 服务前缀
   * @param key         key
   */
  public Long getBitCount(ServiceEnum serviceEnum, String key) {
    return redisTemplate.execute((RedisCallback<Long>) connection -> connection.bitCount(
        composeKey(serviceEnum, key).getBytes()));
  }

  /**
   * 给Key加上服务前缀.
   *
   * @param serviceEnum 服务枚举
   * @param key         key
   */
  public static String composeKey(ServiceEnum serviceEnum, String key) {
    return serviceEnum.keyPrefix + ":" + key;
  }
}