package com.rc.admin.common.redis.model;


import com.rc.admin.common.redis.constant.ServiceNameConstants;

public enum ServiceEnum {

  /**
   * 平台公共通用设定使用的服务枚举
   */
  TCS_COMMON(ServiceNameConstants.TCS_COMMON, 0, "cm"),

  /**
   * TCS基础平台服务
   */
  TCS_PLATFORM_SERVICE(ServiceNameConstants.TCS_PLATFORM_SERVICE, 900, "dmq"),

  /**
   * 三一TCS
   */
  SANY_TCS(ServiceNameConstants.SANY_TCS, 500, "se"),
 /**
   * 点位核验
   */
  POINT_VERIFICATION(ServiceNameConstants.POINT_VERIFICATION, 800, "pv"),
 /**
   * 设备信息
   */
  DEVICE_INFO(ServiceNameConstants.DEVICE_INFO, 800, "device");

  /**
   * 微服务名称，用于Feign接口等进行微服务路由
   */
  public final String serviceName;

  /**
   * appID
   */
  public final Integer appId;

  /**
   * key前缀。如作为Redis key前缀，避免不同微服务的Redis Key冲突，语雀知识库也是用这个作为key前缀
   */
  public final String keyPrefix;

  ServiceEnum(String serviceName, Integer appId, String keyPrefix) {
    this.serviceName = serviceName;
    this.appId = appId;
    this.keyPrefix = keyPrefix;
  }

}