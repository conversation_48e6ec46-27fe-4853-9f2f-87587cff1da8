package com.rc.admin.common.redis.cache;


import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/7/2 16:03
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoCache implements Serializable {

  /**
   * 用户角色ID列表
   */
  private List<String> roleIdList;

  /**
   * jwt版本
   */
  private long jwtVersion;
}