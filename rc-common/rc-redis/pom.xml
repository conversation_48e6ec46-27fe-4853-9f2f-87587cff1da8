<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>rc-admin</artifactId>
        <groupId>com.rc.admin</groupId>
        <version>0.1.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>rc-redis</artifactId>
    <packaging>jar</packaging>

    <description>缓存</description>

    <dependencies>
        <dependency>
            <groupId>com.rc.admin</groupId>
            <artifactId>rc-core</artifactId>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>


</project>