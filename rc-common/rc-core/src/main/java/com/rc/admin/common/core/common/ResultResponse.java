package com.rc.admin.common.core.common;


import com.rc.admin.common.core.common.status.ResultCode;

/**
 * <AUTHOR>
 */
public class ResultResponse {

    private static final String DEFAULT_SUCCESS_MESSAGE = "SUCCESS";

    /**
     * 只返回状态,没有数据返回
     **/
    public static Result getSuccessResult() {
        return new Result().setCode(ResultCode.SUCCESS).setMessage(DEFAULT_SUCCESS_MESSAGE);
    }

    /**
     * 返回成功状态同时返回数据
     **/
    public static Result getSuccessResult(Object data) {
        return new Result().setCode(ResultCode.SUCCESS).setMessage(DEFAULT_SUCCESS_MESSAGE).setData(data);
    }

    /**
     * 返回成功状态同时返回数据
     **/
    public static Result getSuccessResult(String data) {
        return new Result().setCode(ResultCode.SUCCESS).setMessage(DEFAULT_SUCCESS_MESSAGE).setData(data);
    }

    /**
     * 失败
     **/
    public static Result getFailResult(String message) {
        return new Result().setCode(ResultCode.BAD_REQUEST).setMessage(message);
    }


    public static Result getAuthorizedResult(String message) {
        return new Result().setCode(ResultCode.UNAUTHORIZED).setMessage(message);
    }

}
