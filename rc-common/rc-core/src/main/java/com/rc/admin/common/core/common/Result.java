package com.rc.admin.common.core.common;

import com.rc.admin.common.core.common.status.ResultCode;

/**
 * <AUTHOR>
 */
public class Result {
    private String code;

    private String message = "success";

    private Object data;


    public String getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }

    public Object getData() {
        return data;
    }



    public Result setCode(ResultCode resultCode) {
        this.code = resultCode.code;
        return this;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Result setMessage(String message) {
        this.message = message;
        return this;
    }

    public Result setData(Object data) {
        this.data = data;
        return this;
    }
}
