package com.rc.admin.common.core.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/10/31 11:09
 * @describe
 */
public class DateUtils {

    private static final Logger LOG = LoggerFactory.getLogger(DateUtils.class);
    private static final String UTC_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    private static final String SIMPLE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final String BJ_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static String StringTimeTwoUTCtime(String str) {
        SimpleDateFormat utcFormat = new SimpleDateFormat(UTC_FORMAT);
        SimpleDateFormat FORMAT = new SimpleDateFormat(SIMPLE_FORMAT);
        utcFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = FORMAT.parse(str);
        } catch (Exception e) {
            LOG.info("StringTimeTwoUTCtime() -->{}", e);
        }
        return utcFormat.format(date);
    }

    /**
     * UTC时间转北京时间
     *
     * @param utcStr    UTC时间字符串，eg:2022-11-07T21:59:49.831Z
     * @param utcFormat UTC时间格式字符串,eg:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
     * @param format    北京时间格式字符串,eg:yyyy-MM-dd HH:mm:ss
     * @return java.lang.String 北京时间字符串,eg:2022-11-08 05:59:49
     * <AUTHOR>
     * @date 2022-11-09 21:04
     */
    public static String utc2bj(String utcStr, String utcFormat, String format) {
        String bjDateStr = null;
        if (StringUtils.isBlank(utcStr)) {
            return "";
        }
        try {
            if (StringUtils.isBlank(utcFormat)) {
                utcFormat = UTC_FORMAT;
            }
            if (StringUtils.isBlank(format)) {
                format = BJ_FORMAT;
            }
            SimpleDateFormat sdf = new SimpleDateFormat(utcFormat);
            Date utcDate = sdf.parse(utcStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(utcDate);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            bjDateStr = simpleDateFormat.format(calendar.getTime());
        } catch (Exception e) {
            LOG.error("UTC时间转北京时间异常：{}", e.getMessage());
            e.printStackTrace();
        }
        //LOG.info("转换后的北京时间：{}", bjDateStr);
        return bjDateStr;
    }

    // 获取当天的开始时间
    public static Date getDayBegin() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // 获取当天的结束时间
    public static Date getDayEnd() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    // 获取昨天的开始时间
    public static Date getBeginDayOfYesterday() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayBegin());
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }

    // 获取昨天的结束时间
    public static Date getEndDayOfYesterDay() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayEnd());
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) //闰年
                {
                    timeDistance += 366;
                } else //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else //不同年
        {
            //System.out.println("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    /**
     * ISO8601格式转换为yyyy-MM-dd HH:mm:ss
     *
     * @param oldDateStr 需要格式化的ISO8601时间字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-12-10 10:53
     */
    public static String dealDateFormat(String oldDateStr) {
        if (StringUtils.isNotBlank(oldDateStr)) {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
            SimpleDateFormat df1 = new SimpleDateFormat("EEE MMM dd HH:mm:ss Z yyyy", Locale.UK);
            try {
                Date date = df.parse(oldDateStr);
                Date date1 = df1.parse(date.toString());
                DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return df2.format(date1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 获取某月有多少天
     *
     * @param str 需要查询的月份 2023-06
     * @return java.lang.BigDecimal
     * <AUTHOR>
     * @date 2023-06-21 10:53
     */
    public static BigDecimal getMothDay(String str) {
        if (!StringUtils.isNotBlank(str)) {
            return BigDecimal.ZERO;

        }else {
            YearMonth yearMonth = YearMonth.parse(str);
            int daysInMonth = yearMonth.lengthOfMonth();
            return new BigDecimal(daysInMonth);
        }
    }
}
