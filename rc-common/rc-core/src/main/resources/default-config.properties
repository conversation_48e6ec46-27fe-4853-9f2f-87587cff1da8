#\u6B64\u914D\u7F6E\u6587\u4EF6\u4E3A\u9ED8\u8BA4\u7684\u914D\u7F6E,\u53EF\u4EE5\u5728application.yml/application-*.yml\u4E2D\u8986\u76D6

######################### project\u9ED8\u8BA4\u914D\u7F6E #########################

######################### springs\u9ED8\u8BA4\u914D\u7F6E #########################
spring.aop.proxy-target-class=true
# \u6392\u9664\u81EA\u52A8\u914D\u7F6E\u7C7B
spring.autoconfigure.exclude[0]=com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.autoconfigure.exclude[1]=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
spring.autoconfigure.exclude[2]=org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

# \u6392\u9664\u4E00\u4E9B\u9759\u6001\u8D44\u6E90\uFF0C\u4EE5\u63D0\u9AD8\u6548\u7387
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico
# \u5141\u8BB8\u4E00\u6B21\u6267\u884C\u591A\u6761\u8BED\u53E5
spring.datasource.dynamic.druid.wall.multi-statement-allow=true
# \u5141\u8BB8\u975E\u57FA\u672C\u8BED\u53E5\u7684\u5176\u4ED6\u8BED\u53E5
spring.datasource.dynamic.druid.wall.none-base-statement-allow=true
# \u5141\u8BB8\u83B7\u53D6 remarks
spring.datasource.dynamic.druid.connection-properties.remarks=true
spring.datasource.dynamic.druid.connection-properties.useInformationSchema=true

# \u8BBE\u7F6E\u7F16\u7801\u683C\u5F0F
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
spring.messages.encoding=UTF-8
# header \u6700\u5927\u957F\u5EA6
server.max-http-header-size=100KB

###################### mybatis-plus\u9ED8\u8BA4\u914D\u7F6E #######################
# mapper\u8DEF\u5F84
mybatis-plus.mapper-locations=classpath*:com/rc/admin/**/mapping/*.xml

# \u5B9E\u4F53\u7C7B\u8DEF\u5F84
mybatis-plus.typeAliasesPackage=com.rc.admin.**.model
# \u903B\u8F91\u5220\u9664
mybatis-plus.global-config.logic-delete-value=0
mybatis-plus.global-config.logic-not-delete-value=1
# \u91C7\u7528\u9A7C\u5CF0\u547D\u540D\u65B9\u5F0F\u8F6C\u6362
mybatis-plus.configuration.map-underscore-to-camel-case=true
#\u914D\u7F6E\u7684\u7F13\u5B58\u7684\u5168\u5C40\u5F00\u5173
mybatis-plus.configuration.cache-enabled=true
# \u5EF6\u65F6\u52A0\u8F7D\u7684\u5F00\u5173
mybatis-plus.configuration.lazyLoadingEnabled=true
# \u5F00\u542F\u7684\u8BDD\uFF0C\u5EF6\u65F6\u52A0\u8F7D\u4E00\u4E2A\u5C5E\u6027\u65F6\u4F1A\u52A0\u8F7D\u8BE5\u5BF9\u8C61\u5168\u90E8\u5C5E\u6027\uFF0C\u5426\u5219\u6309\u9700\u52A0\u8F7D\u5C5E\u6027
mybatis-plus.configuration.multipleResultSetsEnabled=true
# \u6570\u636Eid\u7C7B\u578B
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#mybatis-plus.global-config.db-config.id-type=uuid