<?xml version="1.0" encoding="UTF-8"?>
<configuration  scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>rc-admin</contextName>
    <springProperty scope="context" name="log.path" source="logging.file.path"/>

    <!-- 日志文件名称 -->
    <property name="log.name" value="rc-admin" />

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${log.name}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件不超过2MB -->
            <maxFileSize>2MB</maxFileSize>
            <!-- 最多保留30天 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file" />
    </root>

    <!-- 开发环境 -->
    <springProfile name="dev">
        <logger name="com.rc.admin" level="info" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="file" />
        </logger>
    </springProfile>

    <!-- 生产环境 -->
    <springProfile name="prod">
        <!-- 控制台+文件,级别为info -->
        <root level="info" />
    </springProfile>

</configuration>